Task Name,Dataset Count,Common Datasets (Top 10),Benchmarks,SOTA Metrics,Subtasks,Categories,Description (First 200 chars)
Question Answering,416,WikiQA; FreebaseQA; TupleInf Open IE Dataset; COVID-Q; ToolQA; CliCR; PDFVQA; PubChemQA; JEC-QA; ConceptNet,VNHSGE-Biology; VNHSGE-English; WikiQA; squadshifts nyt; RecipeQA; UniProtQA; CliCR; COPA; StepGame; PubChemQA,% true (GPT-judge); ROUGE-1; Accuracy-NE; Execution Accuracy; RACE-h; % info; Exact Match; F1 Score; RACE; EM,Long Form Question Answering; Generative Question Answering; True or False Question Answering; Science Question Answering; Answer Selection,Miscellaneous; Reasoning; Natural Language Processing,Question answering can be segmented into domain-specific tasks like community question answering and knowledge-base question answering. Popular benchmark datasets for evaluation question answering sys...
Classification,222,MiST; NLP Taxonomy Classification Data; uBench; DiscoEval; Digital twin-supported deep learning for fault diagnosis; DTD; Morphological Classification of Galaxies; MCSI; Open Radar Datasets; Food Recall Incidents Dataset,EuroSAT; Reddit Ideology Database; Brain Tumor MRI Dataset; MHIST; Adult; CIFAR-10C; stanford-cars; patch-camelyon; DTD; TCGA,Balanced Multi-Class Accuracy; Accuracy on Brightness Corrupted Images; Macro F1; Validation Accuracy; Overall accuracy after last sequence; Accuracy (%); Frame Level Accuracy; 10 fold Cross validation; Detection AUROC (severity 5); Accuracy,Classifier calibration; Learning with coarse labels; Sensitivity Classification; Text Classification; Medical Image Classification,Time Series; Computer Vision; Computer Code; Audio; Natural Language Processing; Adversarial; Medical; Miscellaneous; Methodology; Reasoning; Graphs,"**Classification** is the task of categorizing a set of data into predefined classes or groups. The aim of classification is to train a model to correctly predict the class or group of new, unseen dat..."
Text Classification,167,TREC-10; MiST; NLP Taxonomy Classification Data; Trilemma Dataset; An Amharic News Text classification Dataset; CARER; RESD; FMC-MWO2KG; ShopTC-100K; NOVIC Caption-Object Data,TREC-10; GLUE COLA; financial_phrasebank; An Amharic News Text classification Dataset; Yelp-2; FMC-MWO2KG; Patents; trec; Yahoo! Answers; BoolQ,Weighted F1; Accuracy (2 classes); weighted-F1 score; nDCG@5; Macro F1; Error; Evaluation Macro F1; Accuracy; nDCG@3; macro F1,Few-Shot Text Classification; Semi-Supervised Text Classification; Variable Detection; Hierarchical Text Classification of Blurbs (GermEval 2019); Emotion Classification,Natural Language Processing,**Text Classification** is the task of assigning a sentence or document an appropriate category. The categories depend on the chosen dataset and can range from topics.     Text Classification problems...
Language Modelling,166,BIG-bench; Tencent ML-Images; Alexa Point of View; Kite; RONEC; CoarseWSD-20; BabyLM; Jericho; FewCLUE; LAMBADA,CLUE (C3); WikiText-103; One Billion Word; Penn Treebank (Word Level);  StackExchange; Curation Corpus; Books3; WikiText-2;  Ubuntu IRC; LAMBADA,Test perplexity; Speaker Consistency; Bit per Character (BPC); Background Alignment; 0..5sec; TPUv3 Hours; Sentiment Alignment; Steps; Accuracy; Perplexity,Protein Language Model; Controllable Language Modelling; Sentence Pair Modeling; Long-range modeling; Cross-Document Language Modeling,Miscellaneous; Natural Language Processing; Medical,"A language model is a model of natural language. Language models are useful for a variety of tasks, including speech recognition, machine translation, natural language generation (generating more huma..."
Text Generation,162,LMSYS-USP; WinoGrande; LogiQA; ILSP Greek Evaluation Suite; Negotiation Dialogues Dataset; CoLA; SAD-Instruct; WikiDocEdits; AlpacaEval; Visual Writing Prompts,HateBR Binary; gsarti/flores_101_tha; WinoGrande; LogiQA; MNLI; GSM8k TR; PolContro; Stories/Jokes; CoLA; gsarti/flores_101_zul,ROUGE-L; CIDEr; Accuracy; Perplexity; BLEU-1; BLEU; METEOR; Distinct-2; Distinct-3; NLL,Story Generation; Code Documentation Generation; Dialogue Generation; Sonnet Generation; Fact-based Text Editing,Computer Code; Natural Language Processing; Adversarial; Speech,"**Text Generation** is the task of generating text with the goal of appearing indistinguishable to human-written text. This task is more formally known as ""natural language generation"" in the literatu..."
Sentiment Analysis,105,Capriccio; MPQA Opinion Corpus; MultiBooked; Chinese social media suicide risk and cognitive distortions classification; PANDORA; SubjQA; GeoCoV19; TUNIZI; LSICC; Perspectrum,Amazon Polarity; SLUE; Sentiment Merged; ChnSentiCorp; MPQA; ArSAS; Sogou News; DBRD; ASTD; RuSentiment,Average; Accuracy (2 classes); Training Time; Weighted F1; 1:3 Accuracy; Hate; Macro F1; DVD; Error; Sentiment,target-oriented opinion words extraction; Aspect-Based Sentiment Analysis (ABSA); Vietnamese Aspect-Based Sentiment Analysis; Twitter Sentiment Analysis; Multimodal Sentiment Analysis,Computer Vision; Natural Language Processing,"**Sentiment Analysis** is the task of classifying the polarity of a given text. For instance, a text-based tweet can be categorized into either ""positive"", ""negative"", or ""neutral"". Given the text and..."
Text Summarization,98,WikiCatSum; Elsevier OA CC-BY; CNewSum; ShopTC-100K; PubMedCite; KP20k; SAMSum; XL-Sum; DUC 2007; TQBA++,LCSTS; ACI-Bench; DUC 2004 Task 1; EurekaAlert; S2ORC; BillSum; BookSum; GovReport; Klexikon; MentSum,ROUGE-1; Spearman Correlation; Rouge-L; ROUGE-L; rouge1; Rouge2; Rouge-1; BertScoreF1; ROUGE; BLEU,Opinion Summarization; Extractive Text Summarization; Unsupervised Opinion Summarization; Long-Form Narrative Summarization; Query-Based Extractive Summarization,Natural Language Processing; Knowledge Base,"**Text Summarization** is a natural language processing (NLP) task that involves condensing a lengthy text document into a shorter, more compact version while still retaining the most important inform..."
Domain Adaptation,96,JESC; OpenGDA; Adaptiope; Digital twin-supported deep learning for fault diagnosis; SemanticUSL; DomainNet; ThirdToFirst; Stanceosaurus; Five-Billion-Pixels; Perspectrum,UCF-to-Olympic; Foggy Cityscapes; MoLane; SYNTHIA-to-FoggyCityscapes; Sim10k; Olympic-to-HMDBsmall; SYNTHIA-to-Cityscapes; GTA-to-FoggyCityscapes; HMDBfull-to-UCF; Cityscapes-to-FoggyDriving,Extra Manual Annotation; Accuracy; Average Accuracy; mPQ; Classification Accuracy; SSIM;  mAP; Lane Accuracy (LA); mAP; mIoU,Prompt-driven Zero-shot Domain Adaptation; Universal Domain Adaptation; Unsupervised Domain Adaptation; Online Domain Adaptation; Video Domain Adapation,Methodology; Computer Vision,**Domain Adaptation** is the task of adapting models across domains. This is motivated by the challenge where the test and training datasets fall from different data distributions due to some factor. ...
Reading Comprehension,95,Shmoop Corpus; CosmosQA; UIT-ViNewsQA; CS; C3; CliCR; CBT; JEC-QA; DMQA; CodeQueries,ReClor; CrowdSource QA; RadQA; RACE; AdversarialQA; ReCAM; MuSeRC,EM ; Test; Overall: F1; D(BiDAF): F1; Answer F1; Accuracy; D(RoBERTa): F1; Accuracy (Middle); Average F1; MSE,Question Selection; Intent Recognition; English Proverbs; RACE-h; Machine Reading Comprehension,Natural Language Processing,Most current question answering datasets frame the task as reading comprehension where the question is about a paragraph or document and the answer often is a span in the document.     Some specific t...
Machine Translation,83,Alexa Point of View; HindEnCorp; WMT 2021 Ge'ez-Amharic; WMT 2020; IWSLT2015; X-SRL; eSCAPE; WMT 2016 Biomedical; scb-mt-en-th-2020; WMT 2014 Medical,WMT2016 English-German; Tatoeba (EN-to-EL); Alexa Point of View; V_C (trained on T_H); WMT2019 English-Japanese; WMT2019 English-German; WMT2015 English-Russian; IWSLT2015 English-German; WMT 2018 Finnish-English; FRMT (Chinese - Mainland),BLEU (Scn-En); Operations per network pass; BERTScore; Cased sacreBLEU; BLEU score; BLEU (It-Scn); Accuracy; BLEU; METEOR; Number of Params,Unsupervised Machine Translation; Automatic Post-Editing; Transliteration; Bilingual Lexicon Induction; Multimodal Machine Translation,Natural Language Processing,**Machine translation** is the task of translating a sentence in a source language to a different target language.    Approaches for machine translation can range from rule-based to statistical to neu...
Natural Language Inference,81,DocNLI; WinoGrande; TabFact; ART Dataset; XWINO; probability_words_nli; Torque; NLI4CT; Story Commonsense; RTE3-FR,QNLI; ANLI test; WANLI; GLUE; SciTail; MultiNLI-matched; TabFact; Probability words NLI; SICK; XWINO,A2; Macro F1; Dev Accuracy; Accuracy; BLEU; MCC; Dev Mismatched; % Dev Accuracy; Params (M); % Test Accuracy,Cross-Lingual Natural Language Inference; Visual Entailment; Answer Generation,Reasoning; Natural Language Processing,"**Natural language inference (NLI)** is the task of determining whether a ""hypothesis"" is   true (entailment), false (contradiction), or undetermined (neutral) given a ""premise"".    Example:    | Prem..."
Node Classification,76,Twitter-HyDrug-UR; Yelp-Fraud; Penn94; NBA; PubMed (48%/32%/20% fixed splits); OGB; Twitter-HyDrug; Film (60%/20%/20% random splits); PATTERN; Wiki,PATTERN; Cornell (60%/20%/20% random splits); Cornell; COCO-SP; AMZ Comp; Wikipedia; minesweeper; AMPLUS; Deezer Hungary; Amazon Photo,Micro-F1; Training Split; Macro F1; mAP; Validation Accuracy; AUCROC; Accuracy (%); F1-score; class-average Accuracy; Accuracy,Graph structure learning; Dynamic Node Classification; Node Classification on Non-Homophilic (Heterophilic) Graphs; Atomic number classification; Heterogeneous Node Classification,Graphs,"**Node Classification** is a machine learning task in graph-based data analysis, where the goal is to assign labels to nodes in a graph based on the properties of nodes and the relationships between t..."
Natural Language Understanding,72,CosmosQA; GeoGLUE; GLUE; IDK-MRC; WritingPrompts; WinoGrande; CrossWOZ; DiscoEval; RecipeQA; LogiQA,GLUE; LexGLUE; DialoGLUE fewshot; STREUSLE; DialoGLUE full; PDP60,Average; Tags (Full) Acc; Banking77 (Acc); Accuracy; SCOTUS; TOP (EM); EUR-LEX; CaseHOLD; DSTC8 (F-1); UNFAIR-ToS,Vietnamese Social Media Text Processing; Emotional Dialogue Acts,Natural Language Processing,"**Natural Language Understanding** is an important field of Natural Language Processing which contains various tasks such as text classification, natural language inference and story comprehension. Ap..."
Code Generation,70,DS-1000; JuICe; FloCo; CodeContests; SOEval; EVIL-Decoders; HumanEval-ET; MBPP-ET; CriticBench; RES-Q,FloCo; CodeContests; CodeXGLUE - CodeSearchNet; MBPP-ET; HumanEval-ET; RES-Q; VerilogEval; WebApp1k-Duo-React; HumanEval; TACO-BAAI,Python/EM; Pass@1; Introductory Pass@1000; w/o PE; Competition Pass@5; w/o Intact; BLEU score; Introductory Pass@5; Execution Accuracy; Test Set pass@5,Code Translation; Library-Oriented Code Generation; GitHub issue resolution; Class-level Code Generation; Code Documentation Generation,Reasoning; Computer Code; Natural Language Processing,"**Code Generation** is an important field to predict explicit code or program structure from multimodal data sources such as incomplete code, programs in another programming language, natural language..."
Common Sense Reasoning,56,BIG-bench; CriticBench; Situation Puzzle; WinoGrande; Fig-QA; RecipeQA; DpgMedia2019; Rainbow; SMART-101; MCScript,CrowdSource QA; WinoGrande; BIG-bench (Causal Judgment); ARC (Challenge); BIG-bench (Logical Sequence); Event2Mind dev; PARus; CommonsenseQA; BIG-bench (Date Understanding); SWAG,EM ; F1; Recall@10; Accuracy; BLEU; Jaccard Index; Average Cross-Ent; Dev; EM; MSE,Causal Judgment; Winowhy; Anachronisms; Riddle Sense; Timedial,Reasoning; Natural Language Processing,"Common sense reasoning tasks are intended to require the model to go beyond pattern  recognition. Instead, the model should use ""common sense"" or world knowledge to make inferences."
Recommendation Systems,56,Google Local review; Ranking social media news feed; AntM2C; Douban; Coached Conversational Preference Elicitation; Epinion; Wikidata-14M; Top-N Recommendation Runs; WyzeRule; PixelRec,CiteULike; Amazon-book; Douban; Flixster; Steam; Amazon C&A; PixelRec; Frappe; Alibaba-iFashion; Polyvore,Recall@10; PSP@10; RMSE (Random 90/10 Splits); NDCG@10 (full corpus); HR@50; MAP@30; HR@10; Hits@10; nDCG@20; HR@100,Session-Based Recommendations; Interactive Recommendation; Microvideo Recommendation; Sequential Recommendation; Multibehavior Recommendation,Miscellaneous; Graphs; Knowledge Base,"### **Recommendation System in AI Research**      A **Recommendation System** is a specialized AI-driven model that analyzes user preferences and behaviors to suggest relevant content, products, or se..."
Graph Classification,54,IPC-grounded; HCP Aging; Eth-Exchange; AIDS Antiviral Screen; OGB; ADNI; ENZYMES; UPFD-POL; Digits; COLLAB,IPC-grounded; NEURON-MULTI; HYDRIDES; HCP Aging; IMDb-M; ADNI; ENZYMES; Bench-hard; UPFD-POL; NCI-123,Accuracy(10-fold); F1; Accuracy (10-fold); Accuracy; MCC; Inference Time (ms); Accuracy (10 fold); AP; Acc; Mean Accuracy,Space group classification; Crystal system classification; Isomorphism Testing,Graphs,**Graph Classification** is a task that involves classifying a graph-structured data into different classes or categories. Graphs are a powerful way to represent relationships and interactions between...
Word Embeddings,53,Twitter Death Hoaxes; HCU400; SEND; DICE: a Dataset of Italian Crime Event news; WikiMatrix; pioNER; SemEval-2014 Task-10; Some Like it Hoax; SEMCAT; Billion Word Benchmark,N/A,N/A,Contextualised Word Representations; Learning Word Embeddings; Embeddings Evaluation; Multilingual Word Embeddings,Methodology; Natural Language Processing,Word embedding is the collective name for a set of language modeling and feature learning techniques in natural language processing (NLP) where words or phrases from the vocabulary are mapped to vecto...
Knowledge Graphs,46,DTBM; ORKG-QA; ComplexWebQuestions; UMVM; FB1.5M; Semantic Scholar; Cookie; Organizational Graph; KnowledgeNet; CSQA,JerichoWorld; MARS (Multimodal Analogical Reasoning dataSet);  FB15k; WikiKG90M-LSC,Test MRR; MRR; Set accuracy; Validation MRR,Open Knowledge Graph Canonicalization; Relational Pattern Learning; Person-Centric Knowledge Graphs; Knowledge Graph Completion; Complex Query Answering,Graphs; Knowledge Base,A knowledge graph is a structured representation of information that organizes data into nodes (entities) and edges (relationships) to show how different pieces of knowledge are interconnected. It ena...
Few-Shot Learning,45,EuroSAT; Open MIC; GLUE; mini-Imagenet; Meta-Dataset; PASCAL-5i; MeerKAT: Meerkat Kalahari Audio Transcripts; N-Digit MNIST; FLEURS; DTD,EuroSAT; tieredImageNet - 5-shot; Mini-ImageNet - 10-Shot Learning; DTD; SUN397; StanforCars; GLUE QQP; MedConceptsQA; OxfordPets; MRPC,8-shot Accuracy; Macro Recall; Accuracy; Macro Precision; 5 way 1~2 shot; AUC-ROC; 12-shot Accuracy; Acc; Specificity; Macro F1,Few-Shot Relation Classification; Few-Shot Audio Classification; Few-Shot Semantic Segmentation; Unsupervised Few-Shot Audio Classification; few-shot-htc,Methodology; Natural Language Processing; Computer Vision; Audio,"**Few-Shot Learning** is an example of meta-learning, where a learner is trained on several related tasks, during the meta-training phase, so that it can generalize well to unseen (but related) tasks ..."
Coreference Resolution,45,English Web Treebank; GICoref; PhotoBook; GAP Coreference Dataset; GAP; Marmara Turkish Coreference Resolution Corpus; XWINO; A Game Of Sorts; WikiCREM; MAP,DWIE; DocRED-IE; Winograd Schema Challenge; OntoGUM; XWinograd FR; LitBank; CoNLL12; XWinograd EN; The ARRAU Corpus; OntoNotes,F1; Avg F1; Accuracy; Average F1; Masculine F1 (M); CEAFϕ4; Overall F1; Bias (F/M); CoNLL F1; Feminine F1 (F),coreference-resolution; Cross Document Coreference Resolution,Natural Language Processing,Coreference resolution is the task of clustering mentions in text that refer to the same underlying real world entities.  Example:  ```                +-----------+                |           | I vote...
Audio Classification,43,BGG dataset; nEMO; Common Voice; aGender; YouTube-100M; ICBHI Respiratory Sound Database; MeerKAT: Meerkat Kalahari Audio Transcripts; Zooniverse; ReefSet; HUME-VB,ICBHI Respiratory Sound Database; MeerKAT: Meerkat Kalahari Audio Transcripts; Speech Commands; GTZAN; Multimodal PISA; DiCOVA; CREMA-D; DEEP-VOICE: DeepFake Voice Recognition; VGGSound; SSC,MosquitoSound; Top-1 Noun; ICBHI Score; Top-5 Action; Sensitivity; Top-1 Verb; mAP; d-prime; Top 5 Accuracy; Accuracy (%),Audio Multiple Target Classification; Environmental Sound Classification; Semi-supervised Audio Classification; Parkinson Detection from Speech,Audio,**Audio Classification** is a machine learning task that involves identifying and tagging audio signals into different classes or categories. The goal of audio classification is to enable machines to ...
Zero-Shot Learning,43,EuroSAT; MSVD-QA; AO-CLEVr; TVQA+; DTD; SUN397; MedConceptsQA; FGVC-Aircraft; Sequence Consistency Evaluation (SCE) tests; Oxford 102 Flower,EuroSAT; MSVD-QA; GDSCv2; DTD; SUN397; MedConceptsQA; FGVC-Aircraft; Oxford 102 Flower; COCO-MLT; CUB-200 - 0-Shot Learning,H; A-acc; Accuracy; average top-1 classification accuracy; Accuracy Seen; k=10 mIOU; Top 1 Accuracy; Average mAP; Top-1; Average Per-Class Accuracy,Compositional Zero-Shot Learning; Temporal Action Localization; Generalized Zero-Shot Learning; Multi-label zero-shot learning; Zero-shot gesture recognition,Methodology; Computer Vision,**Zero-shot learning (ZSL)** is a model's ability to detect classes never seen during training. The condition is that the classes are not known during supervised learning.     Earlier work in zero-sho...
Machine Reading Comprehension,43,UIT-ViNewsQA; BIOMRC; IDK-MRC; ReCO; LogiQA; UIT-ViWikiQA; C3; DuoRC; CBT; ExpMRC,N/A,N/A,N/A,N/A,N/A
Unsupervised Domain Adaptation,34,UDA-CH; Foggy Cityscapes; Oxford RobotCar Dataset; Adaptiope; Sim10k; SynLiDAR; ImageNet-A; RoCoG-v2; Sims4Action; DomainNet,MSCOCO to FLIR ADAS; UDA-CH; Portraits (over time); FHIST; BDD100k to Cityscapes; ImageNet-A; SYNTHIA-to-Cityscapes; virtual KITTI to KITTI (MDE); GTA5-to-Cityscapes; DomainNet,mean Corruption Error (mCE); AP@0.7; Market-1501->Rank-1; MIoU (16 classes); mAP; Rank-1; Accuracy (%); R5; CUHK03-NP->mAP; Accuracy,Online unsupervised domain adaptation,N/A,**Unsupervised Domain Adaptation** is a learning framework to transfer knowledge learned from source domains with a large number of annotated training examples to target domains with unlabeled data on...
Dialogue Generation,34,CPsyCounE; FusedChat; Reddit; diaforge-utc-r-0725; LCCC; JDDC 2.0; WDC-Dialogue; ProsocialDialog; OpenViDial 2.0; Arabic-ToD,Ubuntu Dialogue (Entity); Persona-Chat; Ubuntu Dialogue (Cmd); PG-19; Reddit (multi-ref); Ubuntu Dialogue (Activity); Harry Potter Dialogue Dataset; Amazon-5; Ubuntu Dialogue (Tense); FusedChat,ROUGE-1; Rouge-L; ROUGE-L; CIDr; Success; Inform; Avg F1; Accuracy; Perplexity; BLEU,Multi-modal Dialogue Generation,Natural Language Processing,"Dialogue generation is the task of ""understanding"" natural language inputs - within natural language processing in order to produce output. The systems are usually intended for conversing with humans,..."
Music Generation,33,NES-MDB; URMP; ADL Piano MIDI; Groove; Lakh Pianoroll Dataset; VGMIDI; JS Fake Chorales; ComMU; NSynth; MidiCaps,Song Describer Dataset,FAD VGG,Multimodal Music Generation; Music Performance Rendering; Music Texture Transfer,Music; Audio,Musique guitar
Domain Generalization,31,CIFAR-10C; MSK; ImageNet-Sketch; ImageNet-A; Wild-Time; Sims4Action; All-day CityScapes; DomainNet; Cityscapes; Human-Animal-Cartoon,"CIFAR-10C; TerraIncognita; ImageNet-Sketch; ImageNet-A; GTA5-to-Cityscapes; DomainNet; Cityscapes to ACDC; GTA-to-Avg(Cityscapes,BDD,Mapillary); Rotated Fashion-MNIST; PACS",Number of params; Top-1 Error Rate; Accuracy; Top-1 accuracy; Average Accuracy; mean Corruption Error (mCE); Accuracy - Corrupted Images; Accuracy - Clean Images; Mean IoU; Accuracy - All Images,Source-free Domain Generalization; Single-Source Domain Generalization; Evolving Domain Generalization,Computer Vision,"The idea of **Domain Generalization** is to learn from one or multiple training domains, to extract a domain-agnostic model which can be applied to an unseen domain   <span class=""description-source"">..."
Multi-Label Classification,30,NLP Taxonomy Classification Data; Dataset of Propaganda Techniques of the State-Sponsored Information Operation of the People's Republic of China; CAL500; Quda; LSHTC; EXTREME CLASSIFICATION; NUS-WIDE; OpenImages-v6; Multi-Label Classification Dataset Repository; Trailers12k,ChestX-ray14; MS-COCO; MRNet; PASCAL VOC 2012; PASCAL VOC 2007; MIMIC-CXR; NUS-WIDE; CheXpert; MLRSNet; OpenImages-v6,AUC on ACL Tear (ACL); AUC on Abnormality (ABN); Accuracy on ACL Tear (ACL); Average AUC on 14 label; AUC on Meniscus Tear (MEN); Average Accuracy; Average AUC; MAP; Accuracy on Abnormality (ABN); NUM RADS BELOW CURVE,Extreme Multi-Label Classification; Medical Code Prediction; Missing Labels; Hierarchical Multi-label Classification,Methodology; Computer Vision; Reasoning; Medical,"**Multi-Label Classification** is the supervised learning problem where an instance may be associated with multiple labels. This is an extension of single-label classification (i.e., multi-class, or b..."
Slot Filling,28,CareerCoach 2022; ProSLU; pioNER; RiSAWOZ; KILT; diaforge-utc-r-0725; MASSIVE; IndirectRequests; Polyvore; xSID,KILT: Zero Shot RE; Polyvore; SNIPS; ProSLU; SLURP; ATIS; ATIS (vi); KILT: T-REx; MixSNIPS; MASSIVE,F1; KILT-AC; Accuracy; FITB; Slot F1; Micro F1; F1 (5-shot) avg; Recall@5; KILT-F1; F1 (1-shot) avg,Zero-shot Slot Filling; Extracting COVID-19 Events from Twitter,Natural Language Processing,"The goal of **Slot Filling** is to identify from a running dialog different slots, which correspond to different parameters of the user’s query. For instance, when a user queries for nearby restaurant..."
Document Summarization,28,FINDSum; Elsevier OA CC-BY; New York Times Annotated Corpus; Arxiv HEP-TH citation graph; BookSum; FacetSum; AESLC; WikiLingua; WCEP; CQASUMM,WikiLingua (tr->en); HowSumm-Method; arXiv Summarization Dataset; HowSumm-Step; BBC XSum; Arxiv HEP-TH citation graph; CNN / Daily Mail,ROUGE-1; Rouge-2; Rouge-L; ROUGE-L; PPL; ROUGE-2,Email Thread Summarization,Natural Language Processing,Automatic **Document Summarization** is the task of rewriting a document into its shorter form while still retaining its important content. The most popular two paradigms are extractive approaches and...
NER,27,IECSIL FIRE-2018 Shared Task; TASTEset; Mini HAREM; BC4CHEMD; InLegalNER; AISECKG; MNIST; SuperMat; LEPISZCZE; First HAREM,N/A,N/A,N/A,N/A,N/A
Part-Of-Speech Tagging,26,English Web Treebank; Mac-Morpho; CoNLL 2017 Shared Task - Automatically Annotated Raw Texts and Word Embeddings; Alexa Point of View; ANTILLES; XGLUE; Universal Dependencies; FLUE; Ritter PoS; LinCE,Social media; Morphosyntactic-analysis-dataset; Ritter; Spoken Corpus; Sequoia Treebank; ARK; DaNE; XGLUE; ANTILLES; ParTUT,Accuracy; UPOS; Macro-averaged F1; Avg accuracy; Acc; BLEX; Avg. F1; Accuracy (%); Weighted Average F1-score,Unsupervised Part-Of-Speech Tagging,Natural Language Processing,Part-of-speech tagging (POS tagging) is the task of tagging a word in a text with its part of speech.  A part of speech is a category of words with similar grammatical properties. Common English  part...
Open-Domain Question Answering,26,QUASAR-T; BREAK; KILT; InfoSeek; SearchQA; Natural Questions; ScienceQA; ELI5; QReCC; SQuAD,SearchQA; SQuAD1.1 dev; KILT: TriviaQA; KILT: ELI5; KILT: HotpotQA; TriviaQA; Natural Questions; Natural Questions (short); SQuAD1.1; DuReader,F1; Rouge-2; Exact Match; KILT-EM; Rouge-L; EM (Quasar-T); N-gram F1; ROUGE-L; EM; Recall@5,N/A,Natural Language Processing,Open-domain question answering is the task of question answering on open-domain datasets such as Wikipedia.
Mathematical Reasoning,26,CriticBench; MathToF; Lila; ParaMAWPS; PolyMATH; PrOntoQA; PGPS9K; MathMC; Conic10K; MGSM8KInstruct,UniGeo; GSM8K; Lila (IID); Lila (OOD); AIME24; AMC23; UniGeo (PRV); PGPS9K; GeoQA; MATH500,Acc; Accuracy (%); Completion accuracy; Accuracy,Formal Logic; Mathematical Induction; High School Mathematics; Geometry Problem Solving; Abstract Algebra,Natural Language Processing; Reasoning; Knowledge Base,N/A
Question Generation,25,GrailQA; IDK-MRC; FreebaseQA; diaforge-utc-r-0725; WeiboPolls; MMD; ROPES; Natural Questions; ARID; 30MQA,GrailQA-IID; COCO Visual Question Answering (VQA) real images 1.0 open ended; TriviaQA; Natural Questions; SQuAD1.1; Visual Question Generation; FairytaleQA; WeiboPolls; GrailQA-Compositional; GrailQA-Zero-Shot,R-QAE; ROUGE-1; BLEU-1; ROUGE-L; BLEU; METEOR; FactSpotter; bleu; BLEU-3; BLEU-4,Poll Generation,Natural Language Processing,"The goal of **Question Generation** is to generate a valid and fluent question according to a given passage and the target answer. Question Generation can be used in many scenarios, such as automatic ..."
Data-to-Text Generation,24,RotoWire; WikiOFGraph; Wikipedia Person and Animal Dataset; SportSett; ToTTo; RoboCup; MLB Dataset; WebNLG; KELM; MultiWOZ,MLB Dataset (Relation Generation); WebNLG ru; WebNLG Full; RotoWire; WikiOFGraph; Cleaned E2E NLG Challenge; Rotowire (Content Selection); Wikipedia Person and Animal Dataset; Czech Restaurant NLG; RotoWire (Relation Generation),ROUGE-L; BLEU4; CIDEr; BLEU score; PARENT; Bleu; BLEU; METEOR; DLD; Precision,Visual Storytelling; KG-to-Text Generation; Unsupervised KG-to-Text Generation,Natural Language Processing,"A classic problem in natural-language generation (NLG) involves taking structured data, such as a table, as input, and producing text that adequately and fluently describes this data as output. Unlike..."
Relation Classification,23,FREDo; TexRel; AbstRCT - Neoplasm; TFH_Annotated_Dataset; Translated TACRED; TACRED-Revisited; MultiTACRED; FewRel; GUM; Discovery,TACRED; Discovery; AbstRCT - Neoplasm; DRI Corpus; MATRES; FewRel; CDCP; SemEval 2010 Task 8,F1; F1 (5-way 1-shot); F1 (5-way 5-shot; F1 (10-way 1-shot); 1:1 Accuracy; Macro F1; F1 (10-way 5-shot),Few-Shot Relation Classification; Implicit Discourse Relation Classification; Cause-Effect Relation Classification,Natural Language Processing,"**Relation Classification** is the task of identifying the semantic relation holding between two nominal entities in text.   <span class=""description-source"">Source: [Structure Regularized Neural Netw..."
Math Word Problem Solving,23,GSM-Plus; Geometry3K; MathToF; ParaMAWPS; GeoS; PGPS9K; ASDiv; MathMC; ALG514; MGSM,ALG514; ASDiv-A; MATH; MAWPS; DRAW-1K; GSM-Plus; SVAMP; MATH minival; ParaMAWPS; PEN,Accuracy (5-fold); Accuracy; Answer Accuracy; Accuracy (training-test); 1:1 Accuracy; Parameters (Billions); Accuracy (%); Execution Accuracy; weakly-supervised,N/A,Reasoning,"A math word problem is a mathematical exercise (such as in a textbook, worksheet, or exam) where significant background information on the problem is presented in ordinary language rather than in math..."
Task-Oriented Dialogue Systems,22,CrossWOZ; diaforge-utc-r-0725; ODSQA; SSD_PHONE; Arabic-ToD; A Game Of Sorts; MuTual; HR-Multiwoz; SSD_PLATE; SSD_NAME,Kvret; MULTIWOZ 2.0; SGD; KVRET,BLEU; METEOR; Entity F1; BLEU-4; Score,SSTOD,Natural Language Processing,Achieving a pre-defined task through a dialog.
Text-To-SQL,21,ADVETA; SQL-Eval; MultiSpider; TURSpider; CoSQL; Spider 2.0; NSText2SQL: An Open Source Text-to-SQL Dataset for Foundation Model Training; SParC; KaggleDBQA; KITTI,KaggleDBQA; spider; SQL-Eval; Spider 2.0; SEDE; 2D KITTI Cars Easy; BIRD (BIg Bench for LaRge-scale Database Grounded Text-to-SQL Evaluation); Text-To-SQL; SParC; SPIDER,question match accuracy; Execution Accuracy (Dev); Execution Accuracy % (Test); interaction match accuracy; 0-shot MRR; Exact Match Accuracy (Dev); Execution Accuracy % (Dev); Execution Accurarcy (Human); Exact Match (EM); Execution Accuracy (in Dev),MMSQL performance,Computer Code; Natural Language Processing,**Text-to-SQL** is a task in natural language processing (NLP) where the goal is to automatically generate SQL queries from natural language text. The task involves converting the text input into a st...
Text Simplification,20,WikiSplit; EurekaAlert; CEFR-SP; TurkCorpus; Klexikon; DEplain-web-sent; DEplain-web-doc; DEplain-APA-sent; Medical Wiki Paralell Corpus for Medical Text Simplification; TextBox 2.0,DEplain-web-sent; Wiki-Auto + Turk; DEplain-web-doc; WikiLargeFR; EurekaAlert; DEplain-APA-doc; DEplain-APA-sent; TurkCorpus; PWKP / WikiSmall; ASSET,"FRE (Flesch Reading Ease); FKGL; BLEU; METEOR; SARI (EASSE>=0.2.1); QuestEval (Reference-less, BERTScore); SARI; Rouge1; ROUGE-2; BLEU-4",N/A,Natural Language Processing,"**Text Simplification** is the task of reducing the complexity of the vocabulary and sentence structure of text while retaining its original meaning, with the goal of improving readability and underst..."
Style Transfer,20,StyleBench; Touchdown Dataset; MPI Sintel; LaMem; DeepWriting; POP909; DukeMTMC-reID; TE141K; TPIC17; Chinese Traditional Painting dataset,WikiArt; ^(#$!@#$)(()))******; 01/01/1967' AND 2*3*8=6*8 AND 'AncJ'='AncJ; StyleBench; GYAFC,Accuracy; ArtFID; SSIM; BLEU-4; CLIP Score; Harmonic mean; 0..5sec,Serial Style Transfer; Font Style Transfer; Image Stylization; Face Transfer; Reverse Style Transfer,Computer Vision,**Style Transfer** is a technique in computer vision and graphics that involves generating a new image by combining the content of one image with the style of another image. The goal of style transfer...
Language Identification,20,Common Voice; L3Cube-MahaCorpus; HindEnCorp; WiLI-2018; MOROCO; OGTD; OLID; NLI-PT; Universal Dependencies; Dakshina,VoxForge; OpenSubtitles; Nordic Language Identification; VOXLINGUA107; GlotLID-C; Universal Dependencies,Error rate; Accuracy; Macro F1,Dialect Identification; Native Language Identification,Natural Language Processing; Audio,Language identification is the task of determining the language of a text.
Time Series Classification,19,MJFF Levodopa Response Study; ECG5000; Yeast colony morphologies; UCI Machine Learning Repository; EigenWorms; fNIRS2MW; Vulpi et al. 2021; ATMs fault prediction; ECG Heartbeat Categorization Dataset; PLAsTiCC,WalkvsRun; StandWalkJump; Physionet 2017 Atrial Fibrillation; FordA; FaceDetection; ERing; CMUsubject16; ECG5000; BasicMotions; s2-agri,ACC; 0..5sec; mean average precision; Accuracy; AUC Stdev; NLL; Accuracy (Test); F1 score; % Test Accuracy; oAcc,Semi-supervised time series classification; Classification on Time Series with Missing Data,Time Series,**Time Series Classification** is a general task that can be useful across many subject-matter domains and applications. The overall goal is to identify a time series as coming from one of possibly ma...
Transfer Learning,18,Retinal Fundus MultiDisease Image Dataset (RFMiD); BaitBuster-Bangla: A Comprehensive Dataset for Clickbait Detection in Bangla with Multi-Feature and Multi-Modal Analysis; BanglaLekha-Isolated; KITTI; Office-Home; M2QA; fluocells; AppleScabLDs; ReefSet; BIOSCAN-5M,Retinal Fundus MultiDisease Image Dataset (RFMiD); BanglaLekha Isolated Dataset; Office-Home; COCO70; 100 sleep nights of 8 caregivers; KITTI Object Tracking Evaluation 2012,AUROC; 10-20% Mask PSNR; EER; Accuracy,Multi-Task Learning; Transfer Reinforcement Learning; Unsupervised Domain Expansion; Auxiliary Learning,Miscellaneous; Methodology,"**Transfer Learning** is a machine learning technique where a model trained on one task is re-purposed and fine-tuned for a related, but different task. The idea behind transfer learning is to leverag..."
Paraphrase Identification,18,TURL; Paralex; Finnish Paraphrase Corpus; Translated SNLI Dataset in Marathi; Quora Question Pairs; PAWS-X; PIT; PAWS; PARADE; WikiHop,TURL; 2017_test set; MSRP; Translated SNLI Dataset in Marathi; Quora Question Pairs; PIT; WikiHop; AP; IMDb; Yelp,F1; 10 fold Cross validation; Accuracy; Direct Intrinsic Dimension; Dev F1; MCC; Val Accuracy; Val F1 Score; Structure Aware Intrinsic Dimension; AP,N/A,Natural Language Processing,"The goal of **Paraphrase Identification** is to determine whether a pair of sentences have the same meaning.      <span class=""description-source"">Source: [Adversarial Examples with Difficult Common W..."
Document Classification,18,MeSHup; Hyperpartisan News Detection; Wikipedia Title; RVL-CDIP_N_MP; HOC; TACM12K; IMDB-MULTI; Bengali Hate Speech; WOS; RVL-CDIP_MP,Hyperpartisan News Detection; MPQA; IMDb-M; AAPD; WOS-46985; Recipe; Amazon; WOS-5736; Reuters En-De; Classic,F1; F1 (micro); Micro F1; Accuracy,Page Stream Segmentation,Natural Language Processing,"**Document Classification** is a procedure of assigning one or more labels to a document from a predetermined set of labels.   <span class=""description-source"">Source: [Long-length Legal Document Clas..."
Logical Reasoning,18,BIG-bench; TruthQuest; JustLogic; FLD; CheGeKa; Oxford Ontology Library; GSM-Plus; LingOly; Winograd Automatic; REBUS,BIG-bench (Logic Grid Puzzle); BIG-bench (Logical Fallacy Detection); BIG-bench (Formal Fallacies Syllogisms Negation); BIG-bench (Reasoning About Colored Objects); LingOly; Winograd Automatic; BIG-bench (StrategyQA); RuWorldTree; BIG-bench (Temporal Sequences); BIG-bench (Penguins In A Table),Exact Match Accuracy; Delta_NoContext; Accuracy; Accuracy ,Physical Intuition; Novel Concepts; Logical Fallacy Detection; Penguins In A Table; Temporal Sequences,Miscellaneous; Methodology; Reasoning,N/A
Paraphrase Generation,17,Paralex; Message Content Rephrasing; PARANMT-50M; ParaBank; Quda; Opusparcus; ViSP; Quora Question Pairs; AP; Autoregressive Paraphrase Dataset (ARPD),Quora Question Pairs; MSCOCO; Paralex,BLEU; iBLEU,Multilingual Paraphrase Generation,Natural Language Processing,"Paraphrase Generation involves transforming a natural language sentence to a new sentence, that has the same semantic meaning but a different syntactic or lexical surface form."
Sign Language Translation,17,MSASL-1000; AzSLD; ASLG-PC12; LSFB Datasets; CSL-Daily; ISLTranslate; LSA-T; WMT-SLT; DailyMoth-70h; Content4All,ASLG-PC12; CSL-Daily; LSA-T; How2Sign; RWTH-PHOENIX-Weather 2014 T; Mediapi-RGB,BLEU; BLEU-4; Word Error Rate (WER); ROUGE,N/A,Computer Vision,"Given a video containing sign language, the task is to predict the translation into (written) spoken language.    Image credit: [How2Sign](https://how2sign.github.io/)"
Binary Classification,17,TuPyE-Dataset; Student's EEG Brain Signal; Can you predict product backorder?; WMT 2016; TII-SSRC-23; Amazon Multilingual Counterfactual Dataset (AMCD); Adult Census Income; kickstarter; SMDG; AutoRobust,Glyphnet Dataset; kickstarter; nfcorpus tr; TII-SSRC-23; quora duplicates; fake; xnli tr; snli tr; quora tr; dev,AUROC; F1 score; F1-Score,"LLM-generated Text Detection; Cancer-no cancer per breast classification; Stable MCI vs Progressive MCI; Cancer-no cancer per view classification; Suspicous (BIRADS 4,5)-no suspicous (BIRADS 1,2,3) per image classification",Computer Vision; Natural Language Processing,N/A
Translation,17,xP3; HaVG; PhoMT; GigaST; ACES; PETCI; FairTranslate_fr; FRMT; Bornholmsk; BWB,Oxford Radar RobotCar Dataset; small content; PhoMT; MuLD (OpenSubtitles),Rouge-L; BLEU-1; BLEU; METEOR; translation error [%]; BLEU-4,N/A,Natural Language Processing,N/A
Zero-shot Text Search,16,FEVER; NFCorpus; TREC-News; CQADupStack; Robust04; CLIMATE-FEVER; Natural Questions; Signal-1M; SciFact; BioASQ,N/A,N/A,N/A,N/A,N/A
Knowledge Graph Completion,16,Aristo-v4; CoDEx Large; FB15k; DPB-5L (French); FB15k-237; CoDEx Small; MovieLens; CoDEx Medium; DBP-5L (Spanish); InferWiki,MovieLens 1M; DPB-5L (French); DBbook2014; FB15k-237; DBP-5L (Greek); DBP-5L (English); WN18RR,Hits@10; Hits@1; Mean Rank; MR; MRR; Hits@3,Large Language Model; Inductive knowledge graph completion; Inductive Relation Prediction; Triple Classification,Natural Language Processing; Graphs; Knowledge Base,"Knowledge graphs $G$ are represented as a collection of triples $\\{(h, r, t)\\}\subseteq E\times R\times E$, where $E$ and $R$ are the entity set and relation set. The task of **Knowledge Graph Compl..."
Word Sense Disambiguation,16,BIG-bench; Parallel Meaning Bank; RUSSE; DOGC; FEWS; WiC-TSV; SubjQA; Part Whole Relations; Verse; WiC,SensEval 3 Task 1; SensEval 2 Lexical Sample; Words in Context; RUSSE; FEWS; TS50; BIG-bench (Anachronisms); Knowledge-based:; WiC-TSV; SemEval 2015 Task 13,Task 1 Accuracy: general purpose; SemEval 2013; Task 1 Accuracy: domain specific; F1 (Fewshot Test); SemEval 2007; All; Task 2 Accuracy: general purpose; Task 2 Accuracy: all; Accuracy; Senseval 2,Word Sense Induction,Natural Language Processing,The task of Word Sense Disambiguation (WSD) consists of associating words in context with their most suitable entry in a pre-defined sense inventory. The de-facto sense inventory for English in WSD is...
Token Classification,15,WikiNEuRal; SROIE; WikiANN; NERGRIT Corpus; CoNLL; Russian Sentences POS tagged; GeoEDdA; UD_Tagalog-NewsCrawl; CoNLL 2003; LeNER-Br,grit-id/id_nergrit_corpus ner; wikiann sk; ingredients_yes_no; XTREME; SROIE; nielsr/funsd-layoutlmv3; x_glue; indonlu; lener_br; MNIST,N/A,Blackout Poetry Generation; Toxic Spans Detection,Natural Language Processing,N/A
Dependency Parsing,15,English Web Treebank; AMALGUM; CoNLL 2017 Shared Task - Automatically Annotated Raw Texts and Word Embeddings; CoNLL; GUM; DaNE; 100STLYE-Labelled; Universal Dependencies; Chinese Treebank; WiLI-2018,Spoken Corpus; Sequoia Treebank; 100STLYE-Labelled; DaNE; Universal Dependency Treebank; Universal Dependencies; ParTUT; GENIA - UAS; GENIA - LAS; Chinese Treebank,Unlabeled Attachment Score; F1; UAS; Macro-averaged F1; POS; BLEX; Macro F1; Labelled Attachment Score; 10°5 cm; LAS,Transition-Based Dependency Parsing; Unsupervised Dependency Parsing; Cross-lingual zero-shot dependency parsing; Prepositional Phrase Attachment; Dependency Grammar Induction,Natural Language Processing,"Dependency parsing is the task of extracting a dependency parse of a sentence that represents its grammatical structure and defines the relationships between ""head"" words and words, which modify those..."
Sentiment Classification,15,Tweet Sentiment Extraction; AISIA-VN-Review-S; Sentiment Merged; SST-3; ArSen-20; LatamXIX; Spanish Corpus XIX; Yelp Review Polarity; Forex News Annotated Dataset for Sentiment Analysis; ArSen,N/A,N/A,N/A,N/A,N/A
Knowledge Base Question Answering,15,VANiLLa; GrailQA; LC-QuAD; RuBQ; ORKG-QA; ComplexWebQuestions; FrenchMedMCQA; GeoQuestions1089; WebQuestionsSP; WebQuestions,N/A,N/A,N/A,N/A,N/A
Node Classification on Non-Homophilic (Heterophilic) Graphs,15,Chameleon (48%/32%/20% fixed splits); Penn94; Texas (48%/32%/20% fixed splits); Cornell (60%/20%/20% random splits); Deezer-Europe; twitch-gamers; Cornell (48%/32%/20% fixed splits); Film(48%/32%/20% fixed splits); Squirrel (48%/32%/20% fixed splits); Wisconsin (48%/32%/20% fixed splits),N/A,N/A,N/A,N/A,N/A
Hierarchical Multi-label Classification,15,EURLEX57K; Seq Funcat; Spo Funcat; Eisen Funcat; Derisi Funcat; Expr Funcat; BIOSCAN-5M; WOS; WOS Hierarchical Text Classification; New York Times Annotated Corpus,N/A,N/A,N/A,N/A,N/A
Multi-Document Summarization,15,aquamuse; Healthline; GameWikiSum; MS^2; Multi-XScience; LSARS; Wikipedia Generation; BrWac2Wiki; MATINF; WikiSum,N/A,N/A,N/A,N/A,N/A
Cross-Lingual Transfer,15,JESC; WikiNEuRal; XCOPA; Tatoeba; WikiANN; CoNLL; CoVoST; CoNLL 2002; PAWS-X; DaNE,XCOPA,Accuracy,Cross-Lingual NER; Zero-Shot Cross-Lingual Transfer,Natural Language Processing,"Cross-lingual transfer refers to transfer learning using data and models available for one language for which ample such resources are available (e.g., English) to solve tasks in another, commonly mor..."
Sentence Classification,14,DEFT Corpus; CSPubSum; SciCite; E2E Refined; Cards Against Humanity; PubMed RCT; ACL ARC; Paper Field; Press Briefing Claim Dataset; IndoNLU Benchmark,SciCite; ScienceCite; PubMed 20k RCT; Paper Field; ACL-ARC; CHIP-CTC,F1; Macro F1,Unfairness Detection,N/A,N/A
Code Search,14,ISAdetect dataset; washed_contract; RES-Q; CoIR; CodeSearchNet; CodeSCAN; StaQC; CoDesc; Search4Code; PyTorrent,; CoIR; CodeSearchNet; CoDesc; CodeSearchNet - Ruby; CodeXGLUE - WebQueryTest; CodeXGLUE - AdvTest,Test MRR; F1; Overall; Accuracy; Go; Java; JS; Python; nDCG@10; MRR,Annotated Code Search,Computer Code,"The goal of **Code Search** is to retrieve code fragments from a large code corpus that most closely match a developer’s intent, which is expressed in natural language.      <span class=""description-s..."
Spoken Language Understanding,14,Almawave-SLU; Snips-SmartSpeaker; ProSLU; SLURP; CQR; Dialogue State Tracking Challenge; Snips-SmartLights; VlogQA; Skit-S2I; Fluent Speech Commands,Snips-SmartSpeaker; Snips-SmartLights; Fluent Speech Commands; Spoken-SQuAD; Timers and Such,Accuracy (%); F1 score; Accuracy-FR (%); Accuracy-EN (%),Speech Tokenization; Spoken language identification,Speech,N/A
Intent Classification,14,arXivEdits; ORCAS-I; ViMQ; KUAKE-QIC; SLURP; Search4Code; CLINC150; Skit-S2I; MIPD; diaforge-utc-r-0725,KUAKE-QIC; SLURP; ORCAS-I; MASSIVE,F1-score; Accuracy; Recall; Precision; Accuracy (%); Intent Accuracy,N/A,Natural Language Processing,"**Intent Classification** is the task of correctly labeling a natural language utterance from a predetermined set of intents      <span class=""description-source"">Source: [Multi-Layer Ensembling Techn..."
Multi-class Classification,14,CPCXR; KINNEWS and KIRNEWS; SmokEng; AppealCase; TII-SSRC-23; DeepParliament; AutoRobust; SMM4H; Multimodal Emoji Prediction; TURINGBENCH, Reuters-52; Training and validation dataset of capsule vision 2024 challenge.; TII-SSRC-23; COVID chest X-ray; COVID-19 CXR Dataset,Mean AUC; F1-Score; Macro F1; F1 score; Accuracy (%),Patent classification,Computer Vision,"Multi-class classification is a type of supervised learning where the goal is to assign an input to one of three or more distinct classes. Unlike binary classification (which has only two classes), mu..."
Multi-Label Text Classification,13,MIMIC-III; EURLEX57K; CC3M-TagMask; Dataset of Propaganda Techniques of the State-Sponsored Information Operation of the People's Republic of China; Antibody Watch; Slashdot; Korean UnSmile Dataset; ContractNLI; EXTREME CLASSIFICATION; RCV1,SVICTOR (theme); Dataset of Propaganda Techniques of the State-Sponsored Information Operation of the People's Republic of China; LF-AmzonTitles-131K; AAPD; MIMIC-III; CC3M-TagMask; Amazon-12K; Kan-Shan Cup; Freecode; USPTO-3M,Weighted F1; Macro Recall; Micro-F1; nDCG@5; Macro F1; mAP; F1-score; Accuracy; nDCG@3; P@5,N/A,Methodology; Natural Language Processing,"According to Wikipedia ""In machine learning, multi-label classification and the strongly related problem of multi-output classification are variants of the classification problem where multiple labels..."
Document Layout Analysis,13,ICDAR 2021; DocBank; DSSE-200; RVL-CDIP; HJDataset; VIS30K; UrduDoc; U-DIADS-Bib; opendataset; DIVA-HisDB,RVL-CDIP; PubLayNet val; Document Layout Recognition Challenge test; U-DIADS-Bib; Document Layout Recognition Challenge mini-dev; D4LA,Overall; List; Text; Class Average IoU (Few-shot setting); Model Parameters; FAR;  mAP; Figure; Class Average IoU; Title,MS-SSIM,Computer Vision,"""**Document Layout Analysis** is performed to determine physical structure of a document, that is, to determine document components. These document components can consist of single connected component..."
Relational Reasoning,12,WebKB; Machine Number Sense; PISC; WikiDataSets; TexRel; RAVEN-FAIR; RAVEN; PGM; Compositional Visual Reasoning (CVR); Sequence Consistency Evaluation (SCE) tests,CLUTRR (k=3),8 Hops; 9 Hops; 6 Hops; 10 Hops; 5 Hops; 7 Hops; 4 Hops,N/A,Natural Language Processing,"The goal of **Relational Reasoning** is to figure out the relationships among different entities, such as image pixels, words or sentences, human skeletons or interactive moving agents.   <span class=..."
News Classification,12,BalitaNLP; KINNEWS and KIRNEWS; N15News; Reddit Ideological and Extreme Bias Dataset; Verifee; MasakhaNEWS; MN-DS; HLGD; IndicGLUE; CIDII Dataset,N/A,N/A,N/A,N/A,N/A
Code Completion,12,Verified Smart Contracts; Defects4J; OpenAPI completion refined; Verified Smart Contract Code Comments; Spectre-v1; PyTorrent; CodeXGLUE; SAFIM; MMCode; RTL-Repo,Defects4J; CodeXGLUE - PY150; Rambo Benchmark; CodeXGLUE - Github Java Corpus; SAFIM; DotPrompts,Average; API; Pass@1; BLEU; Compilation Rate; Accuracy (token-level); Control; Edit Sim (line-level); Algorithmic; EM (line-level),OpenAPI code completion,Computer Code,N/A
Generalizable Person Re-identification,11,SYSU-MM01-C; CUHK03-C; ClonedPerson; RegDB-C; ENTIRe-ID; Market-1501; CUHK03; DukeMTMC-reID; MSMT17; Market-1501-C,N/A,N/A,N/A,N/A,N/A
Sequential Recommendation,11,Gowalla; Amazon Beauty; KuaiRand; MovieLens; tida-gcn-data; PixelRec; Amazon-Sports; Amazon Review; Amazon Men; Yelp,N/A,N/A,N/A,N/A,N/A
Audio Generation,11,Audio-alpaca; NSynth; NES-MDB; ADL Piano MIDI; dMelodies; MAESTRO; MovieNet; SymphonyNet; MediBeng; AudioCaps,"Symphony music; AudioCaps; Classical music, 5 seconds at 12 kHz",IS; FD; FD_openl3; Bits per byte; FAD; CLAP_MS; KL_passt;  Human listening average results; CLAP_LAION,Voice Cloning; Room Impulse Response (RIR); Audio Super-Resolution; Video-to-Sound Generation,Music; Audio; Speech,"Audio generation (synthesis) is the task of generating raw audio such as speech.    <span style=""color:grey; opacity: 0.6"">( Image credit: [MelNet](https://arxiv.org/pdf/1906.01083v1.pdf) )</span>"
Synthetic Data Generation,11,UNSW-NB15; CBTex; UCI Machine Learning Repository; Creative Flow+ Dataset; Synthetic COVID-19 CXR Dataset; SICKLE; TF1-EN-3M; MediBeng; Titanic; diaforge-utc-r-0725,UNSW-NB15; UCI Epileptic Seizure Recognition,AUROC; EMD,Synthetic Data Evaluation; Synthetic Outliers Evaluation,Miscellaneous; Time Series; Medical,The generation of tabular data by any means possible.
Opinion Mining,11,XED; Conversational Stance Detection; MOS Dataset; RuOpinionNE; NoReC; UR-FUNNY; IMDb Movie Reviews; Youtubean; UIT-ViSFD; FinnSentiment,N/A,N/A,N/A,N/A,N/A
Sentence Embeddings,11,COSTRA 1.0; Discovery; WikiMatrix; PIT; AuxAI; GeoCoV19; SemEval-2014 Task-10; AuxAD; French CASS dataset; GloREPlus,N/A,N/A,Joint Multilingual Sentence Representations; Sentence Compression; Sentence Embedding; Sentence Embeddings For Biomedical Texts,Natural Language Processing,N/A
Mathematical Question Answering,11,IconQA; DART-Math-Hard; Geometry3K; DART-Math-Uniform; ParaMAWPS; GeoS; GeoQA; SMART-101; Mathematics Dataset; MathBench,Geometry3K; GeoS,Accuracy (%),Math Word Problem Solving,Time Series; Reasoning; Knowledge Base,Building systems that automatically answer mathematical questions.
Chatbot,11,ChatGPT Software Testing Study; PhotoBook; FinChat; Blended Skill Talk; AlpacaEval; Taiga Corpus; ChatGPT-software-testing; AlpacaEval-TH; MT-Bench-TH; Customer Support on Twitter,AlpacaEval,Average win rate,Dialogue Generation,Methodology; Natural Language Processing; Speech,"**Chatbot** or conversational AI is a language model designed and implemented to have conversations with humans.       <span class=""description-source"">Source: [Open Data Chatbot ](https://arxiv.org/a..."
Multimodal Reasoning,11,M3GIA; R1-Onevision; MATH-V; REBUS; MeetingBank; VL-RewardBench; GameQA; PolyMATH; EMMA; taste-music-dataset,MATH-V; AlgoPuzzleVQA; REBUS,Acc; Accuracy,MME,Computer Vision; Reasoning,Reasoning over multimodal inputs.
Generalized Zero-Shot Learning,10,Oxford 102 Flower; S3DIS; AwA2; aPY; CUB-200-2011; SUN Attribute; AwA; ScanNet; OntoNotes 5.0; SemanticKITTI,N/A,N/A,N/A,N/A,N/A
Conversational Question Answering,10,MultiDoc2Dial; CANARD; ConvFinQA; Doc2Dial; UIT-ViCoQA; DoQA; CoQA; diaforge-utc-r-0725; QuAC; Alpaca Data Galician,ConvFinQA,Execution Accuracy; Program Accuracy,Question Rewriting,Natural Language Processing,N/A
Robust classification,10,DeepWeeds; Tiny ImageNet-A; MNIST-C; Tiny ImageNet-R; N-ImageNet; WHYSHIFT; EuroSAT-C; MedMNIST-C; ModelNet40-C; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Topic Classification,10,"Amazon Product Data; LatamXIX; Spanish Corpus XIX; Taiga Corpus; Yahoo! Answers; How2Sign; 20Newsgroup (10 tasks); Mapping Topics in 100,000 Real-Life Moral Dilemmas; LLeQA; KLUE",N/A,N/A,N/A,N/A,N/A
Audio Tagging,10,SSC; CHiME-Home; FSDKaggle2018; VocalSound; AudioSet; MeerKAT: Meerkat Kalahari Audio Transcripts; SINGA:PURA; FSDKaggle2019; SONYC-UST-V2; Sound of Water 50,AudioSet,mean average precision,N/A,Audio,"Audio tagging is a task to predict the tags of audio clips. Audio tagging tasks include music tagging, acoustic scene classification, audio event classification, etc."
Discourse Parsing,10,SPOT; PCC; AMALGUM; RRT; DISRPT2021; Molweni; Instructional-DT (Instr-DT); GUM; RST-DT; RRG,STAC; Molweni; Instructional-DT (Instr-DT); RST-DT,RST-Parseval (Full); RST-Parseval (Nuclearity); Standard Parseval (Span); RST-Parseval (Span); Standard Parseval (Full); Standard Parseval (Relation); Link F1; RST-Parseval (Relation); Link & Rel F1; Standard Parseval (Nuclearity),Discourse Segmentation; Connective Detection; End-to-End RST Parsing,Natural Language Processing,N/A
Citation Recommendation,10,unarXive; OC; RefSeer; REFCAT; ACL ARC; S2ORC; SemOpenAlex; Microsoft Academic Graph; FullTextPeerRead; arXiv-200,N/A,N/A,N/A,N/A,N/A
Music Classification,10,ATEPP; MELON; GiantMIDI-Piano; MagnaTagATune; Mid-level perceptual musical features; EMOPIA; PIAST; JS Fake Chorales; Lyra Dataset; XMIDI,N/A,N/A,Singer Identification; Vocal technique classification,Music,N/A
Binary text classification,10,MAGE; DACCORD; Ghostbuster; ECHR; BFRD; OUTFOX; TweepFake; TURINGBENCH; This is not a Dataset; MixSet,"MixSet (Binary); TURINGBENCH (Turing Test, FAIR_wmt20); MAGE (Arbitrary-domains & Arbitrary-models); Ghostbuster (All Domains); TURINGBENCH (Turing Test, GPT-3); TweepFake; ECHR Non-Anonymized",Accuracy (%); F1 score; Average Recall; Macro F1,Detection of potentially void clauses,Natural Language Processing,N/A
Handwriting generation,10,Schiller; MatriVasha:; Konzil; Schwerin; HKR; Ricordi; DeepWriting; Patzig; DigiLeTs; BRUSH,N/A,N/A,N/A,Computer Vision,The inverse of handwriting recognition. From text generate and image of handwriting (offline) of trajectory of handwriting (online).
Code Translation,10,FixEval; CodeTransOcean; ObscuraX; PyTorrent; NLC2CMD; xCodeEval; CRUST-bench; SLTrans; HumanEval-X; CodeXGLUE,N/A,N/A,N/A,N/A,N/A
Text-to-Code Generation,10,CodeContests; RES-Q; Verireason-RTL-Coder_7b_reasoning_tb; MICRO25; PyTorrent; CADBench; SAFIM; Verireason-RTL-Coder_7b_reasoning_tb_simple; BlendNet; CodeXGLUE,CodeXGLUE - CONCODE,BLEU; CodeBLEU; EM,N/A,Computer Code,"**Text-to-Code Generation** is a task where we can generate code based on the natural language description.    Source: [Text-to-code Generation with TensorFlow, 🤗 & MBPP](https://www.kaggle.com/code/r..."
Conversational Response Generation,10,"DuLeMon; BeNYfits; LLMafia; Arabic-ToD; ChatGPT Role-Play Dataset (CRD); MIBot - Motivational Interviewing for Smoking Cessation Dataset, based on MIBOT Version 6.3A; CAsT-snippets; diaforge-utc-r-0725; Alpaca Data Galician; CPED",N/A,N/A,Personalized and Emotional Conversation,Natural Language Processing,"Given an input conversation, generate a natural-looking text reply to the last conversation element.    Image credit: [DIALOGPT : Large-Scale Generative Pre-training for Conversational Response Genera..."
Large Language Model,10,RPEval; GlotSparse; IFEval; TriviaHG; SGXSTest; diaforge-utc-r-0725; VMD; Human Simulacra; MedConceptsQA; HiXSTest,N/A,N/A,Knowledge Graphs; AI Agent; RAG,Methodology; Natural Language Processing; Knowledge Base,N/A
Explanation Generation,9,SuMe; E-ReDial; WHOOPS!; e-SNLI-VE; ExPUNations; SpanEX; VCR; E-KAR; CLEVR-X,WHOOPS!; e-SNLI-VE; VQA-X; VCR; CLEVR-X,C; B4; RL; Accuracy; Acc; Human Explanation Rating; Human (%); M,N/A,Natural Language Processing,N/A
Few-Shot Audio Classification,9,NSynth; VoxCeleb1; Common Voice; Speech Accent Archive; FSDKaggle2018; BirdClef 2020  (Pruned); Watkins Marine Mammal Sounds; ESC-50; CREMA-D,N/A,N/A,N/A,N/A,N/A
KG-to-Text Generation,9,PathQuestion; XAlign; AGENDA; ENT-DESC; EventNarrative; GenWiki; WebQuestions; WikiGraphs; WebNLG,PathQuestion; EventNarrative; WebNLG 2.0 (Constrained); ENT-DESC; WebNLG 2.0 (Unconstrained); AGENDA; WebNLG (All); WebQuestions; WikiGraphs; WebNLG (Unseen),rBLEU (Valid); ROUGE; Test perplexity; rBLEU(w/title)(Valid); ChrF++; BLEU; METEOR; CIDEr; FactSpotter; rBLEU(w/title)(Test),N/A,Natural Language Processing,Knowledge-graph-to-text (KG-to-text) generation aims to generate high-quality texts which are consistent with input graphs.    Description from: [JointGT: Graph-Text Joint Representation Learning for ...
Person Search,9,CUHK-SYSU; DukeMTMC-attribute; MovieNet; Market1501-Attributes; P-DESTRE; XKCDColors; ICFG-PEDES; CUHK-QA; PRW,CUHK-SYSU; PRW,mAP; Top-1; MAP,N/A,Computer Vision,"**Person Search** is a task which aims at matching a specific person among a great number of whole scene images.   <span class=""description-source"">Source: [Re-ID Driven Localization Refinement for Pe..."
Anomaly Classification,9,VisA-AC; edeniss2020; GoodsAD; MVTecAD; Hawk Annotation Dataset; ELPV; MVTec-AC; Lusitano Fabric Defect Detection Dataset; VisA,VisA-AC; GoodsAD; MVTecAD; MVTec-AC; VisA,AUPR; Detection AUROC; Accuracy (% ); Accuracy(%); AUROC,Anomaly Severity Classification (Anomaly vs. Defect),Computer Vision,"Anomaly Classification is the task of identifying and categorizing different types of anomalies in visual data, rather than simply detecting whether an input is normal or anomalous. Unlike anomaly det..."
Cross-Lingual NER,9,WikiNEuRal; WikiANN; CoNLL; MasakhaNER; XGLUE; UNER v1; CoNLL 2003; Europeana Newspapers; MSRA CN NER,N/A,N/A,N/A,N/A,N/A
Chart Question Answering,9,LEAF-QA; Vega-Lite Chart Collection; SBS Figures; ChartQA; DVQA; MMVP; RealCQA; FigureQA; PlotQA,RealCQA; ChartQA; PlotQA,1:1 Accuracy,N/A,Computer Code,Question Answering task on charts images
Abusive Language,9,DKhate; CoRAL dataset; TalkDown; Torque; PointDenoisingBenchmark; Cards Against Humanity; The ComMA Dataset v0.2; Hate Speech; Hate Speech and Offensive Language,N/A,N/A,N/A,Natural Language Processing,N/A
Dialogue Understanding,9,WDC-Dialogue; ProsocialDialog; DiaASQ; MutualFriends; Molweni; Doc2Dial; The Mafia Dataset; Harry Potter Dialogue Dataset; Emotional Dialogue Acts,N/A,N/A,Dialogue Safety Prediction; Spoken Language Understanding,Natural Language Processing,N/A
Arithmetic Reasoning,9,MGSM; GSM-Plus; GSM8K; Game of 24; MathToF; TIME; PolyMATH; SMART-101; MathMC,GSM8K; Game of 24; MathToF; MathMC; MultiArith,Parameters (Billion); Success; Accuracy,N/A,Reasoning,N/A
Multiple Choice Question Answering (MCQA),9,MML; BIG-bench; FrenchMedMCQA; MedMCQA; M3KE; IndicGLUE; SecQA; LMCQA; SF20K,N/A,N/A,N/A,N/A,N/A
General Classification,8,Adult; Hyper-Kvasir Dataset; Wine; Kvasir-Capsule; iris; CVR; MNIST; Fashion-MNIST,Fashion-MNIST; XOR; Wine; iris; Activity; CVR; Shrutime; CVE to CWE mapping; MNIST; Mice Protein,Test error; 5 fold cross validation; Accuracy,N/A,Methodology,Algorithms trying to solve the general task of classification.
Few-Shot Learning - 4 shots,8,EuroSAT; Food-101; UCF101; DTD; SUN397; FGVC-Aircraft; Oxford 102 Flower; Stanford Cars,N/A,N/A,N/A,N/A,N/A
Temporal Tagging,8,TempEval-3; Spanish TimeBank 1.0; KRAUTS; TimeBankPT; Basque TimeBank; French Timebank; HengamCorpus; Catalan TimeBank 1.0,N/A,N/A,N/A,N/A,N/A
Formation Energy,8,OQM9HK; QM9; OQMD v1.2; JARVIS-DFT; WBM; Materials Project; MD17; Matbench,OQM9HK; Ethanol; 3BPA; Aspirin; Naphthalene; QM9; Acetylacetone; LiPS; LiPS20; OQMD v1.2,MAE,N/A,Miscellaneous,"On the QM9 dataset the numbers reported in the table are the mean absolute error in eV on the target variable U0 divided by U0's chemical accuracy, which is equal to 0.043."
Gloss-free Sign Language Translation,8,PHOENIX14T; CSL-Daily; How2Sign; RWTH-PHOENIX-Weather 2014 T; Mediapi-RGB; OpenASL; CSL-News; DailyMoth-70h,N/A,N/A,N/A,N/A,N/A
ECG Classification,8,PTB Diagnostic ECG Database; MIMIC-IV-ECG; PhysioNet Challenge 2021; ECG Heartbeat Categorization Dataset; PTB-XL; CODE-15%; UCR Time Series Classification Archive; PhysioNet Challenge 2020,PhysioNet Challenge 2021; PhysioNet Challenge 2020; UCR Time Series Classification Archive; PTB-XL; Electrocardiography (ECG) on Telehealth Network of Minas Gerais (TNMG),PhysioNet Challenge score 2021; Accuracy(stratified10-fold); F1 (1dAVb); F1(stratified10-fold); PhysioNet/CinC Challenge Score(stratified10-fold); PhysioNet Challenge score (test data); F1 (ST); Accuracy (Test); F1 (RBBB); F2(stratified10-fold),Photoplethysmography (PPG),Medical,N/A
Dialogue Evaluation,8,SaGA; FaithDial; USR-PersonaChat; CPsyCounE; diaforge-utc-r-0725; Reddit; Reddit Engagement Dataset; USR-TopicalChat,USR-PersonaChat; USR-TopicalChat,Spearman Correlation; Pearson Correlation,N/A,Natural Language Processing,N/A
Chinese Reading Comprehension,8,RoadTracer; CMRC 2018; DMQA; Belebele; CJRC; DRCD; ReCO; CMRC,N/A,N/A,N/A,N/A,N/A
Heterogeneous Node Classification,8,DBLP (Heterogeneous Node Classification); Freebase (Heterogeneous Node Classification); DBLP; OAG-Venue; IMDB (Heterogeneous Node Classification); SupplyGraph; OAG-L1-Field; ACM (Heterogeneous Node Classification),N/A,N/A,N/A,N/A,N/A
Summarization,8,XSum; BigPatent; MLSUM; BillSum; SAMSum; MuLD; CNN/Daily Mail; Multi-News,cnn_dailymail; scientific_papers; XSum; SAMSum Corpus: A Human-annotated Dialogue Dataset for Abstractive Summarization; multi_news; big_patent; MuLD (VLSP); launch/gov_report; bazzhangz/sumdataset; BillSum,BLEU-1; BLEU-4; METEOR; Rouge-L,Unsupervised Extractive Summarization; Query-focused Summarization,Natural Language Processing,Summarization is the task of producing a shorter version of one or several documents that preserves most of the input's meaning.
Translation deu-eng,8,WMT 2018; WMT 2015; Tatoeba; WMT 2016; FLoRes-101; FLoRes-200; Multi30K; WMT 2014,N/A,N/A,N/A,N/A,N/A
Translation eng-deu,8,WMT 2018; WMT 2015; Tatoeba; WMT 2016; FLoRes-101; FLoRes-200; Multi30K; WMT 2014,N/A,N/A,N/A,N/A,N/A
Keyword Spotting,8,VoxForge; PodcastFillers; EmoSpeech; FKD; TAU Urban Acoustic Scenes 2019; Google Speech Commands - Musan; Speech Commands; Auto-KWS,VoxForge; Google Speech Commands V2 35; QUESST; FKD; hey Siri; TensorFlow; TAU Urban Acoustic Scenes 2019; Google Speech Commands V2 12; Google Speech Commands (v2); Google Speech Commands,Google Speech Commands V1 6; lowerbound ; Google Speech Commands; Accuracy (%); Google Speech Commands V1 2; Accuracy(10-fold); SSF; PMUs; Accuracy; ATWV,Visual Keyword Spotting; Small-Footprint Keyword Spotting,Computer Vision; Speech,"In speech processing, keyword spotting deals with the identification of keywords in utterances.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Simon Grest](https://github.com/simongrest/ka..."
Sentence Embedding,8,COSTRA 1.0; Tatoeba; Opusparcus; UR-FUNNY; Claim Matching Robustness; OPUS; French CASS dataset; Video2GIF,N/A,N/A,N/A,N/A,N/A
Causal Inference,8,ChatGPT Advice Responses; ParaBank; Jobs; IHDP; ICLR Database; NTPairs; NAIST COVID; ReCO,IDHP; Jobs; IHDP,Average Treatment Effect Error; Average Treatment Effect on the Treated Error,Heterogeneous Treatment Effect Estimation; IHDP-CATE; Counterfactual Inference,Miscellaneous; Knowledge Base,"Causal inference is the task of drawing a conclusion about a causal connection based on the conditions of the occurrence of an effect.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Recove..."
Stance Classification,8,DAST; CoVaxFrames; HpVaxFrames; RuStance; VaccineFrames; VaccineLies; MMVax-Stance; CoVaxLies v2,N/A,N/A,N/A,N/A,N/A
Story Generation,8,MoviePlotEvents; HANNA; WritingPrompts; Visual Writing Prompts; TVRecap; Votranh DREAM_LOG; Scifi TV Shows; Creative Writing,Fandom test; TVMegaSite test; Fandom dev; WritingPrompts; TVMegaSite dev,ROUGE-1; Perplexity; BLEU-1; BLEU; ROUGE-L; BLEU-2; ROUGE-2; Distinct-4,Visual Storytelling,Natural Language Processing,"Story generation is the task of automatically generating a coherent narrative, often from a set of premises or a brief summary."
Code Classification,8,ISAdetect dataset; FixEval; CodeSCAN; A Software Maintainability Dataset; Spectre-v1; PyTorrent; xCodeEval; TACO-BAAI,N/A,N/A,N/A,Computer Code,N/A
Code Summarization,8,PyTorrent; Java scripts; CoSQA+; notebookcdg; MCoNaLa; ParallelCorpus-Python; DeepCom-Java; CodeXGLUE,N/A,N/A,N/A,N/A,N/A
Multilingual NLP,8,MAKED; M2QA; Belebele; English-Pashto Language Dataset (EPLD); GLARE; Duolingo STAPLE Shared Task; mBBC dataset; HumSet,N/A,N/A,N/A,Natural Language Processing,N/A
Response Generation,8,ProsocialDialog; ArgSciChat; KETOD; ComFact; MMConv; Comet; SIMMC2.0; LLMafia,ArgSciChat; SIMMC2.0; MMConv,Inform; BScore; Message-F1; BLEU; Mover; Comb.; Success,N/A,Natural Language Processing,A task where an agent should play the $DE$ role and generate a text to respond to a $P$ message.
Multimodal Sentiment Analysis,7,MuSe-CaR; CMU-MOSI; CMU-MOSEI; CH-SIMS; B-T4SA; Multimodal Opinionlevel Sentiment Intensity; SemEval-2020 Task-8,N/A,N/A,N/A,N/A,N/A
Text Style Transfer,7,Yelp; TextBox 2.0; StyleGallery; Touchdown Dataset; StylePTB; TextSeg; XFORMAL,Yelp Review Dataset (Large); Yelp Review Dataset (Small),"BLEU; G-Score (BLEU, Accuracy)",Word Attribute Transfer; Formality Style Transfer; Semi-Supervised Formality Style Transfer,Natural Language Processing,Text Style Transfer is the task of controlling certain attributes of generated text. The state-of-the-art methods can be categorized into two main types which are used on parallel and non-parallel dat...
imbalanced classification,7,Can you predict product backorder?; Kinetics; Industrial Benchmark Dataset for Customer Escalation Prediction; Imbalanced-MiniKinetics200; kaggle stroke Prediction competition; CI-MNIST; WikiChurches,N/A,N/A,N/A,Miscellaneous,learning classifier from class-imbalanced data
Bayesian Inference,7,WebKB; Summaries of genetic variation; UCLA Aerial Event Dataset; ExBAN; Blackbird; State Traversal Observation Tokens; CIFAR-100,cifar100,Accuracy; Expected Calibration Error,Probabilistic Programming,Methodology,Bayesian Inference is a methodology that employs Bayes Rule to estimate parameters (and their full posterior).
Sequence-to-sequence Language Modeling,7,XSum; WMT 2016; ViHOS; Itihasa; BillSum; Reddit; CNN/Daily Mail,N/A,N/A,N/A,N/A,N/A
News Recommendation,7,MIND; Reddit Ideology Database; Ranking social media news feed; Reddit Ideological and Extreme Bias Dataset; Reddit; xMIND; V-MIND,N/A,N/A,N/A,N/A,N/A
Tabular Data Generation,7,HELOC; Adult Census Income; Travel; Diabetes; California Housing Prices; Thyroid; SICK,HELOC; Adult Census Income; Travel; Diabetes; California Housing Prices; SICK,DT Accuracy; DT Mean Squared Error; LR Mean Squared Error; Parameters(M); RF Accuracy; RF Mean Squared Error; LR Accuracy,N/A,Miscellaneous,Generation of the tabular data using generative models
Source Code Summarization,7,Summarizing Source Code using a Neural Attention Model; CodeSearchNet; StaQC; CoDesc; Java scripts; ParallelCorpus-Python; DeepCom-Java,CodeSearchNet; Summarizing Source Code using a Neural Attention Model - C#; CoDesc; Java scripts; Summarizing Source Code using a Neural Attention Model - Python; CodeSearchNet - Python; ParallelCorpus-Python; DeepCom-Java; Summarizing Source Code using a Neural Attention Model - SQL,F1; BLEU-4; METEOR; Smoothed BLEU-4,Method name prediction,Computer Code; Natural Language Processing,"**Code Summarization** is a task that tries to comprehend code and automatically generate descriptions directly from the source code.      <span class=""description-source"">Source: [Improving Automatic..."
Scientific Document Summarization,7,unarXive; CL-SciSumm; MS^2; ScisummNet; SemOpenAlex; FacetSum; TalkSumm,CL-SciSumm,ROUGE-2,Lay Summarization,Natural Language Processing,N/A
Malware Classification,7,Microsoft Malware Classification Challenge; MOTIF; EMBER; AutoRobust; Malimg; BODMAS; IoT-23,Microsoft Malware Classification Challenge; MaleVis; Malimg Dataset,F1 score (5-fold); Accuracy (5-fold); Accuracy (10-fold); Accuracy; Macro F1 (10-fold); LogLoss; Macro F1,Android Malware Detection; Behavioral Malware Detection; Malware Detection; Behavioral Malware Classification,Miscellaneous,**Malware Classification** is the process of assigning a malware sample to a specific malware family. Malware within a family shares similar properties that can be used to create signatures for detect...
Document Ranking,7,BASIR; Qulac; MSLR WEB30K; MQ2008; CLUE; Istella LETOR; DaReCzech,ClueWeb09-B; DaReCzech,P@10; ERR@20; nDCG@20,Session Search,Natural Language Processing,"Sort documents according to some criterion so that the ""best"" results appear early in the result list displayed to the user (Source: Wikipedia)."
Extreme Summarization,7,XSum; SciTLDR; CiteSum; SummZoo; GEM; Elsevier OA CC-BY; TLDR9+,XSum; GEM-XSum; CiteSum; TLDR9+,Parameters; ROUGE-1; RG-L(%); ROUGE-L; METEOR; RG-1(%); BLEU score; ROUGE-2; RG-2(%),N/A,Natural Language Processing,Image credit: [TLDR: Extreme Summarization of Scientific Documents](https://arxiv.org/pdf/2004.15011v3.pdf)
Language Acquisition,7,BLiMP; New Brown Corpus; Avalinguo Audio Dataset; NLI-PT; Duolingo SLAM Shared Task; BabySLM; Duolingo Spaced Repetition Data,SLAM 2018,AUC,Grounded language learning,Natural Language Processing,Language acquisition refers to tasks related to the learning of a second language.
Systematic Generalization,7,GSCAN; S2B; Cryptics; PCFG SET; Mathematics Dataset; ZEST; SCAN,N/A,N/A,N/A,Reasoning,N/A
General Reinforcement Learning,7,Obstacle Tower; ProcGen; WLD; PackIt; Avalon; bipedal-skills; POPGym,Obstacle Tower (No Gen) varied; Obstacle Tower (No Gen) fixed; Obstacle Tower (Weak Gen) varied; Obstacle Tower (Strong Gen) varied; Obstacle Tower (Strong Gen) fixed; Obstacle Tower (Weak Gen) fixed,Score,Model-based Reinforcement Learning; Offline RL,Methodology; Reasoning; Robots; Playing Games,N/A
Twitter Sentiment Analysis,7,Crypto related tweets from 10.10.2020 to 3.3.2021; #chinahate; Twitter Sentiment Analysis; ArSen-20; RETWEET; Multi-Class Depression Detection Dataset; SentimentArcs: Sentiment Reference Corpus for Novels,N/A,N/A,Tweet-Reply Sentiment Analysis,Natural Language Processing,Twitter sentiment analysis is the task of performing sentiment analysis on tweets from Twitter.
Toxic Comment Classification,7,FairPrism; Civil Comments; Jigsaw Toxic Comment Classification Dataset; WikiDetox; UIT-ViCTSD; CAD; K-MHaS: Korean Multi-label Hate Speech Dataset,N/A,N/A,N/A,N/A,N/A
News Summarization,7,Reddit Ideological and Extreme Bias Dataset; FIB; DICE: a Dataset of Italian Crime Event news; M3LS; VNDS; Bengali Curated News Summary Dataset; ECTSum,N/A,N/A,N/A,N/A,N/A
Masked Language Modeling,7,blbooks; LatamXIX; GENEUTRAL; Spanish Corpus XIX; GENTER; GENTYPES; DiFair,N/A,N/A,N/A,N/A,N/A
Concept-based Classification,6,CelebA; AwA2; aPY; CheXpert; CUB-200-2011; AwA,CelebA; AwA2; aPY; CheXpert; CUB-200-2011,Concept Accuracy (%); Task Accuracy (%),N/A,Computer Vision,Image classification using human-interpretable concepts
Knowledge Distillation,6,KITTI; ImageNet; CIFAR-100; COCO (Common Objects in Context); PASCAL VOC; Cityscapes,COCO 2017 val; small content; en pt br; KITTI; ImageNet; big content; en es; CIFAR-100; COCO (Common Objects in Context); PASCAL VOC,;  box AP; AP@0.5; CRD training setting; AP; RMSE; mask AP; Top-1 Accuracy (%); mAP; model size,Self-Knowledge Distillation; Data-free Knowledge Distillation,Computer Vision; Natural Language Processing,Knowledge distillation is the process of transferring knowledge from a large model to a smaller one. While large models (such as very deep neural networks or ensembles of many models) have higher know...
Constituency Parsing,6,PCC; Taiga Corpus; Alexa Point of View; FLUE; Penn Treebank; MASC,Penn Treebank; CTB7; ATB; CTB5,F1; F1 score,Constituency Grammar Induction,Natural Language Processing,Constituency parsing aims to extract a constituency-based parse tree from a sentence that  represents its syntactic structure according to a [phrase structure grammar](https://en.wikipedia.org/wiki/Ph...
Document Text Classification,6,Food-101; MatriVasha:; Kompetencer; Tobacco-3482; WOS Hierarchical Text Classification; CUB-200-2011,Tobacco small-3482; Tobacco-3482; Food-101; CUB-200-2011,Accuracy (%); Training time (hours); Accuracy; Training time (min),Political Salient Issue Orientation Detection; Learning with noisy labels; Multi-Label Classification Of Biomedical Texts,Computer Vision; Natural Language Processing; Medical,N/A
Multi-Modal Document Classification,6,Food-101; RVL-CDIP; Tobacco-3482; CUB-200-2011; Reuters-21578; HeriGraph,N/A,N/A,N/A,N/A,N/A
Cross-Domain Few-Shot,6,EuroSAT; Earth on Canvas; mini-Imagenet; Places; CUB-200-2011; Places205,EuroSAT; ChestX; ISIC2018; Plantae; CropDisease; cars; CUB; Places; miniImagenet,5 shot; Accuracy (%),cross-domain few-shot learning,Computer Vision,N/A
Few-Shot Relation Classification,6,FREDo; TexRel; FewRel 2.0; FewRel; SciERC; DocRED,N/A,N/A,N/A,N/A,N/A
Generative Question Answering,6,Reddit Ideology Database; CICERO; FrenchMedMCQA; CoQA; Reddit; LLeQA,N/A,N/A,N/A,N/A,N/A
Environmental Sound Classification,6,FSD50K; UrbanSound8K; SINGA:PURA; ESC50; SONYC-UST-V2; ESC-50,FSD50K; ESC-50; UrbanSound8K,Accuracy; mAP,Self-Supervised Sound Classification,Audio,Classification of Environmental Sounds. Most often sounds found in Urban environments. Task related to noise monitoring.
Community Question Answering,6,ANTIQUE; AmazonQA; Quora Question Pairs; PerCQA; SE-PQA; CQASUMM,N/A,N/A,N/A,N/A,N/A
Text Spotting,6,ICDAR 2015; Textual Visual Semantic Dataset; SCUT-CTW1500; UCLA Protest Image; LSVTD; Total-Text,ICDAR 2015; Inverse-Text; Total-Text; SCUT-CTW1500,F-measure (%) - Full Lexicon; F-measure (%) - No Lexicon; F-measure (%) - Weak Lexicon; F-measure (%) - Strong Lexicon; F-measure (%) - Generic Lexicon; F-Measure (%) - Full Lexicon,N/A,Computer Vision,Scene Text Spotting is the combination of Scene Text Detection and Scene Text Recognition in an end-to-end manner.  It is the ability to read natural text in the wild.
Source-Free Domain Adaptation,6,ACDC (Adverse Conditions Dataset with Correspondences); Dark Zurich; VisDA-2017; SYNTHIA; GTA5; PACS,Cityscapes to Dark Zurich; VisDA-2017; GTA5 to Cityscapes; SYNTHIA-to-Cityscapes; PACS; Cityscapes to ACDC; 	VIPER-to-Cityscapes,mIoU; Average Accuracy; Accuracy,Source Free Object Detection; 3D Source-Free Domain Adaptation,Computer Vision,"Source-Free Domain Adaptation (SFDA) is a domain adaptation method in machine learning and computer vision where the goal is to adapt a pre-trained model to a new, target domain without access to the ..."
Table-to-Text Generation,6,WikiBio; RotoWire; Wikipedia Person and Animal Dataset; WebNLG; E2E; DART,WikiBio; RotoWire; WebNLG (All); Wikipedia Person and Animal Dataset; WebNLG (Unseen); E2E; DART; WebNLG (Seen),ROUGE; PARENT; BLEU; ROUGE-L; METEOR; Mover; CIDEr; FactSpotter; TER; BERT,KB-to-Language Generation,Natural Language Processing,**Here is the provided data converted into a table format for clarity:  COUNTRIES	1971-2010	2011	2012	2013	2014	2015	2016	2017	2018  									  Saudi Arabia	2742962	222247	358560	270502	312489	522750	...
Multi-Source Unsupervised Domain Adaptation,6,Tennessee Eastman Process; Office-Home; ImageCLEF-DA; GTZAN; Office-31; DomainNet,N/A,N/A,N/A,N/A,N/A
AMR Parsing,6,Groningen Meaning Bank; LDC2017T10; LDC2020T02; The Little Prince; New3; Bio,LDC2014T12; LDC2017T10; LDC2014T12:; LDC2020T02; The Little Prince; New3; Bio; LDC2015E86,F1 Full; F1 Newswire; Smatch,N/A,Natural Language Processing,"Each AMR is a single rooted, directed graph. AMRs include PropBank semantic roles, within-sentence coreference, named entities and types, modality, negation, questions, quantities, and so on. [See](ht..."
News Generation,6,BalitaNLP; Reddit Ideology Database; Reddit Ideological and Extreme Bias Dataset; Ice Hockey News Dataset; Reddit; FakeNewsNet,N/A,N/A,Headline Generation,Natural Language Processing,Generation of larger segments of text with consistent topic and evolving story.
Compositional Zero-Shot Learning,6,AO-CLEVr; ReaSCAN; MIT-States; UT Zappos50K; UT-Zappos50K; C-GQA,N/A,N/A,N/A,N/A,N/A
Lexical Entailment,6,HyperLex; IMPPRES; SherLIiC; TERRa; Persian NLP Hub; EVALution,N/A,N/A,N/A,N/A,N/A
Multimodal Recommendation,6,Amazon Office Products; Amazon Beauty; Amazon Digital Music; Amazon Review; Amazon Toys & Games; Amazon Baby,N/A,N/A,N/A,N/A,N/A
Age And Gender Classification,6,Age and Gender; LAGENDA; BN-AuthProf; mebeblurf; CI-MNIST; IMDB-Clean,Adience Gender; Adience Age; BN-AuthProf,Accuracy (5-fold); F1 score,Author Profiling,Natural Language Processing,"Age and gender classification is a dual-task of identifying the age and gender of a person from an image or video.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Multi-Expert Gender Classi..."
Text Categorization,6,Multilingual Reuters; Text_VPH; NatCat; DICE: a Dataset of Italian Crime Event news; DAWT; MNAD,N/A,N/A,N/A,N/A,N/A
Genre classification,6,Moviescope; Trailers12k; FMA; PTVD; MuMu; Book Cover Dataset,N/A,N/A,N/A,N/A,N/A
Meme Classification,6,Harm-C; Hateful Memes; HarMeme; Tamil Memes; MMHS150k; MultiOFF,MultiOFF; Tamil Memes; Hateful Memes,F1; ROC-AUC; Accuracy; Micro-F1,Hateful Meme Classification,Computer Vision; Natural Language Processing,Meme classification refers to the task of classifying internet memes.
multimodal generation,6,Multi-Modal CelebA-HQ; MedTrinity-25M; Alex-20; REBUS; MMNeedle; taste-music-dataset,N/A,N/A,N/A,N/A,N/A
Meeting Summarization,6,QMSum; MeetingBank; ICSI Meeting Corpus; public_meetings; AMI Meeting Corpus; ELITR Minuting Corpus,ICSI Meeting Corpus; AMI Meeting Corpus,ROUGE-1 F1,N/A,Natural Language Processing,Generating a summary from meeting transcriptions.     A survey for this task: [Abstractive Meeting Summarization: A Survey](https://paperswithcode.com/paper/abstractive-meeting-summarization-a-survey)
Text-to-Music Generation,6,MusicCaps; AIME; Song Describer Dataset; MusicBench; PIAST; JamendoMaxCaps,MusicBench; MusicCaps,IS; FD; FD_openl3; FAD; CLAP_MS; KL_passt; CLAP_LAION,N/A,Audio,N/A
text annotation,6,"Text_VPH; OASST1; MIBot - Motivational Interviewing for Smoking Cessation Dataset, based on MIBOT Version 6.3A; VMD; Reddit Posts Related To Eating Disorders and Dieting; MATHWELL Human Annotation Dataset",N/A,N/A,N/A,Natural Language Processing,N/A
Generalized Few-Shot Learning,5,Geoclidean-Elements; AwA2; CUB-200-2011; SUN; AwA,CUB; SUN; AwA2,Per-Class Accuracy (1-shot); Per-Class Accuracy (5-shots); Per-Class Accuracy (10-shots); Per-Class Accuracy (2-shots); Per-Class Accuracy  (2-shots); Per-Class Accuracy (20-shots),Long-tail Learning,Methodology,N/A
Reading Comprehension (Zero-Shot),5,CMRC 2018; Belebele; DRCD; DuReader; CMRC,N/A,N/A,N/A,N/A,N/A
Reading Comprehension (One-Shot),5,CMRC 2018; Belebele; DRCD; DuReader; CMRC,N/A,N/A,N/A,N/A,N/A
Reading Comprehension (Few-Shot),5,CMRC 2018; Belebele; DRCD; DuReader; CMRC,N/A,N/A,N/A,N/A,N/A
Open Intent Discovery,5,SNIPS; BANKING77; CLINC150; ATIS; DBpedia,SNIPS; BANKING77; CLINC150; ATIS; Stackoverflow; DBpedia,ARI; NMI; ACC; Clustering Accuracy,N/A,Natural Language Processing,Open intent discovery aims to leverage limited prior knowledge of known intents to find fine-grained known and open intent-wise clusters.
Synthetic-to-Real Translation,5,Syn2Real; SYNTHIA; GTA5; WinSyn; Sims4Action,N/A,N/A,N/A,N/A,N/A
Variational Inference,5,HASY; CIFAR-100; WI-LOCNESS; WikiAtomicEdits; Silhouettes,N/A,N/A,N/A,N/A,N/A
Universal Domain Adaptation,5,Office-Home; VisDA-2017; All-day CityScapes; Office-31; DomainNet,Office-31; DomainNet; VisDA2017; Office-Home,Source-free; Source-Free; H-Score; H-score; VLM,N/A,Computer Vision,N/A
Paper generation,5,"PubMed Paper Reading Dataset; ICLR Database; 2017 Robotic Instrument Segmentation Challenge; ACL Title and Abstract Dataset; PubMed Term, Abstract, Conclusion, Title Dataset",N/A,N/A,N/A,N/A,N/A
AMR-to-Text Generation,5,LDC2017T10; LDC2020T02; The Little Prince; New3; Bio,N/A,N/A,N/A,N/A,N/A
Text Clustering,5,MTEB; COVID-19 Twitter Chatter Dataset; DICE: a Dataset of Italian Crime Event news; 20 Newsgroups; Urdu News Headlines Dataset,Urdu News Headlines Dataset; 20 Newsgroups; MTEB,Related Headlines; V-Measure; Accuracy,Hierarchical Text Clustering; Open Intent Discovery; Short Text Clustering,Natural Language Processing,Grouping a set of texts in such a way that objects in the same group (called a cluster) are more similar (in some sense) to each other than to those in other groups (clusters). (Source: Adapted from W...
Code Documentation Generation,5,CodeSearchNet; PyTorrent; Java scripts; notebookcdg; DeepCom-Java,CodeSearchNet; CodeSearchNet - Ruby; CodeSearchNet - Java; CodeSearchNet - Go; CodeSearchNet - JavaScript; CodeSearchNet - Python; CodeSearchNet - Php,Smoothed BLEU-4,CodeSearchNet - Java,Computer Code,"Code Documentation Generation is a supervised task where a code function is the input to the model, and the model generates the documentation for this function.    Description from: [CodeTrans: Toward..."
Multimodal Machine Translation,5,HaVG; WMT 2016 Biomedical; WMT 2016 IT; Hindi Visual Genome; Multi30K,Multi30K; Hindi Visual Genome (Test Set); Hindi Visual Genome (Challenge Set),BLEU (EN-DE); Meteor (EN-FR); Meteor (EN-DE); BLEU (EN-HI); BLUE (DE-EN),Face to Face Translation; Multimodal Lexical Translation,Computer Vision; Natural Language Processing,"Multimodal machine translation is the task of doing machine translation with multiple data sources - for example, translating ""a bird is flying over water"" + an image of a bird over water to German te..."
Translation eng-fra,5,Tatoeba; FLoRes-101; FLoRes-200; Multi30K; TICO-19,N/A,N/A,N/A,N/A,N/A
Morphological Analysis,5,TrMor2018; Egyptian Arabic Segmentation Dataset; CELEX; Polyglot-NER; Wikipedia Title,N/A,N/A,N/A,Natural Language Processing,**Morphological Analysis** is a central task in language processing that can take a word as input and detect the various morphological entities in the word and provide a morphological representation o...
Low-Resource Neural Machine Translation,5,FLoRes; ASPEC; CVIT PIB; KurdishInterdialect; Leipzig Corpora,N/A,N/A,N/A,N/A,N/A
Goal-Oriented Dialogue Systems,5,DSTC7 Task 1; MMD; MultiDoc2Dial; MetaLWOz; LLMafia,N/A,N/A,N/A,N/A,N/A
Zero-shot Classification (unified classes),5,ImageNet_CN; LLVIP; RESISC45; SynthPAI; AID,N/A,N/A,N/A,N/A,N/A
Hateful Meme Classification,5,Harm-C; PrideMM; Hateful Memes; HarMeme; Harm-P,N/A,N/A,N/A,N/A,N/A
Long Form Question Answering,5,QuALITY; MMInstruct-GPT4V; ELI5; DARai; LLeQA,N/A,N/A,N/A,N/A,N/A
Cross-Lingual Question Answering,5,XQuAD; TyDiQA-GoldP; QALD-9-Plus; MLQA; LitMind Dictionary,N/A,N/A,N/A,N/A,N/A
Transliteration,5,TSAC; ANETAC; ArzEn; Bianet; Dakshina,N/A,N/A,N/A,N/A,N/A
Multilabel Text Classification,5,EURLEX57K; TuPyE-Dataset; arXiv Categories; NLP Taxonomy Classification Data; HumSet,N/A,N/A,N/A,N/A,N/A
Material Classification,5,SpectroVision; MINC; LabPics; OpenSurfaces; Touch and Go,N/A,N/A,N/A,Computer Vision,N/A
Graph Generation,5,PISC; STREETS; Torque; WikiGraphs; TRN,Toulouse Road Network,StreetMover,N/A,Graphs,"**Graph Generation** is an important research area with significant applications in drug and material designs.   <span class=""description-source"">Source: [Graph Deconvolutional Generation ](https://ar..."
Texture Classification,5,CBTex; ITD; Multidimensional Texture Perception; ISOD; ElBa,N/A,N/A,N/A,Computer Vision,"**Texture Classification** is a fundamental issue in computer vision and image processing, playing a significant role in many applications such as medical image analysis, remote sensing, object recogn..."
Crop Classification,5,Sen4AgriNet; EuroCrops; SICKLE; CropAndWeed; SemanticSugarBeets,CropHarvest - Togo; CropHarvest - Kenya; CropHarvest multicrop - Global; CropHarvest - Global; CropHarvest - Brazil,F1 Macro; AUC; Average Accuracy; Target Binary F1,N/A,Miscellaneous; Computer Vision,N/A
Knowledge Probing,5,BEAR-probe; PopQA; BioLAMA; TOFU; Spectral Detection and Analysis Based Paper(SDAAP) dataset,N/A,N/A,N/A,N/A,N/A
Multi-task Language Understanding,5,MML; BIG-bench; MGSM; BBH; Tasksource,N/A,N/A,N/A,N/A,N/A
Temporal Relation Classification,5,2012 i2b2 Temporal Relations; MATRES; SupplyGraph; French Timebank; Catalan TimeBank 1.0,N/A,N/A,N/A,N/A,N/A
Speech-to-Speech Translation,5,TAT; LibriS2S; Heroes Corpus; CVSS; SpeechMatrix,TAT; CVSS; FLEURS X-eng,ASR-BLEU (Dev); Parameters; ASR-BLEU (Test); ASR-BLEU,N/A,Speech,"Speech-to-speech translation (S2ST) consists on translating speech from one language to speech in another language. This can be done with a cascade of automatic speech recognition (ASR), text-to-text ..."
Protein Language Model,5,Protein-Instructions-OOD; CATH 4.2; ProteinGym; CATH 4.3; DAVIS-DTA,N/A,N/A,N/A,N/A,N/A
Spatial Reasoning,5,RePAIR Dataset; EmbSpatial-Bench; MMVP; BlINK; RealWorldQA,N/A,N/A,N/A,N/A,N/A
Gender Classification,5,IUST_PersonReID; inaGVAD; BN-AuthProf; IMDB-WIKI; PsyMo,N/A,N/A,N/A,N/A,N/A
Long-Context Understanding,5,L-Eval; NoLiMa; LongBench; MMNeedle; TIME,Ada-LEval (BestAnswer); L-Eval; Ada-LEval (TSort); LongBench; MMNeedle,"128k; Average Score; 6k; 12k; 10 Images, 2*2 Stitching, Exact Accuracy; 1 Image, 4*4 Stitching, Exact Accuracy; 10 Images, 4*4 Stitching, Exact Accuracy; 32k; 2k; 1 Image, 2*2 Stitching, Exact Accuracy",N/A,Natural Language Processing,N/A
Multi Label Text Classification,4,MNIST; GoEmotions; CARER; HateScore,N/A,N/A,N/A,N/A,N/A
Multi-Human Parsing,4,PASCAL-Part; LLMafia; MHP; CCIHP,N/A,N/A,N/A,N/A,N/A
Predicate Classification,4,ImageCLEF-DA; Visual Genome; Haystack; 3RScan,N/A,N/A,N/A,N/A,N/A
Zero-shot Audio Classification,4,AudioSet; ESC-50; UrbanSound8K; VGG-Sound,N/A,N/A,N/A,N/A,N/A
Intent Discovery,4,ATIS; Persian-ATIS; diaforge-utc-r-0725; SNIPS,ATIS; Persian-ATIS; SNIPS,ARI,N/A,Natural Language Processing,"Given a set of labelled and unlabelled utterances, the idea is to identify existing (known) intents and potential (new intents) intents. This method can be utilised in conversational system setting."
Unsupervised Text Classification,4,Medical Abstracts; Yahoo! Answers; 20NewsGroups; AG News,N/A,N/A,N/A,N/A,N/A
Zero-Shot Text Classification,4,HateXplain; Events classification - Biotech news; This is not a Dataset; AG News,N/A,N/A,N/A,N/A,N/A
Few-Shot Text Classification,4,RAFT; SST-5; SST; Events classification - Biotech news,ODIC 10-way (10-shot); ODIC 5-way (5-shot); RAFT; ODIC 5-way (10-shot); SST-5; Average on NLP datasets; ODIC 10-way (5-shot); Amazon Counterfeit,ADE;  Over; Accuracy; TEH; SRI; ToS; TC; B77; SOT; OSE,Zero-Shot Out-of-Domain Detection,Natural Language Processing,Few-shot Text Classification predicts the semantic label of a given text with a handful of supporting instances [1](https://aclanthology.org/2022.emnlp-main.87)
Citation Intent Classification,4,SciCite; unarXive; REFCAT; ACL ARC,N/A,N/A,N/A,N/A,N/A
Partial Domain Adaptation,4,Office-31; VisDA-2017; DomainNet; Office-Home,N/A,N/A,N/A,N/A,N/A
Multi-target Domain Adaptation,4,Office-31; DomainNet; OBJ-MDA; Office-Home,Office-31; DomainNet; Office-Home; OBJ-MDA,mAP@0.5; Accuracy,N/A,Computer Vision,The idea of Multi-target Domain Adaptation is to adapt a model from a single labelled source domain to multiple unlabelled target domains.
Knowledge Graph Embedding,4,GEval for KGRC-RDF-star; KGRC-RDF-star; FB15k; KG20C,FB15k,MRR,Open Knowledge Graph Embedding,Knowledge Base,N/A
Multi-modal Classification,4,AudioSet; Mudestreda; VGG-Sound; Gaze-CIFAR-10,AudioSet; VGG-Sound,Top-1 Accuracy; Top-5 Accuracy; Average mAP,Image-text Classification,Miscellaneous,N/A
Breast Tumour Classification,4,PCam; BreastClassifications4; BCNB; Breast Lesion Detection in Ultrasound Videos (CVA-Net),PCam,AUC; Accuracy,N/A,Medical,N/A
Logical Reasoning Question Answering,4,JustLogic; SUTD-TrafficQA; ReClor; RoomSpace,N/A,N/A,N/A,N/A,N/A
Translation ces-eng,4,FLoRes-200; Multi30K; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation deu-fra,4,FLoRes-200; Multi30K; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Spoken language identification,4,VOXLINGUA107; VoxForge; FLEURS; IndicTTS,N/A,N/A,N/A,N/A,N/A
Music Auto-Tagging,4,TimeTravel; MagnaTagATune; PIAST; MSD,TimeTravel; MagnaTagATune; MagnaTagATune (clean); Million Song Dataset,ROC-AUC; ROC AUC; PR-AUC; 0..5sec,N/A,Music,N/A
Music Tagging,4,ATD-Dataset; MagnaTagATune; PIAST; EMOPIA,N/A,N/A,N/A,N/A,N/A
End-To-End Dialogue Modelling,4,LLMafia; MultiWOZ; BiToD; Arabic-ToD,N/A,N/A,N/A,N/A,N/A
Unsupervised Machine Translation,4,WMT 2016 News; WMT 2014; WMT 2016; CCMatrix,N/A,N/A,N/A,N/A,N/A
Triple Classification,4,CoDEx Medium; CoDEx Large; CoDEx Small; YAGO,YAGO39K,Precision; F1-Score; Accuracy; Recall,Knowledge Graph Embeddings,Graphs,"Triple classification aims to judge whether a given triple (h, r, t) is correct or not with respect to the knowledge graph."
Speech-to-Text Translation,4,CoVoST; Kosp2e; MuST-C; MediBeng,CoVoST 2 eng-X; FLEURS eng-X; libri-trans; FLEURS X-eng; MuST-C EN->DE; CoVoST 2 X-eng; MediBeng; MuST-C EN->NL; MuST-C; MuST-C EN->FR,SacreBLEU; BLEU; Case-sensitive sacreBLEU; Case-insensitive tokenized BLEU; Case-insensitive sacreBLEU; Bleu; Case-sensitive tokenized BLEU,Simultaneous Speech-to-Text Translation,Natural Language Processing,"Translate audio signals of speech in one language into text in a foreign language, either in an end-to-end or cascade manner."
Cloze (multi-choices) (Zero-Shot),4,CMRC 2017; CMRC 2019; CMRC; ChID,N/A,N/A,N/A,N/A,N/A
Cloze (multi-choices) (Few-Shot),4,CMRC 2017; CMRC 2019; CMRC; ChID,N/A,N/A,N/A,N/A,N/A
Morphological Tagging,4,Szeged Corpus; iLur News Texts; CoNLL 2017 Shared Task - Automatically Annotated Raw Texts and Word Embeddings; CoNLL,N/A,N/A,N/A,Natural Language Processing,"Morphological tagging is the task of assigning labels to a sequence of tokens that describe them morphologically. As compared to Part-of-speech tagging, morphological tagging also considers morphologi..."
Knowledge Graph Embeddings,4,GEval for KGRC-RDF-star; OLPBENCH; KGRC-RDF-star; MutualFriends,N/A,N/A,N/A,N/A,N/A
Zero-Shot Cross-Lingual Transfer,4,XTREME; MaRVL; IGLUE; xSID,N/A,N/A,N/A,N/A,N/A
Translation eng-spa,4,FLoRes-200; TICO-19; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ben-eng,4,FLoRes-200; TICO-19; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Arabic Sentiment Analysis,4,ArSentD-LEV; ArSen; ArSen-20; LABR,!(()&&!|*|*|,0-shot MRR,N/A,Natural Language Processing,"Arabic sentiment analysis is the process of computationally identifying and categorizing opinions expressed in a piece of arabic text, especially in order to determine whether the writer's attitude to..."
Grasp Generation,4,rc_49; FFHNet; A Billion Ways to Grasp; GRAB,N/A,N/A,Grasp rectangle generation; Controllable Grasp Generation,Computer Vision; Robots,N/A
text-based games,4,LLMafia; TextWorld KG; Jericho; Cards Against Humanity,N/A,N/A,N/A,Playing Games,Text-based games to evaluate the Reinforcement Learning Agents
Few-shot NER,4,WNUT 2017; XGLUE; Chem-FINESE; Few-NERD,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Classification,4,ShARe/CLEF 2014: Task 2 Disorders; UBI-Fights; BCNB; THYME-2016,THYME-2016; ShARe/CLEF 2014: Task 2 Disorders,F1,Weakly Supervised Data Denoising,Natural Language Processing,N/A
Conditional Text Generation,4,BalitaNLP; Lipogram-e; VitaminC; WikiGraphs,Lipogram-e,Ignored Constraint Error Rate,Multimedia Generative Script Learning; Contextualized Literature-based Discovery,Natural Language Processing,The task of generating text according to some pre-specified conditioning (e.g. topic or sentiment or constraint)
Multi-hop Question Answering,4,MuSiQue-Ans; ConcurrentQA Benchmark; SPARTQA -; MultiQ,ConcurrentQA; MuSiQue-Ans,An; Answer F1; Sp,N/A,Knowledge Base,N/A
Answer Generation,4,LLMafia; QAMPARI; CICERO; WeiboPolls,N/A,N/A,N/A,N/A,N/A
Temporal/Casual QA,4,TIME; TimelineKGQA; TimeQA; NExT-QA,N/A,N/A,N/A,N/A,N/A
Math Word Problem SolvingΩ,4,DART-Math-Uniform; ParaMAWPS; MAWPS; DART-Math-Hard,N/A,N/A,N/A,N/A,N/A
Science Question Answering,4,FrenchMedMCQA; ScienceQA; SemOpenAlex; BioFuelQR,N/A,N/A,N/A,N/A,N/A
Music Recommendation,4,PIAST; MuseChat Dataset; YouTube8M-MusicTextClips; Music4All-Onion,N/A,N/A,N/A,N/A,N/A
Text Reranking,4,Multi-EuP: The Multilingual European Parliament Dataset for Analysis of Bias in Information Retrieval; MTEB; Claim Matching Robustness; Cards Against Humanity,N/A,N/A,N/A,N/A,N/A
Lay Summarization,4,eLife; AUTH at BioLaySumm 2024 - Bringing Scientific Content to Kids; AUTH at BioLaySumm 2024: Bringing Scientific Content to Kids; PLOS,N/A,N/A,N/A,N/A,N/A
Sports Understanding,4,QASports; Predictive Model for Assessing Knee Muscle Injury Risk in Athletes and Non-Athletes Using sEMG; DeepSportRadar-v1; MultiSenseBadminton,N/A,N/A,N/A,N/A,N/A
Out-of-Distribution Generalization,4,shape bias; UrbanCars; ImageNet-W; Object-Centric Stylized COCO,N/A,N/A,N/A,N/A,N/A
Text2text Generation,4,Kaleidoscope; diaforge-utc-r-0725; HarmfulQA; MTTN,MTTN: Multi-Pair Text to Text Narratives for Prompt Generation; lmqg/qg_jaquad; lmqg/qg_squad,ROUGE-1,Sketch-to-text Generation; Keyphrase Generation; Figurative Language Visualization,Natural Language Processing,N/A
Referring expression generation,4,ColonINST-v1 (Unseen); ColonINST-v1 (Seen); ColonINST-v1; A Game Of Sorts,ColonINST-v1 (Unseen); ColonINST-v1 (Seen),Accuray,N/A,Computer Vision,Generate referring expressions
tabular-classification,4,TACM12K; AjwaOrMedjool; TML1M; TLF2K,N/A,N/A,N/A,N/A,N/A
Classification with Binary Weight Network,3,CIFAR-100; MNIST; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Data-free Knowledge Distillation,3,QNLI; SQuAD; GLUE,QNLI; SQuAD,Exact Match; Accuracy,Benchmarking,Natural Language Processing,N/A
Sentence-Embedding,3,Cards Against Humanity; GLUE; McQueen,N/A,N/A,N/A,N/A,N/A
Classification with Binary Neural Network,3,CIFAR-100; CIFAR-10; ImageNet,N/A,N/A,N/A,N/A,N/A
Human Parsing,3,4D-DRESS; MHP; PASCAL Context,4D-DRESS; PASCAL Context,mIoU; mAcc,Multi-Human Parsing,Computer Vision,"Human parsing is the task of segmenting a human image into different fine-grained semantic parts such as head, torso, arms and legs.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Multi-Hu..."
Unsupervised KG-to-Text Generation,3,GenWiki; Visual Genome; WebNLG,N/A,N/A,N/A,N/A,N/A
Few-Shot Class-Incremental Learning,3,CIFAR-100; CUB-200-2011; mini-Imagenet,N/A,N/A,N/A,N/A,N/A
text2text-generation,3,SQuAD; ChatGPT Paraphrases; ChatHaruhi,N/A,N/A,N/A,N/A,N/A
SQL Parsing,3,ATIS; Yelp; IMDb Movie Reviews,N/A,N/A,N/A,N/A,N/A
Chinese Sentiment Analysis,3,SST-2; SST; STATE ToxiCN,N/A,N/A,N/A,N/A,N/A
Unsupervised Text Style Transfer,3,Yelp; GYAFC; Yelp2018,N/A,N/A,N/A,N/A,N/A
Unsupervised Opinion Summarization,3,SPACE (Opinion Summarization); AmaSum; Yelp,N/A,N/A,N/A,N/A,N/A
Multibehavior Recommendation,3,MovieLens; Multi-behavior Taobao; Yelp,N/A,N/A,N/A,N/A,N/A
Molecular Graph Generation,3,MOSES; QM9; ZINC,N/A,N/A,N/A,N/A,N/A
Blended-target Domain Adaptation,3,Office-31; DomainNet; Office-Home,N/A,N/A,N/A,N/A,N/A
Talking Head Generation,3,VoxCeleb2; VoxCeleb1; AnimeCeleb,VoxCeleb2 - 1-shot learning; VoxCeleb1 - 1-shot learning; VoxCeleb1 - 32-shot learning; VoxCeleb1 - 8-shot learning; VoxCeleb2 - 32-shot learning; VoxCeleb2 - 8-shot learning; 100 sleep nights of 8 caregivers,CSIM; LPIPS; FID; 10%; inference time (ms); Normalized Pose Error; SSIM,Unconstrained Lip-synchronization,Computer Vision,"Talking head generation is the task of generating a talking face from a set of images of a person.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Few-Shot Adversarial Learning of Realistic..."
Annotated Code Search,3,PACS; ISAdetect dataset; PyTorrent,N/A,N/A,N/A,N/A,N/A
Automatic Sleep Stage Classification,3,Montreal Archive of Sleep Studies; ISRUC-Sleep; Sleep-EDF,ISRUC-Sleep; Sleep-EDF,Kappa; Number of parameters (M); Accuracy; Cohen’s Kappa score; AUROC,N/A,Medical,N/A
Topological Data Analysis,3,REDDIT-12K; Reddit; REDDIT-5K,N/A,N/A,N/A,Graphs,N/A
Sentence Fusion,3,PoC; DiscoFuse; WikiSplit,N/A,N/A,N/A,N/A,N/A
Few-Shot Point Cloud Classification,3,ModelNet40-C; ModelNet; ScanObjectNN,N/A,N/A,N/A,N/A,N/A
Recipe Generation,3,RecipeNLG; Food.com Recipes and Interactions; Recipe1M+,Food.com; Now You're Cooking!; Recipe1M; allrecipes.com; RecipeNLG,F1; Word Error Rate (WER); D-2; Perplexity; BLEU; BLEU-1; Rouge-L; GLEU; Mean IoU; BLEU-4,N/A,Miscellaneous; Natural Language Processing,"Food recipe generation from ingredients, title and/or food images."
Translation ces-deu,3,FLoRes-200; Multi30K; Tatoeba,N/A,N/A,N/A,N/A,N/A
Graph Similarity,3,IMDB-MULTI; Linux; IMDb Movie Reviews,IMDb,mse (10^-3),N/A,Graphs,N/A
Spoken Dialogue Systems,3,Dialogue State Tracking Challenge; CQR; SD-Eval,N/A,N/A,N/A,Speech,N/A
Multi-Hop Reading Comprehension,3,MedHop; ConcurrentQA Benchmark; CodeQueries,N/A,N/A,N/A,N/A,N/A
Translation spa-eng,3,TICO-19; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation afr-deu,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation afr-eng,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation cat-eng,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation rus-por,3,FLoRes-200; TICO-19; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation eng-por,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ara-eng,3,TICO-19; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation cym-eng,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation dan-eng,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation heb-eng,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation hun-eng,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation cat-fra,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation glg-spa,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation por-fra,3,FLoRes-200; TICO-19; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation por-spa,3,FLoRes-200; TICO-19; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation dan-spa,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ara-fra,3,TICO-19; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation heb-fra,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation heb-por,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation afr-spa,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ceb-eng,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ces-spa,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation hin-eng,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation hun-deu,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation hun-fra,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation hun-por,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ind-deu,3,FLoRes-200; Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ind-eng,3,FLoRes-200; TICO-19; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ind-fra,3,FLoRes-200; TICO-19; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation mar-eng,3,FLoRes-200; TICO-19; Tatoeba,N/A,N/A,N/A,N/A,N/A
Arabic Text Diacritization,3,WikiNews Dataset; CATT; Arabic Text Diacritization,Tashkeela; CATT,Diacritic Error Rate; Word Error Rate (WER); WER (%); DER(%),N/A,Natural Language Processing; Speech,Addition of diacritics for undiacritized arabic texts for words disambiguation.
Font Generation,3,Dafonts Free; Book Cover Dataset; CCSE,N/A,N/A,N/A,N/A,N/A
automatic-speech-translation,3,CoVoST; FLEURS; MediBeng,N/A,N/A,N/A,N/A,N/A
coreference-resolution,3,DaNE; OntoNotes 5.0; WinoPron,N/A,N/A,N/A,N/A,N/A
Contextual Embedding for Source Code,3,PyTorrent; Spectre-v1; ETH Py150 Open,N/A,N/A,N/A,Computer Code,N/A
Document Embedding,3,French CASS dataset; DAPFAM; unarXive,N/A,N/A,N/A,N/A,N/A
Short-Text Conversation,3,LCCC; ThreatGram 101 - Extreme Telegram Data; VMD,N/A,N/A,N/A,N/A,N/A
Food recommendation,3,Indian Food Image Dataset; Water Footprint Recommender System Data; Oktoberfest Food Dataset,Oktoberfest Food Dataset,10 fold Cross validation,N/A,Miscellaneous,N/A
Knowledge Base Population,3,Perlex; LM-KBC 2023; FarsBase-KBP,LM-KBC 2023,F1,N/A,Natural Language Processing; Knowledge Base,Knowledge base population is the task of filling the incomplete elements of a given knowledge base by automatically processing a large corpus of text.
Zero-shot Slot Filling,3,KAMEL; T-REx; MASSIVE,N/A,N/A,N/A,N/A,N/A
Session-Based Recommendations,3,Gowalla; OTTO Recommender Systems Dataset; Retailrocket,N/A,N/A,N/A,N/A,N/A
Cross Document Coreference Resolution,3,SciCo; WEC-Eng; CoreSearch,N/A,N/A,N/A,N/A,N/A
Intent Classification and Slot Filling,3,ATIS (vi); diaforge-utc-r-0725; MASSIVE,N/A,N/A,N/A,N/A,N/A
Facial Makeup Transfer,3,CPM-Synt-2; BeautyFace; CPM-Synt-1,N/A,N/A,N/A,N/A,N/A
Text to Speech,3,ArVoice; Gun Detection Dataset; OpenSLR,N/A,N/A,N/A,N/A,N/A
Document Translation,3,CodeXGLUE; IWSLT2015; WMT 2020,N/A,N/A,N/A,N/A,N/A
Long-Form Narrative Summarization,3,SummScreen; BookSum; MENSA,N/A,N/A,N/A,N/A,N/A
Translation hin-fra,3,FLoRes-200; TICO-19; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Music Style Transfer,3,JS Fake Chorales; Niko Chord Progression Dataset; EMOPIA,N/A,N/A,N/A,N/A,N/A
Probing Language Models,3,KAMEL; BEAR-probe; BioLAMA,KAMEL,Average F1,N/A,Natural Language Processing,N/A
Word Similarity,3,Bangla Word Analogy; WS353; AnlamVer,WS353,Spearman's Rho,N/A,Natural Language Processing,Calculate a numerical score for the semantic similarity between two words.
ContextNER,3,EDGAR10-Q Dataset; The EMBO SourceData-NLP dataset; SourceData-NLP,N/A,N/A,N/A,N/A,N/A
Multi-modal Dialogue Generation,3,MMDialog; OpenViDial 2.0; MMChat,N/A,N/A,N/A,N/A,N/A
Natural Language Queries,3,Ego4D; YouwikiHow; ViLCo,Ego4D,R@1 IoU=0.5; R@1 IoU=0.3; R@5 IoU=0.3; R@1 Mean(0.3 and 0.5); R@5 IoU=0.5,Text-to-CQL,Natural Language Processing,N/A
Land Cover Classification,3,CEMS-W; DeepGlobe; SPADA Dataset,N/A,N/A,N/A,N/A,N/A
Text-based de novo Molecule Generation,3,ChEBI-20; TOMG-Bench; L+M-24,ChEBI-20,Exact Match; Parameter Count; BLEU; Levenshtein; Validity; Frechet ChemNet Distance (FCD); Text2Mol; MACCS FTS; Morgan FTS; RDK FTS,N/A,Medical,"Text-based de novo molecule generation involves utilizing natural language processing (NLP) techniques and chemical information to generate entirely new molecular structures. In this approach, molecul..."
Formal Logic,3,BIG-bench; Mindgames; probability_words_nli,N/A,N/A,N/A,N/A,N/A
text similarity,3,DICE: a Dataset of Italian Crime Event news; PubMed Cognitive Control Abstracts; Phrase-in-Context,N/A,N/A,N/A,N/A,N/A
Lemmatization,3,GUM; AMALGUM; Szeged Corpus,N/A,N/A,N/A,Natural Language Processing,**Lemmatization** is a process of determining a base or dictionary form (lemma) for a given surface form. Especially for languages with rich morphology it is important to be able to normalize words in...
Word Translation,3,satp-zsm-stage1; satp-zsm-stage2; Soundscape Attributes Translation Project (SATP) Dataset,N/A,N/A,N/A,Natural Language Processing,N/A
Data Summarization,3,M3LS; MentSum; PJM(AEP),N/A,N/A,N/A,Miscellaneous,"**Data Summarization** is a central problem in the area of machine learning, where we want to compute a small summary of the data.   <span class=""description-source"">Source: [How to Solve Fair k-Cente..."
Text Pair Classification,3,DACCORD; MTEB; CRED,N/A,N/A,N/A,N/A,N/A
Document Shadow Removal,3,Jung; Kligler; SD7K,N/A,N/A,N/A,N/A,N/A
Sentence-Pair Classification,3,DACCORD; RTE3-FR; GQNLI-FR,N/A,N/A,N/A,Natural Language Processing,N/A
Music Question Answering,3,MusicQA; MMVP; MuChoMusic,MusicQA,BLEU; ROUGE; METEOR; BERT Score,N/A,Music,N/A
Commonsense Causal Reasoning,3,CausalChaos!; This is not a Dataset; COLD: Causal Reasoning in Closed Daily Activities,N/A,N/A,N/A,Natural Language Processing,"""Commonsense Causal Reasoning is the process of capturing and understanding the causal dependencies amongst events and actions."" Luo, Zhiyi, et al. ""Commonsense causal reasoning between short texts."" ..."
knowledge editing,3,KnowEdit; HalluEditBench; WikiFactDiff,N/A,N/A,N/A,N/A,N/A
Multi-modal Recommendation,3,Amazon Clothing; Amazon Sports; Amazon Baby,N/A,N/A,N/A,N/A,N/A
TruthfulQA,3,GenAIPABench-Dataset; News; Knowledge,N/A,N/A,N/A,Knowledge Base,N/A
Informal-to-formal Style Transfer,3,ProofNet#; ProofNetVerif; RLM25,N/A,N/A,N/A,N/A,N/A
Facial Expression Translation,2,CelebA; MH-FED,N/A,N/A,N/A,N/A,N/A
Point Cloud Generation,2,ShapeNet; JetClass,ShapeNet; ShapeNet Chair; ShapeNet Car; ShapeNet Airplane,MMD-CD; EMD; CD; 1-NNA-CD; 1-NNA-EMD,Point Cloud Completion,Computer Vision,N/A
NR-IQA,2,UHD-IQA; LIVE,N/A,N/A,N/A,N/A,N/A
Zero-shot Relation Classification,2,Wiki-ZSL; FewRel,N/A,N/A,N/A,N/A,N/A
Passage Re-Ranking,2,mMARCO; MS MARCO,TREC-PM; MS MARCO,MRR; mAP,N/A,Natural Language Processing,Passage re-ranking is the task of scoring and re-ranking a collection of retrieved documents based on an input query.
Chinese Sentence Pair Classification,2,XNLI; LCQMC,N/A,N/A,N/A,N/A,N/A
Cross-Lingual Natural Language Inference,2,XGLUE; XNLI,N/A,N/A,N/A,N/A,N/A
Linear-Probe Classification,2,ADNI; SentEval,N/A,N/A,N/A,N/A,N/A
Environment Sound Classification,2,ESC-50; UrbanSound8K,N/A,N/A,N/A,N/A,N/A
Short Text Clustering,2,MNAD; AG News,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Text Classification,2,HeriGraph; AG News,N/A,N/A,N/A,N/A,N/A
One-shot Unsupervised Domain Adaptation,2,SYNTHIA; GTA5,N/A,N/A,N/A,N/A,N/A
Explainable Recommendation,2,MovieLens; Tripadvisor Restaurant Reviews,N/A,N/A,N/A,N/A,N/A
Movie Recommendation,2,Genre2Movies; MovieLens,N/A,N/A,N/A,N/A,N/A
Multi-label zero-shot learning,2,Open Images V4; NUS-WIDE,N/A,N/A,N/A,N/A,N/A
CCG Supertagging,2,aethel; CCGbank,CCGbank,Accuracy,N/A,Natural Language Processing,"Combinatory Categorical Grammar (CCG; [Steedman, 2000](http://www.citeulike.org/group/14833/article/8971002)) is a  highly lexicalized formalism. The standard parsing model of [Clark and Curran (2007)..."
Unconditional Molecule Generation,2,QM9; GEOM-DRUGS,QM9; GEOM-DRUGS,PoseBusters Atoms Connected; PoseBusters Internal Energy; PoseBusters Validity; Validity,N/A,Miscellaneous,"This task evaluates the ability of generative models to sample valid and realistic molecular structures.    The training dataset can be:  - QM9 (Wu et al., 2018) - consists of 130,000 stable small org..."
Extended Summarization,2,Pubmed; Arxiv HEP-TH citation graph,N/A,N/A,N/A,N/A,N/A
Open-Set Multi-Target Domain Adaptation,2,Office-31; Office-Home,N/A,N/A,N/A,N/A,N/A
Few-Shot NLI,2,QNLI; SherLIiC,N/A,N/A,N/A,N/A,N/A
Cross-Lingual Document Classification,2,MLDoc; RCV1,MLDoc Zero-Shot English-to-Chinese; Reuters RCV1/RCV2 German-to-English; MLDoc Zero-Shot English-to-Spanish; MLDoc Zero-Shot English-to-Italian; MLDoc Zero-Shot English-to-German; MLDoc Zero-Shot English-to-Russian; MLDoc Zero-Shot English-to-Japanese; MLDoc Zero-Shot English-to-French; Reuters RCV1/RCV2 English-to-German; MLDoc Zero-Shot German-to-French,Accuracy,News Classification,Natural Language Processing,"Cross-lingual document classification refers to the task of using data and models available for one language for which ample such resources are available (e.g., English) to solve classification tasks ..."
Sentence Completion,2,HellaSwag; xP3,HellaSwag,Accuracy,Hurtful Sentence Completion,Natural Language Processing,N/A
Zero-Shot Learning + Domain Generalization,2,DomainNet; Pano3D,N/A,N/A,N/A,N/A,N/A
Cross-Lingual POS Tagging,2,XGLUE; Universal Dependencies,N/A,N/A,N/A,N/A,N/A
Reasoning Chain Explanations,2,JustLogic; eQASC,N/A,N/A,N/A,N/A,N/A
End-to-End RST Parsing,2,RST-DT; RRT,N/A,N/A,N/A,N/A,N/A
Empathetic Response Generation,2,EmpatheticDialogues; StickerConv,N/A,N/A,N/A,N/A,N/A
Translation fra-eng,2,Multi30K; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ces-fra,2,FLoRes-200; Multi30K,N/A,N/A,N/A,N/A,N/A
Dialogue Rewriting,2,CANARD; FollowUp,Rewrite; CANARD; Multi-Rewrite,ROUGE-1; Rewriting F2; BLEU-1; BLEU; ROUGE-L; Rewriting F3; BLEU-2; ROUGE-2; Rewriting F1,N/A,Natural Language Processing,N/A
domain classification,2,Dialogue State Tracking Challenge; MultiWOZ,N/A,N/A,N/A,N/A,N/A
Fine-Grained Opinion Analysis,2,STATE ToxiCN; MPQA Opinion Corpus,N/A,N/A,N/A,N/A,N/A
Cross-Lingual Sentiment Classification,2,MLDoc; MultiBooked,N/A,N/A,N/A,N/A,N/A
Phrase Ranking,2,KP20k; KPTimes,KP20k; KPTimes,P@5K; P@50K,N/A,Natural Language Processing,This task aims to evaluate the “global” rank list of phrases that a method finds from the input corpus.
Phrase Tagging,2,KP20k; KPTimes,KP20k; KPTimes,Precision; F1; Recall,N/A,Natural Language Processing,A fine-grained task that aims to find all occurrences of phrases in sentences.
Zero-shot Audio Captioning,2,Clotho; AudioCaps,N/A,N/A,N/A,N/A,N/A
Music Genre Classification,2,PIAST; GTZAN,N/A,N/A,N/A,N/A,N/A
Cross-Lingual Paraphrase Identification,2,SV-Ident; PAWS-X,N/A,N/A,N/A,N/A,N/A
Re-Ranking,2,SciDocs; AskUbuntu,N/A,N/A,N/A,N/A,N/A
Natural Language Inference (Few-Shot),2,OCNLI; FLEURS,N/A,N/A,N/A,N/A,N/A
Veracity Classification,2,Twitter Death Hoaxes; PANACEA,N/A,N/A,N/A,N/A,N/A
Translation tur-eng,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation deu-afr,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation eng-afr,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation eng-nld,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation nld-eng,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation rus-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation rus-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation rus-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation bel-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation eng-cat,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation eng-tur,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation bul-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation gle-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ell-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation est-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation isl-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation fao-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hrv-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation pol-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation kor-eng,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation cat-por,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation cat-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation fra-por,2,TICO-19; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation fra-spa,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation glg-por,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ita-por,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ita-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ron-fra,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ron-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation bul-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hrv-fra,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hrv-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation mkd-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation dan-fra,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation isl-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ara-spa,2,TICO-19; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation bul-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hrv-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation deu-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation heb-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation fas-fra,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation dan-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation deu-por,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ell-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ell-fra,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ell-por,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ell-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation epo-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation epo-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation epo-por,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation epo-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation est-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation eus-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hun-spa,2,Tatoeba; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation hye-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ilo-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation isl-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ita-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation jpn-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation jpn-por,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation jpn-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation kat-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation mal-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation mlt-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation pes-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation pol-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation pol-fra,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation pol-por,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation pol-spa,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation por-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ron-deu,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ron-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation run-eng,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation run-fra,2,FLoRes-200; Tatoeba,N/A,N/A,N/A,N/A,N/A
Common Sense Reasoning (Zero-Shot),2,This is not a Dataset; C3,N/A,N/A,N/A,N/A,N/A
Dialogue Management,2,DialoGLUE; SCOUT: The Situated Corpus of Understanding Transaction,N/A,N/A,N/A,N/A,N/A
Knowledge Tracing,2,DBE-KT22; EdNet,Assistments; EdNet,Acc; AUC,N/A,Miscellaneous,**Knowledge Tracing** is the task of modelling student knowledge over time so that we can accurately predict how students will perform on future interactions. Improvement on this task means that resou...
Paper generation (Conclusion-to-title),2,"PubMed Term, Abstract, Conclusion, Title Dataset; Elsevier OA CC-BY",N/A,N/A,N/A,N/A,N/A
Headline Generation,2,YTSeg; Elsevier OA CC-BY,N/A,N/A,N/A,N/A,N/A
Age Classification,2,TML1M; EMOTIC,N/A,N/A,N/A,N/A,N/A
Physiological Computing,2,eSports Sensors Dataset; V4V,N/A,N/A,N/A,Computer Vision; Medical,**Physiological computing** is an interdisciplinary field that focuses on the development of computational systems and technologies that interact with and respond to the physiological signals of the h...
English-Ukrainian Translation,2,FLoRes; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Context Query Reformulation,2,MuDoCo_QueryRewrite; FollowUp,N/A,N/A,N/A,Natural Language Processing,N/A
Zero-Shot Region Description,2,Google Refexp; RefCOCO,N/A,N/A,N/A,N/A,N/A
Extreme Multi-Label Classification,2,LSHTC; EXTREME CLASSIFICATION,N/A,N/A,N/A,Methodology,Extreme Multi-Label Classification is a supervised learning problem where an instance may be associated with multiple labels. The two main problems are the unbalanced labels in the dataset and the amo...
Inference Attack,2,MinneApple; Synthetic Keystroke,N/A,N/A,N/A,Adversarial,N/A
Native Language Identification,2,NLI-PT; italki NLI,N/A,N/A,N/A,N/A,N/A
Adversarial Text,2,HarmfulTasks; Texygen Platform,N/A,N/A,N/A,Adversarial,"Adversarial Text refers to a specialised text sequence that is designed specifically to influence the prediction of a language model. Generally, Adversarial Text attack are carried out on Large Langua..."
Vietnamese Machine Reading Comprehension,2,UIT-ViQuAD; Belebele,N/A,N/A,N/A,N/A,N/A
Multi-modal Knowledge Graph,2,MMKG; VirtualHome2KG,N/A,N/A,N/A,Knowledge Base,"[Link to A Survey for Multi-modal Knowledge Graphs.](https://github.com/zjukg/KG-MM-Survey)  Papers integrating Knowledge Graphs (KGs) and Multi-Modal Learning, focusing on research in two principal a..."
Claim Verification,2,Mocheg; FM2,N/A,N/A,N/A,N/A,N/A
Caption Generation,2,Concadia; L+M-24,N/A,N/A,N/A,N/A,N/A
Cancer type classification,2,TCGA; LeukemiaAttri,N/A,N/A,N/A,N/A,N/A
Robot Manipulation Generalization,2,The COLOSSEUM; RLBench,N/A,N/A,N/A,N/A,N/A
Cross-Lingual Entity Linking,2,XL-BEL; LEMONADE,N/A,N/A,Variable Disambiguation,Natural Language Processing,"Cross-lingual entity linking is the task of using data and models available for one language for which ample such resources are available (e.g., English) to solve entity linking tasks (i.e., assigning..."
Embodied Question Answering,2,OpenEQA; EQA,N/A,N/A,N/A,N/A,N/A
Translation bel-por,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation afr-fra,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation afr-por,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation amh-eng,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ben-deu,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ceb-deu,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ceb-fra,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation ceb-por,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation cym-por,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation cym-spa,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation est-por,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation gle-spa,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation hau-eng,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation hye-deu,2,FLoRes-200; FLoRes-101,N/A,N/A,N/A,N/A,N/A
Core Psychological Reasoning,2,Machine_Mindset_MBTI_dataset; AGENT,N/A,N/A,N/A,N/A,N/A
Logical Reasoning Reading Comprehension,2,JustLogic; RWSD,N/A,N/A,N/A,Natural Language Processing,"Logical reasoning reading comprehension is a task proposed by the paper ReClor (ICLR 2020),  which is to evaluate the logical reasoning ability of machine reading comprehension models. ReClor is the f..."
NLP based Person Retrival,2,ICFG-PEDES; Customer Support on Twitter,N/A,N/A,Decoder,Time Series; Natural Language Processing,N/A
Sentence ReWriting,2,MuDoCo_QueryRewrite; PoliteRewrite,N/A,N/A,N/A,N/A,N/A
Multilingual text classification,2,MultiEURLEX; Belebele,N/A,N/A,N/A,Miscellaneous,N/A
Knowledge Base Completion,2,Aristo-v4; ZeroKBC,N/A,N/A,N/A,N/A,N/A
Code Comment Generation,2,PyTorrent; notebookcdg,DeepCom,Smoothed BLEU-4,N/A,Computer Code,N/A
General Knowledge,2,BEAR-probe; BIG-bench,BIG-bench,Accuracy,Sports Understanding; Global Facts; TriviaQA; Natural Questions; Similarities Abstraction,Miscellaneous,This task aims to evaluate the ability of a model to answer general-knowledge questions.    Source: [BIG-bench](https://github.com/google/BIG-bench/tree/main/bigbench/benchmark_tasks/general_knowledge...
Logical Fallacies,2,BIG-bench; JustLogic,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Implicit Discourse Relation Classification,2,DISRPT2021; Dissonance Twitter Dataset,N/A,N/A,N/A,N/A,N/A
single catogory classification,2,Adult; CVR,N/A,N/A,N/A,N/A,N/A
Behavioral Malware Classification,2,BODMAS; AutoRobust,N/A,N/A,N/A,N/A,N/A
Job classification,2,Kompetencer; Twitter job title prediction,N/A,N/A,N/A,Natural Language Processing,N/A
Vocal technique classification,2,VocalSet; GTSinger,N/A,N/A,N/A,N/A,N/A
Sentence Ordering,2,OrdinalDataset; EconLogicQA,EconLogicQA,Accuracy,N/A,Natural Language Processing,Sentence ordering task deals with finding the correct order of sentences given a randomly ordered paragraph.
Multilingual Machine Comprehension in English Hindi,2,Belebele; Visiting Card | ID Card Images | Hindi-English,Extended XQuAD,F1 (QE-PH); EM(QE-PH); EM(QH-PE); F1 (QE-PE); EM(QH-PH); F1(QH-PE); F1(QH-PH); EM(QE-PE),N/A,Natural Language Processing,"Multilingual Machine Comprehension (MMC) is a Question-Answering (QA) sub-task that involves quoting the answer for a question from a given snippet, where the question and the snippet can be in differ..."
Point Cloud Classification,2,JetClass; PointCloud-C,PointCloud-C; ISPRS,mean Corruption Error (mCE); Average F1,Few-Shot Point Cloud Classification; Jet Tagging,Computer Vision; Graphs,Point Cloud Classification is a task involving the classification of unordered 3D point sets (point clouds).
Text based Person Search,2,TVPReid; RSTPReid,N/A,N/A,N/A,N/A,N/A
Translation rus-fra,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation ben-fra,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation ben-por,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation ben-spa,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation hau-fra,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation hin-por,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation hin-spa,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation ind-spa,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation mar-fra,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation mar-por,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation mar-spa,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation por-eng,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation prs-eng,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation prs-fra,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation prs-por,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Translation prs-spa,2,FLoRes-200; TICO-19,N/A,N/A,N/A,N/A,N/A
Lung Sound Classification,2,Chest wall lung sound dataset; ICBHI Respiratory Sound Database,ICBHI Respiratory Sound Database,Accurcay ,N/A,Audio,ICBHI-2017 dataset based lung sound classification
Cancer-no cancer per breast classification,2,InBreast; CBIS-DDSM,N/A,N/A,N/A,N/A,N/A
Document-level RE with incomplete labeling,2,ChemDisGene; Re-DocRED,N/A,N/A,N/A,N/A,N/A
Generalized Few-Shot Classification,2,FGSCM-52; Geoclidean-Constraints,N/A,N/A,Long-tail Learning,Methodology,N/A
Zero-Shot Machine Translation,2,GATITOS; Multi Lingual Bug Reports,N/A,N/A,N/A,Natural Language Processing,Translate text or speech from one language to another without supervision.
Conversational Search,2,ICConv; BeNYfits,N/A,N/A,N/A,Natural Language Processing,N/A
Genome Understanding,2,GUE; GenoAdv,N/A,N/A,N/A,Medical,N/A
Library-Oriented Code Generation,2,CodeGen4Libs Dataset; RES-Q,N/A,N/A,N/A,N/A,N/A
document understanding,2,PDFVQA; U-DIADS-Bib,N/A,N/A,Line Items Extraction,Computer Vision; Natural Language Processing,"Document understanding involves document classification, layout analysis, information extraction, and DocQA."
Sound Classification,2,InfantMarmosetsVox; Audio de mosquitos Aedes Aegypti,N/A,N/A,N/A,Audio,N/A
Atomic number classification,2,CHILI-3K; CHILI-100K,CHILI-100K; CHILI-3K,F1-score (Weighted),N/A,Graphs,Predict the atomic number of a node in a molecular/material/nanomaterial graph
Crystal system classification,2,CHILI-3K; CHILI-100K,N/A,N/A,N/A,N/A,N/A
Space group classification,2,CHILI-3K; CHILI-100K,N/A,N/A,N/A,N/A,N/A
Distance regression,2,CHILI-3K; CHILI-100K,CHILI-3K; CHILI-100K,MSE ,N/A,Graphs,Prediction of the distance between connected nodes in molecular/material/nanomaterial graphs.
World Knowledge,2,BEAR-probe; MM-Eval,N/A,N/A,N/A,N/A,N/A
Keyphrase Generation,2,Keyphrases CS&Math Russian; EUROPA,N/A,N/A,N/A,N/A,N/A
Persian Sentiment Analysis,2,Persian NLP Hub; Perfume Co-Preference Network,N/A,N/A,Transition-Based Dependency Parsing,Natural Language Processing,Persian Sentiment analysis is the task of classifying the polarity of a given text.
Open-Ended Question Answering,2,WorldCuisines; SF20K,N/A,N/A,N/A,N/A,N/A
text-to-Cypher,2,CypherBench; SpCQL,N/A,N/A,N/A,N/A,N/A
Zero-Shot Multi-Speaker TTS,2,Emilia Dataset; ArVoice,N/A,N/A,N/A,N/A,N/A
POS Tagging,2,Twitter POS; WSJ POS,Twitter POS; WSJ POS,Accuracy,N/A,Natural Language Processing,Part of Speech Tagging
Patent classification,2,DAPFAM; IMPACT Patent,N/A,N/A,N/A,N/A,N/A
Drug ATC Classification,2,ATC-SMILES; ATC-GRAPH,ATC-SMILES; ATC-GRAPH,Accuracy; Aiming; Absolute True; Coverage; Absolute False,N/A,Medical,Identification of compounds into the anatomical therapeutic chemical (ATC) system is crucial for drug development and basic research. It has been researched for over a decade since its initial proposa...
2-task Classification,2,TeleSim; [[full-refund]]How do I get a full refund from Expedia?,N/A,N/A,Topological Data Analysis,Methodology,N/A
Continuously Indexed Domain Adaptation,1,MNIST,N/A,N/A,N/A,N/A,N/A
candy animation generation,1,MNIST,N/A,N/A,N/A,N/A,N/A
text-to-speech,1,MNIST,N/A,N/A,N/A,N/A,N/A
Unsupervised Dependency Parsing,1,Penn Treebank,N/A,N/A,N/A,N/A,N/A
SQL-to-Text,1,WikiSQL,WikiSQL,BLEU-4,N/A,Computer Code,"<span style=""color:grey; opacity: 0.6"">( Image credit: [SQL-to-Text Generation with Graph-to-Sequence Model](https://arxiv.org/pdf/1809.05255v2.pdf) )</span>"
Sql Chatbots,1,WikiSQL,N/A,N/A,N/A,Computer Code,N/A
Error Understanding,1,CUB-200-2011,CUB-200-2011 (ResNet-101); CUB-200-2011,Average highest confidence (MobileNetV2); Insertion AUC score (MobileNetV2); Insertion AUC score (EfficientNetV2-M); Insertion AUC score (ResNet-101); Average highest confidence (EfficientNetV2-M); Average highest confidence (ResNet-101); Average highest confidence; Insertion AUC score,N/A,Reasoning,Discover what causes the model’s prediction errors.
TREC 2019 Passage Ranking,1,MS MARCO,N/A,N/A,N/A,N/A,N/A
Passage Ranking,1,MS MARCO,MS MARCO,MRR@10,N/A,Natural Language Processing,N/A
Temporal Sentence Grounding,1,Charades-STA,Ego4D-Goalstep; Charades-STA,"R1@0.5; R@1,IoU=0.3; R@1,IoU=0.5; R@5,IoU=0.3; R@5,IoU=0.5; R1@0.7; R5@0.5; R5@0.7",N/A,Computer Vision,"Temporal sentence grounding (TSG) aims to locate a specific moment from an untrimmed video with a given natural language query. For this task, different levels of supervision are used. 1) Weak supervi..."
Self-Supervised Audio Classification,1,ESC-50,N/A,N/A,N/A,N/A,N/A
Zero-Shot Environment Sound Classification,1,ESC-50,N/A,N/A,N/A,N/A,N/A
Paraphrase Identification within Bi-Encoder,1,Quora Question Pairs,N/A,N/A,N/A,N/A,N/A
Cross-Domain Document Classification,1,Yelp,N/A,N/A,N/A,N/A,N/A
Multi-Media Recommendation,1,MovieLens,N/A,N/A,N/A,N/A,N/A
Recommendation Systems (Item cold-start),1,MovieLens,N/A,N/A,N/A,N/A,N/A
Sparse Representation-based Classification,1,SVHN,N/A,N/A,N/A,N/A,N/A
Concept-To-Text Generation,1,COCO Captions,N/A,N/A,N/A,N/A,N/A
Few-shot Age Estimation,1,MORPH,N/A,N/A,N/A,N/A,N/A
Unsupervised Domain Adaptationn,1,Office-31,N/A,N/A,N/A,N/A,N/A
cross-domain few-shot learning,1,Office-Home,N/A,N/A,N/A,N/A,N/A
KB-to-Language Generation,1,Wikipedia Person and Animal Dataset,N/A,N/A,N/A,N/A,N/A
Memex Question Answering,1,MemexQA,MemexQA,Accuracy,N/A,Natural Language Processing,"Question answering with real-world multi-modal personal collections, e.g., photo albums with visual, text, time and location information."
Single-Source Domain Generalization,1,PACS,PACS; Digits-five,Accuracy,Photo to Rest Generalization,Computer Vision,In this task a model is trained in a single source domain and then it is tested in a number of target domains
Thoracic Disease Classification,1,ChestX-ray14,N/A,N/A,N/A,N/A,N/A
Pulmonary Artery–Vein Classification,1,SunYs,N/A,N/A,N/A,N/A,N/A
Lung Nodule Classification,1,LIDC-IDRI,LIDC-IDRI,Accuracy(10-fold); Accuracy; F1 Score; AUC; Recall/ Sensitivity; Acc; Precision,Lung Nodule 3D Classification,Computer Vision; Medical,N/A
Question Similarity,1,LCQMC,Q2Q Arabic Benchmark,F1 score,Medical question pair similarity computation,Natural Language Processing,"This is the problem of detecting duplicate questions in forums, which is an important step towards automating the process of answering new questions"
Chinese Document Classification,1,THUCNews,N/A,N/A,N/A,N/A,N/A
Formality Style Transfer,1,GYAFC,N/A,N/A,N/A,N/A,N/A
Graph Ranking,1,ZINC,ogbg-mollipo; ZINC; ogbg-molfreesolv; ogbg-molesol,Kendall's Tau,N/A,Graphs,N/A
Sentence Similarity,1,BIOSSES,N/A,N/A,N/A,N/A,N/A
LWR Classification,1,PhyAAt,N/A,N/A,N/A,N/A,N/A
Pill Classification (Both Sides),1,ePillID,N/A,N/A,N/A,N/A,N/A
Cross-lingual zero-shot dependency parsing,1,Universal Dependencies,N/A,N/A,N/A,N/A,N/A
Translation fra-deu,1,Multi30K,N/A,N/A,N/A,N/A,N/A
Entity Cross-Document Coreference Resolution,1,ECB+,N/A,N/A,N/A,N/A,N/A
Document Dating,1,New York Times Annotated Corpus,N/A,N/A,N/A,N/A,N/A
Mitigating Contextual Bias,1,FGVC-Aircraft,N/A,N/A,N/A,N/A,N/A
Semi-supervised Domain Adaptation,1,VisDA-2017,N/A,N/A,N/A,N/A,N/A
Cross-Document Language Modeling,1,Multi-News,N/A,N/A,N/A,N/A,N/A
Cross-Lingual Bitext Mining,1,BUCC,BUCC Chinese-to-English; BUCC Russian-to-English; BUCC French-to-English; BUCC German-to-English,F1 score,N/A,Natural Language Processing,Cross-lingual bitext mining is the task of mining sentence pairs that are translations of each other from large text corpora.
Graph Question Answering,1,GQA,GQA,Accuracy,N/A,Graphs,N/A
Pitch Classification,1,NSynth,N/A,N/A,N/A,N/A,N/A
Cross-Lingual ASR,1,Common Voice,N/A,N/A,N/A,N/A,N/A
Speech-to-Text,1,Common Voice,N/A,N/A,N/A,N/A,N/A
UCCA Parsing,1,CoNLL,N/A,N/A,N/A,N/A,N/A
Open Knowledge Graph Embedding,1,OLPBENCH,N/A,N/A,N/A,N/A,N/A
Legal Document Summarization,1,BillSum,N/A,N/A,N/A,N/A,N/A
Unsupervised Text Summarization,1,CORD-19,N/A,N/A,N/A,N/A,N/A
Text Infilling,1,WIQA,N/A,N/A,N/A,N/A,N/A
Procedural Text Understanding,1,ProPara,N/A,N/A,N/A,N/A,N/A
Natural Language Inference (Zero-Shot),1,OCNLI,N/A,N/A,N/A,N/A,N/A
Natural Language Inference (One-Shot),1,OCNLI,N/A,N/A,N/A,N/A,N/A
Translation afr-nld,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation deu-nld,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ltz-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ltz-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ltz-nld,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation nld-afr,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation nld-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation fry-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation fry-nld,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hrx-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hrx-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation nds-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation nds-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation nds-nld,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation nld-fry,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation nld-nds,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation gos-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation gos-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation bel-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation bel-fra,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation bos_Latn-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hbs-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation lav-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation slv-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation oci-fra,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation bul-fra,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation slv-fra,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hbs-fra,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hbs-spa,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation slv-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hbs-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation eus-spa,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation deu-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation eng-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation awa-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation epo-fra,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation jpn-fra,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation kaz-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation fas-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation msa-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation msa-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ara-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation cmn_Hans-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation cmn_Hans-fra,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation cmn_Hans-por,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation cmn_Hans-spa,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation cmn_Hant-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation cmn_Hant-fra,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation cmn_Hant-por,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation cmn_Hant-spa,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation dsb-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation hsb-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation ido_Latn-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation kaz_Cyrl-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation lfn_Latn-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation lfn_Latn-eng,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation lfn_Latn-por,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation nor-deu,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Translation por-por,1,Tatoeba,N/A,N/A,N/A,N/A,N/A
Micro-expression Generation,1,CASME II,N/A,N/A,Micro-expression Generation (MEGC2021),Computer Vision,N/A
Wildly Unsupervised Domain Adaptation,1,Amazon Product Data,N/A,N/A,N/A,N/A,N/A
Common Sense Reasoning (Few-Shot),1,C3,N/A,N/A,N/A,N/A,N/A
Common Sense Reasoning (One-Shot),1,C3,N/A,N/A,N/A,N/A,N/A
multi-word expression embedding,1,COS960,N/A,N/A,N/A,Natural Language Processing,Learn embeddings for multi-word expressions
Multi Class Text Classification,1,CARER,N/A,N/A,N/A,N/A,N/A
Coherence Evaluation,1,GCDC,N/A,N/A,N/A,N/A,N/A
Controllable Grasp Generation,1,GRAB,N/A,N/A,N/A,N/A,N/A
Conversation Disentanglement,1,irc-disentanglement,N/A,N/A,N/A,N/A,N/A
Traffic Classification,1,LEAF Benchmark,N/A,N/A,N/A,Miscellaneous,"**Traffic Classification** is a task of categorizing traffic flows into application-aware classes such as chats, streaming, VoIP, etc. Classification can be used for several purposes including policy ..."
Fashion Understanding,1,ModaNet,ModaNet Dev,AP (Detection),Semi-Supervised Fashion Compatibility,Computer Vision,N/A
Complex Word Identification,1,MWE-CWI,N/A,N/A,N/A,Natural Language Processing,Identifying difficult words or expressions in a text.
Audio Question Answering,1,RoadTracer,N/A,N/A,N/A,N/A,N/A
Point cloud classification dataset,1,RobustPointSet,N/A,N/A,N/A,Computer Vision,N/A
dialogue summary,1,SAMSum,N/A,N/A,N/A,N/A,N/A
Chinese Landscape Painting Generation,1,Traditional Chinese Landscape Painting Dataset,N/A,N/A,N/A,N/A,N/A
Text Complexity Assessment (GermEval 2022),1,TextComplexityDE,TextComplexityDE,RMSE,N/A,N/A,The tasks is to predict the complexity of pieces of text for a German learner in a range from 1 to 7.
Lip password classification,1,MIRACL-VC1,N/A,N/A,N/A,N/A,N/A
Open Knowledge Graph Canonicalization,1,ReVerb45K,N/A,N/A,N/A,N/A,N/A
Component Classification,1,CDCP,N/A,N/A,N/A,N/A,N/A
Poem meters classification,1,PCD,PCD,Accuracy,N/A,Natural Language Processing,N/A
Multi-Domain Sentiment Classification,1,RETWEET,N/A,N/A,N/A,N/A,N/A
Tweet-Reply Sentiment Analysis,1,RETWEET,N/A,N/A,N/A,N/A,N/A
Word-level pronunciation scoring,1,speechocean762,N/A,N/A,N/A,N/A,N/A
Zero Shot on BEIR (Inference Free Model),1,BEIR,N/A,N/A,N/A,N/A,N/A
EEG Signal Classification,1,Gun Detection Dataset,N/A,N/A,N/A,N/A,N/A
Keyword Spotting CSS,1,FKD,N/A,N/A,N/A,N/A,N/A
Scale Generalisation,1,MNIST Large Scale dataset,N/A,N/A,N/A,N/A,N/A
Morphological Disambiguation,1,NEMO-Corpus,N/A,N/A,N/A,Natural Language Processing,N/A
Translation oci-eng,1,FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation eng-oci,1,FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation eng-ell,1,FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation bul-por,1,FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation dan-por,1,FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation fas-spa,1,FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation cym-fra,1,FLoRes-101,N/A,N/A,N/A,N/A,N/A
Translation zho-eng,1,FLoRes-101,N/A,N/A,N/A,N/A,N/A
IFC Entity Classification,1,IFCNet,IFCNetCore,F1 Score; Balanced Accuracy,N/A,Computer Vision,N/A
Handwritten Word Generation,1,HKR,N/A,N/A,N/A,N/A,N/A
Multimodal Lexical Translation,1,MultiSubs,N/A,N/A,N/A,N/A,N/A
TFLM sequence generation,1,TLFM dataset,N/A,N/A,N/A,N/A,N/A
Heartbeat Classification,1,MIT-BIH Arrhythmia Database,N/A,N/A,N/A,N/A,N/A
Email Thread Summarization,1,EmailSum,N/A,N/A,N/A,N/A,N/A
Document AI,1,EPHOIE,EPHOIE,Average F1,document understanding,Computer Vision; Natural Language Processing,N/A
Compositional Generalization (AVG),1,ReaSCAN,N/A,N/A,N/A,N/A,N/A
Czech Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized Czech Wikipedia texts.
Vietnamese Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized Vietnamese Wikipedia texts.
Romanian Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized Romanian Wikipedia texts.
Slovak Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized Slovak Wikipedia texts.
Latvian Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized Latvian Wikipedia texts.
Polish Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,N/A,Addition of diacritics for undiacritized Polish Wikipedia texts.
Irish Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized Irish Wikipedia texts.
Hungarian Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized Hungarian Wikipedia texts.
French Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized French Wikipedia texts.
Turkish Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized Turkish Wikipedia texts.
Spanish Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized Spanish Wikipedia texts.
Croatian Text Diacritization,1,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Multilingual Dataset for Training and Evaluating Diacritics Restoration Systems,Alpha-Word accuracy,N/A,Natural Language Processing,Addition of diacritics for undiacritized Croatian Wikipedia texts.
CodeSearchNet - Java,1,PyTorrent,N/A,N/A,N/A,N/A,N/A
Audio Multiple Target Classification,1,SINGA:PURA,N/A,N/A,N/A,N/A,N/A
Syntax Representation,1,Orchard,N/A,N/A,N/A,Natural Language Processing,N/A
Zero-shot Generalization,1,CALVIN,CALVIN,Avg. sequence length,N/A,Methodology,N/A
Analogical Similarity,1,BIG-bench,BIG-bench,Accuracy,N/A,Reasoning,N/A
Understanding Fables,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Sentence Ambiguity,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
TriviaQA,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Analytic Entailment,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Epistemic Reasoning,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Logical Args,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Clinical Knowledge,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Fantasy Reasoning,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
GRE Reading Comprehension,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Nonsense Words Grammar,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Bridging Anaphora Resolution,1,GUM,N/A,N/A,N/A,N/A,N/A
Blackout Poetry Generation,1,BLP,N/A,N/A,N/A,N/A,N/A
Multlingual Neural Machine Translation,1,CVIT PIB,N/A,N/A,N/A,Natural Language Processing,N/A
Font Style Transfer,1,Dafonts Free,N/A,N/A,N/A,N/A,N/A
Physical Commonsense Reasoning,1,Physical Audiovisual CommonSense,N/A,N/A,N/A,N/A,N/A
Hurtful Sentence Completion,1,HONEST,N/A,N/A,N/A,N/A,N/A
Zero-Shot Intent Classification,1,MASSIVE,N/A,N/A,N/A,N/A,N/A
Zero-Shot Intent Classification and Slot Filling,1,MASSIVE,N/A,N/A,N/A,N/A,N/A
Time-Series Few-Shot Learning with Heterogeneous Channels,1,TimeHetNet,N/A,N/A,N/A,N/A,N/A
Question to Declarative Sentence,1,QA2D,N/A,N/A,N/A,Natural Language Processing,"Question Answer to Declarative Sentence (QA2D) is the task of generating declarative statements from question, answer pairs.    See:  Demszky, D., Guu, K., & Liang, P. (2018). Transforming Question An..."
Skill Generalization,1,RGB-Stacking,RGB-Stacking,Average; Group 3; Group 1; Group 4; Group 2; Group 5,N/A,Robots,Image credit: [A Generalist Agent](https://storage.googleapis.com/deepmind-media/A%20Generalist%20Agent/Generalist%20Agent.pdf)
Semi-supervised time series classification,1,Bosch CNC Machining Dataset,N/A,N/A,N/A,Time Series,N/A
Jet Tagging,1,JetClass,N/A,N/A,N/A,N/A,N/A
Word Spotting In Handwritten Documents,1,BN-HTRd,N/A,N/A,N/A,N/A,N/A
Translation fin-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation nob-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation nno-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ita-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mkd-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ast-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ast-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation glg-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ita-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation oci-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ron-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hrv-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mkd-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mkd-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation slv-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation isl-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation nob-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation nob-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mkd-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lim-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation acm-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation acm-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation acm-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation acm-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation acm-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation amh-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation apc-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation apc-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation apc-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation apc-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation arz-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation arz-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation arz-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation arz-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation arz-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ast-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ast-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation awa-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation awa-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation bho-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation bho-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation bho-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation cat-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ceb-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ces-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ckb-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ckb-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation crh-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation crh-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation crh-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation crh-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation crh-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation cym-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation est-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation est-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation eus-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation eus-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation eus-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation fao-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation fao-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation fao-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation fao-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation fij-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation fur-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation fur-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation fur-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation gla-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation gle-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation gle-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation gle-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation glg-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation glg-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation grn-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation grn-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation grn-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation guj-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation guj-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation guj-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation guj-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hat-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hat-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hat-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hat-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hat-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hin-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hne-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hne-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hne-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hne-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hne-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hye-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hye-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation hye-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ibo-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ilo-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ilo-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ilo-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation ilo-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation jav-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation jav-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation jav-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation jav-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation jav-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kat-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kat-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kat-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kaz-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kaz-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kaz-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kea-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kea-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kea-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kea-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kea-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation kon-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lij-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lij-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lij-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lij-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lim-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lim-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lim-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lin-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lin-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation lin-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mag-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mal-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mar-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mlt-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mlt-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mlt-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation mlt-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation nno-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation npi-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation npi-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation nso-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation nya-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation oci-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pag-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pag-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pag-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pan-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pan-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pan-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pan-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pap-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pap-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pap-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pap-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pap-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pes-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pes-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pes-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation pes-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation plt-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation plt-fra,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation plt-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation plt-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation prs-deu,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation run-por,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Translation run-spa,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
text political leaning classification,1,Article Bias Prediction,N/A,N/A,N/A,N/A,N/A
Zero-Shot Counting,1,FSC147,N/A,N/A,N/A,N/A,N/A
Question-Answer-Generation,1,FrenchMedMCQA,N/A,N/A,N/A,N/A,N/A
Transfer Reinforcement Learning,1,bipedal-skills,N/A,N/A,N/A,Methodology,N/A
Document Level Machine Translation,1,BWB,N/A,N/A,N/A,N/A,N/A
Persona Dialogue in Story,1,Harry Potter Dialogue Dataset,N/A,N/A,N/A,N/A,N/A
Enumerative Search,1,standard atomic contexts,N/A,N/A,N/A,N/A,N/A
Grounded language learning,1,IGLU,N/A,N/A,N/A,N/A,N/A
Stroke Classification,1,kaggle stroke Prediction competition,N/A,N/A,N/A,N/A,N/A
Facial expression generation,1,MH-FED,N/A,N/A,N/A,N/A,N/A
ArzEn Code-switched Translation to eng,1,ArzEn,N/A,N/A,N/A,N/A,N/A
ArzEn Code-switched Translation to ara,1,ArzEn,N/A,N/A,N/A,N/A,N/A
Speech Intent Classification,1,Skit-S2I,N/A,N/A,N/A,N/A,N/A
Label shift of blended-target domain adaptation,1,Office-Home-LMT,N/A,N/A,N/A,N/A,N/A
Zero-shot Sentiment Classification,1,AfriSenti,AfriSenti,weighted-F1 score,N/A,Natural Language Processing,N/A
Steiner Tree Problem,1,PACE 2018 Steiner Tree,N/A,N/A,N/A,Graphs,The **Steiner tree problem** is a computational problem in computer science and graph theory that involves finding the minimum weight subgraph in an undirected graph that connects a given set of termi...
Early  Classification,1,ECG200,ECG200,Accuracy,N/A,Time Series,N/A
Vietnamese Natural Language Inference,1,ViNLI,ViNLI,4-class test accuracy; 3-class test accuracy,N/A,N/A,N/A
Profile Generation,1,PGDataset,N/A,N/A,N/A,N/A,N/A
Generalized Referring Expression Comprehension,1,gRefCOCO,gRefCOCO,"Precision@(F1=1, IoU≥0.5); N-acc.",N/A,Computer Vision,"Generalized Referring Expression Comprehension (GREC) allows expressions indicating any number of target objects. GREC takes an image and a referring expression as input, and requires bounding box(es)..."
Poll Generation,1,WeiboPolls,N/A,N/A,N/A,N/A,N/A
Superclass classification,1,PTB-XL,N/A,N/A,N/A,N/A,N/A
Table Search,1,Pylon Benchmark,N/A,N/A,N/A,N/A,N/A
Pretrained Multilingual Language Models,1,Belebele,N/A,N/A,N/A,Natural Language Processing,N/A
Transferability,1,classification benchmark,classification benchmark,Kendall's Tau,N/A,Time Series,N/A
Translation ara-por,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation fas-por,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation ind-por,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation hau-por,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation hau-spa,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation msa-fra,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation lug-eng,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation msa-por,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation pus-eng,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation pus-fra,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation pus-por,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Translation pus-spa,1,TICO-19,N/A,N/A,N/A,N/A,N/A
Summarization Consistency Evaluation,1,AggreFact,N/A,N/A,N/A,N/A,N/A
open-set classification,1,UCCS,N/A,N/A,N/A,N/A,N/A
Tumour Classification,1,Radio-Freqency Ultrasound volume dataset for pre-clinical liver tumors,N/A,N/A,N/A,Medical,N/A
intent-classification,1,Intent-based user instruction for electric automation,N/A,N/A,N/A,N/A,N/A
Hint Generation,1,TriviaHG,N/A,N/A,N/A,N/A,N/A
Similarity Explanation,1,fruit-SALAD,N/A,N/A,N/A,N/A,N/A
OpenAPI code completion,1,OpenAPI completion refined,OpenAPI completion refined,"Validness, max., %; Correctness, max., %; Correctness, avg., %; Validness, avg., %",N/A,Computer Code,Code Completion in the [OpenAPI](https://spec.openapis.org/oas/latest.html) format which is a widely used machine and human-readable format for describing RESTful APIs in YAML or JSON.    The benchmar...
Edge Classification,1,DTGB,N/A,N/A,N/A,N/A,N/A
Person Identification (zero-shot),1,WiGesture,N/A,N/A,N/A,N/A,N/A
Temporal Complex Logical Reasoning,1,Temporal Logic Video (TLV) Dataset,N/A,N/A,N/A,Knowledge Base,Temporal Complex Logical Reasoning over Temporal Knowledge Graphs
Knowledge-Aware Recommendation,1,SemOpenAlex,N/A,N/A,N/A,N/A,N/A
Artist classification,1,TLF2K,N/A,N/A,N/A,N/A,N/A
Text-to-GQL,1,NL2GQL Dataset,N/A,N/A,N/A,Natural Language Processing,Text-to-QGL is a task in natural language processing (NLP) where the goal is to generate Graph Query Language queries from natural language text automatically.
text-classification,1,RadCases,N/A,N/A,N/A,N/A,N/A
Music Genre Transfer,1,PIAST,N/A,N/A,N/A,N/A,N/A
Text2Sparql,1,SIB bioinformatics SPARQL queries,N/A,N/A,N/A,Knowledge Base,Conversion from natural language questions to SPARQL queries
Raw vs Ripe (Generic),1,RawRipe Dataset,N/A,N/A,N/A,N/A,N/A
Query-focused Summarization,1,SumIPCC,N/A,N/A,N/A,N/A,N/A
Description-guided molecule generation,1,TOMG-Bench,TOMG-Bench,wAcc,N/A,Natural Language Processing,The significance of description-based molecule generation lies in its potential to streamline the process of molecular design by enabling the production of molecules that directly meet the criteria ou...
FS-MEVQA,1,SME,N/A,N/A,N/A,N/A,N/A
Chart Understanding,1,SBS Figures,N/A,N/A,N/A,N/A,N/A
Patent Figure Description Generation,1,PatentDesc-355K,N/A,N/A,N/A,N/A,N/A
Time Series Generation,1,Monopedal Gaits,N/A,N/A,N/A,Time Series,N/A
Text-Variation,1,Urdu Text Scene Images,N/A,N/A,N/A,Natural Language Processing,Generate variations of the input text
Cross-Language Text Summarization,1,M3LS,N/A,N/A,N/A,N/A,N/A
Multimodal Large Language Model,1,Offensive Memes in Singapore Context,N/A,N/A,N/A,Computer Vision,N/A
Small-Footprint Keyword Spotting,1,Arabic Speech Commands Dataset,N/A,N/A,N/A,N/A,N/A
Cancer Classification,1,"Multi-omics mRNA, miRNA, and DNA Methylation Dataset","Multi-omics mRNA, miRNA, and DNA Methylation Dataset",1:1 Accuracy,N/A,Computer Vision,N/A
Asthmatic Lung Sound Classification,1,Chest wall lung sound dataset,Chest wall lung sound dataset,2-Class Accuracy,N/A,N/A,Lung sound asthma classification
Lung Disease Classification,1,Chest wall lung sound dataset,N/A,N/A,N/A,Medical,N/A
Poker Hand Classification,1,Playing Cards,N/A,N/A,N/A,N/A,N/A
Conversational Recommendation,1,Usage-related Questions,N/A,N/A,N/A,N/A,N/A
Unconditional Crystal Generation,1,MP20,MP20,"DFT Stable, Unique, Novel Rate; Validity",N/A,Miscellaneous,"This task evaluates the ability of generative models to sample valid and realistic crystal structures.    The training dataset, MP20 (Xie et al., 2022), contains 45,231 metastable crystal structures f..."
Perceptual Distance,1,BERSt,N/A,N/A,N/A,N/A,N/A
TinyQA Benchmark++,1,TQBA++,tinyqabenchmark_core-en,Exact Match; Exact Macth,N/A,Natural Language Processing,Ultra-lightweight evaluation suite and python package designed to expose critical failures in Large Language Model (LLM) systems within seconds
Reranking,1,Claim Matching Robustness,N/A,N/A,N/A,N/A,N/A
AMR Graph Similarity,1,Benchmark for AMR Metrics based on Overt Objectives,Benchmark for AMR Metrics based on Overt Objectives,Pearson’s ρ (amean); Spearman Correlation,N/A,Natural Language Processing,N/A
Graph-To-Graph Translation,0,N/A,N/A,N/A,N/A,Graphs,N/A
Dialogue,0,N/A,Persona-Chat,BLEU-1; Distinct-2; BLEU-2; Distinct-1,End-To-End Dialogue Modelling; Empathetic Response Generation; Dialogue State Tracking; Goal-Oriented Dialogue Systems; Interactive Evaluation of Dialog,Natural Language Processing; Speech,Dialogue is notoriously hard to evaluate. Past approaches have used human evaluation.
Acoustic Question Answering,0,N/A,N/A,N/A,N/A,Speech,N/A
Classification Of Variable Stars,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Bird Classification,0,N/A,N/A,N/A,Bird Audio Detection; Bird Species Classification With Audio-Visual Data,Audio,N/A
Web Page Tagging,0,N/A,N/A,N/A,N/A,Natural Language Processing,Assigning appropriate tags to a web page.
Collaborative Ranking,0,N/A,N/A,N/A,N/A,Graphs,N/A
Age-Related Macular Degeneration Classification,0,N/A,N/A,N/A,N/A,Medical,N/A
Cross-Lingual,0,N/A,N/A,N/A,Cross-Lingual Transfer; Cross-Lingual Entity Linking; Cross-Lingual Document Classification; Cross-Language Text Summarization,Natural Language Processing,"Cross-lingual natural language processing is the task of using data and models available for one language for which ample such resources are available (e.g., English) to solve tasks in another, common..."
Language Modeling,0,N/A,N/A,N/A,Dream Generation,Natural Language Processing,N/A
Joint NER and Classification,0,N/A,N/A,N/A,N/A,Natural Language Processing,Joint named entity recognition and classification refers to the combined task of identifying named entitites in a given text and text classification.
Anaphora Resolution,0,N/A,N/A,N/A,Abstract Anaphora Resolution; Bridging Anaphora Resolution,Natural Language Processing,Resolving what expression a pronoun or a noun phrase refers to.
Knowledge Graphs Data Curation,0,N/A,N/A,N/A,N/A,Knowledge Base,N/A
Vowel Classification,0,N/A,N/A,N/A,N/A,Audio,N/A
Generalized Zero-Shot Learning - Unseen,0,N/A,N/A,N/A,N/A,Computer Vision,"The average of the normalized top-1 prediction scores of unseen classes in the generalized zero-shot learning setting, where the label of a test sample is predicted among all (seen + unseen) classes."
breast density classification,0,N/A,N/A,N/A,N/A,Medical,N/A
Git Commit Message Generation,0,N/A,CommitGen,BLEU-4,N/A,Computer Code,N/A
Sentence Pair Modeling,0,N/A,N/A,N/A,Semantic Similarity,Natural Language Processing,Comparing two sentences and their relationship based on their internal representation.
Oceanic Eddy Classification,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Sperm Morphology Classification,0,N/A,N/A,N/A,N/A,Computer Vision,Multi-class classification of sperm head morphology.
API Sequence Recommendation,0,N/A,DeepAPI,BLEU-4,N/A,Computer Code,N/A
Sentence Summarization,0,N/A,N/A,N/A,Unsupervised Sentence Summarization,Natural Language Processing,Generating a summary of a given sentence.
House Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Immune Repertoire Classification,0,N/A,N/A,N/A,N/A,Medical,N/A
Leadership Inference,0,N/A,N/A,N/A,N/A,N/A,"Leadership is a process of individuals (leaders) who influence a group to achieve collective goals. In nature, leadership can be viewed as a process of initiation of coordinated activity. In the task ..."
Commonsense Reasoning for RL,0,N/A,commonsense-rl,Avg #Steps,N/A,Reasoning; Natural Language Processing,Commonsense reasoning for Reinforcement Learning agents
Next-basket recommendation,0,N/A,Instacart; TaFeng,nDCG@10; Recall@10,N/A,Miscellaneous,N/A
band gap classification,0,N/A,N/A,N/A,N/A,N/A,N/A
Ticket Search,0,N/A,N/A,N/A,N/A,Methodology,N/A
Temporal Knowledge Graph Completion,0,N/A,N/A,N/A,N/A,Knowledge Base,N/A
Log Parsing,0,N/A,N/A,N/A,N/A,Computer Code,**Log Parsing** is the task of transforming unstructured log data into a structured format that can be used to train machine learning algorithms. The structured log data is then used to identify patte...
Animated GIF Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Shallow Syntax,0,N/A,N/A,N/A,Chunking,Natural Language Processing,N/A
Generalization Bounds,0,N/A,N/A,N/A,N/A,Methodology,N/A
Classification on Time Series with Missing Data,0,N/A,N/A,N/A,N/A,Time Series,N/A
Sequential Bayesian Inference,0,N/A,N/A,N/A,N/A,Time Series,"Also known as Bayesian filtering or [recursive Bayesian estimation](https://en.wikipedia.org/wiki/Recursive_Bayesian_estimation), this task aims to perform inference on latent state-space models."
Chinese Spell Checking,0,N/A,SIGHAN 2015,Correction F1; Detection F1,N/A,Natural Language Processing,Chinese Spell Checking (CSC) aims to detect and correct erroneous characters for user-generated text in Chinese language.
Complaint Comment Classification,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Vietnamese Parsing,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Learning Language specific models,0,N/A,N/A,N/A,N/A,N/A,N/A
Approximating Betweenness-Centrality ranking,0,N/A,N/A,N/A,N/A,Graphs,Betweenness-centrality is a popular measure in network analysis that aims to describe the importance of nodes in a graph. It accounts for the fraction of shortest paths passing through that node and i...
Diachronic Word Embeddings,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Subdomain adaptation,0,N/A,N/A,N/A,N/A,Methodology,N/A
Text Anonymization,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Cross-Lingual Word Embeddings,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Geographic Question Answering,0,N/A,N/A,N/A,N/A,N/A,"Geographic Question Answering ( by way of IR, NLP, and/or Knowledge base apply)"
Human Judgment Classification,0,N/A,Pascal-50S,Mean Accuracy,N/A,Reasoning,A task where an algorithm judges which sample is better in accordance with human judgment.
Phenotype classification,0,N/A,"MIMIC-CXR, MIMIC-IV",AUROC,N/A,Medical,N/A
Hindu Knowledge,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Edit script generation,0,N/A,N/A,N/A,N/A,Computer Code,"Generating edit scripts by comparing 2 different files or strings to convert one to another. this script will contain instruction like insert, delete and substitute."
energy management,0,N/A,N/A,N/A,energy trading,Time Series,"energy management is to schedule energy units inside the systems, enabling an reliable, safe and cost-effective operation"
Wireframe Parsing,0,N/A,N/A,N/A,N/A,Computer Vision,Detect Line Segments and their connecting Junctions in a single perspective image.
Phonocardiogram Classification,0,N/A,N/A,N/A,Classify murmurs,Time Series,Classify labels/murmur/clinical outcome based on Phonocardiograms (PCGs)
Community Search,0,N/A,N/A,N/A,N/A,Graphs,N/A
Prompt-driven Zero-shot Domain Adaptation,0,N/A,N/A,N/A,N/A,Computer Vision,Domain adaptation using only a single source domain and a description of the target domain in natural language (No images from target domain are available)
text-guided-generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Readability optimization,0,N/A,N/A,N/A,N/A,Natural Language Processing,It consists of improving the readability of a text automatically and without significantly altering the form or meaning.
Comment Generation,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Article commenting poses new challenges for machines, as it involves multiple cognitive abilities: understanding the given article, formulating opinions and arguments, and organizing natu ral language..."
Coding Problem Tagging,0,N/A,N/A,N/A,N/A,Natural Language Processing,Assigning data structures to coding problems
Legal Reasoning,0,N/A,LegalBench (Rule-recall); LegalBench (Issue-spotting),Balanced Accuracy,N/A,Natural Language Processing,N/A
Potrait Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Voice Similarity,0,N/A,N/A,N/A,N/A,Speech,N/A
Change Data Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Semi-Supervised Domain Generalization,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
trustable and focussed LLM generated content,0,N/A,N/A,N/A,Game Design,Playing Games; Natural Language Processing,ensure that LLM step-by-step generation stays truthful and focussed to the user's goal
Topological Risk Measure,0,N/A,N/A,N/A,N/A,Time Series,N/A
Quantum Circuit Generation,0,N/A,N/A,N/A,N/A,Time Series,Generation of quantum circuits (classic ML) using generative models.
Reference Expression Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Personality Generation,0,N/A,N/A,N/A,Personality Alignment,Natural Language Processing,"The Personality Generation Task involves using machine learning models to generate text or recommendations tailored to different personality types. It aims to create content, suggestions, or responses..."
Ordinal Classification,0,N/A,OASIS+NACC+ICBM+ABIDE+IXI,Mean absolute error,N/A,Methodology,N/A
In-Context Learning,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Sentence,0,N/A,N/A,N/A,N/A,Playing Games,N/A
Generative Temporal Nursing,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Vietnamese Language Models,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Vietnamese Natural Language Understanding,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Simultaneous Speech-to-Speech Translation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Adversarial Natural Language Inference,0,N/A,N/A,N/A,N/A,N/A,N/A
Vietnamese Sentiment Analysis,0,N/A,N/A,N/A,Vietnamese Multimodal Sentiment Analysis,Natural Language Processing,N/A
Runtime ranking,0,N/A,TpuGraphs Layout mean,Kendall's Tau,N/A,Computer Code,"Ranking of computational graph configurations by their runtime. The task assumes a set of computational graphs, each having a set of node and/or edge configurations and corresponding scalar execution ..."
Joint Multilingual Sentence Representations,0,N/A,N/A,N/A,Abstract Meaning Representation,Natural Language Processing,N/A
GRAPH DOMAIN ADAPTATION,0,N/A,FRANKENSTEIN,Accuracy,N/A,Graphs,N/A
Conversational Information Access,0,N/A,N/A,N/A,N/A,N/A,"Provide access to information via a conversation. It includes conversational search, conversational recommendation, and question-answering."
Script Generation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Urban Itinerary Planning,0,N/A,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Text Regression,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Multimodal Music Generation,0,N/A,N/A,N/A,N/A,Music,N/A
Dataset Generation,0,N/A,N/A,N/A,N/A,Robots,The task involves enhancing the training of target application (e.g. autonomous driving systems) by generating datasets of diverse and critical elements (e.g. traffic scenarios). Traditional methods r...
MUlTI-LABEL-ClASSIFICATION,0,N/A,N/A,N/A,N/A,Graphs,multilabel graph classification with highest result
NeRF,0,N/A,N/A,N/A,N/A,N/A,N/A
Layout Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Small Language Model,0,N/A,N/A,N/A,N/A,N/A,N/A
Appearance Transfer,0,N/A,N/A,N/A,N/A,Computer Vision,Transfer of the appearance from one object to another.
Language Model Evaluation,0,N/A,N/A,N/A,N/A,Computer Vision,The task of using LLMs as evaluators of large language and vision language models.
Text Normalization,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
