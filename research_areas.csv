Research Area,Task Count,Example Tasks
Computer Vision,865,Optical Character Recognition (OCR); Deblurring; Foveation; Trajectory Prediction; Depth Estimation; Physical Attribute Prediction; Super-Resolution; Cancer; Reconstruction; Facial Recognition and Modelling; Autonomous Vehicles; Image Manipulation Detection; Image-to-Image Translation; Sketch; Domain Adaptation...
Natural Language Processing,436,Optical Character Recognition (OCR); Link Prediction; Word Alignment; Part-Of-Speech Tagging; Data Integration; Dialogue; Arabic Sentiment Analysis; Text-To-Speech Synthesis; Relation Extraction; Hypernym Discovery; Data Mining; Machine Translation; Image Generation; Reading Comprehension; Few-Shot Learning...
Miscellaneous,219,Crime Prediction; Facial Recognition and Modelling; Learning-To-Rank; Stochastic Optimization; Link Quality Estimation; Image Generation; Open Set Learning; Anomaly Detection; Deception Detection; Transfer Learning; Question Answering; Gender Bias Detection; Knowledge Tracing; Language Modelling; Ecommerce...
Medical,190,Malaria Risk Exposure Prediction; Diabetes Prediction; Medical Code Prediction; Single-cell modeling; Electroencephalogram (EEG); X-Ray; Cancer; Medical Image Segmentation; Image Generation; Disease Prediction; Length-of-Stay prediction; Sleep Quality; Electromyography (EMG); Language Modelling; Drug Discovery...
Methodology,157,Optical Character Recognition (OCR); Structured Prediction; Electroencephalogram (EEG); Domain Adaptation; Stochastic Optimization; Data Mining; 3D Reconstruction; Efficient Exploration; Anomaly Detection; Few-Shot Learning; Zero-Shot Learning; Transfer Learning; Decision Making; Dictionary Learning; Matrix Completion...
Time Series,98,Trajectory Prediction; Stock Market Prediction; Electroencephalogram (EEG); Imputation; Video Quality Assessment; Time Series Classification; Time Series Prediction; Clustering Multivariate Time Series; Time Series Forecasting; Activity Recognition; Action Detection; Non-Intrusive Load Monitoring; Eeg Decoding; Spatio-Temporal Forecasting; Traffic Prediction...
Graphs,87,Link Prediction; graph construction; Super-Resolution; Graph-To-Graph Translation; Learning-To-Rank; Anomaly Detection; Knowledge Graphs; Outlier Detection; Gene Interaction Prediction; Topological Data Analysis; Collaborative Ranking; Community Detection; Graph Matching; Graph Ranking; Graph Embedding...
Audio,69,Speech Recognition; Text-To-Speech Synthesis; Instance Search; Few-Shot Learning; Emotion Recognition; Audio Classification; Environmental Sound Classification; Acoustic Scene Classification; Language Identification; Sound Event Detection; Bird Classification; Audio Tagging; Audio Signal Recognition; Audio-Visual Synchronization; Music Generation...
Computer Code,61,Autonomous Vehicles; Data Mining; Write Computer Programs From Specifications; Text Generation; Code Generation; Semantic Segmentation; Load Forecasting; Program Synthesis; Source Code Summarization; Sql Chatbots; Code Search; Learning to Execute; Text-To-SQL; SQL-to-Text; Git Commit Message Generation...
Robots,56,Autonomous Vehicles; Activity Recognition; Visual Odometry; Robotic Grasping; Robot Task Planning; Semantic Segmentation; Continuous Control; Marine Robot Navigation; Robot Navigation; Visual Navigation; Deformable Object Manipulation; Optimal Motion Planning; Benchmarking; Safe Exploration; Autonomous Navigation...
Knowledge Base,50,Cancer; Data Integration; Knowledge Graphs; Entity Alignment; Non-Intrusive Load Monitoring; Text Summarization; Recommendation Systems; Causal Inference; 3D; Knowledge Graphs Data Curation; Knowledge Base Population; Causal Discovery; RDF Dataset Discovery; Probabilistic Deep Learning; Knowledge Graph Completion...
Reasoning,50,Reconstruction; Question Answering; Video Question Answering; Decision Making; Code Generation; Visual Reasoning; Common Sense Reasoning; Natural Language Inference; Natural Language Visual Grounding; Robot Task Planning; Causal Identification; Program Synthesis; Multi-Label Learning; Multi-Label Classification; Abstract Argumentation...
Speech,48,Speech Recognition; Dialogue; Text-To-Speech Synthesis; Keyword Spotting; Acoustic Question Answering; Text Generation; Speech Separation; Spoken Language Understanding; Spoken Dialogue Systems; Speaker Identification; Speaker Diarization; Speaker Recognition; Speaker Separation; Text-Independent Speaker Recognition; Speaker Verification...
Playing Games,40,Clickbait Detection; Continuous Control; Multi-Agent Path Finding; Atari Games; Video Games; Game of Football; 3D; Game of Chess; Game of Go; Game of Shogi; Multi-agent Reinforcement Learning; FPS Games; Game of Poker; Board Games; Card Games...
Music,32,Facial Recognition and Modelling; Music Auto-Tagging; Music Source Separation; Detection Of Instrumentals Musical Tracks; Recognizing Seven Different Dastgahs Of Iranian Classical Music; Melody Extraction; Music Classification; Music Information Retrieval; Music Generation; Music Emotion Recognition; Drum Transcription; Piano Music Modeling; Audio Generation; 3D; Music Genre Recognition...
Adversarial,31,Text Generation; Adversarial Text; Adversarial Attack; Adversarial Defense; Inference Attack; Image Classification; Neural Network Security; Data Poisoning; Website Fingerprinting Attacks; Fairness; Federated Learning; Model extraction; Website Fingerprinting Defense; Design Synthesis; 2D Semantic Segmentation...
