Task Name,Dataset Count,Common Datasets (Top 10),Benchmarks,SOTA Metrics,Subtasks,Categories,Description (First 200 chars)
Speech Separation,16,WHAMR!; LibriCSS; TIMIT; LibriMix; REAL-M; GRID Dataset; LRS2; WSJ0-2mix; DiPCo; VoxCeleb2,GRID corpus (mixed-speech); WHAM!; Libri15Mix; TCD-TIMIT corpus (mixed-speech); Libri2Mix; LRS3; WSJ0-5mix; VoxCeleb2; WSJ0-4mix; LibriCSS,40%; Number of parameters (M); MACs (G); 30%; 10%; NSDR; STOI; SI-SNRi; PESQ; 20%,Speech Extraction,Speech,"The task of extracting all overlapping speech sources in a given mixed speech signal refers to the **Speech Separation**. Speech Separation is a special scenario of source separation problem, where th..."
Audio Source Separation,15,WHAMR!; FUSS; LibriMix; MedleyVox; AudioSet; SMS-WSJ; WSJ0-2mix; <PERSON><PERSON>; MUSDB18; OpenMIC-2018,AudioSet; MUSIC (multi-source),SDR; SAR; SIR,Single-Label Target Sound Extraction; Target Sound Extraction; Directional Hearing,Audio,"**Audio Source Separation** is the process of separating a mixture (e.g. a pop band recording) into isolated sounds from individual sources (e.g. just the lead vocals).      <span class=""description-s..."
Music Transcription,14,Slakh2100; URMP; Cadenza Woodwind; MusicNet; MAESTRO; ErhuPT; CocoChorales; Guitar-TECHS; ASAP; Music21,Slakh2100; URMP; SMD Piano; MusicNet; MAESTRO; MAPS,note-level F-measure-no-offset (Fno); Onset F1; Number of params; APS,Multi-instrument Music Transcription,Music,"Music transcription is the task of converting an acoustic musical signal into some form of music notation.    <span style=""color:grey; opacity: 0.6"">( Image credit: [ISMIR 2015 Tutorial - Automatic Mu..."
Speaker Verification,12,VoxCeleb1; CN-CELEB; VibraVox (soft in-ear microphone); VibraVox (throat microphone); EVI; VibraVox (headset microphone); VibraVox (temple vibration pickup); VibraVox (rigid in-ear microphone); VibraVox (forehead accelerometer); VoxCeleb2,CALLHOME; CN-CELEB; VibraVox (soft in-ear microphone); VoxCeleb1; VibraVox (throat microphone); VibraVox (headset microphone); ASVspoof 2019 - LA; VibraVox (temple vibration pickup); VibraVox (rigid in-ear microphone); VibraVox (forehead accelerometer),Test EER; EER; minDCF; Test min-DCF; Cosine EER,Text-Independent Speaker Verification; Text-Dependent Speaker Verification; Audio Deepfake Detection,Speech,"Speaker verification is the verifying the identity of a person from characteristics of the voice.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Contrastive-Predictive-Coding-PyTorch  ](ht..."
Speaker Diarization,11,AliMeeting; FSC-P2; RadioTalk; AVA-ActiveSpeaker; DIHARD II; ASR-RAMC-BIGCCSC: A CHINESE CONVERSATIONAL SPEECH CORPUS; AVA-Speech; CHiME-5; CALLHOME American English Speech; AVA,NIST-SRE 2000; DIHARD3-eval; CALLHOME; ETAPE; AMI Lapel; DIHARD; AliMeeting; Hub5'00 CallHome; DIHARD II; AMI,DER(%); DER(ig olp); MI; V; FA; CF; DER - no overlap; Miss,N/A,Speech,"**Speaker Diarization** is the task of segmenting and co-indexing audio recordings by speaker. The way the task is commonly defined, the goal is not to identify known speakers, but to co-index segment..."
Music Source Separation,9,Slakh2100; Cadenza Woodwind; MedleyDB; MuseScore; SynthSOD; MUSDB18; MUSDB18-HQ; CocoChorales; MIR-1K,MUSDB18-HQ; MUSDB18; Slakh2100,SI-SDRi (Bass); Si-SDRi (Drums); SDR (others); SDR (avg); SDR (bass); SDR (drums); SDR (vocals); Si-SDRi (Piano); Si-SDRi (Guitar); SDR (other),N/A,Music,"Music source separation is the task of decomposing music into its constitutive components, e. g., yielding separated stems for the vocals, bass, and drums.    <span style=""color:grey; opacity: 0.6"">( ..."
Voice Conversion,8,LibriSpeech; VESUS; VIVOS; GneutralSpeech Male; ArVoice; ESD; GneutralSpeech Female; VCTK,LibriSpeech test-clean; VCTK; ZeroSpeech 2019 English,Word Error Rate (WER); Character Error Rate (CER); Equal Error Rate; Phone Length Error (PLE); Speaker Similarity; Word Length Error (WLE); Total Length Error (TLE),N/A,Audio; Speech,I remember all the summer days  Drinking wine in the sunshine  I hope it never leaves  And I remember all the summer nights  Staring at you in the moonlight  I hope you never leave 'cause baby  You're...
Music Modeling,6,Nottingham; Lakh MIDI Dataset; JSB Chorales; Lakh Pianoroll Dataset; Music21; JS Fake Chorales,Nottingham; JSB Chorales,Parameters; NLL,N/A,Music,"<span style=""color:grey; opacity: 0.6"">( Image credit: [R-Transformer](https://arxiv.org/pdf/1907.05572v1.pdf) )</span>"
Speech Dereverberation,6,WHAMR!; EARS-Reverb; WHAMR_ext; WHAM!; Reverb-WSJ0; DNS Challenge,N/A,N/A,N/A,N/A,N/A
Multi-instrument Music Transcription,6,Slakh2100; URMP; MIR-ST500; Cadenza Woodwind; ENST Drums; YourMT3 Dataset,N/A,N/A,N/A,N/A,N/A
Voice Anti-spoofing,6,PartialSpoof; ReMASC; ASVspoof 2019; ASVspoof 5; PartialSpoof_v1; Laser Data,ASVspoof2019; ASVspoof 2019 - LA; ASVspoof 2019 - PA,min t-dcf; min a-DCF; EER; minDCF,N/A,Audio,Discriminate genuine speech and spoofing attacks
Audio captioning,5,LongVALE; WavCaps; AudioCaps; Clotho; MACS,Clotho; AudioCaps,ROUGE; ROUGE-L; FENSE; METEOR; SPIDEr; CIDEr; #params (M); SPICE; BLEU-4; SPIDEr-FL,Retrieval-augmented Few-shot In-context Audio Captioning; Zero-shot Audio Captioning,Audio,"Audio Captioning is the task of describing audio using text. The general approach is to use an audio encoder to encode the audio (example: PANN, CAV-MAE), and to use a decoder (example: transformer) t..."
Music Captioning,5,MidiCaps; YouTube8M-MusicTextClips; MusicCaps; Song Describer Dataset; JamendoMaxCaps,N/A,N/A,N/A,Music,N/A
Speaker Identification,4,EVI; VoxCeleb1; FSC-P2; CSI,EVI en-GB; EVI pl-PL; EVI fr-FR; VoxCeleb1,Top-1 (%); Top-5 (%); Accuracy; Number of Params,N/A,Speech,N/A
Speaker Separation,3,LibriCSS; FSDnoisy18k; MC_GRID,N/A,N/A,Multi-Speaker Source Separation,Speech,N/A
Multi-task Audio Source Seperation,3,Acappella; CocoChorales; MTASS,N/A,N/A,N/A,N/A,N/A
Rhythm,2,Fraxtil; PTB-XL,N/A,N/A,N/A,N/A,N/A
Piano Music Modeling,2,Niko Chord Progression Dataset; PIAST,N/A,N/A,N/A,Music,N/A
Audio Signal Processing,2,RemFX; mDRT,N/A,N/A,Audio Effects Modeling; Audio Compression; blind source separation,Audio,"This is a general task that covers transforming audio inputs into audio outputs, not limited to existing PaperWithCode categories of Source Separation, Denoising, Classification, Recognition, etc."
Voice Cloning,2,GneutralSpeech Female; GneutralSpeech Male,N/A,N/A,N/A,N/A,N/A
Drum Transcription in Music (DTM),2,YourMT3 Dataset; ENST Drums,N/A,N/A,N/A,N/A,N/A
Salt-And-Pepper Noise Removal,1,BSD,N/A,N/A,N/A,N/A,N/A
Cross-environment ASR,1,Libri-Adapt,N/A,N/A,N/A,N/A,N/A
Cross-device ASR,1,Libri-Adapt,N/A,N/A,N/A,N/A,N/A
Noise Estimation,1,SIDD,SIDD,Average KL Divergence; PSNR Gap,N/A,Medical,N/A
Audio Fingerprint,1,Fingerprint Dataset,N/A,N/A,N/A,N/A,N/A
Soundscape evaluation,1,Subjective Perception of Active Noise Reduction (SPANR),N/A,N/A,N/A,Audio,Evaluation of soundscape in accordance to ISO/TS 12913-2
"Speaker Attribution in German Parliamentary Debates (GermEval 2023, subtask 1)",1,GePaDe,GePaDe,F1,N/A,Natural Language Processing,"Subtask 1 (full task) consists of predicting the cue words that trigger a speech event, together with the associated roles and their respective labels."
"Speaker Attribution in German Parliamentary Debates (GermEval 2023, subtask 2)",1,GePaDe,GePaDe,F1,N/A,N/A,"Subtask 2 (role labelling): Given the gold cue words,  the task consists in identifying the spans for all associated roles expressed in the text, together with their respective labels."
Acoustic Modelling,1,SALMon,N/A,N/A,N/A,Speech,N/A
Speaker Profiling,1,HeightCeleb,N/A,N/A,N/A,Speech,Estimation of Physical parameters from Speech data
Recognizing Seven Different Dastgahs Of Iranian Classical Music,0,N/A,N/A,N/A,N/A,Music,N/A
Audio declipping,0,N/A,N/A,N/A,N/A,Audio,Audio declipping is the task of estimating the original audio signal given its clipped measurements.
Audio Dequantization,0,N/A,N/A,N/A,N/A,Audio,Audio Dequantization is a process of estimating the original signal from its quantized counterpart.
Speaker Orientation,0,N/A,N/A,N/A,N/A,Audio,Direction of Voice or speaker orientation of the person with respect to the target device.
Hate Speech Normalization,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
blind source separation,0,N/A,N/A,N/A,N/A,N/A,"Blind source separation (BSS) is a signal processing technique that aims to separate multiple source signals from a set of mixed signals, without any prior knowledge about the sources or the mixing pr..."
Speech Representation Learning,0,N/A,N/A,N/A,N/A,Speech,N/A
Semi-Supervised Audio Regression,0,N/A,N/A,N/A,N/A,Audio,N/A
Speaker anonymization,0,N/A,N/A,N/A,N/A,Speech,N/A
