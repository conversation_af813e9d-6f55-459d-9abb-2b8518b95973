[{"categories": [], "datasets": [{"dataset": "Event2Mind", "dataset_citations": [], "dataset_links": [], "description": "Event2Mind is a crowdsourced corpus of 25,000 event phrases covering a diverse range of everyday events and situations.\nGiven an event described in a short free-form text, a model should reason about the likely intents and reactions of the\nevent's participants. Models are evaluated based on average cross-entropy (lower is better).\n", "sota": {"metrics": ["<PERSON>", "Test"], "rows": [{"code_links": [], "metrics": {"Dev": "4.25", "Test": "4.22"}, "model_links": [], "model_name": "BiRNN 100d (2018)", "paper_date": null, "paper_title": "Event2Mind: Commonsense Inference on Events, Intents, and Reactions", "paper_url": "https://arxiv.org/abs/1805.06939", "uses_additional_data": false}, {"code_links": [], "metrics": {"Dev": "4.44", "Test": "4.40"}, "model_links": [], "model_name": "ConvNet (2018)", "paper_date": null, "paper_title": "Event2Mind: Commonsense Inference on Events, Intents, and Reactions", "paper_url": "https://arxiv.org/abs/1805.06939", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "SWAG", "dataset_citations": [], "dataset_links": [], "description": "Situations with Adversarial Generations (SWAG) is a dataset consisting of 113k multiple\nchoice questions about a rich spectrum of grounded situations.\n", "sota": {"metrics": ["<PERSON>", "Test"], "rows": [{"code_links": [], "metrics": {"Dev": "86.6", "Test": "86.3"}, "model_links": [], "model_name": "BERT Large (2018)", "paper_date": null, "paper_title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding", "paper_url": "https://arxiv.org/abs/1810.04805", "uses_additional_data": false}, {"code_links": [], "metrics": {"Dev": "81.6", "Test": "-"}, "model_links": [], "model_name": "BERT Base (2018)", "paper_date": null, "paper_title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding", "paper_url": "https://arxiv.org/abs/1810.04805", "uses_additional_data": false}, {"code_links": [], "metrics": {"Dev": "59.1", "Test": "59.2"}, "model_links": [], "model_name": "ESIM + ELMo (2018)", "paper_date": null, "paper_title": "SWAG: A Large-Scale Adversarial Dataset for Grounded Commonsense Inference", "paper_url": "http://arxiv.org/abs/1808.05326", "uses_additional_data": false}, {"code_links": [], "metrics": {"Dev": "51.9", "Test": "52.7"}, "model_links": [], "model_name": "ESIM + GloVe (2018)", "paper_date": null, "paper_title": "SWAG: A Large-Scale Adversarial Dataset for Grounded Commonsense Inference", "paper_url": "http://arxiv.org/abs/1808.05326", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Winograd <PERSON> Challenge", "dataset_citations": [], "dataset_links": [{"title": "Winograd <PERSON> Challenge", "url": "https://www.aaai.org/ocs/index.php/KR/KR12/paper/view/4492"}], "description": "The [Winograd Schema Challenge](https://www.aaai.org/ocs/index.php/KR/KR12/paper/view/4492)\nis a dataset for common sense reasoning. It employs Winograd Schema questions that\nrequire the resolution of anaphora: the system must identify the antecedent of an ambiguous pronoun in a statement. Models\nare evaluated based on accuracy.\nExample:\nThe trophy doesn’t fit in the suitcase because it is too big. What is too big?\nAnswer 0: the trophy. Answer 1: the suitcase\n", "sota": {"metrics": ["Score"], "rows": [{"code_links": [], "metrics": {"Score": "62.6"}, "model_links": [], "model_name": "Word-LM-partial (2018)", "paper_date": null, "paper_title": "A Simple Method for Commonsense Reasoning", "paper_url": "https://arxiv.org/abs/1806.02847", "uses_additional_data": false}, {"code_links": [], "metrics": {"Score": "57.9"}, "model_links": [], "model_name": "Char-LM-partial (2018)", "paper_date": null, "paper_title": "A Simple Method for Commonsense Reasoning", "paper_url": "https://arxiv.org/abs/1806.02847", "uses_additional_data": false}, {"code_links": [], "metrics": {"Score": "52.8"}, "model_links": [], "model_name": "USSM + Supervised DeepNet + KB (2017)", "paper_date": null, "paper_title": "Combing Context and Commonsense Knowledge Through Neural Networks for Solving Winograd <PERSON> Problems", "paper_url": "https://aaai.org/ocs/index.php/SSS/SSS17/paper/view/15392", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Winograd NLI (WNLI)", "dataset_citations": [], "dataset_links": [{"title": "GLUE benchmark", "url": "https://arxiv.org/abs/1804.07461"}, {"title": "<PERSON> et al., 2017", "url": "https://www.aaai.org/ocs/index.php/SSS/SSS17/paper/view/15392"}, {"title": "GLUE leaderboard", "url": "https://gluebenchmark.com/leaderboard"}], "description": "WNLI is a relaxation of the Winograd Schema Challenge proposed as part of the [GLUE benchmark](https://arxiv.org/abs/1804.07461) and a conversion to the natural language inference (NLI) format. The task is to predict if the sentence with the pronoun substituted is entailed by the original sentence. While the training set is balanced between two classes (entailment and not entailment), the test set is imbalanced between them (35% entailment, 65% not entailment). The majority baseline is thus 65%, while for the Winograd Schema Challenge it is 50% ([<PERSON> et al., 2017](https://www.aaai.org/ocs/index.php/SSS/SSS17/paper/view/15392)). The latter is more challenging.\nResults are available at the [GLUE leaderboard](https://gluebenchmark.com/leaderboard). Here is a subset of results of recent models:\n", "sota": {"metrics": ["Score"], "rows": [{"code_links": [], "metrics": {"Score": "90.4"}, "model_links": [], "model_name": "XLNet-Large (2019)", "paper_date": null, "paper_title": "XLNet: Generalized Autoregressive Pretraining for Language Understanding", "paper_url": "https://arxiv.org/pdf/1906.08237.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Score": "89.0"}, "model_links": [], "model_name": "MT-DNN-ensemble (2019)", "paper_date": null, "paper_title": "Improving Multi-Task Deep Neural Networks via Knowledge Distillation for Natural Language Understanding", "paper_url": "https://arxiv.org/pdf/1904.09482.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Score": "65.1"}, "model_links": [], "model_name": "Snorkel MeTaL (2018)", "paper_date": null, "paper_title": "Training Complex Models with Multi-Task Weak Supervision", "paper_url": "https://arxiv.org/pdf/1810.02840.pdf", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Visual Common Sense", "dataset_citations": [], "dataset_links": [], "description": "Visual Commonsense Reasoning (VCR) is a new task and large-scale dataset for cognition-level visual understanding.\nWith one glance at an image, we can effortlessly imagine the world beyond the pixels (e.g. that [person1] ordered \npancakes). While this task is easy for humans, it is tremendously difficult for today's vision systems, requiring \nhigher-order cognition and commonsense reasoning about the world. We formalize this task as Visual Commonsense \nReasoning. In addition to answering challenging visual questions expressed in natural language, a model must provide a \nrationale explaining why its answer is true.\n", "sota": {"metrics": ["Q->A", "QA->R", "Q->AR"], "rows": [{"code_links": [], "metrics": {"Q->A": "91.0", "Q->AR": "85.0", "QA->R": "93.0"}, "model_links": [], "model_name": "Human Performance University of Washington", "paper_date": null, "paper_title": "From Recognition to Cognition: Visual Commonsense Reasoning", "paper_url": "https://arxiv.org/abs/1811.10830", "uses_additional_data": false}, {"code_links": [], "metrics": {"Q->A": "65.1", "Q->AR": "44.0", "QA->R": "67.3"}, "model_links": [], "model_name": "Recognition to Cognition Networks University of Washington", "paper_date": null, "paper_title": "From Recognition to Cognition: Visual Commonsense Reasoning", "paper_url": "https://arxiv.org/abs/1811.10830", "uses_additional_data": false}, {"code_links": [], "metrics": {"Q->A": "53.9", "Q->AR": "35.0", "QA->R": "64.5"}, "model_links": [], "model_name": "BERT-Base Google AI Language", "paper_date": null, "paper_title": "", "paper_url": "", "uses_additional_data": false}, {"code_links": [], "metrics": {"Q->A": "46.2", "Q->AR": "17.2", "QA->R": "36.8"}, "model_links": [], "model_name": "MLB Seoul National University", "paper_date": null, "paper_title": "", "paper_url": "", "uses_additional_data": false}, {"code_links": [], "metrics": {"Q->A": "25.0", "Q->AR": "6.2", "QA->R": "25.0"}, "model_links": [], "model_name": "Random Performance", "paper_date": null, "paper_title": "", "paper_url": "", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ReCoRD", "dataset_citations": [], "dataset_links": [{"title": "SuperGLUE benchmark", "url": "https://arxiv.org/pdf/1905.00537.pdf"}], "description": "Reading Comprehension with Commonsense Reasoning Dataset (ReCoRD) is a large-scale reading comprehension dataset which requires commonsense reasoning. ReCoRD consists of queries automatically generated from CNN/Daily Mail news articles; the answer to each query is a text span from a summarizing passage of the corresponding news. The goal of ReCoRD is to evaluate a machine's ability of commonsense reasoning in reading comprehension. ReCoRD is pronounced as [ˈrɛkərd] and is part of the [SuperGLUE benchmark](https://arxiv.org/pdf/1905.00537.pdf).\n", "sota": {"metrics": ["EM", "F1"], "rows": [{"code_links": [], "metrics": {"EM": "91.31", "F1": "91.69"}, "model_links": [], "model_name": "Human Performance Johns Hopkins University", "paper_date": null, "paper_title": "ReCoRD: Bridging the Gap between Human and Machine Commonsense Reading Comprehension", "paper_url": "https://arxiv.org/pdf/1810.12885.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM": "90.0", "F1": "90.6"}, "model_links": [], "model_name": "RoBERTa", "paper_date": null, "paper_title": "RoBERTa: A Robustly Optimized BERT Pretraining Approach", "paper_url": "https://arxiv.org/pdf/1907.11692.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM": "83.09", "F1": "83.74"}, "model_links": [], "model_name": "XLNet + MTL + Verifier", "paper_date": null, "paper_title": "", "paper_url": "", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM": "81.78", "F1": "82.58"}, "model_links": [], "model_name": "CSRLM", "paper_date": null, "paper_title": "", "paper_url": "", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Common sense reasoning tasks are intended to require the model to go beyond pattern \nrecognition. Instead, the model should use \"common sense\" or world knowledge\nto make inferences.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Common Sense"}, {"categories": [], "datasets": [{"dataset": "Gigaword", "dataset_citations": [], "dataset_links": [{"title": "<PERSON> et al., 2015", "url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf"}, {"title": "<PERSON> et al., 2015", "url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf"}, {"title": "<PERSON><PERSON> et al., 2016", "url": "http://www.aclweb.org/anthology/N16-1012"}], "description": "The Gigaword summarization dataset has been first used by [<PERSON> et al., 2015](https://www.aclweb.org/anthology/D/D15/D15-1044.pdf) and represents a sentence summarization / headline generation task with very short input documents (31.4 tokens) and summaries (8.3 tokens). It contains 3.8M training, 189k development and 1951 test instances. Models are evaluated with ROUGE-1, ROUGE-2 and ROUGE-L using full-length F1-scores.\nBelow Results are ranking by ROUGE-2 Scores.\n(*) [<PERSON> et al., 2015](https://www.aclweb.org/anthology/D/D15/D15-1044.pdf)  report ROUGE recall, the table here contains ROUGE F1-scores for <PERSON>'s model reported by [<PERSON><PERSON> et al., 2016](http://www.aclweb.org/anthology/N16-1012)\n", "sota": {"metrics": ["ROUGE-1", "ROUGE-2*", "ROUGE-L"], "rows": [{"code_links": [], "metrics": {"ROUGE-1": "39.08", "ROUGE-2*": "20.47", "ROUGE-L": "36.69"}, "model_links": [], "model_name": "ControlCopying (2020)", "paper_date": null, "paper_title": "Controlling the Amount of Verbatim Copying in Abstractive Summarizatio", "paper_url": "https://arxiv.org/pdf/1911.10390.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "39.51", "ROUGE-2*": "20.42", "ROUGE-L": "36.69"}, "model_links": [], "model_name": "ProphetNet (2020)", "paper_date": null, "paper_title": "ProphetNet: Predicting Future N-gram for Sequence-to-Sequence Pre-training", "paper_url": "https://arxiv.org/pdf/2001.04063.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "38.90", "ROUGE-2*": "20.05", "ROUGE-L": "36.00"}, "model_links": [], "model_name": "UniLM (2019)", "paper_date": null, "paper_title": "Unified Language Model Pre-training for Natural Language Understanding and Generation", "paper_url": "https://arxiv.org/pdf/1905.03197.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "39.12", "ROUGE-2*": "19.86", "ROUGE-L": "36.24"}, "model_links": [], "model_name": "PEGASUS (2019)", "paper_date": null, "paper_title": "PEGASUS: Pre-training with Extracted Gap-sentences for Abstractive Summarization", "paper_url": "https://arxiv.org/pdf/1912.08777.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "39.11", "ROUGE-2*": "19.78", "ROUGE-L": "36.87"}, "model_links": [], "model_name": "BiSET (2019)", "paper_date": null, "paper_title": "BiSET: Bi-directional Selective Encoding with Template for Abstractive Summarization", "paper_url": "https://www.aclweb.org/anthology/P19-1207", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "38.73", "ROUGE-2*": "19.71", "ROUGE-L": "35.96"}, "model_links": [], "model_name": "MASS (2019)", "paper_date": null, "paper_title": "MASS: Masked Sequence to Sequence Pre-training for Language Generation", "paper_url": "https://arxiv.org/pdf/1905.02450v5.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "37.04", "ROUGE-2*": "19.03", "ROUGE-L": "34.46"}, "model_links": [], "model_name": "Re^3 Sum (2018)", "paper_date": null, "paper_title": "Retrieve, <PERSON><PERSON> and Rewrite: Soft Template Based Neural Summarization", "paper_url": "http://aclweb.org/anthology/P18-1015", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "36.61", "ROUGE-2*": "18.85", "ROUGE-L": "34.33"}, "model_links": [], "model_name": "JointParsing (2020)", "paper_date": null, "paper_title": "Joint Parsing and Generation for Abstractive Summarization", "paper_url": "https://arxiv.org/pdf/1911.10389.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "37.95", "ROUGE-2*": "18.64", "ROUGE-L": "35.11"}, "model_links": [], "model_name": "CNN-2sent-hieco-RBM (2019)", "paper_date": null, "paper_title": "Abstract Text Summarization with a Convolutional Seq2Seq Model", "paper_url": "https://www.mdpi.com/2076-3417/9/8/1665/pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "36.92", "ROUGE-2*": "18.29", "ROUGE-L": "34.58"}, "model_links": [], "model_name": "Reinforced-Topic-ConvS2S (2018)", "paper_date": null, "paper_title": "A Reinforced Topic-Aware Convolutional Sequence-to-Sequence Model for Abstractive Text Summarization", "paper_url": "https://www.ijcai.org/proceedings/2018/0619.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "36.3", "ROUGE-2*": "18.0", "ROUGE-L": "33.8"}, "model_links": [], "model_name": "CGU (2018)", "paper_date": null, "paper_title": "Global Encoding for Abstractive Summarization", "paper_url": "http://aclweb.org/anthology/P18-2027", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "35.98", "ROUGE-2*": "17.76", "ROUGE-L": "33.63"}, "model_links": [], "model_name": "Pointer + Coverage + EntailmentGen + QuestionGen (2018)", "paper_date": null, "paper_title": "Soft Layer-Specific Multi-Task Summarization with Entailment and Question Generation", "paper_url": "http://aclweb.org/anthology/P18-1064", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "35.47", "ROUGE-2*": "17.66", "ROUGE-L": "33.52"}, "model_links": [], "model_name": "Struct+2Way+Word (2018)", "paper_date": null, "paper_title": "Structure-Infused Copy Mechanisms for Abstractive Summarization", "paper_url": "http://aclweb.org/anthology/C18-1146", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "37.27", "ROUGE-2*": "17.65", "ROUGE-L": "34.24"}, "model_links": [], "model_name": "FTSum_g (2018)", "paper_date": null, "paper_title": "Faithful to the Original: Fact Aware Neural Abstractive Summarization", "paper_url": "https://arxiv.org/pdf/1711.04434.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "36.27", "ROUGE-2*": "17.57", "ROUGE-L": "33.62"}, "model_links": [], "model_name": "DRGD (2017)", "paper_date": null, "paper_title": "Deep Recurrent Generative Decoder for Abstractive Text Summarization", "paper_url": "http://aclweb.org/anthology/D17-1222", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "36.15", "ROUGE-2*": "17.54", "ROUGE-L": "33.63"}, "model_links": [], "model_name": "SEASS (2017)", "paper_date": null, "paper_title": "Selective Encoding for Abstractive Sentence Summarization", "paper_url": "http://aclweb.org/anthology/P17-1101", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "36.30", "ROUGE-2*": "17.31", "ROUGE-L": "33.88"}, "model_links": [], "model_name": "EndDec+WFE (2017)", "paper_date": null, "paper_title": "Cutting-off Redundant Repeating Generations for Neural Abstractive Summarization", "paper_url": "http://aclweb.org/anthology/E17-2047", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "35.33", "ROUGE-2*": "17.27", "ROUGE-L": "33.19"}, "model_links": [], "model_name": "Seq2seq + selective + MTL + ERAM (2018)", "paper_date": null, "paper_title": "Ensure the Correctness of the Summary: Incorporate Entailment Knowledge into Abstractive Sentence Summarization", "paper_url": "http://aclweb.org/anthology/C18-1121", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "37.04", "ROUGE-2*": "16.66", "ROUGE-L": "34.93"}, "model_links": [], "model_name": "Seq2seq + E2T_cnn (2018)", "paper_date": null, "paper_title": "Entity Commonsense Representation for Neural Abstractive Summarization", "paper_url": "http://aclweb.org/anthology/N18-1064", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "33.78", "ROUGE-2*": "15.97", "ROUGE-L": "31.15"}, "model_links": [], "model_name": "RAS-<PERSON><PERSON> (2016)", "paper_date": null, "paper_title": "Abstractive Sentence Summarization with Attentive Recurrent Neural Networks", "paper_url": "http://www.aclweb.org/anthology/N16-1012", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "32.67", "ROUGE-2*": "15.59", "ROUGE-L": "30.64"}, "model_links": [], "model_name": "words-lvt5k-1sent (2016)", "paper_date": null, "paper_title": "Abstractive Text Summarization using Sequence-to-sequence RNNs and Beyond", "paper_url": "http://www.aclweb.org/anthology/K16-1028", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "29.76", "ROUGE-2*": "11.88", "ROUGE-L": "26.96"}, "model_links": [], "model_name": "ABS+ (2015)", "paper_date": null, "paper_title": "A Neural Attention Model for Sentence Summarization *", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "29.55", "ROUGE-2*": "11.32", "ROUGE-L": "26.42"}, "model_links": [], "model_name": "ABS (2015)", "paper_date": null, "paper_title": "A Neural Attention Model for Sentence Summarization *", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "X-Sum", "dataset_citations": [], "dataset_links": [{"title": "<PERSON> et al., 2018", "url": "https://arxiv.org/pdf/1808.08745.pdf"}, {"title": "here", "url": "https://github.com/EdinburghNLP/XSum"}], "description": "X-Sum (standing for Extreme Summarization), introduced by [<PERSON> et al., 2018](https://arxiv.org/pdf/1808.08745.pdf), is a summarization dataset which does not favor extractive strategies and calls for an abstractive modeling approach.\nThe idea of this dataset is to create a short, one sentence news summary.\nData is collected by harvesting online articles from the BBC.\nThe dataset contain 204 045 samples for the training set, 11 332 for the validation set, and 11 334 for the test set. In average the length of article is 431 words (~20 sentences) and the length of summary is 23 words. It can be downloaded [here](https://github.com/EdinburghNLP/XSum).\nEvaluation metrics are ROUGE-1, ROUGE-2 and ROUGE-L.\n", "sota": {"metrics": ["ROUGE-1", "ROUGE-2", "ROUGE-L"], "rows": [{"code_links": [], "metrics": {"ROUGE-1": "47.21", "ROUGE-2": "24.56", "ROUGE-L": "39.25"}, "model_links": [], "model_name": "PEGASUS (2019)", "paper_date": null, "paper_title": "PEGASUS: Pre-training with Extracted Gap-sentences for Abstractive Summarization", "paper_url": "https://arxiv.org/pdf/1912.08777.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "45.14", "ROUGE-2": "22.27", "ROUGE-L": "37.25"}, "model_links": [], "model_name": "BART (2019)", "paper_date": null, "paper_title": "BART: Denoising Sequence-to-Sequence Pre-training for Natural Language Generation, Translation, and Comprehension", "paper_url": "https://arxiv.org/pdf/1910.13461.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "38.81", "ROUGE-2": "16.50", "ROUGE-L": "31.27"}, "model_links": [], "model_name": "BertSumExtAbs (2019)", "paper_date": null, "paper_title": "Text Summarization with Pretrained Encoders", "paper_url": "https://arxiv.org/pdf/1908.08345.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "31.89", "ROUGE-2": "11.54", "ROUGE-L": "25.75"}, "model_links": [], "model_name": "T-ConvS2S", "paper_date": null, "paper_title": "Don’t Give Me the Details, Just the Summary!", "paper_url": "https://arxiv.org/pdf/1808.08745.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "29.70", "ROUGE-2": "9.21", "ROUGE-L": "23.24"}, "model_links": [], "model_name": "PtGen", "paper_date": null, "paper_title": "Don’t Give Me the Details, Just the Summary!", "paper_url": "https://arxiv.org/pdf/1808.08745.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "28.42", "ROUGE-2": "8.77", "ROUGE-L": "22.48"}, "model_links": [], "model_name": "Seq2Seq", "paper_date": null, "paper_title": "Don’t Give Me the Details, Just the Summary!", "paper_url": "https://arxiv.org/pdf/1808.08745.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "28.10", "ROUGE-2": "8.02", "ROUGE-L": "21.72"}, "model_links": [], "model_name": "PtGen-Covg", "paper_date": null, "paper_title": "Don’t Give Me the Details, Just the Summary!", "paper_url": "https://arxiv.org/pdf/1808.08745.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "29.79", "ROUGE-2": "8.81", "ROUGE-L": "22.66"}, "model_links": [], "model_name": "Baseline : Extractive Oracle", "paper_date": null, "paper_title": "Don’t Give Me the Details, Just the Summary!", "paper_url": "https://arxiv.org/pdf/1808.08745.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "16.30", "ROUGE-2": "1.60", "ROUGE-L": "11.95"}, "model_links": [], "model_name": "Baseline : Lead-3", "paper_date": null, "paper_title": "Don’t Give Me the Details, Just the Summary!", "paper_url": "https://arxiv.org/pdf/1808.08745.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "15.16", "ROUGE-2": "1.78", "ROUGE-L": "11.27"}, "model_links": [], "model_name": "Baseline : Random", "paper_date": null, "paper_title": "Don’t Give Me the Details, Just the Summary!", "paper_url": "https://arxiv.org/pdf/1808.08745.pdf", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "DUC 2004 Task 1", "dataset_citations": [], "dataset_links": [{"title": "DUC 2004", "url": "https://duc.nist.gov/duc2004/"}], "description": "Similar to Gigaword, task 1 of [DUC 2004](https://duc.nist.gov/duc2004/) is a sentence summarization task. The dataset contains 500 documents with on average 35.6 tokens and summaries with 10.4 tokens. Due to its size, neural models are typically trained on other datasets and only tested on DUC 2004. Evaluation metrics are ROUGE-1, ROUGE-2 and ROUGE-L recall @ 75 bytes.\n", "sota": {"metrics": ["ROUGE-1", "ROUGE-2", "ROUGE-L"], "rows": [{"code_links": [], "metrics": {"ROUGE-1": "32.57", "ROUGE-2": "11.63", "ROUGE-L": "28.24"}, "model_links": [], "model_name": "Transformer + LRPE + PE + ALONE + RE-ranking (2020)", "paper_date": null, "paper_title": "All Word Embeddings from One Embedding", "paper_url": "https://arxiv.org/abs/2004.12073", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "32.29", "ROUGE-2": "11.49", "ROUGE-L": "28.03"}, "model_links": [], "model_name": "Transformer + LRPE + PE + Re-ranking (2019)", "paper_date": null, "paper_title": "Positional Encoding to Control Output Sequence Length", "paper_url": "https://arxiv.org/abs/1904.07418", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "31.79", "ROUGE-2": "10.75", "ROUGE-L": "27.48"}, "model_links": [], "model_name": "DRGD (2017)", "paper_date": null, "paper_title": "Deep Recurrent Generative Decoder for Abstractive Text Summarization", "paper_url": "http://aclweb.org/anthology/D17-1222", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "32.28", "ROUGE-2": "10.54", "ROUGE-L": "27.8"}, "model_links": [], "model_name": "EndDec+WFE (2017)", "paper_date": null, "paper_title": "Cutting-off Redundant Repeating Generations for Neural Abstractive Summarization", "paper_url": "http://aclweb.org/anthology/E17-2047", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "31.15", "ROUGE-2": "10.85", "ROUGE-L": "27.68"}, "model_links": [], "model_name": "Reinforced-Topic-ConvS2S (2018)", "paper_date": null, "paper_title": "A Reinforced Topic-Aware Convolutional Sequence-to-Sequence Model for Abstractive Text Summarization", "paper_url": "https://www.ijcai.org/proceedings/2018/0619.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "29.74", "ROUGE-2": "9.85", "ROUGE-L": "25.81"}, "model_links": [], "model_name": "CNN-2sent-hieco-RBM (2019)", "paper_date": null, "paper_title": "Abstract Text Summarization with a Convolutional Seq2Seq Model", "paper_url": "https://www.mdpi.com/2076-3417/9/8/1665/pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "29.33", "ROUGE-2": "10.24", "ROUGE-L": "25.24"}, "model_links": [], "model_name": "Seq2seq + selective + MTL + ERAM (2018)", "paper_date": null, "paper_title": "Ensure the Correctness of the Summary: Incorporate Entailment Knowledge into Abstractive Sentence Summarization", "paper_url": "http://aclweb.org/anthology/C18-1121", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "29.21", "ROUGE-2": "9.56", "ROUGE-L": "25.51"}, "model_links": [], "model_name": "SEASS (2017)", "paper_date": null, "paper_title": "Selective Encoding for Abstractive Sentence Summarization", "paper_url": "http://aclweb.org/anthology/P17-1101", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "28.61", "ROUGE-2": "9.42", "ROUGE-L": "25.24"}, "model_links": [], "model_name": "words-lvt5k-1sent (2016)", "paper_date": null, "paper_title": "Abstractive Text Summarization using Sequence-to-sequence RNNs and Beyond", "paper_url": "http://www.aclweb.org/anthology/K16-1028", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "28.18", "ROUGE-2": "8.49", "ROUGE-L": "23.81"}, "model_links": [], "model_name": "ABS+ (2015)", "paper_date": null, "paper_title": "A Neural Attention Model for Sentence Summarization", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "28.97", "ROUGE-2": "8.26", "ROUGE-L": "24.06"}, "model_links": [], "model_name": "RAS-<PERSON><PERSON> (2016)", "paper_date": null, "paper_title": "Abstractive Sentence Summarization with Attentive Recurrent Neural Networks", "paper_url": "http://www.aclweb.org/anthology/N16-1012", "uses_additional_data": false}, {"code_links": [], "metrics": {"ROUGE-1": "26.55", "ROUGE-2": "7.06", "ROUGE-L": "22.05"}, "model_links": [], "model_name": "ABS (2015)", "paper_date": null, "paper_title": "A Neural Attention Model for Sentence Summarization", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1044.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Summarization is the task of producing a shorter version of one or several documents that preserves most of the\ninput's meaning.\n", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "Google Dataset", "dataset_citations": [], "dataset_links": [{"title": "Google Dataset", "url": "https://github.com/google-research-datasets/sentence-compression"}, {"title": "Overcoming the Lack of Parallel Data in Sentence Compression", "url": "https://www.aclweb.org/anthology/D/D13/D13-1155.pdf"}, {"title": "repository", "url": "https://github.com/google-research-datasets/sentence-compression/tree/master/data"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "The [Google Dataset](https://github.com/google-research-datasets/sentence-compression) was built by <PERSON><PERSON><PERSON><PERSON> et al., 2013([Overcoming the Lack of Parallel Data in Sentence Compression](https://www.aclweb.org/anthology/D/D13/D13-1155.pdf)). The first dataset released contained only 10,000 sentence-compression pairs, but last year was released an additional 200,000 pairs. \nExample of a sentence-compression pair:\nIn short, this is a deletion-based task where the compression is a subsequence from the original sentence. From the 10,000 pairs of the eval portion([repository](https://github.com/google-research-datasets/sentence-compression/tree/master/data)) it is used the very first 1,000 sentence for automatic evaluation and the 200,000 pairs for training.\nModels are evaluated using the following metrics:\n* F1 - compute the recall and precision in terms of tokens kept in the golden and the generated compressions.\n* Compression rate (CR) - the length of the compression in characters divided over the sentence length. \n[Go back to the README](../README.md)\n", "sota": {"metrics": ["F1", "CR"], "rows": [{"code_links": [], "metrics": {"CR": "0.39", "F1": "0.851"}, "model_links": [], "model_name": "BiRNN + LM Evaluator (2018)", "paper_date": null, "paper_title": "A Language Model based Evaluator for Sentence Compression", "paper_url": "https://aclweb.org/anthology/P18-2028", "uses_additional_data": false}, {"code_links": [], "metrics": {"CR": "0.38", "F1": "0.82"}, "model_links": [], "model_name": "LSTM (2015)", "paper_date": null, "paper_title": "Sentence Compression by Deletion with LSTMs", "paper_url": "https://research.google.com/pubs/archive/43852.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"CR": "0.43", "F1": "0.8"}, "model_links": [], "model_name": "BiLSTM (2017)", "paper_date": null, "paper_title": "Can Syntax Help? Improving an LSTM-based Sentence Compression Model for New Domains", "paper_url": "http://www.aclweb.org/anthology/P17-1127", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Sentence compression produces a shorter sentence by removing redundant information,\npreserving the grammatically and the important content of the original sentence. \n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Sentence Compression"}], "synonyms": [], "task": "Summarization"}, {"categories": [], "datasets": [], "description": "Taxonomy learning is the task of hierarchically classifying concepts in an automatic manner from text corpora. The process of building taxonomies is usually divided into two main steps: (1) extracting hypernyms for concepts, which may constitute a field of research in itself (see Hypernym Discovery below) and (2) refining the structure into a taxonomy.\n", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "SemEval 2018", "dataset_citations": [], "dataset_links": [{"title": "Camacho-<PERSON> et al. 2018", "url": "http://aclweb.org/anthology/S18-1115"}, {"title": "here", "url": "https://competitions.codalab.org/competitions/17119"}], "description": "The SemEval-2018 hypernym discovery evaluation benchmark ([<PERSON><PERSON><PERSON><PERSON> et al. 2018](http://aclweb.org/anthology/S18-1115)), which can be freely downloaded [here](https://competitions.codalab.org/competitions/17119), contains three domains (general, medical and music) and is also available in Italian and Spanish (not in this repository). For each domain a target corpus and vocabulary (i.e. hypernym search space) are provided. The dataset contains both concepts (e.g. dog) and entities (e.g. Manchester United) up to trigrams. The following table lists the number of hyponym-hypernym pairs for each dataset: \nThe results for each model and dataset (general, medical and music) are presented below (MFH stands for “Most Frequent Hypernyms” and is used as a baseline).\nGeneral:\nMedical domain:\nMusic domain:\n", "sota": {"metrics": [], "rows": []}, "subdatasets": [{"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["MAP", "MRR", "P@5"], "rows": [{"code_links": [], "metrics": {"MAP": "19.78", "MRR": "36.10", "P@5": "19.03"}, "model_links": [], "model_name": "CRIM (2018)", "paper_date": null, "paper_title": "A Hybrid Approach to Hypernym Discovery", "paper_url": "http://aclweb.org/anthology/S18-1116", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "10.60", "MRR": "23.83", "P@5": "9.91"}, "model_links": [], "model_name": "vTE (2016)", "paper_date": null, "paper_title": "Supervised Distributional Hypernym Discovery via Domain Adaptation", "paper_url": "https://aclweb.org/anthology/D16-1041", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "9.37", "MRR": "17.29", "P@5": "9.19"}, "model_links": [], "model_name": "NLP_HZ (2018)", "paper_date": null, "paper_title": "A Nearest Neighbor Approach", "paper_url": "http://aclweb.org/anthology/S18-1148", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "8.95", "MRR": "19.44", "P@5": "8.63"}, "model_links": [], "model_name": "300-sparsans (2018)", "paper_date": null, "paper_title": "Hypernymy as interaction of sparse attributes ", "paper_url": "http://aclweb.org/anthology/S18-1152", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "8.77", "MRR": "21.39", "P@5": "7.81"}, "model_links": [], "model_name": "MFH", "paper_date": null, "paper_title": "", "paper_url": "", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "5.77", "MRR": "10.56", "P@5": "5.96"}, "model_links": [], "model_name": "SJTU BCMI (2018)", "paper_date": null, "paper_title": "Neural Hypernym Discovery with Term Embeddings", "paper_url": "http://aclweb.org/anthology/S18-1147", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "2.68", "MRR": "6.01", "P@5": "2.69"}, "model_links": [], "model_name": "Apollo (2018)", "paper_date": null, "paper_title": "Detecting Hypernymy Relations Using Syntactic Dependencies ", "paper_url": "http://aclweb.org/anthology/S18-1146", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "1.36", "MRR": "3.18", "P@5": "1.30"}, "model_links": [], "model_name": "balAPInc (2017)", "paper_date": null, "paper_title": "Hypernyms under Siege: Linguistically-motivated Artillery for Hypernymy Detection", "paper_url": "http://www.aclweb.org/anthology/E17-1007", "uses_additional_data": false}]}, "subdataset": "General", "subdatasets": []}, {"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["MAP", "MRR", "P@5"], "rows": [{"code_links": [], "metrics": {"MAP": "34.05", "MRR": "54.64", "P@5": "36.77"}, "model_links": [], "model_name": "CRIM (2018)", "paper_date": null, "paper_title": "A Hybrid Approach to Hypernym Discovery", "paper_url": "http://aclweb.org/anthology/S18-1116", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "28.93", "MRR": "35.80", "P@5": "34.20"}, "model_links": [], "model_name": "MFH", "paper_date": null, "paper_title": "", "paper_url": "", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "20.75", "MRR": "40.60", "P@5": "21.43"}, "model_links": [], "model_name": "300-sparsans (2018)", "paper_date": null, "paper_title": "Hypernymy as interaction of sparse attributes ", "paper_url": "http://aclweb.org/anthology/S18-1152", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "18.84", "MRR": "41.07", "P@5": "20.71"}, "model_links": [], "model_name": "vTE (2016)", "paper_date": null, "paper_title": "Supervised Distributional Hypernym Discovery via Domain Adaptation", "paper_url": "https://aclweb.org/anthology/D16-1041", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "13.77", "MRR": "40.76", "P@5": "12.76"}, "model_links": [], "model_name": "EXPR (2018)", "paper_date": null, "paper_title": "A Combined Approach for Hypernym Discovery", "paper_url": "http://aclweb.org/anthology/S18-1150", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "11.69", "MRR": "25.95", "P@5": "11.69"}, "model_links": [], "model_name": "SJTU BCMI (2018)", "paper_date": null, "paper_title": "Neural Hypernym Discovery with Term Embeddings", "paper_url": "http://aclweb.org/anthology/S18-1147", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "8.13", "MRR": "20.56", "P@5": "8.32"}, "model_links": [], "model_name": "ADAPT (2018)", "paper_date": null, "paper_title": "Skip-Gram Word Embeddings for Unsupervised Hypernym Discovery in Specialised Corpora ", "paper_url": "http://aclweb.org/anthology/S18-1151", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "0.91", "MRR": "2.10", "P@5": "1.08"}, "model_links": [], "model_name": "balAPInc (2017)", "paper_date": null, "paper_title": "Hypernyms under Siege: Linguistically-motivated Artillery for Hypernymy Detection", "paper_url": "http://www.aclweb.org/anthology/E17-1007", "uses_additional_data": false}]}, "subdataset": "Medical domain", "subdatasets": []}, {"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["MAP", "MRR", "P@5"], "rows": [{"code_links": [], "metrics": {"MAP": "40.97", "MRR": "60.93", "P@5": "41.31"}, "model_links": [], "model_name": "CRIM (2018)", "paper_date": null, "paper_title": "A Hybrid Approach to Hypernym Discovery", "paper_url": "http://aclweb.org/anthology/S18-1116", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "33.32", "MRR": "51.48", "P@5": "35.76"}, "model_links": [], "model_name": "MFH", "paper_date": null, "paper_title": "", "paper_url": "", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "29.54", "MRR": "46.43", "P@5": "28.86"}, "model_links": [], "model_name": "300-sparsans (2018)", "paper_date": null, "paper_title": "Hypernymy as interaction of sparse attributes ", "paper_url": "http://aclweb.org/anthology/S18-1152", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "12.99", "MRR": "39.36", "P@5": "12.41"}, "model_links": [], "model_name": "vTE (2016)", "paper_date": null, "paper_title": "Supervised Distributional Hypernym Discovery via Domain Adaptation", "paper_url": "https://aclweb.org/anthology/D16-1041", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "4.71", "MRR": "9.15", "P@5": "4.91"}, "model_links": [], "model_name": "SJTU BCMI (2018)", "paper_date": null, "paper_title": "Neural Hypernym Discovery with Term Embeddings", "paper_url": "http://aclweb.org/anthology/S18-1147", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "2.63", "MRR": "7.46", "P@5": "2.64"}, "model_links": [], "model_name": "ADAPT (2018)", "paper_date": null, "paper_title": "Skip-Gram Word Embeddings for Unsupervised Hypernym Discovery in Specialised Corpora ", "paper_url": "http://aclweb.org/anthology/S18-1151", "uses_additional_data": false}, {"code_links": [], "metrics": {"MAP": "1.95", "MRR": "5.01", "P@5": "2.15"}, "model_links": [], "model_name": "balAPInc (2017)", "paper_date": null, "paper_title": "Hypernyms under Siege: Linguistically-motivated Artillery for Hypernymy Detection", "paper_url": "http://www.aclweb.org/anthology/E17-1007", "uses_additional_data": false}]}, "subdataset": "Music domain", "subdatasets": []}]}], "description": "Given a corpus and a target term (hyponym), the task of hypernym discovery consists of extracting a set of its most appropriate hypernyms from the corpus. For example, for the input word “dog”, some valid hypernyms would be “canine”, “mammal” or “animal”.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Hypernym Discovery"}], "synonyms": [], "task": "Taxonomy Learning"}, {"categories": [], "datasets": [], "description": "Dialogue is notoriously hard to evaluate. Past approaches have used human evaluation.\n", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "Switchboard corpus", "dataset_citations": [], "dataset_links": [{"title": "Switchboard-1 corpus", "url": "https://catalog.ldc.upenn.edu/ldc97s62"}, {"title": "download", "url": "https://web.stanford.edu/~j<PERSON><PERSON><PERSON>/swb1_dialogact_annot.tar.gz"}, {"title": "SWBD-DAMSL tagset", "url": "https://web.stanford.edu/~j<PERSON><PERSON><PERSON>/ws97/manual.august1.html"}], "description": "The [Switchboard-1 corpus](https://catalog.ldc.upenn.edu/ldc97s62) is a telephone speech corpus, consisting of about 2,400 two-sided telephone conversation among 543 speakers with about 70 provided conversation topics. The dataset includes the audio files and the transcription files, as well as information about the speakers and the calls.\nThe Switchboard Dialogue Act Corpus (SwDA) [[download](https://web.stanford.edu/~jurafsky/swb1_dialogact_annot.tar.gz)] extends the Switchboard-1 corpus with tags from the [SWBD-DAMSL tagset](https://web.stanford.edu/~jurafsky/ws97/manual.august1.html), which is an augmentation to the Discourse Annotation and Markup System of Labeling (DAMSL) tagset. The 220 tags were reduced to 42 tags by clustering in order to improve the language model on the Switchboard corpus. A subset of the Switchboard-1 corpus consisting of 1155 conversations was used. The resulting tags include dialogue acts like statement-non-opinion, acknowledge, statement-opinion, agree/accept, etc.\nAnnotated example:\nSpeaker: A, Dialogue Act: Yes-No-Question, Utterance: So do you go to college right now?  \n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "81.3"}, "model_links": [], "model_name": "CRF-ASN (2018)", "paper_date": null, "paper_title": "Dialogue Act Recognition via CRF-Attentive Structured Network", "paper_url": "https://arxiv.org/abs/1711.05568", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "79.2"}, "model_links": [], "model_name": "Bi-LSTM-CRF (2017)", "paper_date": null, "paper_title": "Dialogue Act Sequence Labeling using Hierarchical encoder with CRF", "paper_url": "https://arxiv.org/abs/1709.04250", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "77.34"}, "model_links": [], "model_name": "RNN with 3 utterances in context (2018)", "paper_date": null, "paper_title": "A Context-based Approach for Dialogue Act Recognition using Simple Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1805.06280", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "ICSI Meeting Recorder Dialog Act (MRDA) corpus", "dataset_citations": [], "dataset_links": [{"title": "MRDA corpus", "url": "http://www1.icsi.berkeley.edu/Speech/mr/"}, {"title": "download", "url": "http://www.icsi.berkeley.edu/~ees/dadb/icsi_mrda+hs_corpus_050512.tar.gz"}], "description": "The [MRDA corpus](http://www1.icsi.berkeley.edu/Speech/mr/) [[download](http://www.icsi.berkeley.edu/~ees/dadb/icsi_mrda+hs_corpus_050512.tar.gz)] consists of about 75 hours of speech from 75 naturally-occurring meetings among 53 speakers. The tagset used for labeling is a modified version of the SWBD-DAMSL tagset. It is annotated with three types of information: marking of the dialogue act segment boundaries, marking of the dialogue acts and marking of correspondences between dialogue acts. \nAnnotated example:\nTime: 2804-2810, Speaker: c6, Dialogue Act: s^bd, Transcript: i mean these are just discriminative.\nMultiple dialogue acts are separated by \"^\".\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "91.7"}, "model_links": [], "model_name": "CRF-ASN (2018)", "paper_date": null, "paper_title": "Dialogue Act Recognition via CRF-Attentive Structured Network", "paper_url": "https://arxiv.org/abs/1711.05568", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "90.9"}, "model_links": [], "model_name": "Bi-LSTM-CRF (2017)", "paper_date": null, "paper_title": "Dialogue Act Sequence Labeling using Hierarchical encoder with CRF", "paper_url": "https://arxiv.org/abs/1709.04250", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Dialogue act classification is the task of classifying an utterance with respect to the function it serves in a dialogue, i.e. the act the speaker is performing. Dialogue acts are a type of speech acts (for Speech Act Theory, see [Austin (1975)](http://www.hup.harvard.edu/catalog.php?isbn=9780674411524) and [<PERSON><PERSON> (1969)](https://www.cambridge.org/core/books/speech-acts/D2D7B03E472C8A390ED60B86E08640E7)).\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Dialogue Act Classification"}, {"categories": [], "datasets": [{"dataset": "Second dialogue state tracking challenge", "dataset_citations": [], "dataset_links": [{"title": "second Dialogue Systems Technology Challenges", "url": "http://www.aclweb.org/anthology/W14-4337"}], "description": "For goal-oriented dialogue, the dataset of the [second Dialogue Systems Technology Challenges](http://www.aclweb.org/anthology/W14-4337)\n(DSTC2) is a common evaluation dataset. The DSTC2 focuses on the restaurant search domain. Models are\nevaluated based on accuracy on both individual and joint slot tracking.\n", "sota": {"metrics": ["Request", "Area", "Food", "Price", "Joint"], "rows": [{"code_links": [], "metrics": {"Area": "-", "Food": "-", "Joint": "74.5", "Price": "-", "Request": "97.5"}, "model_links": [], "model_name": "<PERSON><PERSON> et al. (2018)", "paper_date": null, "paper_title": "Global-locally Self-attentive Dialogue State Tracker", "paper_url": "https://arxiv.org/abs/1805.09655", "uses_additional_data": false}, {"code_links": [], "metrics": {"Area": "90", "Food": "84", "Joint": "72", "Price": "92", "Request": "-"}, "model_links": [], "model_name": "<PERSON> et al. (2018)", "paper_date": null, "paper_title": "Dialogue Learning with Human Teaching and Feedback in End-to-End Trainable Task-Oriented Dialogue Systems", "paper_url": "https://arxiv.org/abs/1804.06512", "uses_additional_data": false}, {"code_links": [], "metrics": {"Area": "90", "Food": "84", "Joint": "73.4", "Price": "94", "Request": "96.5"}, "model_links": [], "model_name": "Neural belief tracker (2017)", "paper_date": null, "paper_title": "Neural Belief Tracker: Data-Driven Dialogue State Tracking", "paper_url": "https://arxiv.org/abs/1606.03777", "uses_additional_data": false}, {"code_links": [], "metrics": {"Area": "92", "Food": "86", "Joint": "69", "Price": "86", "Request": "95.7"}, "model_links": [], "model_name": "RNN (2014)", "paper_date": null, "paper_title": "Robust dialog state tracking using delexicalised recurrent neural networks and unsupervised gate", "paper_url": "http://svr-ftp.eng.cam.ac.uk/~sjy/papers/htyo14.pdf", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Wizard-of-Oz", "dataset_citations": [], "dataset_links": [{"title": "WoZ 2.0 dataset", "url": "https://arxiv.org/pdf/1606.03777.pdf"}], "description": "The [WoZ 2.0 dataset](https://arxiv.org/pdf/1606.03777.pdf) is a newer dialogue state tracking dataset whose evaluation is detached from the noisy output of speech recognition systems. Similar to DSTC2, it covers the restaurant search domain and has identical evaluation.\n", "sota": {"metrics": ["Request", "Joint"], "rows": [{"code_links": [], "metrics": {"Joint": "88.1", "Request": "97.1"}, "model_links": [], "model_name": "<PERSON><PERSON> et al. (2018)", "paper_date": null, "paper_title": "Global-locally Self-attentive Dialogue State Tracker", "paper_url": "https://arxiv.org/abs/1805.09655", "uses_additional_data": false}, {"code_links": [], "metrics": {"Joint": "84.4", "Request": "96.5"}, "model_links": [], "model_name": "Neural belief tracker (2017)", "paper_date": null, "paper_title": "Neural Belief Tracker: Data-Driven Dialogue State Tracking", "paper_url": "https://arxiv.org/abs/1606.03777", "uses_additional_data": false}, {"code_links": [], "metrics": {"Joint": "70.8", "Request": "87.1"}, "model_links": [], "model_name": "RNN (2014)", "paper_date": null, "paper_title": "Robust dialog state tracking using delexicalised recurrent neural networks and unsupervised gate", "paper_url": "http://svr-ftp.eng.cam.ac.uk/~sjy/papers/htyo14.pdf", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "MultiWOZ", "dataset_citations": [], "dataset_links": [{"title": "MultiWOZ dataset", "url": "https://arxiv.org/abs/1810.00278"}], "description": "The [MultiWOZ dataset](https://arxiv.org/abs/1810.00278) is a fully-labeled collection of human-human written conversations spanning over multiple domains and topics. At a size of 10k dialogues, it is at least one order of magnitude larger than all previous annotated task-oriented corpora. The dialogue are set between a tourist and a clerk in the information. It spans over 7 domains.\n", "sota": {"metrics": ["Joint", "Slot"], "rows": [{"code_links": [], "metrics": {"Joint": "15.57", "Slot": "89.53"}, "model_links": [], "model_name": "<PERSON><PERSON> et al. (2018)", "paper_date": null, "paper_title": "Large-Scale Multi-Domain Belief Tracking with Knowledge Sharing", "paper_url": "https://www.aclweb.org/anthology/P18-2069", "uses_additional_data": false}, {"code_links": [], "metrics": {"Joint": "35.57", "Slot": "95.44"}, "model_links": [], "model_name": "<PERSON><PERSON> et al. (2018)", "paper_date": null, "paper_title": "Global-locally Self-attentive Dialogue State Tracker", "paper_url": "https://arxiv.org/abs/1805.09655", "uses_additional_data": false}, {"code_links": [], "metrics": {"Joint": "36.27", "Slot": "98.42"}, "model_links": [], "model_name": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>-<PERSON><PERSON> (2019)", "paper_date": null, "paper_title": "Toward Scalable Neural Dialogue State Tracking Model", "paper_url": "https://arxiv.org/pdf/1812.00899.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Joint": "48.62", "Slot": "96.92"}, "model_links": [], "model_name": "<PERSON> et al. (2019)", "paper_date": null, "paper_title": "Transferable Multi-Domain State Generator for Task-OrientedDialogue System", "paper_url": "https://arxiv.org/pdf/1905.08743.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Dialogue state tacking consists of determining at each turn of a dialogue the\nfull representation of what the user wants at that point in the dialogue,\nwhich contains a goal constraint, a set of requested slots, and the user's dialogue act.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Dialogue State Tracking"}, {"categories": [], "datasets": [{"dataset": "Reddit Corpus", "dataset_citations": [], "dataset_links": [{"title": "Reddit Corpus", "url": "https://arxiv.org/abs/1904.06472"}, {"title": "here", "url": "https://github.com/PolyAI-LDN/conversational-datasets"}], "description": "The [Reddit Corpus](https://arxiv.org/abs/1904.06472) contains 726 million multi-turn dialogues from the Reddit board. Reddit  is an American social news aggregation website, where users can post links, and take partin discussions on these post. The task of Reddit Corpus is to select the correct response from 100 candidates (others are negatively sampled) by considering previous conversation history.  Models are evaluated with the Recall 1 at 100 metric (the 1-of-100 ranking accuracy). You can find more details at [here](https://github.com/PolyAI-LDN/conversational-datasets).\n", "sota": {"metrics": ["R_1@100"], "rows": [{"code_links": [], "metrics": {"R_1@100": "61.3"}, "model_links": [], "model_name": "PolyAI Encoder (2019)", "paper_date": null, "paper_title": "A Repository of Conversational Dataset", "paper_url": "https://arxiv.org/pdf/1904.06472.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"R_1@100": "47.7"}, "model_links": [], "model_name": "USE (2018)", "paper_date": null, "paper_title": "Universal Sentence Encoder", "paper_url": "https://arxiv.org/abs/1803.11175", "uses_additional_data": false}, {"code_links": [], "metrics": {"R_1@100": "24.0"}, "model_links": [], "model_name": "BERT (2017)", "paper_date": null, "paper_title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding", "paper_url": "https://arxiv.org/abs/1810.04805", "uses_additional_data": false}, {"code_links": [], "metrics": {"R_1@100": "19.3"}, "model_links": [], "model_name": "ELMO (2018)", "paper_date": null, "paper_title": "Deep contextualized word representations", "paper_url": "https://arxiv.org/abs/1802.05365", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Advising Corpus", "dataset_citations": [], "dataset_links": [{"title": "Advising Corpus", "url": "http://workshop.colips.org/dstc7/papers/dstc7_task1_final_report.pdf"}, {"title": "here", "url": "https://ibm.github.io/dstc-noesis/public/index.html"}], "description": "The [Advising Corpus](http://workshop.colips.org/dstc7/papers/dstc7_task1_final_report.pdf), available [here](https://ibm.github.io/dstc-noesis/public/index.html), contains a collection of conversations between a student and an advisor at the University of Michigan. They were released as part of DSTC 7 track 1 and used again in DSTC 8 track 2.\n", "sota": {"metrics": ["R_100@1", "R_100@10", "R_100@50", "MRR"], "rows": [{"code_links": [], "metrics": {"MRR": "67.7", "R_100@1": "56.4", "R_100@10": "87.8", "R_100@50": "-"}, "model_links": [], "model_name": "<PERSON> et. al., (2020)", "paper_date": null, "paper_title": "", "paper_url": "", "uses_additional_data": false}, {"code_links": [], "metrics": {"MRR": "33.9", "R_100@1": "21.4", "R_100@10": "63.0", "R_100@50": "94.8"}, "model_links": [], "model_name": "Seq-Att-Network (2019)", "paper_date": null, "paper_title": "Sequential Attention-based Network for Noetic End-to-End Response Selection", "paper_url": "http://workshop.colips.org/dstc7/papers/07.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "These systems take as input a context and a list of possible responses and rank the responses, returning the highest ranking one.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Retrieval-Based Chatbots"}, {"categories": [], "datasets": [{"dataset": "Personalized Chit-chat", "dataset_citations": [], "dataset_links": [{"title": "PersonaChat", "url": "https://arxiv.org/pdf/1801.07243.pdf"}, {"title": "The Conversational Intelligence Challenge 2 (ConvAI2)", "url": "http://convai.io/"}, {"title": "ConvAI2 Leaderboard", "url": "https://github.com/DeepPavlov/convai/blob/master/leaderboards.md"}], "description": "The task of persinalized chit-chat dialogue generation is first proposed by [PersonaChat](https://arxiv.org/pdf/1801.07243.pdf). The motivation is to enhance the engagingness and consistency of chit-chat bots via endowing explicit personas to agents. Here the persona is defined as several profile natural language sentences like \"I weight 300 pounds.\". NIPS 2018 has hold a competition [The Conversational Intelligence Challenge 2 (ConvAI2)](http://convai.io/) based on the dataset. The Evaluation metric is F1, Hits@1 and ppl. F1 evaluates on the word-level, and Hits@1 represents the probability of the real next utterance ranking the highest according to the model, while ppl is perplexity for language modeling. The following results are reported on dev set (test set is still hidden), almost of them are borrowed from [ConvAI2 Leaderboard](https://github.com/DeepPavlov/convai/blob/master/leaderboards.md).\n", "sota": {"metrics": ["F1", "Hits@1", "ppl"], "rows": [{"code_links": [], "metrics": {"F1": "19.77", "Hits@1": "81.9", "ppl": "15.12"}, "model_links": [], "model_name": "P^2 Bot (2020)", "paper_date": null, "paper_title": "You Impress Me: Dialogue Generation via Mutual Persona Perception", "paper_url": "https://arxiv.org/pdf/2004.05388.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "19.09", "Hits@1": "82.1", "ppl": "17.51"}, "model_links": [], "model_name": "TransferTransfo (2019)", "paper_date": null, "paper_title": "TransferTransfo: A Transfer Learning Approach for Neural Network Based Conversational Agents", "paper_url": "https://arxiv.org/pdf/1901.08149.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "17.79", "Hits@1": "-", "ppl": "17.3"}, "model_links": [], "model_name": "Lost In Conversation", "paper_date": null, "paper_title": "NIPS 2018 Workshop Presentation", "paper_url": "http://convai.io/NeurIPSParticipantSlides.pptx", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "16.18", "Hits@1": "12.6", "ppl": "29.8"}, "model_links": [], "model_name": "Seq2Seq + Attention (2014)", "paper_date": null, "paper_title": "Neural Machine Translation by Jointly Learning to Align and Translate", "paper_url": "https://arxiv.org/pdf/1409.0473.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "11.9", "Hits@1": "55.2", "ppl": "-"}, "model_links": [], "model_name": "KV Profile Memory (2018)", "paper_date": null, "paper_title": "Personalizing Dialogue Agents: I have a dog, do you have pets too?", "paper_url": "https://arxiv.org/pdf/1801.07243.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "The main task of generative-based chatbot is to generate consistent and engaging response given the context.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Generative-Based Chatbots"}, {"categories": [], "datasets": [{"dataset": "Ubuntu IRC", "dataset_citations": [], "dataset_links": [{"title": "<PERSON><PERSON><PERSON> et al. (2019)", "url": "https://www.aclweb.org/anthology/P19-1374"}, {"title": "here", "url": "https://jkk.name/irc-disentanglement/"}], "description": "Manually labeled by [<PERSON><PERSON><PERSON> et al. (2019)](https://www.aclweb.org/anthology/P19-1374), this data is available [here](https://jkk.name/irc-disentanglement/).\n", "sota": {"metrics": ["VI", "1-1", "Precision", "Recall", "F-Score"], "rows": [{"code_links": [], "metrics": {"1-1": "-", "F-Score": "46.8", "Precision": "44.3", "Recall": "49.6", "VI": "93.3"}, "model_links": [], "model_name": "BERT + BiLSTM", "paper_date": null, "paper_title": "", "paper_url": "", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "76.0", "F-Score": "38.0", "Precision": "36.3", "Recall": "39.7", "VI": "91.5"}, "model_links": [], "model_name": "FF ensemble: <PERSON><PERSON> (2019)", "paper_date": null, "paper_title": "A Large-Scale Corpus for Conversation Disentanglement", "paper_url": "https://www.aclweb.org/anthology/P19-1374/", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "75.6", "F-Score": "36.2", "Precision": "34.6", "Recall": "38.0", "VI": "91.3"}, "model_links": [], "model_name": "Feed<PERSON><PERSON><PERSON> (2019)", "paper_date": null, "paper_title": "A Large-Scale Corpus for Conversation Disentanglement", "paper_url": "https://www.aclweb.org/anthology/P19-1374/", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "26.6", "F-Score": "32.1", "Precision": "67.0", "Recall": "21.1", "VI": "69.3"}, "model_links": [], "model_name": "FF ensemble: Intersect (2019)", "paper_date": null, "paper_title": "A Large-Scale Corpus for Conversation Disentanglement", "paper_url": "https://www.aclweb.org/anthology/P19-1374/", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "51.4", "F-Score": "15.5", "Precision": "12.1", "Recall": "21.5", "VI": "82.1"}, "model_links": [], "model_name": "Linear (2008)", "paper_date": null, "paper_title": "You Talking to Me? A Corpus and Algorithm for Conversation Disentanglement", "paper_url": "https://www.aclweb.org/anthology/P08-1095/", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "53.7", "F-Score": "8.9", "Precision": "10.8", "Recall": "7.6", "VI": "80.6"}, "model_links": [], "model_name": "Heuristic (2015)", "paper_date": null, "paper_title": "Training End-to-End Dialogue Systems with the Ubuntu Dialogue Corpus", "paper_url": "http://dad.uni-bielefeld.de/index.php/dad/article/view/3698", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Linux IRC", "dataset_citations": [], "dataset_links": [], "description": "This data has been manually annotated three times:\n", "sota": {"metrics": ["Data", "1-1", "Local", "Shen F-1"], "rows": [{"code_links": [], "metrics": {"1-1": "59.7", "Data": "Kummerfeld", "Local": "80.8", "Shen F-1": "63.0"}, "model_links": [], "model_name": "Linear (2008)", "paper_date": null, "paper_title": "You Talking to Me? A Corpus and Algorithm for Conversation Disentanglement", "paper_url": "https://www.aclweb.org/anthology/P08-1095/", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "57.7", "Data": "Kummerfeld", "Local": "80.3", "Shen F-1": "59.8"}, "model_links": [], "model_name": "Feed<PERSON><PERSON><PERSON> (2019)", "paper_date": null, "paper_title": "A Large-Scale Corpus for Conversation Disentanglement", "paper_url": "https://www.aclweb.org/anthology/P19-1374/", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "43.4", "Data": "Kummerfeld", "Local": "67.9", "Shen F-1": "50.7"}, "model_links": [], "model_name": "Heuristic (2015)", "paper_date": null, "paper_title": "Training End-to-End Dialogue Systems with the Ubuntu Dialogue Corpus", "paper_url": "http://dad.uni-bielefeld.de/index.php/dad/article/view/3698", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "53.1", "Data": "<PERSON><PERSON>", "Local": "81.9", "Shen F-1": "55.1"}, "model_links": [], "model_name": "Linear (2008)", "paper_date": null, "paper_title": "You Talking to Me? A Corpus and Algorithm for Conversation Disentanglement", "paper_url": "https://www.aclweb.org/anthology/P08-1095/", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "52.1", "Data": "<PERSON><PERSON>", "Local": "77.8", "Shen F-1": "53.8"}, "model_links": [], "model_name": "Feed<PERSON><PERSON><PERSON> (2019)", "paper_date": null, "paper_title": "A Large-Scale Corpus for Conversation Disentanglement", "paper_url": "https://www.aclweb.org/anthology/P19-1374/", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "47.0", "Data": "<PERSON><PERSON>", "Local": "75.1", "Shen F-1": "52.8"}, "model_links": [], "model_name": "<PERSON> and <PERSON> (2009)", "paper_date": null, "paper_title": "Context-based Message Expansion for Disentanglement of Interleaved Text Conversations", "paper_url": "https://www.aclweb.org/anthology/N09-1023/", "uses_additional_data": false}, {"code_links": [], "metrics": {"1-1": "45.1", "Data": "<PERSON><PERSON>", "Local": "73.8", "Shen F-1": "51.8"}, "model_links": [], "model_name": "Heuristic (2015)", "paper_date": null, "paper_title": "Training End-to-End Dialogue Systems with the Ubuntu Dialogue Corpus", "paper_url": "http://dad.uni-bielefeld.de/index.php/dad/article/view/3698", "uses_additional_data": false}]}, "subdatasets": []}], "description": "As noted for the Ubuntu data above, sometimes multiple conversations are mixed together in a single channel. Work on conversation disentanglement aims to separate out conversations. There are two main resources for the task.\nThis can be formultated as a clustering problem, with no clear best metric. Several metrics are considered:\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Disentanglement"}], "synonyms": [], "task": "Dialogue"}, {"categories": [], "datasets": [], "description": "Shallow syntactic tasks provide an analysis of a text on the level of the syntactic structure \nof the text.\n", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "Penn Treebank", "dataset_citations": [], "dataset_links": [{"title": "Penn Treebank", "url": "https://catalog.ldc.upenn.edu/LDC99T42"}], "description": "The [Penn Treebank](https://catalog.ldc.upenn.edu/LDC99T42) is typically used for evaluating chunking.\nSections 15-18 are used for training, section 19 for development, and and section 20\nfor testing. Models are evaluated based on F1.\n", "sota": {"metrics": ["F1 score"], "rows": [{"code_links": [], "metrics": {"F1 score": "96.72"}, "model_links": [], "model_name": "<PERSON>lair embeddings (2018)", "paper_date": null, "paper_title": "Contextual String Embeddings for Sequence Labeling", "paper_url": "http://aclweb.org/anthology/C18-1139", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "95.77"}, "model_links": [], "model_name": "JMT (2017)", "paper_date": null, "paper_title": "A Joint Many-Task Model: Growing a Neural Network for Multiple NLP Tasks", "paper_url": "https://www.aclweb.org/anthology/D17-1206", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "95.57"}, "model_links": [], "model_name": "Low supervision (2016)", "paper_date": null, "paper_title": "Deep multi-task learning with low level tasks supervised at lower layers", "paper_url": "http://anthology.aclweb.org/P16-2038", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "95.15"}, "model_links": [], "model_name": "<PERSON> and <PERSON><PERSON><PERSON> (2008)", "paper_date": null, "paper_title": "Semi-Supervised Sequential Labeling and Segmentation using Giga-word Scale Unlabeled Data", "paper_url": "https://aclanthology.info/pdf/P/P08/P08-1076.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "95.06"}, "model_links": [], "model_name": "NCRF++ (2018)", "paper_date": null, "paper_title": "NCRF++: An Open-source Neural Sequence Labeling Toolkit", "paper_url": "http://www.aclweb.org/anthology/P18-4013", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Chunking, also known as shallow parsing, identifies continuous spans of tokens that form syntactic units such as noun phrases or verb phrases.\nExample:\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Chunking"}], "synonyms": [], "task": "Shallow Syntax"}, {"categories": [], "datasets": [{"dataset": "CoNLL 2012", "dataset_citations": [], "dataset_links": [{"title": "CoNLL-2012 shared task", "url": "http://www.aclweb.org/anthology/W12-4501"}], "description": "Experiments are conducted on the data of the [CoNLL-2012 shared task](http://www.aclweb.org/anthology/W12-4501), which\nuses OntoNotes coreference annotations. Papers\nreport the precision, recall, and F1 of the MUC, B3, and CEAFφ4 metrics using the official\nCoNLL-2012 evaluation scripts. The main evaluation metric is the average F1 of the three metrics.\n\u0002wzxhzdk:0\u0003[1]\u0002wzxhzdk:1\u0003 <PERSON><PERSON> et al. (2019): (<PERSON> et al., 2017)+coarse-to-fine & second-order inference (<PERSON> et al., 2018)+SpanBERT (<PERSON><PERSON> et al., 2019)\n\u0002wzxhzdk:2\u0003[2]\u0002wzxhzdk:3\u0003 <PERSON><PERSON> et al. (2019): (<PERSON> et al., 2017)+coarse-to-fine & second-order inference (<PERSON> et al., 2018)+BERT (<PERSON> et al., 2019)\n", "sota": {"metrics": ["Avg F1"], "rows": [{"code_links": [], "metrics": {"Avg F1": "79.6"}, "model_links": [], "model_name": "<PERSON><PERSON> et al. \u0002wzxhzdk:6\u0003 (2019)", "paper_date": null, "paper_title": "SpanBERT: Improving Pre-training by Representing and Predicting Spans", "paper_url": "https://arxiv.org/pdf/1907.10529", "uses_additional_data": false}, {"code_links": [], "metrics": {"Avg F1": "76.9"}, "model_links": [], "model_name": "<PERSON><PERSON> et al. \u0002wzxhzdk:4\u0003 (2019)", "paper_date": null, "paper_title": "BERT for Coreference Resolution: Baselines and Analysis", "paper_url": "https://arxiv.org/abs/1908.09091", "uses_additional_data": false}, {"code_links": [], "metrics": {"Avg F1": "76.6"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON> and <PERSON> (2019)", "paper_date": null, "paper_title": "Coreference Resolution with Entity Equalization", "paper_url": "https://www.aclweb.org/anthology/P19-1066/", "uses_additional_data": false}, {"code_links": [], "metrics": {"Avg F1": "73.8"}, "model_links": [], "model_name": "<PERSON><PERSON> et al. (2019)", "paper_date": null, "paper_title": "End-to-end Deep Reinforcement Learning Based Coreference Resolution", "paper_url": "https://www.aclweb.org/anthology/P19-1064/", "uses_additional_data": false}, {"code_links": [], "metrics": {"Avg F1": "73.0"}, "model_links": [], "model_name": "+ELMo +coarse-to-fine & second-order inference (2017) (2018) (2018)", "paper_date": null, "paper_title": "Higher-order Coreference Resolution with Coarse-to-fine Inference", "paper_url": "http://aclweb.org/anthology/N18-2108", "uses_additional_data": false}, {"code_links": [], "metrics": {"Avg F1": "70.4"}, "model_links": [], "model_name": "+ELMo (2017) (2018)", "paper_date": null, "paper_title": "Deep contextualized word representations", "paper_url": "https://arxiv.org/abs/1802.05365", "uses_additional_data": false}, {"code_links": [], "metrics": {"Avg F1": "67.2"}, "model_links": [], "model_name": "<PERSON> et al. (2017)", "paper_date": null, "paper_title": "End-to-end Neural Coreference Resolution", "paper_url": "https://arxiv.org/abs/1707.07045", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Gendered Ambiguous Pronoun Resolution", "dataset_citations": [], "dataset_links": [{"title": "GAP dataset", "url": "https://github.com/google-research-datasets/gap-coreference"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "Experiments are conducted on [GAP dataset](https://github.com/google-research-datasets/gap-coreference). \nMetrics used are F1 score on Masculine (M) and Feminine (F) examples, Overall, and a Bias factor calculated as F / M.\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["Overall F1", "Masculine F1 (M)", "Feminine F1 (F)", "Bias (F/M)"], "rows": [{"code_links": [], "metrics": {"Bias (F/M)": "0.97", "Feminine F1 (F)": "91.1", "Masculine F1 (M)": "94.0", "Overall F1": "92.5"}, "model_links": [], "model_name": "<PERSON><PERSON> et al. (2019)", "paper_date": null, "paper_title": "Gendered Ambiguous Pronouns Shared Task: Boosting Model Confidence by Evidence Pooling", "paper_url": "https://arxiv.org/abs/1906.00839", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bias (F/M)": "0.98", "Feminine F1 (F)": "89.5", "Masculine F1 (M)": "90.9", "Overall F1": "90.2"}, "model_links": [], "model_name": "<PERSON><PERSON> et al. (2019)", "paper_date": null, "paper_title": "Gendered Pronoun Resolution using BERT and an extractive question answering formulation", "paper_url": "https://arxiv.org/abs/1906.03695", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Coreference resolution is the task of clustering mentions in text that refer to the same underlying real world entities.\nExample:\n+-----------+\n               |           |\nI voted for <PERSON> because he was most aligned with my values\", she said.\n |                                                 |            |\n +-------------------------------------------------+------------+\n\"I\", \"my\", and \"she\" belong to the same cluster and \"<PERSON>\" and \"he\" belong to the same cluster.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Coreference Resolution"}, {"categories": [], "datasets": [{"dataset": "Multi-Domain Sentiment Dataset", "dataset_citations": [], "dataset_links": [{"title": "Multi-Domain Sentiment Dataset", "url": "https://www.cs.jhu.edu/~mdredze/datasets/sentiment/"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "The [Multi-Domain Sentiment Dataset](https://www.cs.jhu.edu/~mdredze/datasets/sentiment/) is a common\nevaluation dataset for domain adaptation for sentiment analysis. It contains product reviews from\nAmazon.com from different product categories, which are treated as distinct domains.\nReviews contain star ratings (1 to 5 stars) that are generally converted into binary labels. Models are\ntypically evaluated on a target domain that is different from the source domain they were trained on, while only\nhaving access to unlabeled examples of the target domain (unsupervised domain adaptation). The evaluation\nmetric is accuracy and scores are averaged across each domain.\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["DVD", "Books", "Electronics", "Kitchen", "Average"], "rows": [{"code_links": [], "metrics": {"Average": "79.15", "Books": "74.86", "DVD": "78.14", "Electronics": "81.45", "Kitchen": "82.14"}, "model_links": [], "model_name": "Multi-task tri-training (2018)", "paper_date": null, "paper_title": "Strong Baselines for Neural Semi-supervised Learning under Domain Shift", "paper_url": "https://arxiv.org/abs/1804.09530", "uses_additional_data": false}, {"code_links": [], "metrics": {"Average": "78.39", "Books": "72.97", "DVD": "76.17", "Electronics": "80.47", "Kitchen": "83.97"}, "model_links": [], "model_name": "Asymmetric tri-training (2017)", "paper_date": null, "paper_title": "Asymmetric Tri-training for Unsupervised Domain Adaptation", "paper_url": "https://arxiv.org/abs/1702.08400", "uses_additional_data": false}, {"code_links": [], "metrics": {"Average": "78.36", "Books": "73.40", "DVD": "76.57", "Electronics": "80.53", "Kitchen": "82.93"}, "model_links": [], "model_name": "VFAE (2015)", "paper_date": null, "paper_title": "The Variational Fair Autoencoder", "paper_url": "https://arxiv.org/abs/1511.00830", "uses_additional_data": false}, {"code_links": [], "metrics": {"Average": "76.26", "Books": "71.43", "DVD": "75.40", "Electronics": "77.67", "Kitchen": "80.53"}, "model_links": [], "model_name": "DANN (2016)", "paper_date": null, "paper_title": "Domain-Adversarial Training of Neural Networks", "paper_url": "https://arxiv.org/abs/1505.07818", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": null, "subtasks": [], "synonyms": [], "task": "Sentiment Analysis"}, {"categories": [], "datasets": [{"dataset": "SUBJ", "dataset_citations": [], "dataset_links": [{"title": "Subjectivity dataset", "url": "http://www.cs.cornell.edu/people/pabo/movie-review-data/"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "[Subjectivity dataset](http://www.cs.cornell.edu/people/pabo/movie-review-data/) includes 5,000 subjective and 5,000 objective processed sentences. \n[Go back to the README](../README.md)\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "95.50"}, "model_links": [], "model_name": "Ada<PERSON>ent (2015)", "paper_date": null, "paper_title": "Self-Adaptive Hierarchical Sentence Model", "paper_url": "https://arxiv.org/pdf/1504.05070.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "94.80"}, "model_links": [], "model_name": "CNN+MCFA (2018)", "paper_date": null, "paper_title": "Translations as Additional Contexts for Sentence Classification", "paper_url": "https://arxiv.org/abs/1806.05516", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "94.60"}, "model_links": [], "model_name": "Byte mLSTM (2017)", "paper_date": null, "paper_title": "Learning to Generate Reviews and Discovering Sentiment", "paper_url": "https://arxiv.org/pdf/1704.01444.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "93.90"}, "model_links": [], "model_name": "USE (2018)", "paper_date": null, "paper_title": "Universal Sentence Encoder", "paper_url": "https://arxiv.org/pdf/1803.11175.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "93.60"}, "model_links": [], "model_name": "Fast Dropout (2013)", "paper_date": null, "paper_title": "Fast Dropout Training", "paper_url": "http://proceedings.mlr.press/v28/wang13a.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "A related task to sentiment analysis is the subjectivity analysis with the goal of labeling an opinion as either subjective or objective.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Subjectivity Analysis"}, {"categories": [], "datasets": [{"dataset": "OntoNotes", "dataset_citations": [], "dataset_links": [{"title": "OntoNotes benchmark", "url": "http://www.aclweb.org/anthology/W13-3516"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "Models are typically evaluated on the [OntoNotes benchmark](http://www.aclweb.org/anthology/W13-3516) based on F1.\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["F1"], "rows": [{"code_links": [], "metrics": {"F1": "85.5"}, "model_links": [], "model_name": "<PERSON> et al., + ELMO (2018)", "paper_date": null, "paper_title": "Jointly Predicting Predicates and Arguments in Neural Semantic Role Labeling", "paper_url": "http://aclweb.org/anthology/P18-2058", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "84.6"}, "model_links": [], "model_name": "+ ELMo (2017) (2018)", "paper_date": null, "paper_title": "Deep contextualized word representations", "paper_url": "https://arxiv.org/abs/1802.05365", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "82.7"}, "model_links": [], "model_name": "<PERSON> et al. (2018)", "paper_date": null, "paper_title": "Deep Semantic Role Labeling with Self-Attention", "paper_url": "https://arxiv.org/abs/1712.01586", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "82.1"}, "model_links": [], "model_name": "<PERSON> et al. (2018)", "paper_date": null, "paper_title": "Jointly Predicting Predicates and Arguments in Neural Semantic Role Labeling", "paper_url": "http://aclweb.org/anthology/P18-2058", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "81.7"}, "model_links": [], "model_name": "<PERSON> et al. (2017)", "paper_date": null, "paper_title": "Deep Semantic Role Labeling: What Works and What’s Next", "paper_url": "http://aclweb.org/anthology/P17-1044", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Semantic role labeling aims to model the predicate-argument structure of a sentence\nand is often described as answering \"Who did what to whom\". BIO notation is typically\nused for semantic role labeling.\nExample:\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Semantic Role Labeling"}, {"categories": [], "datasets": [{"dataset": "Penn Treebank", "dataset_citations": [], "dataset_links": [{"title": "Penn Treebank", "url": "https://catalog.ldc.upenn.edu/LDC99T42"}, {"title": "<PERSON><PERSON><PERSON> and <PERSON> (2018)", "url": "https://arxiv.org/abs/1805.01052"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "The Wall Street Journal section of the [Penn Treebank](https://catalog.ldc.upenn.edu/LDC99T42) is used for\nevaluating constituency parsers. Section 22 is used for development and Section 23 is used for evaluation.\nModels are evaluated based on F1. Most of the below models incorporate external data or features.\nFor a comparison of single models trained only on WSJ, refer to [<PERSON><PERSON><PERSON> and <PERSON> (2018)](https://arxiv.org/abs/1805.01052).\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["F1 score"], "rows": [{"code_links": [], "metrics": {"F1 score": "96.38"}, "model_links": [], "model_name": "Label Attention Layer + HPSG + XLNet (2020)", "paper_date": null, "paper_title": "Rethinking Self-Attention: Towards Interpretability for Neural Parsing", "paper_url": "https://khalilmrini.github.io/Label_Attention_Layer.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "96.33"}, "model_links": [], "model_name": "Head-Driven Phrase Structure Grammar Parsing + XLNet (2019)", "paper_date": null, "paper_title": "Head-Driven Phrase Structure Grammar Parsing on Penn Treebank", "paper_url": "https://arxiv.org/pdf/1907.02684.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "95.84"}, "model_links": [], "model_name": "Head-Driven Phrase Structure Grammar Parsing + BERT (2019)", "paper_date": null, "paper_title": "Head-Driven Phrase Structure Grammar Parsing on Penn Treebank", "paper_url": "https://arxiv.org/pdf/1907.02684.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "95.13"}, "model_links": [], "model_name": "Self-attentive encoder + ELMo (2018)", "paper_date": null, "paper_title": "Constituency Parsing with a Self-Attentive Encoder", "paper_url": "https://arxiv.org/abs/1805.01052", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "94.66"}, "model_links": [], "model_name": "Model combination (2017)", "paper_date": null, "paper_title": "Improving Neural Parsing by Disentangling Model Combination and Reranking Effects", "paper_url": "https://arxiv.org/abs/1707.03058", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "94.47"}, "model_links": [], "model_name": "LSTM Encoder-Decoder + LSTM-LM (2018)", "paper_date": null, "paper_title": "Direct Output Connection for a High-Rank Language Model", "paper_url": "http://aclweb.org/anthology/D18-1489", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "94.32"}, "model_links": [], "model_name": "LSTM Encoder-Decoder + LSTM-LM (2018)", "paper_date": null, "paper_title": "An Empirical Study of Building a Strong Baseline for Constituency Parsing", "paper_url": "http://aclweb.org/anthology/P18-2097", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "94.2"}, "model_links": [], "model_name": "In-order (2017)", "paper_date": null, "paper_title": "In-Order Transition-based Constituent Parsing", "paper_url": "http://aclweb.org/anthology/Q17-1029", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "93.8"}, "model_links": [], "model_name": "Semi-supervised LSTM-LM (2016)", "paper_date": null, "paper_title": "Parsing as Language Modeling", "paper_url": "http://www.aclweb.org/anthology/D16-1257", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "93.6"}, "model_links": [], "model_name": "Stack-only RNNG (2017)", "paper_date": null, "paper_title": "What Do Recurrent Neural Network Grammars Learn About Syntax?", "paper_url": "https://arxiv.org/abs/1611.05774", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "﻿93.3"}, "model_links": [], "model_name": "RNN Grammar (2016)", "paper_date": null, "paper_title": "Recurrent Neural Network Grammars", "paper_url": "https://www.aclweb.org/anthology/N16-1024", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "92.7"}, "model_links": [], "model_name": "Transformer (2017)", "paper_date": null, "paper_title": "Attention Is All You Need", "paper_url": "https://arxiv.org/abs/1706.03762", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "92.4"}, "model_links": [], "model_name": "Combining Constituent Parsers (2009)", "paper_date": null, "paper_title": "Combining constituent parsers via parse selection or parse hybridization", "paper_url": "https://dl.acm.org/citation.cfm?id=1620923", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "92.1"}, "model_links": [], "model_name": "Semi-supervised LSTM (2015)", "paper_date": null, "paper_title": "Grammar as a Foreign Language", "paper_url": "https://papers.nips.cc/paper/5635-grammar-as-a-foreign-language.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "92.1"}, "model_links": [], "model_name": "Self-trained parser (2006)", "paper_date": null, "paper_title": "Effective Self-Training for Parsing", "paper_url": "https://pdfs.semanticscholar.org/6f0f/64f0dab74295e5eb139c160ed79ff262558a.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Constituency parsing aims to extract a constituency-based parse tree from a sentence that\nrepresents its syntactic structure according to a [phrase structure grammar](https://en.wikipedia.org/wiki/Phrase_structure_grammar).\nExample:\n[Recent approaches](https://papers.nips.cc/paper/5635-grammar-as-a-foreign-language.pdf)\nconvert the parse tree into a sequence following a depth-first traversal in order to\nbe able to apply sequence-to-sequence models to it. The linearized version of the\nabove parse tree looks as follows: (S (N) (VP V N)).\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Constituency Parsing"}, {"categories": [], "datasets": [{"dataset": "Supervised", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Senseval 2", "Senseval 3", "SemEval 2007", "SemEval 2013", "SemEval 2015"], "rows": [{"code_links": [], "metrics": {"SemEval 2007": "54.5", "SemEval 2013": "63.8", "SemEval 2015": "67.1", "Senseval 2": "65.6", "Senseval 3": "66.0"}, "model_links": [], "model_name": "MFS baseline", "paper_date": null, "paper_title": "[1]", "paper_url": "http://aclweb.org/anthology/E/E17/E17-1010.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "63.7*", "SemEval 2013": "66.4", "SemEval 2015": "72.4", "Senseval 2": "72.0", "Senseval 3": "69.4"}, "model_links": [], "model_name": "Bi-LSTM\u0002wzxhzdk:12\u0003att+LEX\u0002wzxhzdk:13\u0003", "paper_date": null, "paper_title": "[2]", "paper_url": "http://aclweb.org/anthology/D17-1120", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "64.8*", "SemEval 2013": "66.9", "SemEval 2015": "71.5", "Senseval 2": "72.0", "Senseval 3": "69.1"}, "model_links": [], "model_name": "Bi-LSTM\u0002wzxhzdk:10\u0003att+LEX+POS\u0002wzxhzdk:11\u0003", "paper_date": null, "paper_title": "[2]", "paper_url": "http://aclweb.org/anthology/D17-1120", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "61.3", "SemEval 2013": "65.6", "SemEval 2015": "71.9", "Senseval 2": "71.8", "Senseval 3": "69.1"}, "model_links": [], "model_name": "context2vec", "paper_date": null, "paper_title": "[3]", "paper_url": "http://www.aclweb.org/anthology/K16-1006", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "62.2", "SemEval 2013": "66.2", "SemEval 2015": "71.3", "Senseval 2": "71.6", "Senseval 3": "69.6"}, "model_links": [], "model_name": "ELMo", "paper_date": null, "paper_title": "[4]", "paper_url": "http://aclweb.org/anthology/N18-1202", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "--*", "SemEval 2013": "66.7", "SemEval 2015": "71.6", "Senseval 2": "72.0", "Senseval 3": "70.0"}, "model_links": [], "model_name": "GAS", "paper_date": null, "paper_title": "[5]", "paper_url": "http://aclweb.org/anthology/P18-1230", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "--*", "SemEval 2013": "67", "SemEval 2015": "71.8", "Senseval 2": "72.1", "Senseval 3": "70.2"}, "model_links": [], "model_name": "GAS", "paper_date": null, "paper_title": "[5]", "paper_url": "http://aclweb.org/anthology/P18-1230", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "--*", "SemEval 2013": "67.1", "SemEval 2015": "72.1", "Senseval 2": "72.4", "Senseval 3": "70.1"}, "model_links": [], "model_name": "GAS\u0002wzxhzdk:8\u0003ext\u0002wzxhzdk:9\u0003", "paper_date": null, "paper_title": "[5]", "paper_url": "http://aclweb.org/anthology/P18-1230", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "--*", "SemEval 2013": "67.2", "SemEval 2015": "72.6", "Senseval 2": "72.2", "Senseval 3": "70.5"}, "model_links": [], "model_name": "GAS\u0002wzxhzdk:6\u0003ext\u0002wzxhzdk:7\u0003", "paper_date": null, "paper_title": "[5]", "paper_url": "http://aclweb.org/anthology/P18-1230", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "60.2", "SemEval 2013": "65.8", "SemEval 2015": "70.0", "Senseval 2": "71.3", "Senseval 3": "68.8"}, "model_links": [], "model_name": "supWSD", "paper_date": null, "paper_title": "[6] [11]", "paper_url": "https://aclanthology.info/pdf/P/P10/P10-4014.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "63.1", "SemEval 2013": "66.8", "SemEval 2015": "71.8", "Senseval 2": "72.7", "Senseval 3": "70.6"}, "model_links": [], "model_name": "supWSD\u0002wzxhzdk:4\u0003emb\u0002wzxhzdk:5\u0003", "paper_date": null, "paper_title": "[7] [11]", "paper_url": "http://www.aclweb.org/anthology/P16-1085", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "63.3", "SemEval 2013": "69.2", "SemEval 2015": "74.4", "Senseval 2": "73.8", "Senseval 3": "71.6"}, "model_links": [], "model_name": "BERT", "paper_date": null, "paper_title": "[13] [code]", "paper_url": "https://www.aclweb.org/anthology/D19-1533.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "68.1", "SemEval 2013": "71.1", "SemEval 2015": "76.2", "Senseval 2": "75.5", "Senseval 3": "73.6"}, "model_links": [], "model_name": "BERT", "paper_date": null, "paper_title": "[13] [code]", "paper_url": "https://www.aclweb.org/anthology/D19-1533.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "72.5", "SemEval 2013": "76.1", "SemEval 2015": "80.4", "Senseval 2": "77.7", "Senseval 3": "75.2"}, "model_links": [], "model_name": "GlossBERT", "paper_date": null, "paper_title": "[14]", "paper_url": "https://arxiv.org/pdf/1908.07245.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "73.4", "SemEval 2013": "78.7", "SemEval 2015": "82.6", "Senseval 2": "79.7", "Senseval 3": "77.8"}, "model_links": [], "model_name": "SemCor+WNGC, hypernyms", "paper_date": null, "paper_title": "[15]", "paper_url": "https://arxiv.org/abs/1905.05677", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Knowledge-based", "dataset_citations": [], "dataset_links": [{"title": "Word Sense Disambiguation: A Unified Evaluation Framework and Empirical Comparison", "url": "http://aclweb.org/anthology/E/E17/E17-1010.pdf"}, {"title": "Neural Sequence Learning Models for Word Sense Disambiguation", "url": "http://aclweb.org/anthology/D17-1120"}, {"title": "context2vec: Learning generic context embedding with bidirectional lstm", "url": "http://www.aclweb.org/anthology/K16-1006"}, {"title": "Deep contextualized word representations", "url": "http://aclweb.org/anthology/N18-1202"}, {"title": "Incorporating Glosses into Neural Word Sense Disambiguation", "url": "http://aclweb.org/anthology/P18-1230"}, {"title": "It makes sense: A wide-coverage word sense disambiguation system for free text", "url": "https://aclanthology.info/pdf/P/P10/P10-4014.pdf"}, {"title": "Embeddings for Word Sense Disambiguation: An Evaluation Study", "url": "http://www.aclweb.org/anthology/P16-1085"}, {"title": "Entity Linking meets Word Sense Disambiguation: A Unified Approach", "url": "http://aclweb.org/anthology/Q14-1019"}, {"title": "Random walks for knowledge-based word sense disambiguation", "url": "https://www.mitpressjournals.org/doi/full/10.1162/COLI_a_00164"}, {"title": "Knowledge-based Word Sense Disambiguation using Topic Models", "url": "https://arxiv.org/pdf/1801.01900.pdf"}, {"title": "SupWSD: A Flexible Toolkit for Supervised Word Sense Disambiguation", "url": "http://aclweb.org/anthology/D17-2018"}, {"title": "The risk of sub-optimal use of Open Source NLP Software: UKB is inadvertently state-of-the-art in knowledge-based WSD", "url": "http://aclweb.org/anthology/W18-2505"}, {"title": "Improved Word Sense Disambiguation Using Pre-Trained Contextualized Word Representations", "url": "https://www.aclweb.org/anthology/D19-1533.pdf"}, {"title": "GlossBERT: BERT for Word Sense Disambiguation with Gloss Knowledge", "url": "https://arxiv.org/pdf/1908.07245.pdf"}, {"title": "Sense Vocabulary Compression through the Semantic Knowledge of WordNet for Neural Word Sense Disambiguation", "url": "https://arxiv.org/abs/1905.05677"}, {"title": "Word Sense Disambiguation: A Comprehensive Knowledge Exploitation Framework", "url": "https://doi.org/10.1016/j.knosys.2019.105030"}], "description": "Note: 'All' is the concatenation of all datasets, as described in [10] and [12]. The scores of [6,7] and [9] are not taken from the original papers but from the results of the implementations of [11] and [12], respectively.\n[1] [Word Sense Disambiguation: A Unified Evaluation Framework and Empirical Comparison](http://aclweb.org/anthology/E/E17/E17-1010.pdf)\n[2] [Neural Sequence Learning Models for Word Sense Disambiguation](http://aclweb.org/anthology/D17-1120)\n[3] [context2vec: Learning generic context embedding with bidirectional lstm](http://www.aclweb.org/anthology/K16-1006)\n[4] [Deep contextualized word representations](http://aclweb.org/anthology/N18-1202)\n[5] [Incorporating Glosses into Neural Word Sense Disambiguation](http://aclweb.org/anthology/P18-1230)\n[6] [It makes sense: A wide-coverage word sense disambiguation system for free text](https://aclanthology.info/pdf/P/P10/P10-4014.pdf)\n[7] [Embeddings for Word Sense Disambiguation: An Evaluation Study](http://www.aclweb.org/anthology/P16-1085)\n[8] [Entity Linking meets Word Sense Disambiguation: A Unified Approach](http://aclweb.org/anthology/Q14-1019)\n[9] [Random walks for knowledge-based word sense disambiguation](https://www.mitpressjournals.org/doi/full/10.1162/COLI_a_00164)\n[10] [Knowledge-based Word Sense Disambiguation using Topic Models](https://arxiv.org/pdf/1801.01900.pdf)\n[11] [SupWSD: A Flexible Toolkit for Supervised Word Sense Disambiguation](http://aclweb.org/anthology/D17-2018)\n[12] [The risk of sub-optimal use of Open Source NLP Software: UKB is inadvertently state-of-the-art in knowledge-based WSD](http://aclweb.org/anthology/W18-2505)\n[13] [Improved Word Sense Disambiguation Using Pre-Trained Contextualized Word Representations](https://www.aclweb.org/anthology/D19-1533.pdf)\n[14] [GlossBERT: BERT for Word Sense Disambiguation with Gloss Knowledge](https://arxiv.org/pdf/1908.07245.pdf)\n[15] [Sense Vocabulary Compression through the Semantic Knowledge of WordNet for Neural Word Sense Disambiguation](https://arxiv.org/abs/1905.05677)\n[16] [Word Sense Disambiguation: A Comprehensive Knowledge Exploitation Framework](https://doi.org/10.1016/j.knosys.2019.105030)\n", "sota": {"metrics": ["All", "Senseval 2", "Senseval 3", "SemEval 2007", "SemEval 2013", "SemEval 2015"], "rows": [{"code_links": [], "metrics": {"All": "65.2", "SemEval 2007": "55.2", "SemEval 2013": "63.0", "SemEval 2015": "67.8", "Senseval 2": "66.8", "Senseval 3": "66.2"}, "model_links": [], "model_name": "WN 1st sense baseline", "paper_date": null, "paper_title": "[1]", "paper_url": "http://aclweb.org/anthology/E/E17/E17-1010.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"All": "65.5", "SemEval 2007": "51.6", "SemEval 2013": "66.4", "SemEval 2015": "70.3", "Senseval 2": "67.0", "Senseval 3": "63.5"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON>", "paper_date": null, "paper_title": "[8]", "paper_url": "http://aclweb.org/anthology/Q14-1019", "uses_additional_data": false}, {"code_links": [], "metrics": {"All": "57.5", "SemEval 2007": "40.0", "SemEval 2013": "64.5", "SemEval 2015": "64.5", "Senseval 2": "64.2", "Senseval 3": "54.8"}, "model_links": [], "model_name": "UKB\u0002wzxhzdk:2\u0003ppr_w2w-nf\u0002wzxhzdk:3\u0003", "paper_date": null, "paper_title": "[9] [12]", "paper_url": "https://www.mitpressjournals.org/doi/full/10.1162/COLI_a_00164", "uses_additional_data": false}, {"code_links": [], "metrics": {"All": "67.3", "SemEval 2007": "53.0", "SemEval 2013": "", "SemEval 2015": "70.3", "Senseval 2": "68.8", "Senseval 3": "66.1"}, "model_links": [], "model_name": "UKB\u0002wzxhzdk:0\u0003ppr_w2w\u0002wzxhzdk:1\u0003", "paper_date": null, "paper_title": "[9] [12]", "paper_url": "https://www.mitpressjournals.org/doi/full/10.1162/COLI_a_00164", "uses_additional_data": false}, {"code_links": [], "metrics": {"All": "66.9", "SemEval 2007": "55.6", "SemEval 2013": "65.3", "SemEval 2015": "69.6", "Senseval 2": "69.0", "Senseval 3": ""}, "model_links": [], "model_name": "WSD-TM", "paper_date": null, "paper_title": "[10]", "paper_url": "https://arxiv.org/pdf/1801.01900.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"All": "", "SemEval 2007": "", "SemEval 2013": "68.4", "SemEval 2015": "", "Senseval 2": "", "Senseval 3": "66.1"}, "model_links": [], "model_name": "KEF", "paper_date": null, "paper_title": "[16] [code]", "paper_url": "https://doi.org/10.1016/j.knosys.2019.105030", "uses_additional_data": false}]}, "subdatasets": []}], "description": "The task of Word Sense Disambiguation (WSD) consists of associating words in context with their most suitable entry in a pre-defined sense inventory. The de-facto sense inventory for English in WSD is [WordNet](https://wordnet.princeton.edu).\nFor example, given the word “mouse” and the following sentence:\n“A mouse consists of an object held in one's hand, with one or more buttons.” \nwe would assign “mouse”  with its electronic device sense ([the 4th sense in the WordNet sense inventory](http://wordnetweb.princeton.edu/perl/webwn?c=8&sub=Change&o2=&o0=1&o8=1&o1=1&o7=&o5=&o9=&o6=&o3=&o4=&i=-1&h=000000&s=mouse)).\n", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "Lexical Sample results", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Senseval 2", "Senseval 3", "SemEval 2007"], "rows": [{"code_links": [], "metrics": {"SemEval 2007": "-", "Senseval 2": "71.4", "Senseval 3": "76.2"}, "model_links": [], "model_name": "IMSE + heuristics", "paper_date": null, "paper_title": "[Preprint] [2]", "paper_url": "http://cv.znu.ac.ir/afsharchim/pub/JofIFS2019-2.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "89.4", "Senseval 2": "69.9", "Senseval 3": "75.2"}, "model_links": [], "model_name": "IMS + Word2vec", "paper_date": null, "paper_title": "[1]", "paper_url": "http://www.aclweb.org/anthology/P16-1085", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "−", "Senseval 2": "66.5", "Senseval 3": "73.6"}, "model_links": [], "model_name": "AutoExtend", "paper_date": null, "paper_title": "[3] [4]", "paper_url": "https://arxiv.org/abs/1507.01127", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "−", "Senseval 2": "66.2", "Senseval 3": "73.4"}, "model_links": [], "model_name": "Taghipour and Ng", "paper_date": null, "paper_title": "[4]", "paper_url": "https://www.aclweb.org/anthology/N15-1035.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"SemEval 2007": "87.9", "Senseval 2": "65.3", "Senseval 3": "72.9"}, "model_links": [], "model_name": "IMS", "paper_date": null, "paper_title": "[6]", "paper_url": "https://www.aclweb.org/anthology/P10-4014.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Above task is called All-words WSD because the systems attempt to disambiguate all of the words in a document, while there is another task which is called \nLexical Sample task. In this task a number of words are selected and the system should only disambiguate the occurrences of these words in a test set. \n<PERSON><PERSON><PERSON><PERSON><PERSON> et, al. (2016) provide the state-of-the-art results until 2016 [1]. Main tasks include Senseval 2, Senseval 3  and SemEval 2007. Evaluation metrics are as same as All words task. \n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Wsd Lexical Sample Task:"}, {"categories": [], "datasets": [{"dataset": "SemEval 2010", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["F-S", "V-M", "AVG"], "rows": [{"code_links": [], "metrics": {"AVG": "53.6", "F-S": "71.3", "V-M": "40.4"}, "model_links": [], "model_name": "BERT+DP (2019)", "paper_date": null, "paper_title": "Towards better substitution-based word sense induction", "paper_url": "https://arxiv.org/pdf/1905.12598.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"AVG": "24.59", "F-S": "61.7", "V-M": "9.8"}, "model_links": [], "model_name": "AutoSense (2019)", "paper_date": null, "paper_title": "AutoSense Model for Word Sense Induction", "paper_url": "https://wvvw.aaai.org/ojs/index.php/AAAI/article/view/4580/4458", "uses_additional_data": false}, {"code_links": [], "metrics": {"AVG": "23.24", "F-S": "55.1", "V-M": "9.8"}, "model_links": [], "model_name": "SE-WSI-fix (2016)", "paper_date": null, "paper_title": "Sense Embedding Learning for Word Sense Induction", "paper_url": "https://aclweb.org/anthology/S16-2009/", "uses_additional_data": false}, {"code_links": [], "metrics": {"AVG": "22.23", "F-S": "23.1", "V-M": "21.4"}, "model_links": [], "model_name": "BNP-HC (2014)", "paper_date": null, "paper_title": "Inducing Word Sense with Automatically Learned Hidden Concepts", "paper_url": "https://www.aclweb.org/anthology/C14-1035/", "uses_additional_data": false}, {"code_links": [], "metrics": {"AVG": "16.34", "F-S": "60.7", "V-M": "4.4"}, "model_links": [], "model_name": "LDA (2014)", "paper_date": null, "paper_title": "Unsupervised Word Sense Induction using Distributional Statistics", "paper_url": "https://www.aclweb.org/anthology/C14-1123/", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "SemEval 2013", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["F-BC", "F_NMI", "AVG"], "rows": [{"code_links": [], "metrics": {"AVG": "37.0", "F-BC": "64.0", "F_NMI": "21.4"}, "model_links": [], "model_name": "BERT+DP (2019)", "paper_date": null, "paper_title": "Towards better substitution-based word sense induction", "paper_url": "https://arxiv.org/pdf/1905.12598.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"AVG": "25.4", "F-BC": "57.5", "F_NMI": "11.3"}, "model_links": [], "model_name": "LSDP (2018)", "paper_date": null, "paper_title": "Word Sense Induction with Neural biLM and Symmetric Patterns", "paper_url": "https://www.aclweb.org/anthology/D18-1523/", "uses_additional_data": false}, {"code_links": [], "metrics": {"AVG": "22.16", "F-BC": "61.7", "F_NMI": "7.96"}, "model_links": [], "model_name": "AutoSense (2019)", "paper_date": null, "paper_title": "AutoSense Model for Word Sense Induction", "paper_url": "https://wvvw.aaai.org/ojs/index.php/AAAI/article/view/4580/4458", "uses_additional_data": false}, {"code_links": [], "metrics": {"AVG": "20.58", "F-BC": "55.6", "F_NMI": "7.62"}, "model_links": [], "model_name": "MCC-S (2016)", "paper_date": null, "paper_title": "Structured Generative Models of Continuous Features for Word Sense Induction", "paper_url": "https://www.aclweb.org/anthology/C16-1337/", "uses_additional_data": false}, {"code_links": [], "metrics": {"AVG": "19.89", "F-BC": "55.4", "F_NMI": "7.14"}, "model_links": [], "model_name": "STM+w2v (2016)", "paper_date": null, "paper_title": "A Sense-Topic Model for Word Sense Induction with Unsupervised Data Enrichment", "paper_url": "https://www.aclweb.org/anthology/Q15-1005/", "uses_additional_data": false}, {"code_links": [], "metrics": {"AVG": "15.92", "F-BC": "39.0", "F_NMI": "6.5"}, "model_links": [], "model_name": "AI-KU (2013)", "paper_date": null, "paper_title": "AI-KU: Using Substitute Vectors and Co-Occurrence Modeling For Word Sense Induction and Disambiguation", "paper_url": "https://www.aclweb.org/anthology/S13-2050/", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Word sense induction (WSI) is widely known as the \"unsupervised version\" of WSD. The problem states as: Given a target word (e.g., \"cold\") and a collection of sentences (e.g., \"I caught a cold\", \"The weather is cold\") that use the word, cluster the sentences according to their different senses/meanings. We do not need to know the sense/meaning of each cluster, but sentences inside a cluster should have used the target words with the same sense.\nThere are two widely used datasets: SemEval 2010 and 2013, and both of them use different kinds of metrices: V-Measure (V-M) and paired F-Score (F-S) for SemEval 2010, and fuzzy B-Cubed (F-BC) and fuzzy normalized mutual information (F-NMI). For ease of system comparisons, the metrics are usually aggregated using a geometric mean (AVG).\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Word Sense Induction"}], "synonyms": [], "task": "Word Sense Disambiguation"}, {"categories": [], "datasets": [{"dataset": "AG News", "dataset_citations": [], "dataset_links": [{"title": "AG News corpus", "url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf"}, {"title": "AG's corpus of news articles on the web", "url": "http://www.di.unipi.it/~gulli/AG_corpus_of_news_articles.html"}], "description": "The [AG News corpus](https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf)\nconsists of news articles from the [AG's corpus of news articles on the web](http://www.di.unipi.it/~gulli/AG_corpus_of_news_articles.html)\npertaining to the 4 largest classes. The dataset contains 30,000 training and 1,900 testing examples for each class.\nModels are evaluated based on error rate (lower is better).\n\u000242\u0003 Results reported in Johnson and Zhang, 2017\n", "sota": {"metrics": ["Error"], "rows": [{"code_links": [], "metrics": {"Error": "4.49"}, "model_links": [], "model_name": "XLNet (2019)", "paper_date": null, "paper_title": "XLNet: Generalized Autoregressive Pretraining for Language Understanding", "paper_url": "https://arxiv.org/pdf/1906.08237.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "5.01"}, "model_links": [], "model_name": "ULMFiT (2018)", "paper_date": null, "paper_title": "Universal Language Model Fine-tuning for Text Classification", "paper_url": "https://arxiv.org/abs/1801.06146", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "6.57"}, "model_links": [], "model_name": "CNN * (2016)", "paper_date": null, "paper_title": "Supervised and Semi-Supervised Text Categorization using LSTM for Region Embeddings", "paper_url": "https://arxiv.org/abs/1602.02373", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "6.87"}, "model_links": [], "model_name": "DPCNN (2017)", "paper_date": null, "paper_title": "Deep Pyramid Convolutional Neural Networks for Text Categorization", "paper_url": "http://aclweb.org/anthology/P17-1052", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "8.67"}, "model_links": [], "model_name": "VDCN (2016)", "paper_date": null, "paper_title": "Very Deep Convolutional Networks for Text Classification", "paper_url": "https://arxiv.org/abs/1606.01781", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "9.51"}, "model_links": [], "model_name": "Char-level CNN (2015)", "paper_date": null, "paper_title": "Character-level Convolutional Networks for Text Classification", "paper_url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "DBpedia", "dataset_citations": [], "dataset_links": [{"title": "DBpedia ontology", "url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf"}], "description": "The [DBpedia ontology](https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf) \ndataset contains 560,000 training samples and 70,000 testing samples for each of 14 nonoverlapping classes from DBpedia.\nModels are evaluated based on error rate (lower is better).\n", "sota": {"metrics": ["Error"], "rows": [{"code_links": [], "metrics": {"Error": "0.62"}, "model_links": [], "model_name": "XLNet (2019)", "paper_date": null, "paper_title": "XLNet: Generalized Autoregressive Pretraining for Language Understanding", "paper_url": "https://arxiv.org/pdf/1906.08237.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "0.64"}, "model_links": [], "model_name": "Bidirectional Encoder Representations from Transformers (2018)", "paper_date": null, "paper_title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding", "paper_url": "https://arxiv.org/abs/1810.04805", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "0.80"}, "model_links": [], "model_name": "ULMFiT (2018)", "paper_date": null, "paper_title": "Universal Language Model Fine-tuning for Text Classification", "paper_url": "https://arxiv.org/abs/1801.06146", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "0.84"}, "model_links": [], "model_name": "CNN (2016)", "paper_date": null, "paper_title": "Supervised and Semi-Supervised Text Categorization using LSTM for Region Embeddings", "paper_url": "https://arxiv.org/abs/1602.02373", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "0.88"}, "model_links": [], "model_name": "DPCNN (2017)", "paper_date": null, "paper_title": "Deep Pyramid Convolutional Neural Networks for Text Categorization", "paper_url": "http://aclweb.org/anthology/P17-1052", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "1.29"}, "model_links": [], "model_name": "VDCN (2016)", "paper_date": null, "paper_title": "Very Deep Convolutional Networks for Text Classification", "paper_url": "https://arxiv.org/abs/1606.01781", "uses_additional_data": false}, {"code_links": [], "metrics": {"Error": "1.55"}, "model_links": [], "model_name": "Char-level CNN (2015)", "paper_date": null, "paper_title": "Character-level Convolutional Networks for Text Classification", "paper_url": "https://papers.nips.cc/paper/5782-character-level-convolutional-networks-for-text-classification.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Text classification is the task of assigning a sentence or document an appropriate category.\nThe categories depend on the chosen dataset and can range from topics.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Text Classification"}, {"categories": [], "datasets": [{"dataset": "CoNLL-2014 Shared Task", "dataset_citations": [], "dataset_links": [{"title": "CoNLL-2014 shared task test set", "url": "https://www.comp.nus.edu.sg/~nlp/conll14st/conll14st-test-data.tar.gz"}, {"title": "<PERSON><PERSON><PERSON><PERSON> and <PERSON>, 2012", "url": "http://www.aclweb.org/anthology/N12-1067"}], "description": "The [CoNLL-2014 shared task test set](https://www.comp.nus.edu.sg/~nlp/conll14st/conll14st-test-data.tar.gz) is the most widely used dataset to benchmark GEC systems. The test set contains 1,312 English sentences with error annotations by 2 expert annotators. Models are evaluated with <PERSON><PERSON><PERSON> scorer ([<PERSON><PERSON><PERSON><PERSON> and <PERSON>, 2012](http://www.aclweb.org/anthology/N12-1067)) which computes a span-based F\u0002wzxhzdk:0\u0003β\u0002wzxhzdk:1\u0003-score (β set to 0.5 to weight precision twice as recall).\nThe shared task setting restricts that systems use only publicly available datasets for training to ensure a fair comparison between systems. The highest published scores on the the CoNLL-2014 test set are given below. A distinction is made between papers that report results in the restricted CoNLL-2014 shared task setting of training using publicly-available training datasets only (Restricted) and those that made use of large, non-public datasets (Unrestricted).\nRestricted:\nUnrestricted:\nRestricted: uses only publicly available datasets. Unrestricted: uses non-public datasets.\n", "sota": {"metrics": [], "rows": []}, "subdatasets": [{"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["F0.5"], "rows": [{"code_links": [], "metrics": {"F0.5": "65.2"}, "model_links": [], "model_name": "Transformer + Pre-train with Pseudo Data + BERT (2020)", "paper_date": null, "paper_title": "Encoder-Decoder Models Can Benefit from Pre-trained Masked Language Models in Grammatical Error Correction", "paper_url": "https://arxiv.org/pdf/2005.00987.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "65.0"}, "model_links": [], "model_name": "Transformer + Pre-train with Pseudo Data (2019)", "paper_date": null, "paper_title": "An Empirical Study of Incorporating Pseudo Data into Grammatical Error Correction", "paper_url": "https://arxiv.org/abs/1909.00502", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "61.2"}, "model_links": [], "model_name": "Sequence Labeling with edits using BERT, Faster inference (2019)", "paper_date": null, "paper_title": "Parallel Iterative Edit Models for Local Sequence Transduction", "paper_url": "https://www.aclweb.org/anthology/D19-1435.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "61.15"}, "model_links": [], "model_name": "Copy-Augmented Transformer + Pre-train (2019)", "paper_date": null, "paper_title": "Improving Grammatical Error Correction via Pre-Training a Copy-Augmented Architecture with Unlabeled Data", "paper_url": "https://arxiv.org/pdf/1903.00138.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "59.7"}, "model_links": [], "model_name": "Sequence Labeling with edits using BERT, Faster inference (2019)", "paper_date": null, "paper_title": "Parallel Iterative Edit Models for Local Sequence Transduction", "paper_url": "https://www.aclweb.org/anthology/D19-1435.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "56.52"}, "model_links": [], "model_name": "CNN Seq2Seq + Quality Estimation (2018)", "paper_date": null, "paper_title": "Neural Quality Estimation of Grammatical Error Correction", "paper_url": "http://aclweb.org/anthology/D18-1274", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "56.25"}, "model_links": [], "model_name": "SMT + BiGRU (2018)", "paper_date": null, "paper_title": "Near Human-Level Performance in Grammatical Error Correction with Hybrid Machine Translation", "paper_url": "http://aclweb.org/anthology/N18-2046", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "55.8"}, "model_links": [], "model_name": "Transformer (2018)", "paper_date": null, "paper_title": "Approaching Neural Grammatical Error Correction as a Low-Resource Machine Translation Task", "paper_url": "http://aclweb.org/anthology/N18-1055", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "54.79"}, "model_links": [], "model_name": "CNN Seq2Seq (2018)", "paper_date": null, "paper_title": "A Multilayer Convolutional Encoder-Decoder Neural Network for Grammatical Error Correction", "paper_url": "https://www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/viewFile/17308/16137", "uses_additional_data": false}]}, "subdataset": "Restricted", "subdatasets": []}, {"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["F0.5"], "rows": [{"code_links": [], "metrics": {"F0.5": "61.34"}, "model_links": [], "model_name": "CNN Seq2Seq + Fluency Boost (2018)", "paper_date": null, "paper_title": "Reaching Human-level Performance in Automatic Grammatical Error Correction: An Empirical Study", "paper_url": "https://arxiv.org/pdf/1807.01270.pdf", "uses_additional_data": false}]}, "subdataset": "Unrestricted", "subdatasets": []}]}, {"dataset": "CoNLL-2014 10 Annotations", "dataset_citations": [], "dataset_links": [{"title": "<PERSON> and <PERSON>, 2015", "url": "http://aclweb.org/anthology/P15-1068"}, {"title": "link", "url": "http://www.comp.nus.edu.sg/~nlp/sw/10gec_annotations.zip"}], "description": "[<PERSON> and <PERSON>, 2015](http://aclweb.org/anthology/P15-1068) released 8 additional annotations (in addition to the two official annotations) for the CoNLL-2014 shared task test set ([link](http://www.comp.nus.edu.sg/~nlp/sw/10gec_annotations.zip)).\nRestricted:\nUnrestricted:\nRestricted: uses only publicly available datasets. Unrestricted: uses non-public datasets.\n", "sota": {"metrics": [], "rows": []}, "subdatasets": [{"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["F0.5"], "rows": [{"code_links": [], "metrics": {"F0.5": "72.04"}, "model_links": [], "model_name": "SMT + BiGRU (2018)", "paper_date": null, "paper_title": "Near Human-Level Performance in Grammatical Error Correction with Hybrid Machine Translation", "paper_url": "http://aclweb.org/anthology/N18-2046", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "70.14 (measured by <PERSON><PERSON> et al., 2018)"}, "model_links": [], "model_name": "CNN Seq2Seq (2018)", "paper_date": null, "paper_title": " A Multilayer Convolutional Encoder-Decoder Neural Network for Grammatical Error Correction", "paper_url": "https://www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/viewFile/17308/16137", "uses_additional_data": false}]}, "subdataset": "Restricted", "subdatasets": []}, {"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["F0.5"], "rows": [{"code_links": [], "metrics": {"F0.5": "76.88"}, "model_links": [], "model_name": "CNN Seq2Seq + Fluency Boost (2018)", "paper_date": null, "paper_title": "Reaching Human-level Performance in Automatic Grammatical Error Correction: An Empirical Study", "paper_url": "https://arxiv.org/pdf/1807.01270.pdf", "uses_additional_data": false}]}, "subdataset": "Unrestricted", "subdatasets": []}]}, {"dataset": "JFLEG", "dataset_citations": [], "dataset_links": [{"title": "JFLEG test set", "url": "https://github.com/keisks/jfleg"}, {"title": "<PERSON><PERSON><PERSON> et al., 2017", "url": "http://aclweb.org/anthology/E17-2037"}, {"title": "GLEU", "url": "https://github.com/cnap/gec-ranking/"}, {"title": "<PERSON><PERSON><PERSON> et al., 2016", "url": "https://arxiv.org/pdf/1605.02592.pdf"}], "description": "[JFLEG test set](https://github.com/keisks/jfleg) released by [<PERSON><PERSON><PERSON> et al., 2017](http://aclweb.org/anthology/E17-2037) consists of 747 English sentences with 4 references for each sentence. Models are evaluated with [GLEU](https://github.com/cnap/gec-ranking/) metric ([<PERSON><PERSON><PERSON> et al., 2016](https://arxiv.org/pdf/1605.02592.pdf)).\nRestricted:  \nUnrestricted:\nRestricted: uses only publicly available datasets. Unrestricted: uses non-public datasets.\n", "sota": {"metrics": [], "rows": []}, "subdatasets": [{"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["GLEU"], "rows": [{"code_links": [], "metrics": {"GLEU": "62.0"}, "model_links": [], "model_name": "Transformer + Pre-train with Pseudo Data + BERT (2020)", "paper_date": null, "paper_title": "Encoder-Decoder Models Can Benefit from Pre-trained Masked Language Models in Grammatical Error Correction", "paper_url": "https://arxiv.org/pdf/2005.00987.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"GLEU": "61.50"}, "model_links": [], "model_name": "SMT + BiGRU (2018)", "paper_date": null, "paper_title": "Near Human-Level Performance in Grammatical Error Correction with Hybrid Machine Translation", "paper_url": "http://aclweb.org/anthology/N18-2046", "uses_additional_data": false}, {"code_links": [], "metrics": {"GLEU": "59.9"}, "model_links": [], "model_name": "Transformer (2018)", "paper_date": null, "paper_title": "Approaching Neural Grammatical Error Correction as a Low-Resource Machine Translation Task", "paper_url": "http://aclweb.org/anthology/N18-1055", "uses_additional_data": false}, {"code_links": [], "metrics": {"GLEU": "57.47"}, "model_links": [], "model_name": "CNN Seq2Seq (2018)", "paper_date": null, "paper_title": " A Multilayer Convolutional Encoder-Decoder Neural Network for Grammatical Error Correction", "paper_url": "https://www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/viewFile/17308/16137", "uses_additional_data": false}]}, "subdataset": "Restricted", "subdatasets": []}, {"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["GLEU"], "rows": [{"code_links": [], "metrics": {"GLEU": "62.42"}, "model_links": [], "model_name": "CNN Seq2Seq + Fluency Boost and inference (2018)", "paper_date": null, "paper_title": "Reaching Human-level Performance in Automatic Grammatical Error Correction: An Empirical Study", "paper_url": "https://arxiv.org/pdf/1807.01270.pdf", "uses_additional_data": false}]}, "subdataset": "Unrestricted", "subdatasets": []}]}, {"dataset": "Results on WI-LOCNESS test set", "dataset_citations": [], "dataset_links": [{"title": "Proceedings of the Fourteenth Workshop on Innovative Use of NLP for Building Educational Applications", "url": "https://www.aclweb.org/anthology/W19-44"}], "description": "Restricted track:\nLow-resource track:\nReference:\n - <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, in [Proceedings of the Fourteenth Workshop on Innovative Use of NLP for Building Educational Applications](https://www.aclweb.org/anthology/W19-44)\n - <PERSON>, <PERSON>, and <PERSON>. 2017. Automatic annotation and evaluation of Error Types for Grammatical Error Correction. In Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). Vancouver, Canada.\n", "sota": {"metrics": [], "rows": []}, "subdatasets": [{"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["F0.5"], "rows": [{"code_links": [], "metrics": {"F0.5": "73.18"}, "model_links": [], "model_name": "BEA Combination", "paper_date": null, "paper_title": "Learning to Combine Grammatical Error Corrections ", "paper_url": "https://www.aclweb.org/anthology/W19-4414/", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "70.2"}, "model_links": [], "model_name": "Transformer + Pre-train with Pseudo Data (2019)", "paper_date": null, "paper_title": "An Empirical Study of Incorporating Pseudo Data into Grammatical Error Correction", "paper_url": "https://arxiv.org/abs/1909.00502", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "69.8"}, "model_links": [], "model_name": "Transformer + Pre-train with Pseudo Data + BERT (2020)", "paper_date": null, "paper_title": "Encoder-Decoder Models Can Benefit from Pre-trained Masked Language Models in Grammatical Error Correction", "paper_url": "https://arxiv.org/pdf/2005.00987.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "69.47"}, "model_links": [], "model_name": "Transformer", "paper_date": null, "paper_title": "Neural Grammatical Error Correction Systems with UnsupervisedPre-training on Synthetic Data", "paper_url": "https://www.aclweb.org/anthology/W19-4427", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "69.00"}, "model_links": [], "model_name": "Transformer", "paper_date": null, "paper_title": "A Neural Grammatical Error Correction System Built OnBetter Pre-training and Sequential Transfer Learning", "paper_url": "https://www.aclweb.org/anthology/W19-4423", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "66.78"}, "model_links": [], "model_name": "Ensemble of models", "paper_date": null, "paper_title": "The LAIX Systems in the BEA-2019 GEC Shared Task", "paper_url": "https://www.aclweb.org/anthology/W19-4416", "uses_additional_data": false}]}, "subdataset": "Restricted track", "subdatasets": []}, {"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["F0.5"], "rows": [{"code_links": [], "metrics": {"F0.5": "64.24"}, "model_links": [], "model_name": "Transformer", "paper_date": null, "paper_title": "Neural Grammatical Error Correction Systems with UnsupervisedPre-training on Synthetic Data", "paper_url": "https://www.aclweb.org/anthology/W19-4427", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "58.80"}, "model_links": [], "model_name": "Transformer", "paper_date": null, "paper_title": "A Neural Grammatical Error Correction System Built OnBetter Pre-training and Sequential Transfer Learning", "paper_url": "https://www.aclweb.org/anthology/W19-4423", "uses_additional_data": false}, {"code_links": [], "metrics": {"F0.5": "51.81"}, "model_links": [], "model_name": "Ensemble of models", "paper_date": null, "paper_title": "The LAIX Systems in the BEA-2019 GEC Shared Task", "paper_url": "https://www.aclweb.org/anthology/W19-4416", "uses_additional_data": false}]}, "subdataset": "Low-resource track", "subdatasets": []}]}], "description": "Grammatical Error Correction (GEC) is the task of correcting different kinds of errors in text such as spelling, punctuation, grammatical, and word choice errors. \nGEC is typically formulated as a sentence correction task. A GEC system takes a potentially erroneous sentence as input and is expected to transform it to its corrected version. See the example given below: \n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Grammatical Error Correction"}, {"categories": [], "datasets": [{"dataset": "Penn Treebank", "dataset_citations": [], "dataset_links": [{"title": "<PERSON> Dependency", "url": "https://nlp.stanford.edu/software/dependencies_manual.pdf"}], "description": "Models are evaluated on the [Stanford Dependency](https://nlp.stanford.edu/software/dependencies_manual.pdf)\nconversion (v3.3.0) of the Penn Treebank with predicted POS-tags. Punctuation symbols\nare excluded from the evaluation. Evaluation metrics are unlabeled attachment score (UAS) and labeled attachment score (LAS). UAS does not consider the semantic relation (e.g. Subj) used to label the attachment between the head and the child, while LAS requires a semantic correct label for each attachment.Here, we also mention the predicted POS tagging accuracy.\n", "sota": {"metrics": ["POS", "UAS", "LAS"], "rows": [{"code_links": [], "metrics": {"LAS": "96.26", "POS": "97.3", "UAS": "97.42"}, "model_links": [], "model_name": "Label Attention Layer + HPSG + XLNet (2019)", "paper_date": null, "paper_title": "Rethinking Self-Attention: Towards Interpretability for Neural Parsing", "paper_url": "https://khalilmrini.github.io/Label_Attention_Layer.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "95.72", "POS": "97.3", "UAS": "97.20"}, "model_links": [], "model_name": "HPSG Parser + XLNet (2019)", "paper_date": null, "paper_title": "Head-Driven Phrase Structure Grammar Parsing on Penn Treebank", "paper_url": "https://www.aclweb.org/anthology/P19-1230", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "95.43", "POS": "97.3", "UAS": "97.00"}, "model_links": [], "model_name": "HPSG Parser + BERT (2019)", "paper_date": null, "paper_title": "Head-Driven Phrase Structure Grammar Parsing on Penn Treebank", "paper_url": "https://www.aclweb.org/anthology/P19-1230", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "95.02", "POS": "97.74", "UAS": "96.61"}, "model_links": [], "model_name": "CVT + Multi-Task (2018)", "paper_date": null, "paper_title": "Semi-Supervised Sequence Modeling with Cross-View Training", "paper_url": "https://arxiv.org/abs/1809.08370", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "94.43", "POS": "97.3", "UAS": "96.04"}, "model_links": [], "model_name": "Left-to-Right Pointer Network (2019)", "paper_date": null, "paper_title": "Left-to-Right Dependency Parsing with Pointer Networks", "paper_url": "https://www.aclweb.org/anthology/N19-1076/", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "94.31", "POS": "97.3", "UAS": "95.97"}, "model_links": [], "model_name": "Graph-based parser with GNNs (2019)", "paper_date": null, "paper_title": "Graph-based Dependency Parsing with Graph Neural Networks", "paper_url": "https://www.aclweb.org/anthology/P19-1237", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "94.08", "POS": "97.3", "UAS": "95.74"}, "model_links": [], "model_name": "Deep Biaffine (2017)", "paper_date": null, "paper_title": "Deep Biaffine Attention for Neural Dependency Parsing", "paper_url": "https://arxiv.org/abs/1611.01734", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "92.87", "POS": "97.97", "UAS": "94.51"}, "model_links": [], "model_name": "jPTDP (2018)", "paper_date": null, "paper_title": "An improved neural network model for joint POS tagging and dependency parsing", "paper_url": "https://arxiv.org/abs/1807.03955", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "92.79", "POS": "97.44", "UAS": "94.61"}, "model_links": [], "model_name": "<PERSON><PERSON> et al. (2016)", "paper_date": null, "paper_title": "Globally Normalized Transition-Based Neural Networks", "paper_url": "https://www.aclweb.org/anthology/P16-1231", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "92.06", "POS": "97.3", "UAS": "94.26"}, "model_links": [], "model_name": "Distilled neural FOG (2016)", "paper_date": null, "paper_title": "Distilling an Ensemble of Greedy Dependency Parsers into One MST Parser", "paper_url": "https://arxiv.org/abs/1609.07561", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "92.14", "POS": "97.3", "UAS": "94.05"}, "model_links": [], "model_name": "Distilled transition-based parser (2018)", "paper_date": null, "paper_title": "Distilling Knowledge for Search-based Structured Prediction", "paper_url": "http://aclweb.org/anthology/P18-1129", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "92.05", "POS": "97.44", "UAS": "93.99"}, "model_links": [], "model_name": "<PERSON> et al. (2015)", "paper_date": null, "paper_title": "Structured Training for Neural Network Transition-Based Parsing", "paper_url": "http://anthology.aclweb.org/P/P15/P15-1032.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "91.9", "POS": "97.3", "UAS": "93.9"}, "model_links": [], "model_name": "BIST transition-based parser (2016)", "paper_date": null, "paper_title": "Simple and Accurate Dependency Parsing Using Bidirectional LSTM Feature Representations", "paper_url": "https://aclweb.org/anthology/Q16-1023", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "91.42", "POS": "97.3", "UAS": "93.56"}, "model_links": [], "model_name": "Arc-hybrid (2016)", "paper_date": null, "paper_title": "Training with Exploration Improves a Greedy Stack-LSTM Parser", "paper_url": "https://arxiv.org/abs/1603.03793", "uses_additional_data": false}, {"code_links": [], "metrics": {"LAS": "91.0", "POS": "97.3", "UAS": "93.1"}, "model_links": [], "model_name": "BIST graph-based parser (2016)", "paper_date": null, "paper_title": "Simple and Accurate Dependency Parsing Using Bidirectional LSTM Feature Representations", "paper_url": "https://aclweb.org/anthology/Q16-1023", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Dependency parsing is the task of extracting a dependency parse of a sentence that represents its grammatical\nstructure and defines the relationships between \"head\" words and words, which modify those heads.\nExample:\nroot\n      |\n      | +-------dobj---------+\n      | |                    |\nnsubj | |   +------det-----+ | +-----nmod------+\n+--+  | |   |              | | |               |\n|  |  | |   |      +-nmod-+| | |      +-case-+ |\n+  |  + |   +      +      || + |      +      | |\nI  prefer  the  morning   flight  through  Denver\nRelations among the words are illustrated above the sentence with directed, labeled\narcs from heads to dependents (+ indicates the dependent).\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Dependency Parsing"}, {"categories": [], "datasets": [{"dataset": "Sent<PERSON><PERSON>", "dataset_citations": [], "dataset_links": [{"title": "Sent<PERSON><PERSON>", "url": "https://arxiv.org/abs/1803.05449"}, {"title": "here", "url": "https://github.com/facebookresearch/SentEval"}], "description": "[SentEval](https://arxiv.org/abs/1803.05449) is an evaluation toolkit for evaluating sentence\nrepresentations. It includes 17 downstream tasks, including common semantic textual similarity\ntasks. The semantic textual similarity (STS) benchmark tasks from 2012-2016 (STS12, STS13, STS14, STS15, STS16, STS-B) measure the relatedness\nof two sentences based on the cosine similarity of the two representations. The evaluation criterion is Pearson correlation.\nThe SICK relatedness (SICK-R) task trains a linear model to output a score from 1 to 5 indicating the relatedness of two sentences. For\nthe same dataset (SICK-E) can be treated as a three-class classification problem using the entailment labels (classes are 'entailment', 'contradiction', and 'neutral').\nThe evaluation metric for SICK-R is Pearson correlation and classification accuracy for SICK-E.\nThe Microsoft Research Paraphrase Corpus (MRPC) corpus is a paraphrase identification dataset, where systems\naim to identify if two sentences are paraphrases of each other. The evaluation metric is classification accuracy and F1.\nThe data can be downloaded from [here](https://github.com/facebookresearch/SentEval).\n\u000242\u0003 only evaluated on STS-B\n", "sota": {"metrics": ["MRPC", "SICK-R", "SICK-E", "STS"], "rows": [{"code_links": [], "metrics": {"MRPC": "93.0/90.7", "SICK-E": "-", "SICK-R": "-", "STS": "91.6/91.1*"}, "model_links": [], "model_name": "XLNet-Large (2019)", "paper_date": null, "paper_title": "XLNet: Generalized Autoregressive Pretraining for Language Understanding", "paper_url": "https://arxiv.org/pdf/1906.08237.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"MRPC": "92.7/90.3", "SICK-E": "-", "SICK-R": "-", "STS": "91.1/90.7*"}, "model_links": [], "model_name": "MT-DNN-ensemble (2019)", "paper_date": null, "paper_title": "Improving Multi-Task Deep Neural Networks via Knowledge Distillation for Natural Language Understanding", "paper_url": "https://arxiv.org/pdf/1904.09482.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"MRPC": "91.5/88.5", "SICK-E": "-", "SICK-R": "-", "STS": "90.1/89.7*"}, "model_links": [], "model_name": "Snorkel MeTaL (2018)", "paper_date": null, "paper_title": "Training Complex Models with Multi-Task Weak Supervision", "paper_url": "https://arxiv.org/pdf/1810.02840.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"MRPC": "78.6/84.4", "SICK-E": "87.8", "SICK-R": "0.888", "STS": "78.9/78.6"}, "model_links": [], "model_name": "GenSen (2018)", "paper_date": null, "paper_title": "Learning General Purpose Distributed Sentence Representations via Large Scale Multi-task Learning", "paper_url": "https://arxiv.org/abs/1804.00079", "uses_additional_data": false}, {"code_links": [], "metrics": {"MRPC": "76.2/83.1", "SICK-E": "86.3", "SICK-R": "0.884", "STS": "75.8/75.5"}, "model_links": [], "model_name": "InferSent (2017)", "paper_date": null, "paper_title": "Supervised Learning of Universal Sentence Representations from Natural Language Inference Data", "paper_url": "https://arxiv.org/abs/1705.02364", "uses_additional_data": false}, {"code_links": [], "metrics": {"MRPC": "80.4/85.9", "SICK-E": "-", "SICK-R": "-", "STS": "-"}, "model_links": [], "model_name": "TF-KLD (2013)", "paper_date": null, "paper_title": "Discriminative Improvements to Distributional Sentence Similarity", "paper_url": "http://www.aclweb.org/anthology/D/D13/D13-1090.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Semantic textual similarity deals with determining how similar two pieces of texts are.\nThis can take the form of assigning a score from 1 to 5. Related tasks are paraphrase or duplicate identification.\n", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "Quora Question Pairs", "dataset_citations": [], "dataset_links": [{"title": "Quora Question Pairs dataset", "url": "https://data.quora.com/First-Quora-Dataset-Release-Question-Pairs"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "The [Quora Question Pairs dataset](https://data.quora.com/First-Quora-Dataset-Release-Question-Pairs)\nconsists of over 400,000 pairs of questions on Quora. Systems must identify whether one question is a\nduplicate of the other. Models are evaluated based on accuracy.\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["F1", "Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "90.3", "F1": "74.2"}, "model_links": [], "model_name": "XLNet-Large (2019)", "paper_date": null, "paper_title": "XLNet: Generalized Autoregressive Pretraining for Language Understanding", "paper_url": "https://arxiv.org/pdf/1906.08237.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "89.9", "F1": "73.7"}, "model_links": [], "model_name": "MT-DNN-ensemble (2019)", "paper_date": null, "paper_title": "Improving Multi-Task Deep Neural Networks via Knowledge Distillation for Natural Language Understanding", "paper_url": "https://arxiv.org/pdf/1904.09482.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "89.9", "F1": "73.1"}, "model_links": [], "model_name": "Snorkel MeTaL (2018)", "paper_date": null, "paper_title": "Training Complex Models with Multi-Task Weak Supervision", "paper_url": "https://arxiv.org/pdf/1810.02840.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "89.12", "F1": ""}, "model_links": [], "model_name": "MwAN (2018)", "paper_date": null, "paper_title": "Multiway Attention Networks for Modeling Sentence Pairs", "paper_url": "https://www.ijcai.org/proceedings/2018/0613.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "89.06", "F1": ""}, "model_links": [], "model_name": "DIIN (2018)", "paper_date": null, "paper_title": "Natural Language Inference Over Interaction Space", "paper_url": "https://arxiv.org/pdf/1709.04348.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "88.40", "F1": ""}, "model_links": [], "model_name": "pt-<PERSON><PERSON><PERSON> (2017)", "paper_date": null, "paper_title": "Neural Paraphrase Identification of Questions with Noisy Pretraining", "paper_url": "https://arxiv.org/abs/1704.04565", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "88.17", "F1": ""}, "model_links": [], "model_name": "BiMPM (2017)", "paper_date": null, "paper_title": "Bilateral Multi-Perspective Matching for Natural Language Sentences", "paper_url": "https://arxiv.org/abs/1702.03814", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "87.01", "F1": ""}, "model_links": [], "model_name": "GenSen (2018)", "paper_date": null, "paper_title": "Learning General Purpose Distributed Sentence Representations via Large Scale Multi-task Learning", "paper_url": "https://arxiv.org/abs/1804.00079", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": null, "subtasks": [], "synonyms": [], "task": "Paraphrase Identification"}], "synonyms": [], "task": "Semantic Textual Similarity"}, {"categories": [], "datasets": [], "description": "", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "LDC2014T12", "dataset_citations": [], "dataset_links": [{"title": "smatch", "url": "https://amr.isi.edu/smatch-13.pdf"}], "description": "13,051 sentences\nModels are evaluated on the newswire section and the full dataset based on [smatch](https://amr.isi.edu/smatch-13.pdf).\n", "sota": {"metrics": ["F1 Newswire", "F1 Full"], "rows": [{"code_links": [], "metrics": {"F1 Full": "71.3", "F1 Newswire": "--"}, "model_links": [], "model_name": "Broad-Coverage Semantic Parsing as Transduction \u0002wzxhzdk:41\u0003 (2019)", "paper_date": null, "paper_title": "Broad-Coverage Semantic Parsing as Transduction", "paper_url": "https://www.aclweb.org/anthology/D19-1392.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 Full": "70.2", "F1 Newswire": "--"}, "model_links": [], "model_name": "Two-stage Sequence-to-Graph Transducer \u0002wzxhzdk:40\u0003 (2019)", "paper_date": null, "paper_title": "AMR Parsing as Sequence-to-Graph Transduction", "paper_url": "https://www.aclweb.org/anthology/P19-1009.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 Full": "68.4", "F1 Newswire": "73.3"}, "model_links": [], "model_name": "Transition-based+improved aligner+ensemble \u0002wzxhzdk:39\u0003 (2018)", "paper_date": null, "paper_title": "An AMR Aligner Tuned by Transition-based Parser", "paper_url": "http://aclweb.org/anthology/D18-1264", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 Full": "68.1", "F1 Newswire": "--"}, "model_links": [], "model_name": "Improved CAMR \u0002wzxhzdk:37\u0003\u0002wzxhzdk:38\u0003 (2017)", "paper_date": null, "paper_title": "Getting the Most out of AMR Parsing", "paper_url": "http://aclweb.org/anthology/D17-1129", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 Full": "66", "F1 Newswire": "71"}, "model_links": [], "model_name": "Incremental joint model \u0002wzxhzdk:35\u0003\u0002wzxhzdk:36\u0003 (2016)", "paper_date": null, "paper_title": "AMR Parsing with an Incremental Joint Model", "paper_url": "https://aclweb.org/anthology/D16-1065", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 Full": "66", "F1 Newswire": "70"}, "model_links": [], "model_name": "Transition-based transducer \u0002wzxhzdk:32\u0003\u0002wzxhzdk:33\u0003\u0002wzxhzdk:34\u0003 (2015)", "paper_date": null, "paper_title": "Boosting Transition-based AMR Parsing with Refined Actions and Auxiliary Analyzers", "paper_url": "http://www.aclweb.org/anthology/P15-2141", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 Full": "--", "F1 Newswire": "70"}, "model_links": [], "model_name": "Imitation learning \u0002wzxhzdk:30\u0003\u0002wzxhzdk:31\u0003 (2016)", "paper_date": null, "paper_title": "Noise reduction and targeted exploration in imitation learning for Abstract Meaning Representation parsing", "paper_url": "http://www.aclweb.org/anthology/P16-1001", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 Full": "66", "F1 Newswire": "--"}, "model_links": [], "model_name": "MT-Based \u0002wzxhzdk:29\u0003 (2015)", "paper_date": null, "paper_title": "Parsing English into Abstract Meaning Representation Using Syntax-Based Machine Translation ", "paper_url": "http://www.aclweb.org/anthology/D15-1136", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 Full": "64", "F1 Newswire": "69"}, "model_links": [], "model_name": "Transition-based parser-Stack-LSTM \u0002wzxhzdk:27\u0003\u0002wzxhzdk:28\u0003 (2017)", "paper_date": null, "paper_title": "AMR Parsing using Stack-LSTMs", "paper_url": "http://www.aclweb.org/anthology/D17-1130", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 Full": "63", "F1 Newswire": "68"}, "model_links": [], "model_name": "Transition-based parser-Stack-LSTM (2017)", "paper_date": null, "paper_title": "AMR Parsing using Stack-LSTMs", "paper_url": "http://www.aclweb.org/anthology/D17-1130", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "LDC2015E86", "dataset_citations": [], "dataset_links": [{"title": "smatch", "url": "https://amr.isi.edu/smatch-13.pdf"}], "description": "19,572 sentences\nModels are evaluated based on [smatch](https://amr.isi.edu/smatch-13.pdf).\n", "sota": {"metrics": ["Smatch"], "rows": [{"code_links": [], "metrics": {"Smatch": "73.7"}, "model_links": [], "model_name": "Joint model \u0002wzxhzdk:25\u0003\u0002wzxhzdk:26\u0003 (2018)", "paper_date": null, "paper_title": "AMR Parsing as Graph Prediction with Latent Alignment", "paper_url": "https://arxiv.org/abs/1805.05286", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "70.7"}, "model_links": [], "model_name": "Mul-BiLSTM \u0002wzxhzdk:24\u0003 (2017)", "paper_date": null, "paper_title": "Abstract Meaning Representation Parsing using LSTM Recurrent Neural Networks", "paper_url": "http://aclweb.org/anthology/P17-1043", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "67.0"}, "model_links": [], "model_name": "JAMR \u0002wzxhzdk:21\u0003\u0002wzxhzdk:22\u0003\u0002wzxhzdk:23\u0003 (2016)", "paper_date": null, "paper_title": "CMU at SemEval-2016 Task 8: Graph-based AMR Parsing with Infinite Ramp Loss", "paper_url": "http://www.aclweb.org/anthology/S16-1186", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "66.5"}, "model_links": [], "model_name": "CAMR \u0002wzxhzdk:18\u0003\u0002wzxhzdk:19\u0003\u0002wzxhzdk:20\u0003 (2016)", "paper_date": null, "paper_title": "CAMR at SemEval-2016 Task 8: An Extended Transition-based AMR Parser", "paper_url": "http://www.aclweb.org/anthology/S16-1181", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "64.0"}, "model_links": [], "model_name": "AMREager \u0002wzxhzdk:15\u0003\u0002wzxhzdk:16\u0003\u0002wzxhzdk:17\u0003 (2017)", "paper_date": null, "paper_title": "An Incremental Parser for Abstract Meaning Representation", "paper_url": "http://www.aclweb.org/anthology/E17-1051", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "62.1"}, "model_links": [], "model_name": "SEQ2SEQ + 20M \u0002wzxhzdk:14\u0003 (2017)", "paper_date": null, "paper_title": "Neural AMR: Sequence-to-Sequence Models for Parsing and Generation", "paper_url": "https://arxiv.org/abs/1704.08381", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "LDC2016E25", "dataset_citations": [], "dataset_links": [{"title": "smatch", "url": "https://amr.isi.edu/smatch-13.pdf"}], "description": "39,260 sentences\nResults are computed over 8 runs. Models are evaluated based on [smatch](https://amr.isi.edu/smatch-13.pdf).\n", "sota": {"metrics": ["Smatch"], "rows": [{"code_links": [], "metrics": {"Smatch": "77.0"}, "model_links": [], "model_name": "Broad-Coverage Semantic Parsing as Transduction \u0002wzxhzdk:13\u0003 (2019)", "paper_date": null, "paper_title": "Broad-Coverage Semantic Parsing as Transduction", "paper_url": "https://www.aclweb.org/anthology/D19-1392.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "76.3"}, "model_links": [], "model_name": "Two-stage Sequence-to-Graph Transducer \u0002wzxhzdk:12\u0003 (2019)", "paper_date": null, "paper_title": "AMR Parsing as Sequence-to-Graph Transduction", "paper_url": "https://www.aclweb.org/anthology/P19-1009.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "75.5"}, "model_links": [], "model_name": "Rewarding Smatch: Transition-Based AMR Parsing with Reinforcement Learning \u0002wzxhzdk:9\u0003\u0002wzxhzdk:10\u0003\u0002wzxhzdk:11\u0003 (2019)", "paper_date": null, "paper_title": "Rewarding Smatch: Transition-Based AMR Parsing with Reinforcement Learning", "paper_url": "https://arxiv.org/pdf/1905.13370", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "74.4"}, "model_links": [], "model_name": "Joint model \u0002wzxhzdk:7\u0003\u0002wzxhzdk:8\u0003 (2018)", "paper_date": null, "paper_title": "AMR Parsing as Graph Prediction with Latent Alignment", "paper_url": "https://arxiv.org/abs/1805.05286", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "73.4"}, "model_links": [], "model_name": "Rewarding Smatch: Transition-Based AMR Parsing with Reinforcement Learning ; (2019)", "paper_date": null, "paper_title": "Rewarding Smatch: Transition-Based AMR Parsing with Reinforcement Learning", "paper_url": "https://arxiv.org/pdf/1905.13370", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "71.0"}, "model_links": [], "model_name": "ChSeq + 100K \u0002wzxhzdk:6\u0003 (2017)", "paper_date": null, "paper_title": "Neural Semantic Parsing by Character-based Translation: Experiments with Abstract Meaning Representations", "paper_url": "https://arxiv.org/abs/1705.09980", "uses_additional_data": false}, {"code_links": [], "metrics": {"Smatch": "61.9"}, "model_links": [], "model_name": "Neural-Pointer \u0002wzxhzdk:4\u0003\u0002wzxhzdk:5\u0003 (2017)", "paper_date": null, "paper_title": "Oxford at SemEval-2017 Task 9: Neural AMR Parsing with <PERSON><PERSON>-Augmented Attention", "paper_url": "http://aclweb.org/anthology/S17-2157", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Each AMR is a single rooted, directed graph. AMRs include PropBank semantic roles, within-sentence coreference, named entities and types, modality, negation, questions, quantities, and so on. [See](https://amr.isi.edu/index.html).\nIn the following tables, systems marked with \u0002wzxhzdk:0\u0003 are pipeline systems that require POS as input,\n\u0002wzxhzdk:1\u0003 is for those require NER,\n\u0002wzxhzdk:2\u0003 is for those require syntax parsing,\nand \u0002wzxhzdk:3\u0003 is for those require SRL.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "<PERSON><PERSON>"}], "synonyms": [], "task": "Semantic Parsing"}, {"categories": [], "datasets": [], "description": "Combinatory Categorical Grammar (CCG; [<PERSON><PERSON><PERSON>, 2000](http://www.citeulike.org/group/14833/article/8971002)) is a\nhighly lexicalized formalism. The standard parsing model of [<PERSON> and <PERSON> (2007)](https://www.mitpressjournals.org/doi/abs/10.1162/coli.2007.33.4.493)\nuses over 400 lexical categories (or supertags), compared to about 50 part-of-speech tags for typical parsers.\nExample:\n", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "CCGBank", "dataset_citations": [], "dataset_links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> and Steedman (2007)", "url": "http://www.aclweb.org/anthology/J07-3004"}], "description": "The CCGBank is a corpus of CCG derivations and dependency structures extracted from the Penn Treebank by\n[<PERSON><PERSON><PERSON><PERSON> and Steedman (2007)](http://www.aclweb.org/anthology/J07-3004). Sections 2-21 are used for training,\nsection 00 for development, and section 23 as in-domain test set. Some work\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "88.32"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON><PERSON> et al. (2016)", "paper_date": null, "paper_title": "Supertagging with LSTMs", "paper_url": "https://aclweb.org/anthology/N/N16/N16-1027.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "88.1"}, "model_links": [], "model_name": "<PERSON> et al. (2016)", "paper_date": null, "paper_title": "LSTM CCG Parsing", "paper_url": "https://aclweb.org/anthology/N/N16/N16-1026.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "87.04"}, "model_links": [], "model_name": "<PERSON> et al. (2015)", "paper_date": null, "paper_title": "CCG Supertagging with a Recurrent Neural Network", "paper_url": "http://www.aclweb.org/anthology/P15-2041", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "85.95"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON> et al. , (2010)", "paper_date": null, "paper_title": "Faster Parsing by Supertagger Adaptation", "paper_url": "https://www.aclweb.org/anthology/papers/P/P10/P10-1036/", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "85.45"}, "model_links": [], "model_name": "<PERSON> and <PERSON> (2007)", "paper_date": null, "paper_title": "Wide-Coverage Efficient Statistical Parsing with CCG and Log-Linear Models", "paper_url": "https://www.aclweb.org/anthology/J07-4004", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Wikipedia", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "82.49"}, "model_links": [], "model_name": "<PERSON> et al. (2015)", "paper_date": null, "paper_title": "CCG Supertagging with a Recurrent Neural Network", "paper_url": "http://www.aclweb.org/anthology/P15-2041", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "81.7"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON> et al. , (2010)", "paper_date": null, "paper_title": "Faster Parsing by Supertagger Adaptation", "paper_url": "https://www.aclweb.org/anthology/papers/P/P10/P10-1036/", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Bioinfer", "dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Bio specifc taggers?", "Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "82.3", "Bio specifc taggers?": "Yes"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON> et al. , (2010)", "paper_date": null, "paper_title": "Faster Parsing by Supertagger Adaptation", "paper_url": "https://www.aclweb.org/anthology/papers/P/P10/P10-1036/", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "81.5", "Bio specifc taggers?": "Yes"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON> and <PERSON> (2008)", "paper_date": null, "paper_title": "Adapting a Lexicalized-Grammar Parser to Contrasting Domains", "paper_url": "https://aclweb.org/anthology/papers/D/D08/D08-1050/", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "77.74", "Bio specifc taggers?": "No"}, "model_links": [], "model_name": "<PERSON> et al. (2015)", "paper_date": null, "paper_title": "CCG Supertagging with a Recurrent Neural Network", "paper_url": "http://www.aclweb.org/anthology/P15-2041", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "76.1", "Bio specifc taggers?": "No"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON> et al. , (2010)", "paper_date": null, "paper_title": "Faster Parsing by Supertagger Adaptation", "paper_url": "https://www.aclweb.org/anthology/papers/P/P10/P10-1036/", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "76.0", "Bio specifc taggers?": "No"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON> and <PERSON> (2008)", "paper_date": null, "paper_title": "Adapting a Lexicalized-Grammar Parser to Contrasting Domains", "paper_url": "https://aclweb.org/anthology/papers/D/D08/D08-1050/", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": null, "subtasks": [], "synonyms": [], "task": "Parsing"}, {"categories": [], "datasets": [{"dataset": "CCGBank", "dataset_citations": [], "dataset_links": [], "description": "For Supertagging evaluation on CCGBank, performance is only calculated over the 425 most frequent labels. Models are evaluated based on accuracy.\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "96.1"}, "model_links": [], "model_name": "<PERSON> et al. (2018)", "paper_date": null, "paper_title": "Semi-Supervised Sequence Modeling with Cross-View Training", "paper_url": "https://arxiv.org/abs/1809.08370", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "94.7"}, "model_links": [], "model_name": "<PERSON> et al. (2016)", "paper_date": null, "paper_title": "LSTM CCG Parsing", "paper_url": "https://aclweb.org/anthology/N/N16/N16-1026.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "94.24"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON><PERSON> et al. (2016)", "paper_date": null, "paper_title": "Supertagging with LSTMs", "paper_url": "https://aclweb.org/anthology/N/N16/N16-1027.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "93.26"}, "model_links": [], "model_name": "Low supervision (2016)", "paper_date": null, "paper_title": "Deep multi-task learning with low level tasks supervised at lower layers", "paper_url": "http://anthology.aclweb.org/P16-2038", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "93.00"}, "model_links": [], "model_name": "<PERSON> et al. (2015)", "paper_date": null, "paper_title": "CCG Supertagging with a Recurrent Neural Network", "paper_url": "http://www.aclweb.org/anthology/P15-2041", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "92.00"}, "model_links": [], "model_name": "<PERSON> and <PERSON> (2004)", "paper_date": null, "paper_title": "The Importance of Supertagging for Wide-Coverage CCG Parsing (result from <PERSON> et al. (2016))", "paper_url": "https://aclweb.org/anthology/papers/C/C04/C04-1041/", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": null, "subtasks": [], "synonyms": [], "task": "Supertagging"}], "synonyms": [], "task": "Combinatory Categorical Grammar"}, {"categories": [], "datasets": [{"dataset": "CoNLL 2003 (English)", "dataset_citations": [], "dataset_links": [{"title": "CoNLL 2003 NER task", "url": "http://www.aclweb.org/anthology/W03-0419.pdf"}], "description": "The [CoNLL 2003 NER task](http://www.aclweb.org/anthology/W03-0419.pdf) consists of newswire text from the Reuters RCV1 \ncorpus tagged with four different entity types (PER, LOC, ORG, MISC). Models are evaluated based on span-based F1 on the test set. ♦ used both the train and development splits for training.\n", "sota": {"metrics": ["F1"], "rows": [{"code_links": [], "metrics": {"F1": "93.5"}, "model_links": [], "model_name": "CNN Large + fine-tune (2019)", "paper_date": null, "paper_title": "Cloze-driven Pretraining of Self-attention Networks", "paper_url": "https://arxiv.org/pdf/1903.07785.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "93.47"}, "model_links": [], "model_name": "RNN-CRF+Flair", "paper_date": null, "paper_title": "Improved Differentiable Architecture Search for Language Modeling and Named Entity Recognition", "paper_url": "https://www.aclweb.org/anthology/D19-1367/", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "93.38"}, "model_links": [], "model_name": "LSTM-CRF+ELMo+BERT+Flair", "paper_date": null, "paper_title": "Neural Architectures for Nested NER through Linearization", "paper_url": "https://www.aclweb.org/anthology/P19-1527/", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "93.09"}, "model_links": [], "model_name": "<PERSON>lair embeddings ♦ (2018)", "paper_date": null, "paper_title": "Contextual String Embeddings for Sequence Labeling", "paper_url": "https://drive.google.com/file/d/17yVpFA7MmXaQFTe-HDpZuqw9fJlmzg56/view", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "92.8"}, "model_links": [], "model_name": "BERT Large (2018)", "paper_date": null, "paper_title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding", "paper_url": "https://arxiv.org/abs/1810.04805", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "92.61"}, "model_links": [], "model_name": "CVT + Multi-Task (2018)", "paper_date": null, "paper_title": "Semi-Supervised Sequence Modeling with Cross-View Training", "paper_url": "https://arxiv.org/abs/1809.08370", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "92.4"}, "model_links": [], "model_name": "BERT Base (2018)", "paper_date": null, "paper_title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding", "paper_url": "https://arxiv.org/abs/1810.04805", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "92.22"}, "model_links": [], "model_name": "BiLSTM-CRF+ELMo (2018)", "paper_date": null, "paper_title": "Deep contextualized word representations", "paper_url": "https://arxiv.org/abs/1802.05365", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.93"}, "model_links": [], "model_name": "<PERSON> et al. ♦ (2017)", "paper_date": null, "paper_title": "Semi-supervised sequence tagging with bidirectional language models", "paper_url": "https://arxiv.org/abs/1705.00108", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.87"}, "model_links": [], "model_name": "CRF + AutoEncoder (2018)", "paper_date": null, "paper_title": "Evaluating the Utility of Hand-crafted Features in Sequence Labelling", "paper_url": "http://aclweb.org/anthology/D18-1310", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.73"}, "model_links": [], "model_name": "Bi-LSTM-CRF + Lexical Features (2018)", "paper_date": null, "paper_title": "Robust Lexical Features for Improved Neural Network Named-Entity Recognition", "paper_url": "https://arxiv.org/pdf/1806.03489.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.64"}, "model_links": [], "model_name": "BiLSTM-CRF + IntNet (2018)", "paper_date": null, "paper_title": "Learning Better Internal Structure of Words for Sequence Labeling", "paper_url": "https://www.aclweb.org/anthology/D18-1279", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.62"}, "model_links": [], "model_name": "<PERSON><PERSON> and <PERSON> ♦ (2016)", "paper_date": null, "paper_title": "Named entity recognition with bidirectional LSTM-CNNs", "paper_url": "https://arxiv.org/abs/1511.08308", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.38"}, "model_links": [], "model_name": "HSCRF (2018)", "paper_date": null, "paper_title": "Hybrid semi-Markov CRF for Neural Sequence Labeling", "paper_url": "http://aclweb.org/anthology/P18-2038", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.36"}, "model_links": [], "model_name": "IXA pipes (2016)", "paper_date": null, "paper_title": "Robust multilingual Named Entity Recognition with shallow semi-supervised features", "paper_url": "https://doi.org/10.1016/j.artint.2016.05.003", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.35"}, "model_links": [], "model_name": "NCRF++ (2018)", "paper_date": null, "paper_title": "NCRF++: An Open-source Neural Sequence Labeling Toolkit", "paper_url": "http://www.aclweb.org/anthology/P18-4013", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.24"}, "model_links": [], "model_name": "LM-LSTM-CRF (2018)", "paper_date": null, "paper_title": "Empowering Character-aware Sequence Labeling with Task-Aware Neural Language Model", "paper_url": "https://arxiv.org/pdf/1709.04109.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.26"}, "model_links": [], "model_name": "<PERSON> et al. ♦ (2017)", "paper_date": null, "paper_title": "Transfer Learning for Sequence Tagging with Hierarchical Recurrent Networks", "paper_url": "https://arxiv.org/abs/1703.06345", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "91.21"}, "model_links": [], "model_name": "<PERSON> <PERSON> <PERSON><PERSON> (2016)", "paper_date": null, "paper_title": "End-to-end Sequence Labeling via Bi-directional LSTM-CNNs-CRF", "paper_url": "https://arxiv.org/abs/1603.01354", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "90.94"}, "model_links": [], "model_name": "LSTM-CRF (2016)", "paper_date": null, "paper_title": "Neural Architectures for Named Entity Recognition", "paper_url": "https://arxiv.org/abs/1603.01360", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Ontonotes v5 (English)", "dataset_citations": [], "dataset_links": [{"title": "Ontonotes corpus v5", "url": "https://catalog.ldc.upenn.edu/docs/LDC2013T19/OntoNotes-Release-5.0.pdf"}, {"title": "<PERSON><PERSON><PERSON> et al 2013", "url": "https://www.semanticscholar.org/paper/Towards-Robust-Linguistic-Analysis-using-OntoNotes-P<PERSON><PERSON>-<PERSON>i/a94e4fe6f475e047be5dcc9077f445e496240852"}, {"title": "here", "url": "http://cemantix.org/data/ontonotes.html"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "The [Ontonotes corpus v5](https://catalog.ldc.upenn.edu/docs/LDC2013T19/OntoNotes-Release-5.0.pdf) is a richly annotated corpus with several layers of annotation, including named entities, coreference, part of speech, word sense, propositions, and syntactic parse trees. These annotations are over a large number of tokens, a broad cross-section of domains, and 3 languages (English, Arabic, and Chinese). The NER dataset (of interest here) includes 18 tags, consisting of 11 types (PERSON, ORGANIZATION, etc) and 7 values (DATE, PERCENT, etc), and contains 2 million tokens. The common datasplit used in NER is defined in [<PERSON><PERSON><PERSON> et al 2013](https://www.semanticscholar.org/paper/Towards-Robust-Linguistic-Analysis-using-OntoNotes-Pradhan-Moschitti/a94e4fe6f475e047be5dcc9077f445e496240852) and can be found [here](http://cemantix.org/data/ontonotes.html).\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["F1"], "rows": [{"code_links": [], "metrics": {"F1": "89.71"}, "model_links": [], "model_name": "<PERSON>lair embeddings (2018)", "paper_date": null, "paper_title": "Contextual String Embeddings for Sequence Labeling", "paper_url": "http://aclweb.org/anthology/C18-1139", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "88.81"}, "model_links": [], "model_name": "CVT + Multi-Task (2018)", "paper_date": null, "paper_title": "Semi-Supervised Sequence Modeling with Cross-View Training", "paper_url": "https://arxiv.org/abs/1809.08370", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "87.95"}, "model_links": [], "model_name": "Bi-LSTM-CRF + Lexical Features (2018)", "paper_date": null, "paper_title": "Robust Lexical Features for Improved Neural Network Named-Entity Recognition", "paper_url": "https://arxiv.org/pdf/1806.03489.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "86.99"}, "model_links": [], "model_name": "BiLSTM-CRF (2017)", "paper_date": null, "paper_title": "Fast and Accurate Entity Recognition with Iterated Dilated Convolutions", "paper_url": "https://arxiv.org/pdf/1702.02098.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "86.84"}, "model_links": [], "model_name": "Iterated Dilated CNN (2017)", "paper_date": null, "paper_title": "Fast and Accurate Entity Recognition with Iterated Dilated Convolutions", "paper_url": "https://arxiv.org/pdf/1702.02098.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "86.28"}, "model_links": [], "model_name": "<PERSON><PERSON> and <PERSON> (2016)", "paper_date": null, "paper_title": "Named entity recognition with bidirectional LSTM-CNNs", "paper_url": "https://arxiv.org/abs/1511.08308", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "84.04"}, "model_links": [], "model_name": "Joint Model (2014)", "paper_date": null, "paper_title": "A Joint Model for Entity Analysis: Coreference, Typing, and Linking", "paper_url": "https://pdfs.semanticscholar.org/2eaf/f2205c56378e715d8d12c521d045c0756a76.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "83.45"}, "model_links": [], "model_name": "Averaged Perceptron (2009)", "paper_date": null, "paper_title": "Design Challenges and Misconceptions in Named Entity Recognition (These scores reported in (<PERSON><PERSON><PERSON> and <PERSON> 2014))", "paper_url": "https://www.semanticscholar.org/paper/Design-Challenges-and-Misconceptions-in-Named-<PERSON><PERSON><PERSON>-<PERSON>/27496a2ee337db705e7c611dea1fd8e6f41437c2", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Named entity recognition (NER) is the task of tagging entities in text with their corresponding type.\nApproaches typically use BIO notation, which differentiates the beginning (B) and the inside (I) of entities.\nO is used for non-entity tokens.\nExample:\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Named Entity Recognition"}, {"categories": [], "datasets": [{"dataset": "LexNorm", "dataset_citations": [], "dataset_links": [{"title": "LexNorm", "url": "http://people.eng.unimelb.edu.au/tbaldwin/etc/lexnorm_v1.2.tgz"}, {"title": "<PERSON> and <PERSON> (2011)", "url": "http://aclweb.org/anthology/P/P11/P11-1038.pdf"}, {"title": "<PERSON> and <PERSON><PERSON><PERSON>", "url": "http://www.aclweb.org/anthology/D13-1007"}, {"title": "<PERSON> and <PERSON>(2014)", "url": "http://www.aclweb.org/anthology/P14-3012"}], "description": "The [LexNorm](http://people.eng.unimelb.edu.au/tbaldwin/etc/lexnorm_v1.2.tgz) corpus was originally introduced by [<PERSON> and <PERSON> (2011)](http://aclweb.org/anthology/P/P11/P11-1038.pdf).\nSeveral mistakes in annotation were resolved by [<PERSON> and <PERSON>](http://www.aclweb.org/anthology/D13-1007);\non this page, we only report results on the new dataset. For this dataset, the 2,577\ntweets from [<PERSON> and <PERSON>(2014)](http://www.aclweb.org/anthology/P14-3012) is often\nused as training data, because of its similar annotation style.\nThis dataset is commonly evaluated with accuracy on the non-standard words. This\nmeans that the system knows in advance which words are in need of normalization.\n\u000242\u0003 used a slightly different version of the data\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "87.63"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON> (2017)", "paper_date": null, "paper_title": "MoNoise: Modeling Noise Using a Modular Normalization System", "paper_url": "http://www.let.rug.nl/rob/doc/clin27.paper.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "87.58*"}, "model_links": [], "model_name": "Joint POS + Norm in a Viterbi decoding (2015)", "paper_date": null, "paper_title": "Joint POS Tagging and Text Normalization for Informal Text", "paper_url": "http://www.aaai.org/ocs/index.php/IJCAI/IJCAI15/paper/download/10839/10838", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "86.08"}, "model_links": [], "model_name": "Syllable based (2015)", "paper_date": null, "paper_title": "Tweet Normalization with Syllables", "paper_url": "http://www.aclweb.org/anthology/P15-1089", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "82.06"}, "model_links": [], "model_name": "unLOL (2013)", "paper_date": null, "paper_title": "A Log-Linear Model for Unsupervised Text Normalization", "paper_url": "http://www.aclweb.org/anthology/D13-1007", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Lexical normalization is the task of translating/transforming a non standard text to a standard register.\nExample:\nnew pix comming tomoroe\nnew pictures coming tomorrow\nDatasets usually consists of tweets, since these naturally contain a fair amount of \nthese phenomena.\nFor lexical normalization, only replacements on the word-level are annotated.\nSome corpora include annotation for 1-N and N-1 replacements. However, word\ninsertion/deletion and reordering is not part of the task.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Lexical Normalization"}, {"categories": [], "datasets": [], "description": "", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "TimeBank", "dataset_citations": [], "dataset_links": [{"title": "TimeBank 1.2", "url": "https://catalog.ldc.upenn.edu/LDC2006T08"}], "description": "TimeBank, based on the TIMEX3 standard embedded in ISO-TimeML, is a benchmark corpus containing 64K tokens of English newswire, and annotated for all asepcts of ISO-TimeML - including temporal expressions. TimeBank is freely distributed by the LDC: [TimeBank 1.2](https://catalog.ldc.upenn.edu/LDC2006T08)\nEvaluation is for both entity chunking and attribute annotation, as well as temporal relation accuracy, typically measured with F1 -- although this metric is not sensitive to inconsistencies or free wins from interval logic induction over the whole set.\n", "sota": {"metrics": ["F1 score"], "rows": [{"code_links": [], "metrics": {"F1 score": "0.511"}, "model_links": [], "model_name": "<PERSON><PERSON>", "paper_date": null, "paper_title": "CATENA: CAusal and TEmporal relation extraction from NAtural language texts", "paper_url": "http://www.aclweb.org/anthology/C16-1007", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "0.507"}, "model_links": [], "model_name": "CAEVO", "paper_date": null, "paper_title": "Dense Event Ordering with a Multi-Pass Architecture", "paper_url": "https://www.transacl.org/ojs/index.php/tacl/article/download/255/50", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "TempEval-3", "dataset_citations": [], "dataset_links": [{"title": "TempEval-3", "url": "http://www.aclweb.org/anthology/S13-2001"}, {"title": "TempEval-3 data", "url": "https://www.cs.york.ac.uk/semeval-2013/task1/index.php%3Fid=data.html"}], "description": "The TempEval-3 corpus accompanied the shared [TempEval-3](http://www.aclweb.org/anthology/S13-2001) SemEval task in 2013. This uses a timelines-based metric to assess temporal relation structure. The corpus is fresh and somewhat more varied than TimeBank, though markedly smaller. [TempEval-3 data](https://www.cs.york.ac.uk/semeval-2013/task1/index.php%3Fid=data.html)\n", "sota": {"metrics": ["Temporal awareness"], "rows": [{"code_links": [], "metrics": {"Temporal awareness": "67.2"}, "model_links": [], "model_name": "<PERSON> et al.", "paper_date": null, "paper_title": "A Structured Learning Approach to Temporal Relation Extraction", "paper_url": "http://www.aclweb.org/anthology/D17-1108", "uses_additional_data": false}, {"code_links": [], "metrics": {"Temporal awareness": "30.98"}, "model_links": [], "model_name": "ClearTK", "paper_date": null, "paper_title": "Cleartk-timeml: A minimalist approach to tempeval 2013", "paper_url": "http://www.aclweb.org/anthology/S13-2002", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Temporal information extraction is the identification of chunks/tokens corresponding to temporal intervals, and the extraction and determination of the temporal relations between those. The entities extracted may be temporal expressions (timexes), eventualities (events), or auxiliary signals that support the interpretation of an entity or relation. Relations may be temporal links (tlinks), describing the order of events and times, or subordinate links (slinks) describing modality and other subordinative activity, or aspectual links (alinks) around the various influences aspectuality has on event structure.\nThe markup scheme used for temporal information extraction is well-described in the ISO-TimeML standard, and also on [www.timeml.org](http://www.timeml.org).\n```\n<?xml version=\"1.0\" ?>\n\u0002wzxhzdk:0\u0003\n\u0002wzxhzdk:1\u0003\nPRI20001020.2000.0127 \n NEWS STORY \n \u0002wzxhzdk:2\u000310/20/2000 20:02:07.85\u0002wzxhzdk:3\u0003 \nThe Navy has changed its account of the attack on the USS Cole in Yemen.\n Officials \u0002wzxhzdk:4\u0003now\u0002wzxhzdk:5\u0003 say the ship was hit \u0002wzxhzdk:6\u0003nearly two hours \u0002wzxhzdk:7\u0003after it had docked.\n Initially the Navy said the explosion occurred while several boats were helping\n the ship to tie up. The change raises new questions about how the attackers\n were able to get past the Navy security.\n\u0002wzxhzdk:8\u000310/20/2000 20:02:28.05\u0002wzxhzdk:9\u0003 \n\u0002wzxhzdk:10\u0003\n\u0002wzxhzdk:11\u0003\n\u0002wzxhzdk:12\u0003\n```\nTo avoid leaking knowledge about temporal structure, train, dev and test splits must be made at document level for temporal information extraction.\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Temporal Information Extraction"}, {"categories": [], "datasets": [{"dataset": "TimeBank", "dataset_citations": [], "dataset_links": [{"title": "TimeBank 1.2", "url": "https://catalog.ldc.upenn.edu/LDC2006T08"}], "description": "TimeBank, based on the TIMEX3 standard embedded in ISO-TimeML, is a benchmark corpus containing 64K tokens of English newswire, and annotated for all asepcts of ISO-TimeML - including temporal expressions. TimeBank is freely distributed by the LDC: [TimeBank 1.2](https://catalog.ldc.upenn.edu/LDC2006T08)\n", "sota": {"metrics": ["F1 score"], "rows": [{"code_links": [], "metrics": {"F1 score": "0.89"}, "model_links": [], "model_name": "TIMEN", "paper_date": null, "paper_title": "TIMEN: An Open Temporal Expression Normalisation Resource", "paper_url": "http://aclweb.org/anthology/L12-1015", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "0.876"}, "model_links": [], "model_name": "HeidelTime", "paper_date": null, "paper_title": "A baseline temporal tagger for all languages", "paper_url": "http://aclweb.org/anthology/D15-1063", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "PNT", "dataset_citations": [], "dataset_links": [{"title": "Parsing Time Normalizations corpus", "url": "https://github.com/bethard/anafora-annotations/releases"}, {"title": "SCATE", "url": "http://www.lrec-conf.org/proceedings/lrec2016/pdf/288_Paper.pdf"}, {"title": "SemEval 2018 Task 6", "url": "http://aclweb.org/anthology/S18-1011"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "The [Parsing Time Normalizations corpus](https://github.com/bethard/anafora-annotations/releases) in [SCATE](http://www.lrec-conf.org/proceedings/lrec2016/pdf/288_Paper.pdf) format allows the representation of a wider variety of time expressions than previous approaches. This corpus was release with [SemEval 2018 Task 6](http://aclweb.org/anthology/S18-1011).\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["F1 score"], "rows": [{"code_links": [], "metrics": {"F1 score": "0.764"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON> et al. 2018", "paper_date": null, "paper_title": "From Characters to Time Intervals: New Paradigms for Evaluation and Neural Parsing of Time Normalizations", "paper_url": "http://aclweb.org/anthology/Q18-1025", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "0.74"}, "model_links": [], "model_name": "HeidelTime", "paper_date": null, "paper_title": "A baseline temporal tagger for all languages", "paper_url": "http://aclweb.org/anthology/D15-1063", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1 score": "0.70"}, "model_links": [], "model_name": "Chrono", "paper_date": null, "paper_title": "Chrono at SemEval-2018 task 6: A system for normalizing temporal expressions", "paper_url": "http://aclweb.org/anthology/S18-1012", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Temporal expression normalisation is the grounding of a lexicalisation of a time to a calendar date or other formal temporal representation.\nExample:\n\u0002wzxhzdk:13\u000310/18/2000 21:01:00.65\u0002wzxhzdk:14\u0003\nDozens of Palestinians were wounded in\nscattered clashes in the West Bank and Gaza Strip, \u0002wzxhzdk:15\u0003Wednesday\u0002wzxhzdk:16\u0003,\ndespite the Sharm el-Sheikh truce accord. \n<PERSON> reports on entertainment \u0002wzxhzdk:17\u0003every Saturday\u0002wzxhzdk:18\u0003\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Timex Normalisation"}], "synonyms": [], "task": "Temporal Processing"}, {"categories": [], "datasets": [{"dataset": "RACE", "dataset_citations": [], "dataset_links": [{"title": "RACE dataset", "url": "https://arxiv.org/abs/1704.04683"}, {"title": "here", "url": "http://www.cs.cmu.edu/~glai1/data/race/"}, {"title": "RACE leaderboard", "url": "http://www.qizhexie.com//data/RACE_leaderboard"}], "description": "The [RACE dataset](https://arxiv.org/abs/1704.04683) is a reading comprehension dataset\ncollected from English examinations in China, which are designed for middle school and high school students.\nThe dataset contains more than 28,000 passages and nearly 100,000 questions and can be\ndownloaded [here](http://www.cs.cmu.edu/~glai1/data/race/). Models are evaluated based on accuracy\non middle school examinations (RACE-m), high school examinations (RACE-h), and on the total dataset (RACE).\nThe public leaderboard is available on the [RACE leaderboard](http://www.qizhexie.com//data/RACE_leaderboard).\n", "sota": {"metrics": ["RACE-m", "RACE-h", "RACE"], "rows": [{"code_links": [], "metrics": {"RACE": "81.75", "RACE-h": "80.21", "RACE-m": "85.45"}, "model_links": [], "model_name": "XLNet (2019)", "paper_date": null, "paper_title": "XLNet: Generalized Autoregressive Pretraining for Language Understanding", "paper_url": "https://arxiv.org/pdf/1906.08237.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"RACE": "71.7", "RACE-h": "69.6", "RACE-m": "76.7"}, "model_links": [], "model_name": "OCN_large (2019)", "paper_date": null, "paper_title": "Option Comparison Network for Multiple-choice Reading Comprehension", "paper_url": "https://arxiv.org/pdf/1903.03033.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"RACE": "69.7", "RACE-h": "68.1", "RACE-m": "73.4"}, "model_links": [], "model_name": "DCMN_large (2019)", "paper_date": null, "paper_title": "Dual Co-Matching Network for Multi-choice Reading Comprehension", "paper_url": "https://arxiv.org/pdf/1901.09381.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"RACE": "59.0", "RACE-h": "57.4", "RACE-m": "62.9"}, "model_links": [], "model_name": "Finetuned Transformer LM (2018)", "paper_date": null, "paper_title": "Improving Language Understanding by Generative Pre-Training", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"RACE": "53.3", "RACE-h": "50.3", "RACE-m": "60.2"}, "model_links": [], "model_name": "BiAttention MRU (2018)", "paper_date": null, "paper_title": "Multi-range Reasoning for Machine Comprehension", "paper_url": "https://arxiv.org/abs/1803.09074", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "<PERSON>", "dataset_citations": [], "dataset_links": [{"title": "<PERSON>", "url": "http://aclweb.org/anthology/W17-0906.pdf"}, {"title": "Story Cloze Test Challenge", "url": "https://competitions.codalab.org/competitions/15333"}], "description": "The [Story Cloze Test](http://aclweb.org/anthology/W17-0906.pdf) is a dataset for\nstory understanding that provides systems with four-sentence stories and two possible\nendings. The systems must then choose the correct ending to the story.\nMore details are available on the [Story Cloze Test Challenge](https://competitions.codalab.org/competitions/15333).\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "88.3"}, "model_links": [], "model_name": "Reading Strategies Model (2018)", "paper_date": null, "paper_title": "Improving Machine Reading Comprehension by General Reading Strategies", "paper_url": "https://arxiv.org/pdf/1810.13441v1.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "86.5"}, "model_links": [], "model_name": "Finetuned Transformer LM (2018)", "paper_date": null, "paper_title": "Improving Language Understanding by Generative Pre-Training", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "78.7"}, "model_links": [], "model_name": "<PERSON> et al. (2018)", "paper_date": null, "paper_title": "Narrative Modeling with Memory Chains and Semantic Supervision", "paper_url": "http://aclweb.org/anthology/P18-2045", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "77.6"}, "model_links": [], "model_name": "Hidden Coherence Model (2017)", "paper_date": null, "paper_title": "Story Comprehension for Predicting What Happens Next", "paper_url": "http://aclweb.org/anthology/D17-1168", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "76.5"}, "model_links": [], "model_name": "val-LS-skip (2018)", "paper_date": null, "paper_title": "A Simple and Effective Approach to the Story Cloze Test", "paper_url": "http://aclweb.org/anthology/N18-2015", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "NarrativeQA", "dataset_citations": [], "dataset_links": [{"title": "NarrativeQA", "url": "https://arxiv.org/abs/1712.07040"}], "description": "[NarrativeQA](https://arxiv.org/abs/1712.07040) is a dataset built to encourage deeper comprehension of language. This dataset involves reasoning over reading entire books or movie scripts. This dataset contains approximately 45K question answer pairs in free form text. There are two modes of this dataset (1) reading comprehension over summaries and (2) reading comprehension over entire books/scripts. \n*Note that the above is for the Summary setting. There are no official published results for reading over entire books/stories except for the original paper. \n", "sota": {"metrics": ["BLEU-1", "BLEU-4", "METEOR", "Rouge-L"], "rows": [{"code_links": [], "metrics": {"BLEU-1": "44.35", "BLEU-4": "27.61", "METEOR": "21.80", "Rouge-L": "44.69"}, "model_links": [], "model_name": "DecaProp (2018)", "paper_date": null, "paper_title": "Densely Connected Attention Propagation for Reading Comprehension", "paper_url": "https://arxiv.org/abs/1811.04210", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU-1": "36.55", "BLEU-4": "19.79", "METEOR": "17.87", "Rouge-L": "41.44"}, "model_links": [], "model_name": "BiAttention + DCU-LSTM (2018)", "paper_date": null, "paper_title": "Multi-Granular Sequence Encoding via Dilated Compositional Units for Reading Comprehension", "paper_url": "http://aclweb.org/anthology/D18-1238", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU-1": "33.45", "BLEU-4": "15.69", "METEOR": "15.68", "Rouge-L": "36.74"}, "model_links": [], "model_name": "BiDAF (2017)", "paper_date": null, "paper_title": "Bidirectional Attention Flow for Machine Comprehension", "paper_url": "https://arxiv.org/abs/1611.01603", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Most current question answering datasets frame the task as reading comprehension where the question is about a paragraph\nor document and the answer often is a span in the document. The Machine Reading group\nat UCL also provides an [overview of reading comprehension tasks](https://uclnlp.github.io/ai4exams/data.html).\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Reading Comprehension"}, {"categories": [], "datasets": [], "description": "Question answering is the task of answering a question.\n", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "Quasar", "dataset_citations": [], "dataset_links": [{"title": "Quasar", "url": "https://arxiv.org/abs/1707.03904"}], "description": "[Quasar](https://arxiv.org/abs/1707.03904) is a dataset for open-domain question answering. It includes two parts: (1) The Quasar-S dataset consists of 37,000 cloze-style queries constructed from definitions of software entity tags on the popular website Stack Overflow. (2) The Quasar-T dataset consists of 43,000 open-domain trivia questions and their answers obtained from various internet sources. \n", "sota": {"metrics": ["EM (Quasar-T)", "F1 (Quasar-T)"], "rows": [{"code_links": [], "metrics": {"EM (Quasar-T)": "42.2", "F1 (Quasar-T)": "49.3"}, "model_links": [], "model_name": "Denoising QA (2018)", "paper_date": null, "paper_title": "Denoising Distantly Supervised Open-Domain Question Answering", "paper_url": "http://aclweb.org/anthology/P18-1161", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM (Quasar-T)": "38.6", "F1 (Quasar-T)": "46.9"}, "model_links": [], "model_name": "DecaProp (2018)", "paper_date": null, "paper_title": "Densely Connected Attention Propagation for Reading Comprehension", "paper_url": "https://arxiv.org/abs/1811.04210", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM (Quasar-T)": "35.3", "F1 (Quasar-T)": "41.7"}, "model_links": [], "model_name": "R^3 (2018)", "paper_date": null, "paper_title": "R^3: <PERSON>inforce<PERSON> <PERSON>er-Reader for Open-Domain Question Answering", "paper_url": "https://aaai.org/ocs/index.php/AAAI/AAAI18/paper/view/16712/16165", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM (Quasar-T)": "25.9", "F1 (Quasar-T)": "28.5"}, "model_links": [], "model_name": "BiDAF (2017)", "paper_date": null, "paper_title": "Bidirectional Attention Flow for Machine Comprehensio", "paper_url": "https://arxiv.org/abs/1611.01603", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM (Quasar-T)": "26.4", "F1 (Quasar-T)": "26.4"}, "model_links": [], "model_name": "GA (2017)", "paper_date": null, "paper_title": "Gated-Attention Readers for Text Comprehension", "paper_url": "https://arxiv.org/pdf/1606.01549", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "SearchQA", "dataset_citations": [], "dataset_links": [{"title": "SearchQA", "url": "https://arxiv.org/abs/1704.05179"}], "description": "[SearchQA](https://arxiv.org/abs/1704.05179) was constructed to reflect a full pipeline of general question-answering. SearchQA consists of more than 140k question-answer pairs with each pair having 49.6 snippets on average. Each question-answer-context tuple of the SearchQA comes with additional meta-data such as the snippet's URL.\n", "sota": {"metrics": ["Unigram Acc", "N-gram F1", "EM", "F1"], "rows": [{"code_links": [], "metrics": {"EM": "56.8", "F1": "63.6", "N-gram F1": "70.8", "Unigram Acc": "62.2"}, "model_links": [], "model_name": "DecaProp (2018)", "paper_date": null, "paper_title": "Densely Connected Attention Propagation for Reading Comprehension", "paper_url": "https://arxiv.org/abs/1811.04210", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM": "58.8", "F1": "64.5", "N-gram F1": "-", "Unigram Acc": "-"}, "model_links": [], "model_name": "Denoising QA (2018)", "paper_date": null, "paper_title": "Denoising Distantly Supervised Open-Domain Question Answering", "paper_url": "http://aclweb.org/anthology/P18-1161", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM": "49.0", "F1": "55.3", "N-gram F1": "-", "Unigram Acc": "-"}, "model_links": [], "model_name": "R^3 (2018)", "paper_date": null, "paper_title": "R^3: <PERSON>inforce<PERSON> <PERSON>er-Reader for Open-Domain Question Answering", "paper_url": "https://aaai.org/ocs/index.php/AAAI/AAAI18/paper/view/16712/16165", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM": "-", "F1": "-", "N-gram F1": "59.5", "Unigram Acc": "49.4"}, "model_links": [], "model_name": "Bi-Attention + DCU-LSTM (2018)", "paper_date": null, "paper_title": "Multi-Granular Sequence Encoding via Dilated Compositional Units for Reading Comprehension", "paper_url": "http://aclweb.org/anthology/D18-1238", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM": "-", "F1": "-", "N-gram F1": "56.6", "Unigram Acc": "46.8"}, "model_links": [], "model_name": "AMANDA (2018)", "paper_date": null, "paper_title": "A Question-Focused Multi-Factor Attention Network for Question Answering", "paper_url": "https://arxiv.org/abs/1801.08290", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM": "-", "F1": "-", "N-gram F1": "53.4", "Unigram Acc": "46.8"}, "model_links": [], "model_name": "Focused Hierarchical RNN (2018)", "paper_date": null, "paper_title": "Focused Hierarchical RNNs for Conditional Sequence Processing", "paper_url": "http://proceedings.mlr.press/v80/ke18a/ke18a.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"EM": "-", "F1": "-", "N-gram F1": "22.8", "Unigram Acc": "41.3"}, "model_links": [], "model_name": "ASR (2016)", "paper_date": null, "paper_title": "Text Understanding with the Attention Sum Reader Network", "paper_url": "https://arxiv.org/abs/1603.01547", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": null, "subtasks": [], "synonyms": [], "task": "Open-Domain Question Answering"}], "synonyms": [], "task": "Question Answering"}, {"categories": [], "datasets": [{"dataset": "New York Times Corpus", "dataset_citations": [], "dataset_links": [{"title": "<PERSON><PERSON><PERSON> et al, 2010", "url": "http://www.riedelcastro.org//publications/papers/riedel10modeling.pdf"}, {"title": "New York Times Annotated Corpus", "url": "https://catalog.ldc.upenn.edu/ldc2008t19"}], "description": "The standard corpus for distantly supervised relationship extraction is the New York Times (NYT) corpus, published in\n[<PERSON><PERSON><PERSON> et al, 2010](http://www.riedelcastro.org//publications/papers/riedel10modeling.pdf).\nThis contains text from the [New York Times Annotated Corpus](https://catalog.ldc.upenn.edu/ldc2008t19) with named\nentities extracted from the text using the Stanford NER system and automatically linked to entities in the Freebase\nknowledge base. Pairs of named entities are labelled with relationship types by aligning them against facts in the\nFreebase knowledge base. (The process of using a separate database to provide label is known as 'distant supervision')\nExample:\n(founded_by, Elevation_Partners, Roger_<PERSON>)\nDifferent papers have reported various metrics since the release of the dataset, making it difficult to compare systems\ndirectly. The main metrics used are either precision at N results or plots of the precision-recall. The range of recall\nhas increased over the years as systems improve, with earlier systems having very low precision at 30% recall.\n(+) Obtained from results in the paper \"Neural Relation Extraction with Selective Attention over Instances\"\n", "sota": {"metrics": ["P@10%", "P@30%"], "rows": [{"code_links": [], "metrics": {"P@10%": "84.9", "P@30%": "72.8"}, "model_links": [], "model_name": "HRERE (2019)", "paper_date": null, "paper_title": "Connecting Language and Knowledge with Heterogeneous Representations for Neural Relation Extraction", "paper_url": "https://arxiv.org/abs/1903.10126", "uses_additional_data": false}, {"code_links": [], "metrics": {"P@10%": "81.7", "P@30%": "61.8"}, "model_links": [], "model_name": "PCNN+noise_convert+cond_opt (2019)", "paper_date": null, "paper_title": "Improving Distantly Supervised Relation Extraction with Neural Noise Converter and Conditional Optimal Selector", "paper_url": "https://arxiv.org/pdf/1811.05616.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"P@10%": "78.9", "P@30%": "62.4"}, "model_links": [], "model_name": "Intra- and Inter-Bag (2019)", "paper_date": null, "paper_title": "Distant Supervision Relation Extraction with Intra-Bag and Inter-Bag Attentions", "paper_url": "https://arxiv.org/pdf/1904.00143.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"P@10%": "73.6", "P@30%": "59.5"}, "model_links": [], "model_name": "RESIDE (2018)", "paper_date": null, "paper_title": "RESIDE: Improving Distantly-Supervised Neural Relation Extraction using Side Information", "paper_url": "http://malllabiisc.github.io/publications/papers/reside_emnlp18.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"P@10%": "69.4", "P@30%": "51.8"}, "model_links": [], "model_name": "PCNN+ATT (2016)", "paper_date": null, "paper_title": "Neural Relation Extraction with Selective Attention over Instances", "paper_url": "http://www.aclweb.org/anthology/P16-1200", "uses_additional_data": false}, {"code_links": [], "metrics": {"P@10%": "60.7+", "P@30%": "-"}, "model_links": [], "model_name": "MIML-RE (2012)", "paper_date": null, "paper_title": "Multi-instance Multi-label Learning for Relation Extraction", "paper_url": "http://www.aclweb.org/anthology/D12-1042", "uses_additional_data": false}, {"code_links": [], "metrics": {"P@10%": "60.9+", "P@30%": "-"}, "model_links": [], "model_name": "MultiR (2011)", "paper_date": null, "paper_title": "Knowledge-Based Weak Supervision for Information Extraction of Overlapping Relations", "paper_url": "http://www.aclweb.org/anthology/P11-1055", "uses_additional_data": false}, {"code_links": [], "metrics": {"P@10%": "39.9+", "P@30%": "-"}, "model_links": [], "model_name": " (2009)", "paper_date": null, "paper_title": "Distant supervision for relation extraction without labeled data", "paper_url": "http://www.aclweb.org/anthology/P09-1113", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "TACRED", "dataset_citations": [], "dataset_links": [{"title": "TACRED", "url": "https://nlp.stanford.edu/projects/tacred/"}, {"title": "corpus", "url": "https://catalog.ldc.upenn.edu/LDC2018T03"}, {"title": "TAC Knowledge Base Population (TAC KBP) challenges", "url": "https://tac.nist.gov/2017/KBP/index.html"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "[TACRED](https://nlp.stanford.edu/projects/tacred/) is a large-scale relation extraction dataset with 106,264 examples built over newswire and web text from the [corpus](https://catalog.ldc.upenn.edu/LDC2018T03) used in the yearly [TAC Knowledge Base Population (TAC KBP) challenges](https://tac.nist.gov/2017/KBP/index.html). Examples in TACRED cover 41 relation types as used in the TAC KBP challenges (e.g., per:schools_attended and org:members) or are labeled as no_relation if no defined relation is held. These examples are created by combining available human annotations from the TAC KBP challenges and crowdsourcing.\nExample:\n(per:city_of_death, <PERSON>, Tampa)\nThe main evaluation metric used is micro-averaged F1 over instances with proper relationships (i.e. excluding the\nno_relation type).\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["F1"], "rows": [{"code_links": [], "metrics": {"F1": ""}, "model_links": [], "model_name": "Matching-the-Blanks (2019)", "paper_date": null, "paper_title": "Matching the Blanks: Distributional Similarity for Relation Learning", "paper_url": "https://www.aclweb.org/anthology/P19-1279", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": ""}, "model_links": [], "model_name": "C-GCN + PA-LSTM (2018)", "paper_date": null, "paper_title": "Graph Convolution over Pruned Dependency Trees Improves Relation Extraction", "paper_url": "http://aclweb.org/anthology/D18-1244", "uses_additional_data": false}, {"code_links": [], "metrics": {"F1": "65.1"}, "model_links": [], "model_name": "PA-LSTM (2017)", "paper_date": null, "paper_title": "Position-aware Attention and Supervised Data Improve Slot Filling", "paper_url": "http://aclweb.org/anthology/D17-1004", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Relationship extraction is the task of extracting semantic relationships from a text. Extracted relationships usually\noccur between two or more entities of a certain type (e.g. Person, Organisation, Location) and fall into a number of\nsemantic categories (e.g. married to, employed by, lives in).\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Relationship Extraction"}, {"categories": [], "datasets": [{"dataset": "Penn Treebank", "dataset_citations": [], "dataset_links": [], "description": "A standard dataset for POS tagging is the Wall Street Journal (WSJ) portion of the Penn Treebank, containing 45 \ndifferent POS tags. Sections 0-18 are used for training, sections 19-21 for development, and sections \n22-24 for testing. Models are evaluated based on accuracy.\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "97.96"}, "model_links": [], "model_name": "Meta BiLSTM (2018)", "paper_date": null, "paper_title": "Morphosyntactic Tagging with a Meta-BiLSTM Model over Context Sensitive Token Encodings", "paper_url": "https://arxiv.org/abs/1805.08237", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.85"}, "model_links": [], "model_name": "<PERSON>lair embeddings (2018)", "paper_date": null, "paper_title": "Contextual String Embeddings for Sequence Labeling", "paper_url": "http://aclweb.org/anthology/C18-1139", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.78"}, "model_links": [], "model_name": "Char Bi-LSTM (2015)", "paper_date": null, "paper_title": "Finding Function in Form: Compositional Character Models for Open Vocabulary Word Representation", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1176.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.59"}, "model_links": [], "model_name": "Adversarial Bi-LSTM (2018)", "paper_date": null, "paper_title": "Robust Multilingual Part-of-Speech Tagging via Adversarial Training", "paper_url": "https://arxiv.org/abs/1711.04903", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.58"}, "model_links": [], "model_name": "BiLSTM-CRF + IntNet (2018)", "paper_date": null, "paper_title": "Learning Better Internal Structure of Words for Sequence Labeling", "paper_url": "https://www.aclweb.org/anthology/D18-1279", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.55"}, "model_links": [], "model_name": "<PERSON> et al. (2017)", "paper_date": null, "paper_title": "Transfer Learning for Sequence Tagging with Hierarchical Recurrent Networks", "paper_url": "https://arxiv.org/abs/1703.06345", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.55"}, "model_links": [], "model_name": "<PERSON> <PERSON> <PERSON><PERSON> (2016)", "paper_date": null, "paper_title": "End-to-end Sequence Labeling via Bi-directional LSTM-CNNs-CRF", "paper_url": "https://arxiv.org/abs/1603.01354", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.53"}, "model_links": [], "model_name": "LM-LSTM-CRF (2018)", "paper_date": null, "paper_title": "Empowering Character-aware Sequence Labeling with Task-Aware Neural Language Model", "paper_url": "https://arxiv.org/pdf/1709.04109.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.49"}, "model_links": [], "model_name": "NCRF++ (2018)", "paper_date": null, "paper_title": "NCRF++: An Open-source Neural Sequence Labeling Toolkit", "paper_url": "http://www.aclweb.org/anthology/P18-4013", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.4"}, "model_links": [], "model_name": "Feed Forward (2016)", "paper_date": null, "paper_title": "Supertagging with LSTMs", "paper_url": "https://aclweb.org/anthology/N/N16/N16-1027.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.36"}, "model_links": [], "model_name": "Bi-LSTM (2017)", "paper_date": null, "paper_title": "Finding Function in Form: Compositional Character Models for Open Vocabulary Word Representation", "paper_url": "https://www.aclweb.org/anthology/D/D15/D15-1176.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "97.22"}, "model_links": [], "model_name": "Bi-LSTM (2016)", "paper_date": null, "paper_title": "Multilingual Part-of-Speech Tagging with Bidirectional Long Short-Term Memory Models and Auxiliary Loss", "paper_url": "https://arxiv.org/abs/1604.05529", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Social media", "dataset_citations": [], "dataset_links": [{"title": "<PERSON> (2011)", "url": "https://www.aclweb.org/anthology/D11-1141"}], "description": "The [<PERSON> (2011)](https://www.aclweb.org/anthology/D11-1141) dataset has become the benchmark for social media part-of-speech tagging. This is comprised of  some 50K tokens of English social media sampled in late 2011, and is tagged using an extended version of the PTB tagset.\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "90.53"}, "model_links": [], "model_name": "FastText + CNN + CRF", "paper_date": null, "paper_title": "Twitter word embeddings (<PERSON><PERSON> et al. 2019 (Chapter 3))", "paper_url": "https://fredericgodin.com/research/twitter-word-embeddings/", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "90.0 ± 0.5"}, "model_links": [], "model_name": "CMU", "paper_date": null, "paper_title": "Improved Part-of-Speech Tagging for Online Conversational Text with Word Clusters", "paper_url": "http://www.cs.cmu.edu/~ark/TweetNLP/owoputi+etal.naacl13.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "88.69"}, "model_links": [], "model_name": "GATE", "paper_date": null, "paper_title": "Twitter Part-of-Speech Tagging for All: Overcoming Sparse and Noisy Data", "paper_url": "https://www.aclweb.org/anthology/R13-1026", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "UD", "dataset_citations": [], "dataset_links": [{"title": "Universal Dependencies (UD)", "url": "http://universaldependencies.org/"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "[Universal Dependencies (UD)](http://universaldependencies.org/) is a framework for \ncross-linguistic grammatical annotation, which contains more than 100 treebanks in over 60 languages.\nModels are typically evaluated based on the average test accuracy across 21 high-resource languages (♦ evaluated on 17 languages).\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["Avg accuracy"], "rows": [{"code_links": [], "metrics": {"Avg accuracy": "96.77"}, "model_links": [], "model_name": "Multilingual BERT and BPEmb (2019)", "paper_date": null, "paper_title": "Sequence Tagging with Contextual and Non-Contextual Subword Representations: A Multilingual Evaluation", "paper_url": "https://arxiv.org/abs/1906.01569", "uses_additional_data": false}, {"code_links": [], "metrics": {"Avg accuracy": "96.65"}, "model_links": [], "model_name": "Adversarial Bi-LSTM (2018)", "paper_date": null, "paper_title": "Robust Multilingual Part-of-Speech Tagging via Adversarial Training", "paper_url": "https://arxiv.org/abs/1711.04903", "uses_additional_data": false}, {"code_links": [], "metrics": {"Avg accuracy": "96.62"}, "model_links": [], "model_name": "MultiBPEmb (2019)", "paper_date": null, "paper_title": "Sequence Tagging with Contextual and Non-Contextual Subword Representations: A Multilingual Evaluation", "paper_url": "https://arxiv.org/abs/1906.01569", "uses_additional_data": false}, {"code_links": [], "metrics": {"Avg accuracy": "96.40"}, "model_links": [], "model_name": "Bi-LSTM (2016)", "paper_date": null, "paper_title": "Multilingual Part-of-Speech Tagging with Bidirectional Long Short-Term Memory Models and Auxiliary Loss", "paper_url": "https://arxiv.org/abs/1604.05529", "uses_additional_data": false}, {"code_links": [], "metrics": {"Avg accuracy": "95.55"}, "model_links": [], "model_name": "Joint Bi-LSTM ♦ (2017)", "paper_date": null, "paper_title": "A Novel Neural Network Model for Joint POS Tagging and Graph-based Dependency Parsing", "paper_url": "https://arxiv.org/abs/1705.05952", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Part-of-speech tagging (POS tagging) is the task of tagging a word in a text with its part of speech.\nA part of speech is a category of words with similar grammatical properties. Common English\nparts of speech are noun, verb, adjective, adverb, pronoun, preposition, conjunction, etc.\nExample: \n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Part-Of-Speech Tagging"}, {"categories": [], "datasets": [{"dataset": "WMT 2014 EN-DE", "dataset_citations": [], "dataset_links": [], "description": "Models are evaluated on the English-German dataset of the Ninth Workshop on Statistical Machine Translation (WMT 2014) based\non BLEU.\n", "sota": {"metrics": ["BLEU"], "rows": [{"code_links": [], "metrics": {"BLEU": "35.0"}, "model_links": [], "model_name": "Transformer Big + BT (2018)", "paper_date": null, "paper_title": "Understanding Back-Translation at Scale", "paper_url": "https://arxiv.org/pdf/1808.09381.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "33.3"}, "model_links": [], "model_name": "DeepL", "paper_date": null, "paper_title": "DeepL Press release", "paper_url": "https://www.deepl.com/press.html", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "29.9"}, "model_links": [], "model_name": "MUSE (2019)", "paper_date": null, "paper_title": "MUSE: Parallel Multi-Scale Attention for Sequence to Sequence Learning", "paper_url": "https://arxiv.org/abs/1911.09483", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "29.7"}, "model_links": [], "model_name": "DynamicConv (2019)", "paper_date": null, "paper_title": "Pay Less Attention With Lightweight and Dynamic Convolutions", "paper_url": "https://arxiv.org/abs/1901.10430", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "29.52"}, "model_links": [], "model_name": "AdvSoft + Transformer Big (2019)", "paper_date": null, "paper_title": "Improving Neural Language Modeling via Adversarial Training", "paper_url": "http://proceedings.mlr.press/v97/wang19f/wang19f.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "29.3"}, "model_links": [], "model_name": "Transformer Big (2018)", "paper_date": null, "paper_title": "Scaling Neural Machine Translation", "paper_url": "https://arxiv.org/abs/1806.00187", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "28.5*"}, "model_links": [], "model_name": "RNMT+ (2018)", "paper_date": null, "paper_title": "The Best of Both Worlds: Combining Recent Advances in Neural Machine Translation", "paper_url": "https://arxiv.org/abs/1804.09849", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "28.4"}, "model_links": [], "model_name": "Transformer Big (2017)", "paper_date": null, "paper_title": "Attention Is All You Need", "paper_url": "https://arxiv.org/abs/1706.03762", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "27.3"}, "model_links": [], "model_name": "Transformer Base (2017)", "paper_date": null, "paper_title": "Attention Is All You Need", "paper_url": "https://arxiv.org/abs/1706.03762", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "26.03"}, "model_links": [], "model_name": "MoE (2017)", "paper_date": null, "paper_title": "Outrageously Large Neural Networks: The Sparsely-Gated Mixture-of-Experts Layer", "paper_url": "https://arxiv.org/abs/1701.06538", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "25.16"}, "model_links": [], "model_name": "ConvS2S (2017)", "paper_date": null, "paper_title": "Convolutional Sequence to Sequence Learning", "paper_url": "https://arxiv.org/abs/1705.03122", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "WMT 2014 EN-FR", "dataset_citations": [], "dataset_links": [{"title": "Go back to the README", "url": "../README.md"}], "description": "Similarly, models are evaluated on the English-French dataset of the Ninth Workshop on Statistical Machine Translation (WMT 2014) based\non BLEU.\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["BLEU"], "rows": [{"code_links": [], "metrics": {"BLEU": "45.9"}, "model_links": [], "model_name": "DeepL", "paper_date": null, "paper_title": "DeepL Press release", "paper_url": "https://www.deepl.com/press.html", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "45.6"}, "model_links": [], "model_name": "Transformer Big + BT (2018)", "paper_date": null, "paper_title": "Understanding Back-Translation at Scale", "paper_url": "https://arxiv.org/pdf/1808.09381.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "43.5"}, "model_links": [], "model_name": "MUSE (2019)", "paper_date": null, "paper_title": "MUSE: Parallel Multi-Scale Attention for Sequence to Sequence Learning", "paper_url": "https://arxiv.org/abs/1911.09483", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "43.2"}, "model_links": [], "model_name": "DynamicConv (2019)", "paper_date": null, "paper_title": "Pay Less Attention With Lightweight and Dynamic Convolutions", "paper_url": "https://arxiv.org/abs/1901.10430", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "43.2"}, "model_links": [], "model_name": "Transformer Big (2018)", "paper_date": null, "paper_title": "Scaling Neural Machine Translation", "paper_url": "https://arxiv.org/abs/1806.00187", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "41.0*"}, "model_links": [], "model_name": "RNMT+ (2018)", "paper_date": null, "paper_title": "The Best of Both Worlds: Combining Recent Advances in Neural Machine Translation", "paper_url": "https://arxiv.org/abs/1804.09849", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "41.0"}, "model_links": [], "model_name": "Transformer Big (2017)", "paper_date": null, "paper_title": "Attention Is All You Need", "paper_url": "https://arxiv.org/abs/1706.03762", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "40.56"}, "model_links": [], "model_name": "MoE (2017)", "paper_date": null, "paper_title": "Outrageously Large Neural Networks: The Sparsely-Gated Mixture-of-Experts Layer", "paper_url": "https://arxiv.org/abs/1701.06538", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "40.46"}, "model_links": [], "model_name": "ConvS2S (2017)", "paper_date": null, "paper_title": "Convolutional Sequence to Sequence Learning", "paper_url": "https://arxiv.org/abs/1705.03122", "uses_additional_data": false}, {"code_links": [], "metrics": {"BLEU": "38.1"}, "model_links": [], "model_name": "Transformer Base (2017)", "paper_date": null, "paper_title": "Attention Is All You Need", "paper_url": "https://arxiv.org/abs/1706.03762", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Machine translation is the task of translating a sentence in a source language to a different target language. \nResults with a * indicate that the mean test score over the the best window based on average dev-set BLEU score over \n21 consecutive evaluations is reported as in [<PERSON> et al. (2018)](https://arxiv.org/abs/1804.09849).\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Machine Translation"}, {"categories": [], "datasets": [{"dataset": "MultiNLI", "dataset_citations": [], "dataset_links": [{"title": "Multi-Genre Natural Language Inference (MultiNLI) corpus", "url": "https://arxiv.org/abs/1704.05426"}, {"title": "MultiNLI", "url": "https://www.nyu.edu/projects/bowman/multinli/"}, {"title": "in-genre (matched)", "url": "https://www.kaggle.com/c/multinli-matched-open-evaluation/leaderboard"}, {"title": "cross-genre (mismatched)", "url": "https://www.kaggle.com/c/multinli-mismatched-open-evaluation/leaderboard"}], "description": "The [Multi-Genre Natural Language Inference (MultiNLI) corpus](https://arxiv.org/abs/1704.05426)\ncontains around 433k hypothesis/premise pairs. It is similar to the SNLI corpus, but\ncovers a range of genres of spoken and written text and supports cross-genre evaluation. The data\ncan be downloaded from the [MultiNLI](https://www.nyu.edu/projects/bowman/multinli/) website.\nPublic leaderboards for [in-genre (matched)](https://www.kaggle.com/c/multinli-matched-open-evaluation/leaderboard) \nand [cross-genre (mismatched)](https://www.kaggle.com/c/multinli-mismatched-open-evaluation/leaderboard)\nevaluation are available, but entries do not correspond to published models.\n", "sota": {"metrics": ["Matched", "Mismatched"], "rows": [{"code_links": [], "metrics": {"Matched": "90.8", "Mismatched": "90.2"}, "model_links": [], "model_name": "RoBERTa (2019)", "paper_date": null, "paper_title": "RoBERTa: A Robustly Optimized BERT Pretraining Approach", "paper_url": "https://arxiv.org/pdf/1907.11692.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Matched": "90.2", "Mismatched": "89.8"}, "model_links": [], "model_name": "XLNet-Large (2019)", "paper_date": null, "paper_title": "XLNet: Generalized Autoregressive Pretraining for Language Understanding", "paper_url": "https://arxiv.org/pdf/1906.08237.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Matched": "87.9", "Mismatched": "87.4"}, "model_links": [], "model_name": "MT-DNN-ensemble (2019)", "paper_date": null, "paper_title": "Improving Multi-Task Deep Neural Networks via Knowledge Distillation for Natural Language Understanding", "paper_url": "https://arxiv.org/pdf/1904.09482.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Matched": "87.6", "Mismatched": "87.2"}, "model_links": [], "model_name": "Snorkel MeTaL (2018)", "paper_date": null, "paper_title": "Training Complex Models with Multi-Task Weak Supervision", "paper_url": "https://arxiv.org/pdf/1810.02840.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Matched": "82.1", "Mismatched": "81.4"}, "model_links": [], "model_name": "Finetuned Transformer LM (2018)", "paper_date": null, "paper_title": "Improving Language Understanding by Generative Pre-Training", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Matched": "72.2", "Mismatched": "72.1"}, "model_links": [], "model_name": "Multi-task BiLSTM + Attn (2018)", "paper_date": null, "paper_title": "GLUE: A Multi-Task Benchmark and Analysis Platform for Natural Language Understanding", "paper_url": "https://arxiv.org/abs/1804.07461", "uses_additional_data": false}, {"code_links": [], "metrics": {"Matched": "71.4", "Mismatched": "71.3"}, "model_links": [], "model_name": "GenSen (2018)", "paper_date": null, "paper_title": "Learning General Purpose Distributed Sentence Representations via Large Scale Multi-task Learning", "paper_url": "https://arxiv.org/abs/1804.00079", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "SciTail", "dataset_citations": [], "dataset_links": [{"title": "SciTail", "url": "http://ai2-website.s3.amazonaws.com/publications/scitail-aaai-2018_cameraready.pdf"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "The [SciTail](http://ai2-website.s3.amazonaws.com/publications/scitail-aaai-2018_cameraready.pdf)\nentailment dataset consists of 27k. In contrast to the SNLI and MultiNLI, it was not crowd-sourced\nbut created from sentences that already exist \"in the wild\". Hypotheses were created from\nscience questions and the corresponding answer candidates, while relevant web sentences from a large\ncorpus were used as premises. Models are evaluated based on accuracy.\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "88.3"}, "model_links": [], "model_name": "Finetuned Transformer LM (2018)", "paper_date": null, "paper_title": "Improving Language Understanding by Generative Pre-Training", "paper_url": "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "86.0"}, "model_links": [], "model_name": "Hierarchical BiLSTM Max Pooling (2018)", "paper_date": null, "paper_title": "Natural Language Inference with Hierarchical BiLSTM Max Pooling", "paper_url": "https://arxiv.org/abs/1808.08762", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "83.3"}, "model_links": [], "model_name": "CAFE (2018)", "paper_date": null, "paper_title": "A Compare-Propagate Architecture with Alignment Factorization for Natural Language Inference", "paper_url": "https://arxiv.org/abs/1801.00102", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Natural language inference is the task of determining whether a \"hypothesis\" is \ntrue (entailment), false (contradiction), or undetermined (neutral) given a \"premise\".\nExample:\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "Natural Language Inference"}, {"categories": [], "datasets": [], "description": "", "source_link": null, "subtasks": [{"categories": [], "datasets": [{"dataset": "IEMOCAP", "dataset_citations": [], "dataset_links": [{"title": "<PERSON><PERSON>  et  al., 2008", "url": "https://link.springer.com/article/10.1007/s10579-008-9076-6"}], "description": "The  IEMOCAP ([<PERSON><PERSON>  et  al., 2008](https://link.springer.com/article/10.1007/s10579-008-9076-6)) contains the acts of 10 speakers in a two-way conversation segmented into utterances. The medium of the conversations in all the videos is English. The database contains the following categorical labels: anger, happiness, sadness, neutral, excitement, frustration, fear, surprise,  and other.\nMonologue:\nConversational:\nConversational setting enables the models to capture emotions expressed by the speakers in a conversation. Inter speaker dependencies are considered in this setting.\n", "sota": {"metrics": [], "rows": []}, "subdatasets": [{"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "76.5%"}, "model_links": [], "model_name": "CHFusion (2017)", "paper_date": null, "paper_title": "Multimodal Sentiment Analysis using Hierarchical Fusion with Context Modeling", "paper_url": "https://arxiv.org/pdf/1806.06228.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "74.10%"}, "model_links": [], "model_name": "bc-LSTM (2017)", "paper_date": null, "paper_title": "Context-Dependent Sentiment Analysis in User-Generated Videos", "paper_url": "http://sentic.net/context-dependent-sentiment-analysis-in-user-generated-videos.pdf", "uses_additional_data": false}]}, "subdataset": "Monologue", "subdatasets": []}, {"dataset_citations": [], "dataset_links": [], "description": "", "sota": {"metrics": ["Weighted Accuracy (WAA)"], "rows": [{"code_links": [], "metrics": {"Weighted Accuracy (WAA)": "77.62%"}, "model_links": [], "model_name": "CMN (2018)", "paper_date": null, "paper_title": "Conversational Memory Network for Emotion Recognition in Dyadic Dialogue Videos", "paper_url": "http://aclweb.org/anthology/N18-1193", "uses_additional_data": false}, {"code_links": [], "metrics": {"Weighted Accuracy (WAA)": "75.08"}, "model_links": [], "model_name": "Memn2n", "paper_date": null, "paper_title": "Conversational Memory Network for Emotion Recognition in Dyadic Dialogue Videos", "paper_url": "http://aclweb.org/anthology/N18-1193", "uses_additional_data": false}]}, "subdataset": "Conversational", "subdatasets": []}]}], "description": "", "source_link": null, "subtasks": [], "synonyms": [], "task": "Multimodal Emotion Recognition"}, {"categories": [], "datasets": [{"dataset": "MOSI", "dataset_citations": [], "dataset_links": [{"title": "<PERSON><PERSON><PERSON> et al., 2016", "url": "https://arxiv.org/pdf/1606.06259.pdf"}], "description": "The MOSI dataset ([<PERSON><PERSON><PERSON> et al., 2016](https://arxiv.org/pdf/1606.06259.pdf)) is a dataset rich in sentimental expressions where 93 people review topics in English. The videos are segmented with each segments sentiment label scored between +3 (strong positive) to -3 (strong negative)  by  5  annotators.\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "80.3%"}, "model_links": [], "model_name": "bc-LSTM (2017)", "paper_date": null, "paper_title": "Context-Dependent Sentiment Analysis in User-Generated Videos", "paper_url": "http://sentic.net/context-dependent-sentiment-analysis-in-user-generated-videos.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "77.1%"}, "model_links": [], "model_name": "MARN (2018)", "paper_date": null, "paper_title": "Multi-attention Recurrent Network for Human Communication Comprehension", "paper_url": "https://arxiv.org/pdf/1802.00923.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": null, "subtasks": [], "synonyms": [], "task": "Multimodal Sentiment Analysis"}, {"categories": [], "datasets": [{"dataset": "VQAv2", "dataset_citations": [], "dataset_links": [], "description": "Given an image and a natural language question about the image, the task is to provide an accurate natural language answer\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "73.4"}, "model_links": [], "model_name": "UNITER (2019)", "paper_date": null, "paper_title": "UNITER: LEARNING UNIVERSAL IMAGE-TEXT REPRESENTATIONS", "paper_url": "https://arxiv.org/pdf/1909.11740.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "72.54"}, "model_links": [], "model_name": "LXMERT (2019)", "paper_date": null, "paper_title": "LXMERT: Learning Cross-Modality Encoder Representations from Transformers", "paper_url": "https://arxiv.org/abs/1908.07490", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "GQA - Visual Reasoning in the Real World", "dataset_citations": [], "dataset_links": [], "description": "GQA focuses on real-world compositional reasoning. \n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "73.24"}, "model_links": [], "model_name": "Ka<PERSON>ao <PERSON>", "paper_date": null, "paper_title": "GQA Challenge", "paper_url": "https://drive.google.com/file/d/1CtFk0ldbN5w2qhwvfKrNzAFEj-I9Tjgy/view", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "60.3"}, "model_links": [], "model_name": "LXMERT (2019)", "paper_date": null, "paper_title": "LXMERT: Learning Cross-Modality Encoder Representations from Transformers", "paper_url": "https://arxiv.org/abs/1908.07490", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "TextVQA", "dataset_citations": [], "dataset_links": [], "description": "TextVQA requires models to read and reason about text in an image to answer questions based on them.\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "40.46"}, "model_links": [], "model_name": "M4C (2020)", "paper_date": null, "paper_title": "Iterative Answer Prediction with Pointer-Augmented Multimodal Transformers for TextVQA", "paper_url": "https://arxiv.org/pdf/1911.06258.pdf", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "VizWiz dataset", "dataset_citations": [], "dataset_links": [{"title": "Website", "url": "https://vizwiz.org/tasks-and-datasets/vqa/"}, {"title": "Challenge", "url": "https://vizwiz.org/tasks-and-datasets/vqa/"}], "description": "This task focuses on answering visual questions that originate from a real use case where blind people were submitting images with recorded spoken questions in order to learn about their physical surroundings.\n- [Website](https://vizwiz.org/tasks-and-datasets/vqa/)\n- [Challenge](https://vizwiz.org/tasks-and-datasets/vqa/)\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "54.22"}, "model_links": [], "model_name": "Pythia", "paper_date": null, "paper_title": "FB's Pythia repository", "paper_url": "https://github.com/facebookresearch/pythia/blob/master/docs/source/tutorials/pretrained_models.md", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "46.9"}, "model_links": [], "model_name": "BUTD Vizwiz (2018)", "paper_date": null, "paper_title": "VizWiz Grand Challenge: Answering Visual Questions from Blind People", "paper_url": "https://arxiv.org/abs/1802.08218", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": null, "subtasks": [], "synonyms": [], "task": "Visual Question Answering"}], "synonyms": [], "task": "Multimodal"}, {"categories": [], "datasets": [{"dataset": "Penn Treebank", "dataset_citations": [], "dataset_links": [{"title": "<PERSON><PERSON><PERSON> et al., (2011)", "url": "https://www.isca-speech.org/archive/archive_papers/interspeech_2011/i11_0605.pdf"}], "description": "A common evaluation dataset for language modeling ist the Penn Treebank,\nas pre-processed by [<PERSON><PERSON><PERSON> et al., (2011)](https://www.isca-speech.org/archive/archive_papers/interspeech_2011/i11_0605.pdf).\nThe dataset consists of 929k training words, 73k validation words, and\n82k test words. As part of the pre-processing, words were lower-cased, numbers\nwere replaced with N, newlines were replaced with &lt;eos&gt;,\nand all other punctuation was removed. The vocabulary is\nthe most frequent 10k words with the rest of the tokens replaced by an &lt;unk&gt; token.\nModels are evaluated based on perplexity, which is the average\nper-word log-probability (lower is better).\n", "sota": {"metrics": ["Validation perplexity", "Test perplexity", "Number of params"], "rows": [{"code_links": [], "metrics": {"Number of params": "24M", "Test perplexity": "44.8", "Validation perplexity": "44.9"}, "model_links": [], "model_name": "Mogrifier LSTM + dynamic eval (2019)", "paper_date": null, "paper_title": "Mogrifier LSTM", "paper_url": "http://arxiv.org/abs/1909.01792", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "22M", "Test perplexity": "46.01", "Validation perplexity": "46.63"}, "model_links": [], "model_name": "AdvSoft + AWD-LSTM-MoS + dynamic eval (2019)", "paper_date": null, "paper_title": "Improving Neural Language Modeling via Adversarial Training", "paper_url": "http://proceedings.mlr.press/v97/wang19f/wang19f.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "22M", "Test perplexity": "46.54", "Validation perplexity": "47.38"}, "model_links": [], "model_name": "FRAGE + AWD-LSTM-MoS + dynamic eval (2018)", "paper_date": null, "paper_title": "FRAGE: Frequency-Agnostic Word Representation", "paper_url": "https://arxiv.org/abs/1809.06858", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "185M", "Test perplexity": "47.17", "Validation perplexity": "48.63"}, "model_links": [], "model_name": "AWD-LSTM-DOC x5 (2018)", "paper_date": null, "paper_title": "Direct Output Connection for a High-Rank Language Model", "paper_url": "https://arxiv.org/abs/1808.10143", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "22M", "Test perplexity": "47.69", "Validation perplexity": "48.33"}, "model_links": [], "model_name": "AWD-LSTM-MoS + dynamic eval * (2018)", "paper_date": null, "paper_title": "Breaking the Softmax Bottleneck: A High-Rank RNN Language Model", "paper_url": "https://arxiv.org/abs/1711.03953", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "24M", "Test perplexity": "50.1", "Validation perplexity": "51.4"}, "model_links": [], "model_name": "Mogrifier LSTM (2019)", "paper_date": null, "paper_title": "Mogrifier LSTM", "paper_url": "http://arxiv.org/abs/1909.01792", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "24M", "Test perplexity": "51.1", "Validation perplexity": "51.6"}, "model_links": [], "model_name": "AWD-LSTM + dynamic eval * (2017)", "paper_date": null, "paper_title": "Dynamic Evaluation of Neural Sequence Models", "paper_url": "https://arxiv.org/abs/1709.07432", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "23M", "Test perplexity": "52.00", "Validation perplexity": "53.79"}, "model_links": [], "model_name": "AWD-LSTM-DOC + Partial Shuffle (2019)", "paper_date": null, "paper_title": "Partially Shuffling the Training Data to Improve Language Models", "paper_url": "https://arxiv.org/abs/1903.04167", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "23M", "Test perplexity": "52.38", "Validation perplexity": "54.12"}, "model_links": [], "model_name": "AWD-LSTM-DOC (2018)", "paper_date": null, "paper_title": "Direct Output Connection for a High-Rank Language Model", "paper_url": "https://arxiv.org/abs/1808.10143", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "24M", "Test perplexity": "52.8", "Validation perplexity": "53.9"}, "model_links": [], "model_name": "AWD-LSTM + continuous cache pointer * (2017)", "paper_date": null, "paper_title": "Regularizing and Optimizing LSTM Language Models", "paper_url": "https://arxiv.org/abs/1708.02182", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "34M", "Test perplexity": "54.19", "Validation perplexity": "-"}, "model_links": [], "model_name": "Trellis Network (2019)", "paper_date": null, "paper_title": "Trellis Networks for Sequence Modeling", "paper_url": "https://openreview.net/pdf?id=HyeVtoRqtQ", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "22M", "Test perplexity": "54.33", "Validation perplexity": "56.44"}, "model_links": [], "model_name": "AWD-LSTM-MoS + ATOI (2019)", "paper_date": null, "paper_title": "Alleviating Sequence Information Loss with Data Overlapping and Prime Batch Sizes", "paper_url": "https://arxiv.org/abs/1909.08700", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "22M", "Test perplexity": "54.44", "Validation perplexity": "56.54"}, "model_links": [], "model_name": "AWD-LSTM-MoS + finetune (2018)", "paper_date": null, "paper_title": "Breaking the Softmax Bottleneck: A High-Rank RNN Language Model", "paper_url": "https://arxiv.org/abs/1711.03953", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "24M", "Test perplexity": "54.52", "Validation perplexity": "56.72"}, "model_links": [], "model_name": "Transformer-XL (2018)", "paper_date": null, "paper_title": "Transformer-XL: Attentive Language Models Beyond a Fixed-Length Context", "paper_url": "https://arxiv.org/pdf/1901.02860.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "22M", "Test perplexity": "55.97", "Validation perplexity": "58.08"}, "model_links": [], "model_name": "AWD-LSTM-MoS (2018)", "paper_date": null, "paper_title": "Breaking the Softmax Bottleneck: A High-Rank RNN Language Model", "paper_url": "https://arxiv.org/abs/1711.03953", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "24M", "Test perplexity": "56.8", "Validation perplexity": "58.9"}, "model_links": [], "model_name": "AWD-LSTM 3-layer with Fraternal dropout (2018)", "paper_date": null, "paper_title": "Fraternal dropout", "paper_url": "https://arxiv.org/pdf/1711.00066.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "24M", "Test perplexity": "57.3", "Validation perplexity": "60.0"}, "model_links": [], "model_name": "AWD-LSTM (2017)", "paper_date": null, "paper_title": "Regularizing and Optimizing LSTM Language Models", "paper_url": "https://arxiv.org/abs/1708.02182", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "WikiText-2", "dataset_citations": [], "dataset_links": [{"title": "WikiText-2", "url": "https://arxiv.org/abs/1609.07843"}], "description": "[WikiText-2](https://arxiv.org/abs/1609.07843) has been proposed as a more realistic\nbenchmark for language modeling than the pre-processed Penn Treebank. WikiText-2\nconsists of around 2 million words extracted from Wikipedia articles.\n", "sota": {"metrics": ["Validation perplexity", "Test perplexity", "Number of params"], "rows": [{"code_links": [], "metrics": {"Number of params": "35M", "Test perplexity": "38.6", "Validation perplexity": "40.2"}, "model_links": [], "model_name": "Mogrifier LSTM + dynamic eval (2019)", "paper_date": null, "paper_title": "Mogrifier LSTM", "paper_url": "http://arxiv.org/abs/1909.01792", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "35M", "Test perplexity": "38.65", "Validation perplexity": "40.27"}, "model_links": [], "model_name": "AdvSoft + AWD-LSTM-MoS + dynamic eval (2019)", "paper_date": null, "paper_title": "Improving Neural Language Modeling via Adversarial Training", "paper_url": "http://proceedings.mlr.press/v97/wang19f/wang19f.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "35M", "Test perplexity": "39.14", "Validation perplexity": "40.85"}, "model_links": [], "model_name": "FRAGE + AWD-LSTM-MoS + dynamic eval (2018)", "paper_date": null, "paper_title": "FRAGE: Frequency-Agnostic Word Representation", "paper_url": "https://arxiv.org/abs/1809.06858", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "35M", "Test perplexity": "40.68", "Validation perplexity": "42.41"}, "model_links": [], "model_name": "AWD-LSTM-MoS + dynamic eval * (2018)", "paper_date": null, "paper_title": "Breaking the Softmax Bottleneck: A High-Rank RNN Language Model", "paper_url": "https://arxiv.org/abs/1711.03953", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "33M", "Test perplexity": "44.3", "Validation perplexity": "46.4"}, "model_links": [], "model_name": "AWD-LSTM + dynamic eval * (2017)", "paper_date": null, "paper_title": "Dynamic Evaluation of Neural Sequence Models", "paper_url": "https://arxiv.org/abs/1709.07432", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "33M", "Test perplexity": "52.0", "Validation perplexity": "53.8"}, "model_links": [], "model_name": "AWD-LSTM + continuous cache pointer * (2017)", "paper_date": null, "paper_title": "Regularizing and Optimizing LSTM Language Models", "paper_url": "https://arxiv.org/abs/1708.02182", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "185M", "Test perplexity": "53.09", "Validation perplexity": "54.19"}, "model_links": [], "model_name": "AWD-LSTM-DOC x5 (2018)", "paper_date": null, "paper_title": "Direct Output Connection for a High-Rank Language Model", "paper_url": "https://arxiv.org/abs/1808.10143", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "35M", "Test perplexity": "55.1", "Validation perplexity": "57.3"}, "model_links": [], "model_name": "Mogrifier LSTM (2019)", "paper_date": null, "paper_title": "Mogrifier LSTM", "paper_url": "http://arxiv.org/abs/1909.01792", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "37M", "Test perplexity": "57.85", "Validation perplexity": "60.16"}, "model_links": [], "model_name": "AWD-LSTM-DOC + Partial Shuffle (2019)", "paper_date": null, "paper_title": "Partially Shuffling the Training Data to Improve Language Models", "paper_url": "https://arxiv.org/abs/1903.04167", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "37M", "Test perplexity": "58.03", "Validation perplexity": "60.29"}, "model_links": [], "model_name": "AWD-LSTM-DOC (2018)", "paper_date": null, "paper_title": "Direct Output Connection for a High-Rank Language Model", "paper_url": "https://arxiv.org/abs/1808.10143", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "35M", "Test perplexity": "61.45", "Validation perplexity": "63.88"}, "model_links": [], "model_name": "AWD-LSTM-MoS (2018)", "paper_date": null, "paper_title": "Breaking the Softmax Bottleneck: A High-Rank RNN Language Model", "paper_url": "https://arxiv.org/abs/1711.03953", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "34M", "Test perplexity": "64.1", "Validation perplexity": "66.8"}, "model_links": [], "model_name": "AWD-LSTM 3-layer with Fraternal dropout (2018)", "paper_date": null, "paper_title": "Fraternal dropout", "paper_url": "https://arxiv.org/pdf/1711.00066.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "33M", "Test perplexity": "64.73", "Validation perplexity": "67.47"}, "model_links": [], "model_name": "AWD-LSTM + ATOI (2019)", "paper_date": null, "paper_title": "Alleviating Sequence Information Loss with Data Overlapping and Prime Batch Sizes", "paper_url": "https://arxiv.org/abs/1909.08700", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "33M", "Test perplexity": "65.8", "Validation perplexity": "68.6"}, "model_links": [], "model_name": "AWD-LSTM (2017)", "paper_date": null, "paper_title": "Regularizing and Optimizing LSTM Language Models", "paper_url": "https://arxiv.org/abs/1708.02182", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "WikiText-103", "dataset_citations": [], "dataset_links": [{"title": "WikiText-103", "url": "https://arxiv.org/abs/1609.07843"}], "description": "[WikiText-103](https://arxiv.org/abs/1609.07843) The WikiText-103 corpus contains 267,735 unique words and each word occurs at least three times in the training set.\n", "sota": {"metrics": ["Validation perplexity", "Test perplexity", "Number of params"], "rows": [{"code_links": [], "metrics": {"Number of params": "-", "Test perplexity": "15.8", "Validation perplexity": "-"}, "model_links": [], "model_name": "Routing Transformer (2020)", "paper_date": null, "paper_title": "Efficient Content-Based Sparse Attention with Routing Transformers", "paper_url": "https://arxiv.org/pdf/2003.05997.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "257M", "Test perplexity": "16.4", "Validation perplexity": "15.8"}, "model_links": [], "model_name": "Transformer-XL + RMS dynamic eval (2019)", "paper_date": null, "paper_title": "Dynamic Evaluation of Transformer Language Models", "paper_url": "https://arxiv.org/pdf/1904.08378.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "~257M", "Test perplexity": "17.1(16.1 with basic dynamic evaluation)", "Validation perplexity": "16.0"}, "model_links": [], "model_name": "Compressive Transformer (2019)", "paper_date": null, "paper_title": "Compressive Transformers for Long-Range Sequence Modelling", "paper_url": "https://arxiv.org/pdf/1911.05507.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "257M", "Test perplexity": "18.3", "Validation perplexity": "17.7"}, "model_links": [], "model_name": "Transformer-XL Large (2018)", "paper_date": null, "paper_title": "Transformer-XL: Attentive Language Models Beyond a Fixed-Length Context", "paper_url": "https://arxiv.org/pdf/1901.02860.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "247M", "Test perplexity": "20.5", "Validation perplexity": "19.8"}, "model_links": [], "model_name": "Transformer with tied adaptive embeddings (2018)", "paper_date": null, "paper_title": "Adaptive Input Representations for Neural Language Modeling", "paper_url": "https://arxiv.org/pdf/1809.10853.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "151M", "Test perplexity": "24.0", "Validation perplexity": "23.1"}, "model_links": [], "model_name": "Transformer-XL Standard (2018)", "paper_date": null, "paper_title": "Transformer-XL: Attentive Language Models Beyond a Fixed-Length Context", "paper_url": "https://arxiv.org/pdf/1901.02860.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "", "Test perplexity": "28.0", "Validation perplexity": "27.2"}, "model_links": [], "model_name": "AdvSoft + 4 layer QRNN + dynamic eval (2019)", "paper_date": null, "paper_title": "Improving Neural Language Modeling via Adversarial Training", "paper_url": "http://proceedings.mlr.press/v97/wang19f/wang19f.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "", "Test perplexity": "29.2", "Validation perplexity": "29.0"}, "model_links": [], "model_name": "LSTM + Hebbian + Cache + MbPA (2018)", "paper_date": null, "paper_title": "Fast Parametric Learning with Activation Memorization", "paper_url": "http://arxiv.org/abs/1803.10049", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "180M", "Test perplexity": "30.35", "Validation perplexity": "-"}, "model_links": [], "model_name": "Trellis Network (2019)", "paper_date": null, "paper_title": "Trellis Networks for Sequence Modeling", "paper_url": "https://openreview.net/pdf?id=HyeVtoRqtQ", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "", "Test perplexity": "32.85", "Validation perplexity": "31.92"}, "model_links": [], "model_name": "AWD-LSTM-MoS + ATOI (2019)", "paper_date": null, "paper_title": "Alleviating Sequence Information Loss with Data Overlapping and Prime Batch Sizes", "paper_url": "https://arxiv.org/abs/1909.08700", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "", "Test perplexity": "34.3", "Validation perplexity": "34.1"}, "model_links": [], "model_name": "LSTM + <PERSON><PERSON><PERSON> (2018)", "paper_date": null, "paper_title": "Fast Parametric Learning with Activation Memorization", "paper_url": "http://arxiv.org/abs/1803.10049", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "", "Test perplexity": "36.4", "Validation perplexity": "36.0"}, "model_links": [], "model_name": "LSTM (2018)", "paper_date": null, "paper_title": "Fast Parametric Learning with Activation Memorization", "paper_url": "http://arxiv.org/abs/1803.10049", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "", "Test perplexity": "37.2", "Validation perplexity": "-"}, "model_links": [], "model_name": "Gated CNN (2016)", "paper_date": null, "paper_title": "Language modeling with gated convolutional networks", "paper_url": "https://arxiv.org/abs/1612.08083", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "", "Test perplexity": "40.8", "Validation perplexity": "-"}, "model_links": [], "model_name": "Neural cache model (2017)", "paper_date": null, "paper_title": "Improving Neural Language Models with a Continuous Cache", "paper_url": "https://arxiv.org/pdf/1612.04426.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "", "Test perplexity": "45.2", "Validation perplexity": "-"}, "model_links": [], "model_name": "Temporal CNN (2018)", "paper_date": null, "paper_title": "Convolutional sequence modeling revisited", "paper_url": "https://openreview.net/forum?id=BJEX-H1Pf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "", "Test perplexity": "48.7", "Validation perplexity": "-"}, "model_links": [], "model_name": "LSTM (2017)", "paper_date": null, "paper_title": "Improving Neural Language Models with a Continuous Cache", "paper_url": "https://arxiv.org/pdf/1612.04426.pdf", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "1B Words / Google Billion Word benchmark", "dataset_citations": [], "dataset_links": [{"title": "The One-Billion Word benchmark", "url": "https://arxiv.org/pdf/1312.3005.pdf"}], "description": "[The One-Billion Word benchmark](https://arxiv.org/pdf/1312.3005.pdf) is a large dataset derived from a news-commentary site.\nThe dataset consists of 829,250,940 tokens over a vocabulary of 793,471 words.\nImportantly, sentences in this model are shuffled and hence context is limited.\n", "sota": {"metrics": ["Test perplexity", "Number of params"], "rows": [{"code_links": [], "metrics": {"Number of params": "0.8B", "Test perplexity": "21.8"}, "model_links": [], "model_name": "Transformer-XL Large (2018)", "paper_date": null, "paper_title": "Transformer-XL: Attentive Language Models Beyond a Fixed-Length Context", "paper_url": "https://arxiv.org/pdf/1901.02860.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "0.46B", "Test perplexity": "23.5"}, "model_links": [], "model_name": "Transformer-XL Base (2018)", "paper_date": null, "paper_title": "Transformer-XL: Attentive Language Models Beyond a Fixed-Length Context", "paper_url": "https://arxiv.org/pdf/1901.02860.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "0.8B", "Test perplexity": "23.7"}, "model_links": [], "model_name": "Transformer with shared adaptive embeddings - Very large (2018)", "paper_date": null, "paper_title": "Adaptive Input Representations for Neural Language Modeling", "paper_url": "https://arxiv.org/pdf/1809.10853.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "43B?", "Test perplexity": "23.7"}, "model_links": [], "model_name": "10 LSTM+CNN inputs + SNM10-SKIP (2016)", "paper_date": null, "paper_title": "Exploring the Limits of Language Modeling", "paper_url": "https://arxiv.org/pdf/1602.02410.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "0.46B", "Test perplexity": "24.1"}, "model_links": [], "model_name": "Transformer with shared adaptive embeddings (2018)", "paper_date": null, "paper_title": "Adaptive Input Representations for Neural Language Modeling", "paper_url": "https://arxiv.org/pdf/1809.10853.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "1.04B", "Test perplexity": "30.0"}, "model_links": [], "model_name": "Big LSTM+CNN inputs (2016)", "paper_date": null, "paper_title": "Exploring the Limits of Language Modeling", "paper_url": "https://arxiv.org/pdf/1602.02410.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "?", "Test perplexity": "31.9"}, "model_links": [], "model_name": "Gated CNN-14<PERSON><PERSON><PERSON><PERSON> (2017)", "paper_date": null, "paper_title": "Language Modeling with Gated Convolutional Networks", "paper_url": "https://arxiv.org/pdf/1612.08083.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "0.151B", "Test perplexity": "35.1"}, "model_links": [], "model_name": "BIGLSTM baseline (2018)", "paper_date": null, "paper_title": "Factorization tricks for LSTM networks", "paper_url": "https://arxiv.org/pdf/1703.10722.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "0.052B", "Test perplexity": "36.3"}, "model_links": [], "model_name": "BIG F-LSTM F512 (2018)", "paper_date": null, "paper_title": "Factorization tricks for LSTM networks", "paper_url": "https://arxiv.org/pdf/1703.10722.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Number of params": "0.035B", "Test perplexity": "39.4"}, "model_links": [], "model_name": "BIG G-LSTM G-8 (2018)", "paper_date": null, "paper_title": "Factorization tricks for LSTM networks", "paper_url": "https://arxiv.org/pdf/1703.10722.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": null, "subtasks": [], "synonyms": [], "task": "Word Level Models"}, {"categories": [], "datasets": [{"dataset": "Hutter Prize", "dataset_citations": [], "dataset_links": [{"title": "The Hutter Prize", "url": "http://prize.hutter1.net"}], "description": "[The Hutter Prize](http://prize.hutter1.net) Wikipedia dataset, also known as enwiki8, is a byte-level dataset consisting of the\nfirst 100 million bytes of a Wikipedia XML dump. For simplicity we shall refer to it as a character-level dataset.\nWithin these 100 million bytes are 205 unique tokens.\n", "sota": {"metrics": ["Bit per Character (BPC)", "Number of params"], "rows": [{"code_links": [], "metrics": {"Bit per Character (BPC)": "0.94", "Number of params": "277M"}, "model_links": [], "model_name": "Transformer-XL + RMS dynamic eval (2019)", "paper_date": null, "paper_title": "Dynamic Evaluation of Transformer Language Models", "paper_url": "https://arxiv.org/pdf/1904.08378.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "0.97", "Number of params": "-"}, "model_links": [], "model_name": "Compressive Transformer (2019)", "paper_date": null, "paper_title": "Compressive Transformers for Long-Range Sequence Modelling", "paper_url": "https://arxiv.org/pdf/1911.05507.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "0.988", "Number of params": "96M"}, "model_links": [], "model_name": "Mogrifier LSTM + dynamic eval (2019)", "paper_date": null, "paper_title": "Mogrifier LSTM", "paper_url": "http://arxiv.org/abs/1909.01792", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "0.99", "Number of params": "277M"}, "model_links": [], "model_name": "24-layer Transformer-XL (2018)", "paper_date": null, "paper_title": "Transformer-XL: Attentive Language Models Beyond a Fixed-Length Context", "paper_url": "https://arxiv.org/pdf/1901.02860.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "0.99", "Number of params": "102M"}, "model_links": [], "model_name": "Longformer Large (2020)", "paper_date": null, "paper_title": "Longformer: The Long-Document Transformer", "paper_url": "https://arxiv.org/pdf/2004.05150.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.00", "Number of params": "41M"}, "model_links": [], "model_name": "Longformer Small (2020)", "paper_date": null, "paper_title": "Longformer: The Long-Document Transformer", "paper_url": "https://arxiv.org/pdf/2004.05150.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.03", "Number of params": "88M"}, "model_links": [], "model_name": "18-layer Transformer-XL (2018)", "paper_date": null, "paper_title": "Transformer-XL: Attentive Language Models Beyond a Fixed-Length Context", "paper_url": "https://arxiv.org/pdf/1901.02860.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.06", "Number of params": "41M"}, "model_links": [], "model_name": "12-layer Transformer-XL (2018)", "paper_date": null, "paper_title": "Transformer-XL: Attentive Language Models Beyond a Fixed-Length Context", "paper_url": "https://arxiv.org/pdf/1901.02860.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.06", "Number of params": "235M"}, "model_links": [], "model_name": "64-layer Character Transformer Model (2018)", "paper_date": null, "paper_title": "Character-Level Language Modeling with Deeper Self-Attention", "paper_url": "https://arxiv.org/abs/1808.04444", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.08", "Number of params": "46M"}, "model_links": [], "model_name": "mLSTM + dynamic eval * (2017)", "paper_date": null, "paper_title": "Dynamic Evaluation of Neural Sequence Models", "paper_url": "https://arxiv.org/abs/1709.07432", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.11", "Number of params": "44M"}, "model_links": [], "model_name": "12-layer Character Transformer Model (2018)", "paper_date": null, "paper_title": "Character-Level Language Modeling with Deeper Self-Attention", "paper_url": "https://arxiv.org/abs/1808.04444", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.122", "Number of params": "96M"}, "model_links": [], "model_name": "Mogrifier LSTM (2019)", "paper_date": null, "paper_title": "Mogrifier LSTM", "paper_url": "http://arxiv.org/abs/1909.01792", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.232", "Number of params": "47M"}, "model_links": [], "model_name": "3-layer AWD-LSTM (2018)", "paper_date": null, "paper_title": "An Analysis of Neural Language Modeling at Multiple Scales", "paper_url": "https://arxiv.org/abs/1803.08240", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.24", "Number of params": "46M"}, "model_links": [], "model_name": "Large mLSTM +emb +WN +VD (2017)", "paper_date": null, "paper_title": "Multiplicative LSTM for sequence modelling", "paper_url": "https://arxiv.org/abs/1609.07959", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.245", "Number of params": "47M"}, "model_links": [], "model_name": "Large FS-LSTM-4 (2017)", "paper_date": null, "paper_title": "Fast-Slow Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1705.08639", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.27", "Number of params": "46M"}, "model_links": [], "model_name": "Large RHN (2016)", "paper_date": null, "paper_title": "Recurrent Highway Networks", "paper_url": "https://arxiv.org/abs/1607.03474", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.277", "Number of params": "27M"}, "model_links": [], "model_name": "FS-LSTM-4 (2017)", "paper_date": null, "paper_title": "Fast-Slow Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1705.08639", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Text8", "dataset_citations": [], "dataset_links": [{"title": "The text8 dataset", "url": "http://mattmahoney.net/dc/textdata.html"}], "description": "[The text8 dataset](http://mattmahoney.net/dc/textdata.html) is also derived from Wikipedia text, but has all XML removed, and is lower cased to only have 26 characters of English text plus spaces.\n", "sota": {"metrics": ["Bit per Character (BPC)", "Number of params"], "rows": [{"code_links": [], "metrics": {"Bit per Character (BPC)": "1.038", "Number of params": "277M"}, "model_links": [], "model_name": "Transformer-XL + RMS dynamic eval (2019)", "paper_date": null, "paper_title": "Dynamic Evaluation of Transformer Language Models", "paper_url": "https://arxiv.org/pdf/1904.08378.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.08", "Number of params": "277M"}, "model_links": [], "model_name": "Transformer-XL Large (2018)", "paper_date": null, "paper_title": "Transformer-XL: Attentive Language Models Beyond a Fixed-Length Context", "paper_url": "https://arxiv.org/pdf/1901.02860.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.10", "Number of params": "41M"}, "model_links": [], "model_name": "Longformer Small (2020)", "paper_date": null, "paper_title": "Longformer: The Long-Document Transformer", "paper_url": "https://arxiv.org/pdf/2004.05150.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.13", "Number of params": "235M"}, "model_links": [], "model_name": "64-layer Character Transformer Model (2018)", "paper_date": null, "paper_title": "Character-Level Language Modeling with Deeper Self-Attention", "paper_url": "https://arxiv.org/abs/1808.04444", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.18", "Number of params": "44M"}, "model_links": [], "model_name": "12-layer Character Transformer Model (2018)", "paper_date": null, "paper_title": "Character-Level Language Modeling with Deeper Self-Attention", "paper_url": "https://arxiv.org/abs/1808.04444", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.19", "Number of params": "45M"}, "model_links": [], "model_name": "mLSTM + dynamic eval * (2017)", "paper_date": null, "paper_title": "Dynamic Evaluation of Neural Sequence Models", "paper_url": "https://arxiv.org/abs/1709.07432", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.27", "Number of params": "45M"}, "model_links": [], "model_name": "Large mLSTM +emb +WN +VD (2016)", "paper_date": null, "paper_title": "Multiplicative LSTM for sequence modelling", "paper_url": "https://arxiv.org/abs/1609.07959", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.27", "Number of params": "46M"}, "model_links": [], "model_name": "Large RHN (2016)", "paper_date": null, "paper_title": "Recurrent Highway Networks", "paper_url": "https://arxiv.org/abs/1607.03474", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.29", "Number of params": "35M"}, "model_links": [], "model_name": "LayerNorm HM-LSTM (2017)", "paper_date": null, "paper_title": "Hierarchical Multiscale Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1609.01704", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.36", "Number of params": "16M"}, "model_links": [], "model_name": "BN LSTM (2016)", "paper_date": null, "paper_title": "Recurrent Batch Normalization", "paper_url": "https://arxiv.org/abs/1603.09025", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.40", "Number of params": "45M"}, "model_links": [], "model_name": "Unregularised mLSTM (2016)", "paper_date": null, "paper_title": "Multiplicative LSTM for sequence modelling", "paper_url": "https://arxiv.org/abs/1609.07959", "uses_additional_data": false}]}, "subdatasets": []}, {"dataset": "Penn Treebank", "dataset_citations": [], "dataset_links": [], "description": "The vocabulary of the words in the character-level dataset is limited to 10 000 - the same vocabulary as used in the word level dataset.  This vastly simplifies the task of character-level language modeling as character transitions will be limited to those found within the limited word level vocabulary.\n", "sota": {"metrics": ["Bit per Character (BPC)", "Number of params"], "rows": [{"code_links": [], "metrics": {"Bit per Character (BPC)": "1.083", "Number of params": "24M"}, "model_links": [], "model_name": "Mogrifier LSTM + dynamic eval (2019)", "paper_date": null, "paper_title": "Mogrifier LSTM", "paper_url": "http://arxiv.org/abs/1909.01792", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.120", "Number of params": "24M"}, "model_links": [], "model_name": "Mogrifier LSTM (2019)", "paper_date": null, "paper_title": "Mogrifier LSTM", "paper_url": "http://arxiv.org/abs/1909.01792", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.159", "Number of params": "13.4M"}, "model_links": [], "model_name": "Trellis Network (2019)", "paper_date": null, "paper_title": "Trellis Networks for Sequence Modeling", "paper_url": "https://openreview.net/pdf?id=HyeVtoRqtQ", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.175", "Number of params": "13.8M"}, "model_links": [], "model_name": "3-layer AWD-LSTM (2018)", "paper_date": null, "paper_title": "An Analysis of Neural Language Modeling at Multiple Scales", "paper_url": "https://arxiv.org/abs/1803.08240", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.187", "Number of params": "13.8M"}, "model_links": [], "model_name": "6-layer QRNN (2018)", "paper_date": null, "paper_title": "An Analysis of Neural Language Modeling at Multiple Scales", "paper_url": "https://arxiv.org/abs/1803.08240", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.190", "Number of params": "27M"}, "model_links": [], "model_name": "FS-LSTM-4 (2017)", "paper_date": null, "paper_title": "Fast-Slow Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1705.08639", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.193", "Number of params": "27M"}, "model_links": [], "model_name": "FS-LSTM-2 (2017)", "paper_date": null, "paper_title": "Fast-Slow Recurrent Neural Networks", "paper_url": "https://arxiv.org/abs/1705.08639", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.214", "Number of params": "16.3M"}, "model_links": [], "model_name": "NASCell (2016)", "paper_date": null, "paper_title": "Neural Architecture Search with Reinforcement Learning", "paper_url": "https://arxiv.org/abs/1611.01578", "uses_additional_data": false}, {"code_links": [], "metrics": {"Bit per Character (BPC)": "1.219", "Number of params": "14.4M"}, "model_links": [], "model_name": "2-layer Norm HyperLSTM (2016)", "paper_date": null, "paper_title": "HyperNetworks", "paper_url": "https://arxiv.org/abs/1609.09106", "uses_additional_data": false}]}, "subdatasets": []}], "description": "", "source_link": null, "subtasks": [], "synonyms": [], "task": "Character Level Models"}, {"categories": [], "datasets": [{"dataset": "RumourEval", "dataset_citations": [], "dataset_links": [{"title": "RumourEval 2017", "url": "http://www.aclweb.org/anthology/S/S17/S17-2006.pdf"}, {"title": "PHEME collection of rumors and stance", "url": "http://journals.plos.org/plosone/article?id=10.1371/journal.pone.0150989"}, {"title": "Go back to the README", "url": "../README.md"}], "description": "The [RumourEval 2017](http://www.aclweb.org/anthology/S/S17/S17-2006.pdf) dataset has been used for stance detection in English (subtask A). It features multiple stories and thousands of reply:response pairs, with train, test and evaluation splits each containing a distinct set of over-arching narratives.\nThis dataset subsumes the large [PHEME collection of rumors and stance](http://journals.plos.org/plosone/article?id=10.1371/journal.pone.0150989), which includes German.\n[Go back to the README](../README.md)\n", "sota": {"metrics": ["Accuracy"], "rows": [{"code_links": [], "metrics": {"Accuracy": "0.784"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON> et al. 2017", "paper_date": null, "paper_title": "Turing at SemEval-2017 Task 8: Sequential Approach to Rumour Stance Classification with Branch-LSTM", "paper_url": "http://www.aclweb.org/anthology/S/S17/S17-2083.pdf", "uses_additional_data": false}, {"code_links": [], "metrics": {"Accuracy": "0.780"}, "model_links": [], "model_name": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> 2017", "paper_date": null, "paper_title": "UWaterloo at SemEval-2017 Task 8: Detecting <PERSON><PERSON> towards Rumours with Topic Independent Features", "paper_url": "http://www.aclweb.org/anthology/S/S17/S17-2080.pdf", "uses_additional_data": false}]}, "subdatasets": []}], "description": "Stance detection is the extraction of a subject's reaction to a claim made by a primary actor. It is a core part of a set of approaches to fake news assessment.\nExample:\n", "source_link": null, "subtasks": [], "synonyms": [], "task": "<PERSON><PERSON> Detection"}]