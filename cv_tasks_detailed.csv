Task Name,Dataset Count,Common Datasets (Top 10),Benchmarks,SOTA Metrics,Subtasks,Categories,Description (First 200 chars)
Semantic Segmentation,348,Fine-Grained Cloud Segmentation Dataset; UPLight; SemanticUSL; CARLA2Real; PIC; Virtual KITTI; FMB Dataset; DeepFashion2; TAS-NIR; ISPRS Potsdam,SUN-RGBD; Fine-Grained Cloud Segmentation Dataset; UPLight; Cityscapes val; SemanticPOSS; PASCAL VOC 2012 val; COCO-Stuff test; SynPASS; FMB Dataset; Forward-Looking Sonar Marine Debris Datasets,mIoU (Real); Dice (Average); Test Score; Frame (fps); Jaccard (Mean); Per-Class Accuracy; Mean F1; AIOU; mAcc; class iIoU,Amodal Panoptic Segmentation; Histopathological Segmentation; Flood extent forecasting; Hyperspectral Semantic Segmentation; Bird's-Eye View Semantic Segmentation,Computer Vision; Computer Code; Robots; Medical,N/A
Object Detection,333,HGP; Active Terahertz; ReDWeb-S; MSCOCO; MLGESTURE DATASET; EGO-CH-Gaze; Parasitic Egg Detection and Classification in Microscopic Images; DeepTrash; POG; WoodScape,; COCO minival; LVIS v1.0; AODRaw; SIXray; SpaceNet 2; MSCOCO; nuScenes; Waymo 2D detection all_ns f0val; Pascal VOC to Clipart1K,; APt; Param.; boxAP; APM50; mMR; mAP@0.50; Params; boxAP75; APvt,Object Detection In Indoor Scenes; Head Detection; Medical Object Detection; One-Shot Object Detection; Small Object Detection,Computer Vision,N/A
Image Classification,283,Tiny Images; Tencent ML-Images; ColonINST-v1 (Unseen); NumtaDB; LSA16; Banapple; Id Pattern Dataset; DTD; Visual Wake Words; HaSPeR,ColonINST-v1 (Unseen); Id Pattern Dataset; DTD; Flower102; Visual Wake Words; finetuned-websites; cats_vs_dogs; Split Fashion M-NIST; FGVC-Aircraft; ISIC2018,Quadratic Weighted Kappa; Macro F1; Classification Error; Cross Entropy Loss; ImageNet Top-1 Accuracy; Params; ARI; FLOPS; Top-1; Test Accuracy,Genre classification; Multi-Label Image Classification; Small Data Image Classification; Token Reduction; Classification Consistency,Computer Vision; Adversarial,**Image Classification** is a fundamental task in vision recognition that aims to understand and categorize an image as a whole under a specific label. Unlike [object detection](/task/object-detection...
Visual Question Answering (VQA),145,uBench; VizWiz; VLM2-Bench; X-TransferBench; IllusionAnimals_test; AutoHallusion; TextVQA; CV-Bench; InfiMM-Eval; T2 Guiding,MSVD-QA; TDIUC; VizWiz 2018; WebSRC; COCO Visual Question Answering (VQA) abstract 1.0 multiple choice; Visual7W; MM-Vet; AutoHallusion; TextVQA; RetVQA,Reasoning (Fra.); Follow-up ExactMatch; Accuracy * Fluency; ExactMatch; PC-VID; Overall score; Reasoning (Sen.); Params; ClipMatch@1; Contains w. Synonyms,Chart Understanding; Chart Question Answering; Visual Question Answering; Embodied Question Answering; Generative Visual Question Answering,Computer Vision; Natural Language Processing,**Visual Question Answering (VQA)** is a task in computer vision that involves answering questions about an image. The goal of VQA is to teach machines to understand the content of an image and answer...
Named Entity Recognition (NER),130,CoNLL++; Mini HAREM; Rare Diseases Mentions in MIMIC-III; ESG-DLT-NER; RONEC; CORD-r; COVID-Q; CORD; KazNERD; InLegalNER,SLUE; LINNAEUS; CoNLL++; IECSIL FIRE-2018 Shared Task; WNUT 2020; UNER v1 - PUD (English); BC5CDR-chemical; UNER v1 (Chinese); SemEval 2022 - BanglaCoNER; Species-800,Micro F1 (Exact Span); NER Macro F1; Micro F1 (Tokens); Avg F1; Multi-Task Supervision; F1 (micro); Precision; label-F1 (%); F1-score (Weighted); F1 (surface form),Chinese Named Entity Recognition; Named Entity Recognition In Vietnamese; Few-shot NER; Multilingual Named Entity Recognition; Medical Named Entity Recognition,Natural Language Processing,"**Named Entity Recognition (NER)** is a task of Natural Language Processing (NLP) that involves identifying and classifying named entities in a text into predefined categories such as person names, or..."
Pose Estimation,124,Immediacy Dataset; Drunkard's Dataset; Halpe-FullBody; ATRW; TotalCapture; Poser; ITOP; InterHand2.6M; xR-EgoPose; BRACE,COCO minival; InLoc; 3DPW; Pix3D; !(()&&!|*|*|; BRACE; MS-COCO;  ITOP front-view; DensePose-COCO; COCO test-dev,"MAE pitch (º); DUC1-Acc@1.0m,10°; DUC2-Acc@0.25m,10°; DUC2-Acc@1.0m,10°; AP50; AR50; PCKh-0.5; Validation AP; Mean PCK@0.05; mAP",Hand Pose Estimation; RF-based Pose Estimation; 6D Pose Estimation; Car Pose Estimation; 3D Human Pose Estimation,Computer Vision,"**Pose Estimation** is a computer vision task where the goal is to detect the position and orientation of a person or an object. Usually, this is done by predicting the location of specific keypoints ..."
Anomaly Detection,120,DARPA; MIT-BIH Arrhythmia Database; COCO-OOC; SensumSODF; ESA-AD; UBnormal; 5GAD-2022; Vehicle Claims; TII-SSRC-23; ADFI,AeBAD-S; AeBAD-V; Anomaly Detection on Unlabeled CIFAR-10 vs LSUN (Fix); ASSIRA Cat Vs Dog; Leave-One-Class-Out CIFAR-10; NB15-Analysis; One-class ImageNet-30; ADNI; Cats-and-Dogs; MIT-BIH Arrhythmia Database,Mean AUC; Segmentation AU-sPRO (until FPR 5%); Avg. ROC-AUC; Segmentation AUPRO; ROC-AUC; AUCROC; F1-score; ROC AUC; RBDC; NAB score,RGB+Depth Anomaly Detection and Segmentation; Depth Anomaly Detection and Segmentation; Abnormal Event Detection In Video; 3D Anomaly Detection; Self-Supervised Anomaly Detection,Miscellaneous; Methodology; Computer Vision; Graphs,"**Anomaly Detection** is a binary classification identifying unusual or unexpected patterns in a dataset, which deviate significantly from the majority of the data. The goal of anomaly detection is to..."
Action Recognition,115,Florence3D; RareAct; Hollywood 3D dataset; WiFall; FineGym; OST; CATER; H2O  (2 Hands and Objects); Charades; UCF101-DS,Hockey; UCF-101; UCF 101; MTL-AQA; Penn Action; UAV Human; RareAct; THUMOS’14; SL-Animals; RoCoG-v2,Clip Hit@1; mAP@0.3; Param.; 3-fold Accuracy; mAP (Val); Accuracy (Cross-View); Average accuracy of 3 splits; mAP; Top 5 Accuracy; Accuracy (CV),Atomic action recognition; Open Set Action Recognition; Micro-Action Recognition; Action Recognition In Still Images; Self-Supervised Human Action Recognition,Computer Vision; Time Series,**Action Recognition** is a computer vision task that involves recognizing human actions in videos or images. The goal is to classify and categorize the actions being performed in the video or image i...
Instance Segmentation,111,NDD20; ROBUST-MIS; SIXray; SpaceNet 2; UVO; nuScenes; Fashionpedia; COCO-WAN (Medium noise); 3D-FUTURE; ShipSG,COCO minival; ADE20K val; Cityscapes val; Box-IS; NYUDv2-IS; LDD; Cityscapes; nuScenes; TBBR; LVIS v1.0 val,maskAP50; AP50; mask AP; mAP; mAP50; MOTA; mIOU; Dice Coef; AP75; Params (M),Box-supervised Instance Segmentation; Unsupervised Object Segmentation; Human Instance Segmentation; RGB-D Instance Segmentation; Real-time Instance Segmentation,Computer Vision,"**Instance Segmentation** is a computer vision task that involves identifying and separating individual objects within an image, including detecting the boundaries of each object and assigning a uniqu..."
Speech Recognition,96,Common Voice; ADL Piano MIDI; RESD; ODSQA; Kite; i3-video; SpeakingFaces; ClovaCall; ADIMA; EasyCom,"Common Voice 7.0 Finnish; Common Voice Sorbian, Upper; Common Voice; Common Voice Chinese (Hong Kong); Vox Populi; Common Voice 8.0 Hausa; Robust Speech Event - Dev Data; Common Voice Assamese; Common Voice 8.0 Swedish; Hub5'00 FISHER-SWBD",WER for French; ABX-within; VoxPopuli (Dev); Error rate - SNR 0dB; WER for Spanish; Accuracy (%); Hub5'00; VoxCeleb (Test); WER for Turkish; Word Error Rate (WER),Target Speaker Extraction; Accented Speech Recognition; Distant Speech Recognition; English Conversational Speech Recognition; Automatic Lyrics Transcription,Audio; Speech,**Speech Recognition** is the task of converting spoken language into text. It involves recognizing the words spoken in an audio recording and transcribing them into a written format. The goal is to a...
Information Retrieval,93,Robust04; WikiPII; Webis-Touché-2020; NIAN; TREC-News; TripClick; WMT 2014 Medical; ThreatGram 101 - Extreme Telegram Data; QUASAR-S; NFCorpus,NanoHotpotQA; GermanQuAD; Unknown; Amazon; NanoFiQA2018; Evaluate; NanoSCIDOCS; !(()&&!|*|*|; NanoNFCorpus; dim 768,MRR@10; HR@30; infNDCG; NDCG; Time (ms); Recall@100; 10-20% Mask PSNR; Recall@200; 1:1 Accuracy; nDCG@10,Scientific Results Extraction; Zero Shot on BEIR (Inference Free Model); TAR; Passage Retrieval; Cross-Lingual Information Retrieval,Natural Language Processing,"Information retrieval is the task of ranking a list of documents or search results in response to a query    <span style=""color:grey; opacity: 0.6"">( Image credit: [sudhanshumittal](https://github.com..."
2D Object Detection,93,A collection of 131 CT datasets of pieces of modeling clay containing stones; TiROD; Br35H :: Brain Tumor Detection 2020; Blood Cell Detection Dataset; COCO-OOC; HRPlanesV2; CY101 Dataset; EGO-CH-Gaze; Bone Fracture Multi-Region X-ray Dataset; PFruitlet640,; RTTS; DUO; CLCXray; RADIATE; ExDark; UAV-PDD2023; TRR360D; BDD100K val; Clear Weather,COCO-style AP; ; mAP@0.3; AP50; All mAP; snow/rain hard (AP); box mAP; mAP; dense fog hard (AP); mAP50,Thermal Image Segmentation; Hand Detection; 2D Cyclist Detection; Intention-oriented Object Detection; GUI Element Detection,Methodology; Computer Vision,N/A
Image Retrieval,87,Tiny Images; MSCOCO; NUS-WIDE; InstaCities1M; Retrieval-SfM; DyML-Animal; QuickDraw-Extended; IAPR TC-12; DeepFashion2; COCO-CN,ROxford Medium without fine-tuning; PhotoChat; WIT; In-Shop; Localized Narratives; FooDI-ML (Global); 24/7 Tokyo; Par6k; AmsterTime; DeepFashion - Consumer-to-shop,R@10; PNR; Recall@10; mAP; R@4; Rank-1; mean average precision; Rank-20; Accuracy; R@8,Content-Based Image Retrieval; Semi-Supervised Sketch Based Image Retrieval; Video-to-Shop; Multi-Label Image Retrieval; Medical Image Retrieval,Computer Vision,"**Image Retrieval** is a fundamental and long-standing computer vision task that involves finding images similar to a given query from a large database. It is often considered a form of fine-grained, ..."
2D Semantic Segmentation,83,ACCT Data Repository; Id Pattern Dataset; Microscopy Image Dataset of Pulmonary Vascular Changes; FaceOcc; FALLMUD; GF-PA66 3D XCT; [[FAQs~fee]]How much is the Expedia cancellation fee?; Deep Indices; DeepSport Dataset; monkey doo,WorldFloods; RELLIS-3D; Time Series Prediction Benchmarks; WaterScenes; CamVid; Deep Indices; Cityscapes val; GF-PA66 3D XCT; WildScenes; xBD,mIoU (Env DA); GMac; MParams; Category mIoU; Jaccard (Mean); mIoU (Temporal DA) ; Classification F1-score; 1:3 Accuracy; Mean IoU (class); mIoU,Human Part Segmentation; Material Segmentation; Text Style Transfer; Building Damage Assessment; Image Segmentation,Computer Vision; Natural Language Processing; Audio; Adversarial,N/A
Relation Extraction,82,TurkQA; MultiTACRED; JNLPBA; WebNLG; REFinD; ARF; RadGraph; Financial Dynamic Knowledge Graph; TimeBankPT; BioRED,NYT-single; PGR; WNUT 2020; SKE; Google RE; GAD; 2010 i2b2/VA; TACRED-Revisited; REBEL; ADE Corpus,Cross Sentence; Macro F1; NER Macro F1; F1 (5% Few-Shot); F1 (Zero-Shot); NER Micro F1; F1 (micro); Precision; Relation classification F1; RE+ Macro F1 ,Relation Classification; Relation Mention Extraction; Binary Relation Extraction; DrugProt; Relationship Extraction (Distant Supervised),Natural Language Processing,"**Relation Extraction** is the task of predicting attributes and relations for entities in a sentence. For example, given a sentence “Barack Obama was born in Honolulu, Hawaii.”, a relation classifier..."
Image Generation,79,VGGFace2 HQ; Oxford 102 Flower; CelebA; TextAtlas5M; GVGAI; FaceForensics++; DensePose; MMAct; Large Labelled Logo Dataset (L3D); WiFiCam,Places50; CelebA-HQ 64x64; Pokemon 256x256; CelebA-HQ 512x512; CelebA; Oxford 102 Flowers 128x128; Pokemon 1024x1024; CelebA-HQ 1024x1024; LSUN Bedroom 256 x 256; Indian Celebs 256 x 256,FID_CLIP; Coverage; MS-SSIM; EQ-R; KID; SSIM; SIFID; NFE; sFID; TextVisionBlend FID,Image Inpainting; Image Harmonization; Virtual Staining; Text-to-Image Generation; Image Generation from Scene Graphs,Miscellaneous; Computer Vision; Natural Language Processing; Medical,"**Image Generation** (synthesis) is the task of generating new images from an existing dataset.    - **Unconditional generation** refers to generating samples unconditionally from the dataset, i.e. $p..."
Image Captioning,79,VizWiz; Tencent ML-Images; X-TransferBench; Image Editing Request Dataset; MSCOCO; GroundCap; T2 Guiding; EgoShots; ReferItGame; Peir Gross,nocaps in-domain; VizWiz 2020 test-dev; Localized Narratives; FlickrStyle10K; nocaps entire; AIC-ICC; MSCOCO; BanglaLekhaImageCaptions; nocaps-val-near-domain; nocaps-val-in-domain,ROUGE-L; Pre-train (#images); CIDEr; CLIPScore; B1; B3; RDK FTS; ROUGE; BLEU-1; BLEU,Semi Supervised Learning for Image Captioning; Aesthetic Image Captioning; Vietnamese Image Captioning; Patent Figure Description Generation; 3D dense captioning,Computer Vision; Natural Language Processing,**Image Captioning** is the task of describing the content of an image in words. This task lies at the intersection of computer vision and natural language processing. Most image captioning systems us...
Depth Estimation,77,3D60; HAMMER; Middlebury; CARLA2Real; Virtual KITTI; EndoSLAM; ENRICH; HUMAN4D; ETH3D; TransProteus,DCM; NYU-Depth V2; Stanford2D3D Panoramic; KITTI Eigen split; Mars DTM Estimation; Matterport3D; ScanNetV2; eBDtheque; 4D Light Field Dataset; DIODE,mean absolute error; Abs Rel; BadPix(0.01); mAP; RMSE log; Delta < 1.25; absolute relative error; RMS; L1 error; Delta < 1.25^2,Indoor Monocular Depth Estimation; Depth And Camera Motion; Bathymetry prediction; Stereo Depth Estimation; Monocular Depth Estimation,Computer Vision,**Depth Estimation** is the task of measuring the distance of each pixel relative to the camera. Depth is extracted from either monocular (single) or stereo (multiple views of a scene) images. Traditi...
Autonomous Driving,70,IDD; Apron Dataset; SODA10M; Long-term visual localization; BLVD; ROAD; SDN; Driving Weather; PedX; Talk2Car,Town05 Short; CARLA Leaderboard; Town05 Long; ApolloCar3D,A3DP; Route Completion; Infraction penalty; Driving Score; DS; RC,NavSim; Motion Forecasting; CARLA MAP Leaderboard; Dead-Reckoning Prediction; 3D Pedestrian Tracking,Miscellaneous; Computer Vision; Robots,Autonomous driving is the task of driving a vehicle without human conduction.     Many of the state-of-the-art results can be found at more general task pages such as [3D Object Detection](https://pap...
Object Tracking,69,Perception Test; PersonPath22; GOT-10k; LaSOT; VOT2017; CDTB; Event-Camera Dataset; Open Radar Datasets; MDOT; Virtual KITTI,Perception Test; KITTI; COESOT; FE108; BIRDSAI - ICVGIP 2020; MMPTRACK; 1; QuadTrack; SeaDronesSee; VisEvent,0S; Humans; 3DMOTA; HOTA; Precision Plot; mean success; Success Rate; Animals; Average IOU; Precision Rate,Amodal Tracking; Sports Ball Detection and Tracking; Multi-Object Tracking; Multiple Object Tracking; Cell Tracking,Computer Vision,"**Object tracking** is the task of taking an initial set of object detections, creating a unique ID for each of the initial detections, and then tracking each of the objects as they move around frames..."
Face Recognition,67,CASIA-WebFace+masks; MLFW; IJB-S; IMFW; LFW; LTFT; WildestFaces; DiveFace; DemogPairs; Color FERET,CASIA-WebFace+masks; MLFW; LFW; UND-X1; Color FERET; CelebA+masks; LFW (Online Open Set); Carl; CFP-FF; CPLFW,Caucasian; TAR @ FAR=1e-5; Rank-1; F1-score; Average Accuracy (10 times); East Asian; Accuracy; FNMR [%] @ 10-3 FMR; TAR @ FAR=1e-3; Precision,Age-Invariant Face Recognition; Synthetic Face Recognition; Face Quality Assessement; Face Image Quality Assessment; Lightweight Face Recognition,Methodology; Computer Vision,**Facial Recognition** is the task of making a positive identification of a face in a photo or video image against a pre-existing database of faces. It begins with detection - distinguishing human fac...
3D Object Detection,67,KITTI-C; Mono3DRefer; Waymo Open Dataset; LiDAR-CS; ScanNet++; FAT; ONCE; DAIR-V2X; Aria Digital Twin Dataset; Cityscapes 3D,SUN-RGBD; KITTI Pedestrian Hard; nuScenes-F; KITTI Pedestrian Easy; KITTI Cyclists Hard; Waymo Open Dataset; ScanNet++; KITTI Cars Easy val; ONCE; DAIR-V2X,mod. Pedestrian AP@.25IoU; mod. mAP; SDS; AP50; AP@0.7; AP@0.7@CulverCity; APH/L2; ADD AUC; mAP; mod. Car AP@.5IoU,Monocular 3D Object Detection; 3D Object Detection From Stereo Images; Robust BEV Detection; Robust 3D Object Detection; Multiview Detection,Computer Vision,"**3D Object Detection** is a task in computer vision where the goal is to identify and locate objects in a 3D environment based on their shape, location, and orientation. It involves detecting the pre..."
Link Prediction,67,Arxiv GR-QC; Aristo-v4; PubMed Paper Reading Dataset; Decagon; GDELT; Douban; FB15k-237; IS-A; AbstRCT - Neoplasm; OGB,GPS; Cora (nonstandard variant); COLLAB; FB15k-237-ind; WN18 (filtered);  FB15k; JF17K; NELL-995; Citeseer; CoDEx Large,Interpretable; Hits; H@1; AUPR; Macro F1; Test Hits@50; MRR; ACC; Hits@5; HR@10,Dynamic Link Prediction; Link prediction on DH-KGs; Calibration for Link Prediction; Hyperedge Prediction; Inductive Link Prediction,Graphs; Natural Language Processing,"**Link Prediction** is a task in graph and network analysis where the goal is to predict missing or future connections between nodes in a network. Given a partially observed network, the goal of link ..."
3D Reconstruction,58,CVGL; SceneNet; Drunkard's Dataset; Common Objects in 3D; Aria Synthetic Environments; BlendedMVS; ShapenetRender; Aria Digital Twin Dataset; Middlebury MVS; 3DPeople Dataset,3DPeople; Scan2CAD; ShapeNet; Aria Digital Twin Dataset; 300W; Aria Synthetic Environments; DTU; Data3D−R2N2; ScanNet; ApolloCar3D,Overall; F-Score@1%; Chamfer Distance; P2S; Accuracy; A3DP; 1-of-100 Accuracy; IoU; Comp; Average Accuracy,3D Wireframe Reconstruction; 3D Semantic Scene Completion; Garment Reconstruction; Point cloud reconstruction; Unsupervised 3D Human Pose Estimation,Methodology; Computer Vision,**3D Reconstruction** is the task of creating a 3D model or representation of an object or scene from 2D images or other data sources. The goal of 3D reconstruction is to create a virtual representati...
Video Understanding,56,ChronoMagic-Pro; MTL-AQA; AirLetters; Stanford-ECM; STAR Benchmark; CONVERSE; EPIC-KITCHENS-55; SEED-Bench; i3-video; VTC,N/A,N/A,Temporal Sentence Grounding; Video Quality Assessment; Anomaly Detection In Surveillance Videos; Streaming video understanding; Long-video Activity Recognition,Computer Vision,"A crucial task of **Video Understanding** is to recognise and localise (in space and time) different actions or events appearing in the video.      <span class=""description-source"">Source: [Action Det..."
Optical Character Recognition (OCR),55,FSNS - Test; Kannada-MNIST; UTRSet-Synth; Chinese Text in the Wild; CodeSCAN; SUT; IllusionChar_test; im2latex-100k; TextCaps; UFPR-AMR,"VideoDB's OCR Benchmark Public Collection; FSNS - Test; SUT; im2latex-100k; Benchmarking Chinese Text Recognition: Datasets, Baselines, and an Empirical Study; I2L-140K",Character Error Rate (CER); Word Error Rate (WER); BLEU; Average Accuracy; Sequence error; Accuracy (%),Handwritten Digit Recognition; Irregular Text Recognition; Handwritten Chinese Text Recognition; Handwritten Text Recognition; Handwriting Recognition,Methodology; Computer Vision; Natural Language Processing,"**Optical Character Recognition** or **Optical Character Reader** (OCR) is the electronic or mechanical conversion of images of typed, handwritten or printed text into machine-encoded text, whether fr..."
Abstractive Text Summarization,53,Global Voices; Shmoop Corpus; Gigaword Entailment; LCSTS; FINDSum; Abstractive Text Summarization from Fanpage; Liputan6; MLSUM; BillSum; New York Times Annotated Corpus,Abstractive Text Summarization from Fanpage; AESLC; EDUsum; XSum; SAMSum Corpus: A Human-annotated Dialogue Dataset for Abstractive Summarization; vietnews; CNN/Daily Mail; Inshorts News; MLSUM es; MLSum-it,Rouge-1; ROUGE-1; Rouge-2; ROUGE; Rouge-L; ROUGE-L; rouge1; BERTScore; METEOR; Test ROGUE-1,Multimodal Abstractive Text Summarization; Timeline Summarization; Reader-Aware Summarization,Natural Language Processing,**Abstractive Text Summarization** is the task of generating a short and concise summary that captures the salient ideas of the source text. The generated summaries potentially contain new phrases and...
Medical Image Segmentation,52,LiTS17; CHASE_DB1; BreastDICOM4; ROBUST-MIS; Cell; 2018 Data Science Bowl; Extended Task10_Colon Medical Decathlon; GlaS; HC18; MoNuSeg,ASU-Mayo Clinic dataset; CHASE_DB1; SegPC-2021; ROBUST-MIS; Cell; 2018 Data Science Bowl; Extended Task10_Colon Medical Decathlon; GlaS; MoNuSeg; ETIS-LARIBPOLYPDB,Dice (Average); Dice Score; VS; MSD; MAE (5-folds); mean Dice; mIoU (5-folds); Accuracy; Test F1-Score; IoU,Liver Segmentation; Retinal Vessel Segmentation; Skin Lesion Segmentation; Placenta Segmentation; Skin Cancer Segmentation,Computer Vision; Medical,"**Medical Image Segmentation** is a computer vision task that involves dividing an medical image into multiple segments, where each segment represents a different object or structure of interest in th..."
Emotion Recognition,52,MSP-Podcast; nEMO; SEND; Emomusic; SEED; Affective Text; Aff-Wild; BERSt; CARER; Hateful Memes,MSP-Podcast; MPED; EMOTIC; Emomusic; SEED; FER2013; MaSaC_ERC; คลิปคุณพ่อให้ลูกสาวยืมโทรศัพท์และความสนุกสนาน; MAFW; RAVDESS,"Top-3 Accuracy (%); 5-class test accuracy; 1'""; Accuracy; Concordance correlation coefficient (CCC); EmoV; WAR; F1-score (Weighted); EmoA",Video Emotion Recognition; Facial Emotion Recognition; A-VB Two; A-VB Culture; Emotion-Cause Pair Extraction,Computer Vision; Natural Language Processing; Audio; Speech; Miscellaneous,"**Emotion Recognition** is an important area of research to enable effective human-computer interaction. Human emotions can be detected using speech signal, facial expressions, body language, and elec..."
3D Human Pose Estimation,50,ExPose; EgoCap; EMDB; Waymo Open Dataset; Campus & Shelf; AGORA; 3DPW; TotalCapture; MoVi; HPS,EMDB; Waymo Open Dataset; SPEC-MTP; AGORA; 3DPW; AIST++; SLOPER4D; RICH; UBody; Panoptic,PA-PVE-Hands; PCK3D (CA); Jitter (10m/s^3); Angular Error; Using 2D ground-truth joints; W-MPJPE; MPVE (mm); 3DPCK; Average MPJAE (deg); B-NMVE,Pose Prediction; Global 3D Human Pose Estimation; Multi-Hypotheses 3D Human Pose Estimation; Egocentric Pose Estimation; 3D Absolute Human Pose Estimation,Computer Vision,**3D Human Pose Estimation** is a computer vision task that involves estimating the 3D positions and orientations of body joints and bones from 2D images or videos. The goal is to reconstruct the 3D p...
Hate Speech Detection,47,HatEval; EchoKG; Echo Corpus; DeToxy; L3Cube-MahaCorpus; STATE ToxiCN; ETHOS; ToLD-Br; Bengali Hate Speech; DKhate,"HatEval; bajer_danish_misogyny; Ethos Binary; Automatic Misogynistic Identification; SHAJ; OffensEval 2019; HateMM; Waseem et al., 2018; Ethos MultiLabel; ToLD-Br",F1; Accuracy; TEST F1 (macro); AAA; Hamming Loss; F1 (micro); Macro-F1; Macro F1; Classification Accuracy; Precision,Hate Speech Detection CrisisHateMM Benchmark; Hate Speech Normalization; Hope Speech Detection,Natural Language Processing,"Hate speech detection is the task of detecting if communication such as text, audio, and so on contains hatred and or encourages violence towards a person or a group of people. This is usually based o..."
Novel View Synthesis,46,ScanNet++; BlendedMVS; IBL-NeRF; X3D; NRHints-Synthetic; Mip-NeRF 360; NERDS 360; Synthetic Soccer NeRF Dataset; RefRef; Replay,ScanNet++; X3D; Mip-NeRF 360; RefRef; iFF; RTMV; DONeRF: Evaluation Dataset; Deep Blending; Dosovitskiy Chairs; RealEstate10K,LPIPS; FID; PSNR/SSIM; Average PSNR (dB); PSIM; SSIM; NLL; Size (MB); Focal Error; PSNR,Novel LiDAR View Synthesis; Gournd video synthesis from satellite image,Computer Vision,Synthesize a target image with an arbitrary target camera pose from given source images and their camera poses.    See [Wiki](https://en.wikipedia.org/wiki/View_synthesis) for more introductions.    T...
Object Recognition,45,CIFAR10-DVS; Open MIC; AU Dataset for Visuo-Haptic Object Recognition for Robots; NYU Symmetry Database; ChessReD; Fine-grained 3D Pose; RP2K; Freiburg Groceries; DeepScores; EGO-CH,"CIFAR10-DVS; DVS128 Gesture; ObjectNet (All classes); ObjectNet (ImageNet classes, trained on ImageNet); shape bias; N-CARS; MECCANO; N-Caltech 101; ObjectNet (ImageNet classes)",Top 1 Accuracy; Accuracy (% ); shape bias; mAP; Top 5 Accuracy,Continuous Object Recognition; 3D Object Recognition; Depiction Invariant Object Recognition,Computer Vision,"Object recognition is a computer vision technique for detecting + classifying objects in images or videos. Since this is a combined task of object detection plus image classification, the state-of-the..."
Semantic Parsing,45,ComQA; NomBank; SpCQL; ComplexWebQuestions; Stanford Schema2QA Dataset; Geometry3K; PCFG SET; CSQA; WikiTableQuestions; KQA Pro,"spider; complexWebQuestions-V1.0; WikiTableQuestions; DRG (english, MRP 2020); DRG (german, MRP 2020); SQA; WikiSQL; EDS (english, MRP 2020); WebQuestionsSP; SParC",Test Accuracy; F1; Exact Match; Accuracy; F1 Score; Accuracy (Dev); EM; Denotation Accuracy; Exact; Accuracy (Test),AMR Parsing; Text-To-SQL; UCCA Parsing; Unsupervised semantic parsing; DRS Parsing,Natural Language Processing,**Semantic Parsing** is the task of transducing natural language utterances into formal meaning representations. The target meaning representations can be defined according to a wide variety of formal...
Segmentation,45,SegRCDB; MMFlood; DeepCrack; ROBUST-MIS; NPO; Coil100-Augmented; Extended Task10_Colon Medical Decathlon; Microscopy Image Dataset of Pulmonary Vascular Changes; Box-IS; CUTS,SA-1B; !(()&&!|*|*|; MMFlood; MFSD; SimGas,10%; AR-medium; AR-small; IoU; Average Precision; F1 Score; Recall; Precision; AR-large; F1 score,Open-Vocabulary Semantic Segmentation,Computer Vision,N/A
Image Clustering,44,EuroSAT; ImageNet-100 (TEMI Split); Kinetics-700; Hateful Memes; DTD; SUN397; FER2013; FGVC-Aircraft; STL-10; Oxford 102 Flower,EuroSAT; Extended Yale-B; ImageNet-100 (TEMI Split); Kinetics-700; UMist; MNIST-full; Coil-20; Hateful Memes; DTD; SUN397,	 ACCURACY; Accuracy; Train Set; Image Size; ARI; Backbone; Train set; NMI; ACCURACY; Train Split,Online Clustering; Face Clustering; Multi-view Subspace Clustering; Multi-modal Subspace Clustering,Computer Vision,"Models that partition the dataset into semantically meaningful clusters without having access to the ground truth labels.     <span style=""color:grey; opacity: 0.6""> Image credit: ImageNet clustering ..."
Visual Reasoning,44,SVRT; TextCaps; ADE-Affordance; NLVR; VSR; ComPhy; lilGym; SMART-101; InfiMM-Eval; PolyMATH,NLVR2 Test; IRFL: Image Recognition of Figurative Language; NLVR2 Dev; PHYRE-1B-Within; VSR; PHYRE-1B-Cross; NLVR; Bongard-OpenWorld; CLEVRER; VASR,Explanatory-per ques.; Average-per ques.; Text Score; AUCCESS; Descriptive; Accuracy (Test-U); Accuracy; Image Score; Jaccard Index; Predictive-per ques.,Visual Commonsense Reasoning,Computer Vision; Reasoning,Ability to understand  actions and reasoning  associated with any  visual images
Image Super-Resolution,43,FFHQ; Middlebury; AeroRIT; Sen2venus; TESTIMAGES; xView; Vimeo90K; PIRM; EDFace-Celeb-1M; CelebA,Manga109 - 4x upscaling; Set14 - 4x upscaling; BSD100 - 2x upscaling; Set14 - 2x upscaling; DIV8K val - 16x upscaling; 3x upscaling; WebFace - 8x upscaling; BSD100 - 8x upscaling; Manga109 - 8x upscaling; Manga109 - 2x upscaling,MOS; NIQE; MS-SSIM; FED; SSIM; Frechet Inception Distance; UIQM; LRPSNR; Average Accuracy; #params (K),satellite image super-resolution; Multi-Frame Super-Resolution; Stereo Image Super-Resolution; Burst Image Super-Resolution; Multispectral Image Super-resolution,Computer Vision,"**Image Super-Resolution** is a machine learning task where the goal is to increase the resolution of an image, often by a factor of 4x or more, while maintaining its content and details as much as po..."
Image Segmentation,43,Raw-Microscopy and Raw-Drone; CEMS-W; UV6K; MAS3K; MineralImage5k; WildScenes; EBHI-Seg; EVD4UAV; IITKGP_Fence Dataset; Pascal Panoptic Parts,COCO val2017; MSD Heart; MAS3K; ImageNet; MARIDA; RMAS; HuTu 80; MSD (Mirror Segmentation Dataset); PMD; EVD4UAV,F1; mIoUPartS; Dice Score; GFLOPs; S-measure; IoU; F1@M; MAE; F-measure; E-measure,Few-shot Instance Segmentation,Computer Vision,"**Image Segmentation** is a computer vision task that involves dividing an image into multiple segments or regions, each of which corresponds to a different object or part of an object. The goal of im..."
Video Question Answering,43,MSVD-QA; MovieQA; Perception Test; HowToVQA69M; VLM2-Bench; TGIF; TVQA+; VALUE; STAR Benchmark; Neptune,MSVD-QA; Perception Test; STAR Benchmark; TVQA; RoadTextVQA; OVBench; NExT-QA (Efficient); LSMDC-MC; VideoQA; How2QA,	 ACCURACY; ROUGE-1; CH; Accuracy; Confidence score; Avg.; ROUGE-L; Accuracy (Top-1); Average Accuracy; 1/2,Zero-Shot Video Question Answer; Few-shot Video Question Answering,Computer Vision; Reasoning,N/A
Multi-Object Tracking,43,BEE23; LTFT; Synthehicle; PersonPath22; Youtube-VIS 2022 Validation; Virtual KITTI; PathTrack; GMOT-40; RailEye3D Dataset; MOT17,VisDrone2019; PersonPath22; Synthehicle; MOT17; MOT16; JRDB; SportsMOT; MOT15_3D; TAO; 2D MOT 2015,IDF1; Track mAP; ClsA; AssocA; e2e-MOT; MOTP; LocA; MOTA; AssA; TETA,Grounded Multiple Object Tracking; Trajectory Long-tail Distribution for Muti-object Tracking; Real-Time Multi-Object Tracking; Multi-Animal Tracking with identification; 3D Multi-Object Tracking,Computer Vision,**Multi-Object Tracking** is a task in computer vision that involves detecting and tracking multiple objects within a video sequence. The goal is to identify and locate objects of interest in each fra...
Scene Understanding,43,DeepLoc; SceneNet; Apron Dataset; SEL; Stanford-ECM; AeroRIT; PSI-AVA; DeepScores; RADIATE; Pascal Panoptic Parts,Semantic Scene Understanding Challenge (passive actuation & ground-truth localisation); ADE20K val; Semantic Scene Understanding Challenge (active actuation & ground-truth localisation),avg_fp_quality; OMQ; avg_spatial; avg_pairwise; Mean IoU; avg_label,road scene understanding; Outdoor Light Source Estimation; Lighting Estimation; Visual Relationship Detection; Video Semantic Segmentation,Computer Vision,"Scene understanding involves interpreting the visual information of a scene, including objects, their spatial relationships, and the overall layout. It goes beyond simple object recognition by conside..."
Temporal Action Localization,42,Perception Test; Kinetics-700; Ego4D; DECADE; Florence3D; CrossTask; CONVERSE; FineAction; MCAD; TinyVIRAT,ActivityNet-1.3; Ego4D MQ val; THUMOS14; EPIC-KITCHENS-100; MEXaction2; Ego4D MQ test; HACS; THUMOS’14; CrossTask; MUSES,mAP@0.3; mAP IOU@0.9; mAP IOU@0.95; mAP IOU@0.5; mAP IOU@0.8; mAP@0.6; mAP@0.7; mAP; Avg mAP (0.3:0.7); mAP@0.4,Action Recognition In Still Images; Temporal Group Activity Localization; Temporal Action Proposal Generation; Weakly Supervised Action Localization; 3D Action Recognition,Computer Vision,Temporal Action Localization aims to detect activities in the video stream and  output beginning and end timestamps. It is closely related to  Temporal Action Proposal Generation.
Fine-Grained Image Classification,41,Apron Dataset; Orchid2024; Herbarium 2021 Half–Earth; LymphoMNIST; RP2K; SUN397; DIB-10K; FoodX-251; FGSCM-52; FGVC-Aircraft,Fruits-360; Herbarium 2021 Half–Earth; BoxCars116K; SUN397; FoodX-251; EMNIST-Letters; SOP; DIB-10K; NABirds; FGVC-Aircraft,Average Per-Class Accuracy; Top-1; Accuracy; PARAMS; Top 1 Accuracy; Test F1 score; Test F1 score (private); FLOPS; mAP; Top-1 Error Rate,Displaced People Recognition,Computer Vision,"**Fine-Grained Image Classification** is a task in computer vision where the goal is to classify images into subcategories within a larger category. For example, classifying different species of birds..."
3D Semantic Segmentation,41,Waymo Open Dataset; ScanNet++; SynLiDAR; WildScenes; SemanticPOSS; ECLAIR; nuScenes; HM3DSem; Toronto-3D; AMOS,Waymo Open Dataset; ScanNet++; WildScenes; ECLAIR; nuScenes; Toronto-3D; RELLIS-3D Dataset; DALES; Hypersim; SemanticKITTI,Top-3 IoU; mIoU (Env DA); Mean IoU; mIoU (Temporal DA); mAcc; Model size; miou; Top-1 IoU; mIOU; Overall Accuracy,Real-Time 3D Semantic Segmentation; 3D Point Cloud Part Segmentation; furniture segmentation; Unsupervised 3D Semantic Segmentation; Robust 3D Semantic Segmentation,Computer Vision,**3D Semantic Segmentation** is a computer vision task that involves dividing a 3D point cloud or 3D mesh into semantically meaningful parts or regions. The goal of 3D semantic segmentation is to iden...
Face Detection,39,LTFT; MaskedFace-Net; PASCAL Face; FDDB-360; MS-EVS Dataset; HDA Facial Tattoo and Painting Database; AnimalWeb; Human-Parts; BAFMD; WIDER,WIDER FACE; iCartoonFace; COCO-WholeBody; DCM; PASCAL Face; WIDER Face (Hard); Annotated Faces in the Wild; WIDER Face (Medium); WIDER Face; WIDER Face (Easy),GFLOPs; Accuracy; Average Precision; Average Top-1 Accuracy; AP50; APL; AP; AP75; APM; mIoU,Occluded Face Detection,Computer Vision,**Face Detection** is a computer vision task that involves automatically identifying and locating human faces within digital images or videos. It is a fundamental technology that underpins many applic...
Retrieval,38,FanOutQA; ProofNet; DIOR-RSVG; Polyvore; InfoSeek; PTVD; PubMedQA corpus with metadata; PopQA; DAPFAM; Natural Questions,PubMedQA corpus with metadata; Quora Question Pairs; Natural Questions; OK-VQA; คลิปไวรัล!! ไอซ์ ปรีชญา ลืมปิดไลฟ์สดตอนอาบน้ำ ถูกแชร์กระหึ่มเน็ต; MVK; PubMedQA; Polyvore; ToolLens; HotpotQA,text-to-video Mean Rank; Queries per second; Accuracy (Top-1); COMP@; Recall@5; 0L,Text Retrieval; Table Retrieval; Deep Hashing,Methodology; Computer Vision; Natural Language Processing,"A methodology that involves selecting relevant data or examples from a large dataset to support tasks like prediction, learning, or inference. It enhances models by providing context or additional inf..."
Video Captioning,38,MSVD-Indonesian; MTL-AQA; LSMDC-Context; Hindi MSR-VTT; MSVD; TGIF; NSVA; VALUE; MMAC Captions; TVC,MSVD-Indonesian; TVC; YouCook2; VATEX; Shot2Story20K; Hindi MSR-VTT; ActivityNet Captions; MSRVTT-CTN; MSVD; VidChapters-7M,ROUGE; ROUGE-L; BLEU-3; BLEU4; METEOR; CIDEr; SPICE; BLEU-4; GS,Audio-Visual Video Captioning; Dense Video Captioning; Visual Text Correction; Video Boundary Captioning; Live Video Captioning,Computer Vision,**Video Captioning** is a task of automatic captioning a video by understanding the action and event in the video which can help in the retrieval of the video efficiently through text.      <span clas...
Panoptic Segmentation,37,PhenoBench; PASTIS; YIM Dataset; PanNuke; Mapillary Vistas Dataset; CropAndWeed; ScanNet; Pascal Panoptic Parts; Cityscapes; 4D-OR,Panoptic nuScenes test; SUN-RGBD; COCO minival; ADE20K val; PASTIS; Cityscapes val; PanNuke; KITTI Panoptic Segmentation; ScanNet; COCO test-dev,PQth; SQ (with stuff); RQth; PQ; PQ_dagger; RQ; PQ_th; RQ (with stuff); PQ (with stuff); Params (M),Video Panoptic Segmentation; Uncertainty-Aware Panoptic Segmentation,Computer Vision,**Panoptic Segmentation** is a computer vision task that combines semantic segmentation and instance segmentation to provide a comprehensive understanding of the scene. The goal of panoptic segmentati...
Text Retrieval,37,ACR Appropriateness Criteria Corpus; french-legal-cases; 20 Newsgroups; DIOR-RSVG; PolyNewsParallel; SciDocs; TripClick; DAPFAM; Spanish Corpus XIX; Natural Questions,N/A,N/A,N/A,N/A,N/A
3D Pose Estimation,36,Drunkard's Dataset; HO-3D v2; PedX; Fine-grained 3D Pose; Accidental Turntables; LSP; Symmetric Solids; CarFusion; IBISCape; InfiniteRep,CarFusion; Google-AR; HARPER; K2HPD; Human3.6M; Google-Yoga; ApolloCar3D,A3DP; PCK@0.2; 3DPCK; Average MPJPE (mm); FPS,N/A,Computer Vision,"Image credit: [GSNet: Joint Vehicle Pose and Shape Reconstruction with Geometrical and Scene-aware Supervision  , ECCV'20](https://www.ecva.net/papers/eccv_2020/papers_ECCV/papers/123600511.pdf)"
Optical Flow Estimation,35,RobotPush; I2-2000FPS; OmniFlow; Creative Flow+ Dataset; QUVA Repetition; VisDrone; Event-Camera Dataset; GOF; MPI Sintel; Virtual KITTI,KITTI 2012 unsupervised; Sintel Final unsupervised; Sintel-final; Sintel-clean; KITTI 2015 unsupervised; Sintel Clean unsupervised; Spring; KITTI 2015; KITTI 2015 (train) ; KITTI 2012,F1-all; Average End-Point Error; Fl-all; Out-Noc; Fl-fg; 1px total; Noc; EPE,Video Stabilization,Computer Vision,**Optical Flow Estimation** is a computer vision task that involves computing the motion of objects in an image or a video sequence. The goal of optical flow estimation is to determine the movement of...
Video Retrieval,35,MSVD-Indonesian; ChronoMagic-Pro; MSVD; TGIF; DiDeMo; VTC; TVC; YouCook2; Kinetics; ActivityNet,MSVD-Indonesian; MSVD; TGIF; DiDeMo; SSv2-label retrieval; YouCook2; VATEX; ActivityNet; RUDDER; QuerYD,R@10; text-to-video Median Rank; mAP (ISVR); video-to-text R@1; text-to-video R@5; text-to-video R@1; text-to-video MedianR; video-to-text R@5; mAP (CSVR); Accuracy,Replay Grounding; Video Grounding; Video-Adverb Retrieval; Composed Video Retrieval (CoVR); Video-Text Retrieval,Computer Vision,"The objective of video retrieval is as follows: given a text query and a pool of candidate videos, select the video which corresponds to the text query.  Typically, the videos are returned as a ranked..."
Trajectory Prediction,35,FPL; FollowMe Vehicle Behaviour Prediction Dataset; BLVD; Euro-PVI; uniD Dataset; UCY; Argoverse; highD Dataset; PROX; SDD,TrajNet++; GPS; Hotel BIWI Walking Pedestrians dataset; YJMob100K@C; YJMob100K@D; UCY; Argoverse; PROX; ETH/UCY; SDD,ADE(1.5); FDE(1.5); AMD; Avg AMD/AMV 8/12; FDE-8/12; MR (K=6); MSE(0.5); MissRateTopK_2_5; ADE; C_MSE(1.5),Human motion prediction; Trajectory Forecasting; Out-of-Sight Trajectory Prediction,Computer Vision; Time Series,"**Trajectory Prediction** is the problem of predicting the short-term (1-3 seconds) and long-term (3-5 seconds) spatial coordinates of various road-agents such as cars, buses, pedestrians, rickshaws, ..."
Stance Detection,35,FNC-1; RAWFC; P-Stance; CoVaxFrames; CoVaxLies v2; ExaASC; Conversational Stance Detection; BigNews; x-stance; SemEval-2016 Task-6,RumourEval; FNC-1; mtsd; wtwt; P-Stance; poldeb; emergent; iac1; Trump Midterm Elections 2018; VAST,F1; Accuracy (10-fold); Macro Recall; Accuracy; Macro Precision; F1 Score; Avg F1; Average F1; Recall; Acc,Zero-Shot Stance Detection; Stance Detection (US Election 2020 - Trump); Stance Detection (US Election 2020 - Biden); Few-Shot Stance Detection,Natural Language Processing,"Stance detection is the extraction of a subject's reaction to a claim made by a primary actor. It is a core part of a set of approaches to fake news assessment.  Example:  * Source: ""Apples are the mo..."
Action Detection,34,UCF101-24; ROAD; MLB-YouTube Dataset; WEAR; ActivityNet; TSU; OREBA; InfiniteRep; Mouse Grooming Behavior; Charades,UCF101-24; TSU; J-HMDB; THUMOS' 14; Charades; TTStroke-21 ME21; Multi-THUMOS; UCF Sports; TTStroke-21 ME22; MultiTHUMOS,Frame-mAP 0.5; Video-mAP 0.1; Frame-mAP; IoU; Video-mAP 0.2; mAP; Video-mAP 0.5,Few Shot Temporal Action Localization; Fine-Grained Action Detection; Multiple Action Detection; Skeleton Based Action Recognition; Human Activity Recognition,Computer Vision; Time Series,"Action Detection aims to find both where and when an action occurs within a video clip and classify what the action is taking place. Typically results are given in the form of action tublets, which ar..."
Automatic Speech Recognition (ASR),34,The Spoken Wikipedia Corpora; VibraVox (throat microphone); VibraVox (rigid in-ear microphone); FLEURS; RealMAN; Voxforge German; EdAcc; GneutralSpeech Female; IMaSC; ESB,The Spoken Wikipedia Corpora; VoxPopuli; LRS2; HUI speech corpus; Sagalee; LRS3-TED; RealMAN; Voxforge German; M-AILabs speech dataset,Word Error Rate (WER); Test WER; WER (%); WER; CER,Automatic Phoneme Recognition,Speech,"**Automatic Speech Recognition (ASR)** involves converting spoken language into written text. It is designed to transcribe spoken words into text in real-time, allowing people to communicate with comp..."
Monocular Depth Estimation,33,3D60; Matterport3D; DIODE; DDAD; Cityscapes 3D; Virtual KITTI; InfraParis; MannequinChallenge; UASOL; Cityscapes,N/A,N/A,N/A,N/A,N/A
Action Classification,33,Kinetics-700; CelebV-HQ; Something-Something V2; HAA500; Kinetics 400; Sims4Action; MLB; SoccerNet; YouCook2; Kinetics,N/A,N/A,N/A,N/A,N/A
Visual Question Answering,32,MSVD-QA; uBench; VizWiz; OpenViVQA; MM-Vet; TextVQA; MM-Vet v2; CII-Bench; GQA-OOD; ViLCo,MSVD-QA; VizWiz; MM-Vet; VQA v2; COCO Visual Question Answering (VQA) real images 2.0 open ended; MM-Vet v2; TextVQA test-standard; V*bench; VQA v2 test-dev; VQA v2 val,VQA (ablation); GPT-4 score (bbox); CIDEr; number; Hallucination Rate; yes/no; Accuracy; Overall Accuracy; Params; Accuracy (% ),Object Hallucination; Explanatory Visual Question Answering; Spatial Reasoning; MM-Vet v2; Vietnamese Visual Question Answering,Computer Vision; Natural Language Processing,MLLM Leaderboard
Image-to-Image Translation,32,UDA-CH; FFHQ-Aging; Foggy Cityscapes; SEN12MS-CR-TS; CARLA2Real; RASMD; LaMem; Mila Simulated Floods; NIR2RGB VCIP Challange Dataset; Cityscapes,dog2cat; COCO-Stuff Labels-to-Photos; vangogh2photo; anime-to-selfie; photo2portrait; SYNTHIA-to-Cityscapes; Deep-Fashion; KITTI Object Tracking Evaluation 2012; Aerial-to-Map; Zebra and Horses,SSIM; Frechet Inception Distance; mAP; 10 way 1~2 shot; Classification Error; Accuracy; Per-pixel Accuracy; Per-class Accuracy; Number of Params; fwIOU,Real-to-Cartoon translation; Bird View Synthesis; Fundus to Angiography Generation; Multimodal Unsupervised Image-To-Image Translation; Photo-To-Caricature Translation,Computer Vision,"**Image-to-Image Translation** is a task in computer vision and machine learning where the goal is to learn a mapping between an input image and an output image, such that the output image can be used..."
Skeleton Based Action Recognition,30,Drive&Act; G3D; Penn Action; Florence3D; NTU-X; NTU RGB+D; Kinetics 400; Kinetics; SBU / SBU-Refine; UCF101,N/A,N/A,N/A,N/A,N/A
Activity Recognition,30,UniMiB SHAR; Simitate; MotionSense; MOSAD; MoVi; OPERAnet; Sims4Action; Stanford40; InfiniteRep; CLAD,First-Person Hand Action Benchmark; Stanford40; RWF-2000; Self-Stimulatory Behavior Dataset,Accuracy; Top-3 Accuracy (%); Activity Recognition; 1:1 Accuracy,Multimodal Activity Recognition; Group Activity Recognition; Cross-Domain Activity Recognition; Recognizing And Localizing Human Actions; Stay Region Extraction,Computer Vision; Robots; Time Series,Human **Activity Recognition** is the problem of identifying events performed by humans given a video input. It is formulated as a binary (or multiclass) classification problem of outputting activity ...
Fake News Detection,30,FNC-1; RAWFC; Twitter MediaEval; UPFD-POL; Some Like it Hoax; UPFD-GOS; Fake News Filipino Dataset; LIAR-RAW; BanFakeNews; MM-COVID,FNC-1; Social media; RAWFC; Weibo NER; MediaEval2016; PolitiFact; Hostility Detection Dataset in Hindi; LIAR; Grover-Mega; COVID-19 Fake News Dataset,F1; Accuracy; Unpaired Accuracy; Per-class Accuracy (Unrelated); 1:1 Accuracy; Per-class Accuracy (Disagree); Per-class Accuracy (Discuss); Weighted Accuracy; Validation Accuracy; F1 score,N/A,Natural Language Processing,**Fake News Detection** is a natural language processing task that involves identifying and classifying news articles or other types of text as real or fake. The goal of fake news detection is to deve...
Sign Language Recognition,30,LSFB Datasets; CSL-Daily; Bukva; MINDS-Libras; RWTH-PHOENIX-Weather 2014 T; MSASL-1000; ChicagoFSWild; GSL; RWTH-PHOENIX-Weather 2014; How2Sign,Bukva; CSL-Daily; WLASL-2000; WLASL100; MINDS-Libras; RWTH-PHOENIX-Weather 2014 T; MSASL-1000; ChicagoFSWild; GSL; RWTH-PHOENIX-Weather 2014,Word Error Rate (WER); P-I Top-1 Accuracy; Accuracy; Official Test Split; Top-1 Accuracy; Rank-1 Recognition Rate; Accuracy (Top-1); P-C Top-1 Accuracy; Actions Top-1; Recall,N/A,Computer Vision,**Sign Language Recognition** is a computer vision and natural language processing task that involves automatically recognizing and translating sign language gestures into written or spoken language. ...
Visual Object Tracking,29,VOT2022; TrackingNet; OTB-2013; GOT-10k; LaSOT; VOT2017; CDTB; RGBD1K; YouTube-VOS 2018; VOT2018,VOT2022; TrackingNet; TempleColor128; OTB-2013; VOT2017; LaSOT; GOT-10k; VOT2017/18; VOT2018; YouTube-VOS 2018,Success Rate 0.75; Tracking quality; Accuracy; Normalized Precision; F-Measure (Unseen); Average Overlap; AUC; O (Average of Measures); Jaccard (Seen); EAO,Zero-Shot Single Object Tracking,Computer Vision,"**Visual Object Tracking** is an important research topic in computer vision, image understanding and pattern recognition. Given the initial state (centre location and scale) of a target in the first ..."
Facial Expression Recognition (FER),29,FERG; Oulu-CASIA; FER2013; DAiSEE; FER+; MH-FED; 4DFAB; CREMA-D; MAFW; DFEW,FERG; Oulu-CASIA; FER2013; FER+; CREMA-D; CK+; ^(#$!@#$)(()))******; SFEW; BP4D; RAF-DB,Accuracy (8 emotion); Accuracy (10-fold); UAR; Accuracy; Accuracy(on validation set); Accuracy (6 emotion); Overall Accuracy; Avg. Accuracy; Accuracy (7 emotion); Accuracy(pretrained),Smile Recognition; 3D Facial Expression Recognition; Cross-corpus; Micro-Expression Spotting; Micro-Expression Recognition,Computer Vision,**Facial Expression Recognition (FER)** is a computer vision task aimed at identifying and categorizing emotional expressions depicted on a human face. The goal is to automate the process of determini...
Scene Text Recognition,29,UTRSet-Synth; Vehicle-Rear; UFPR-AMR; UFPR-ALPR; TextOCR; Copel-AMR; IIIT5k; ICDAR 2013; TS-TR; GSL,ICDAR2013; SVTP; SVT; COCO-Text; IC13; CUTE80; SVT-P; IC19-Art; ICDAR2015; Uber-Text,1:1 Accuracy; Average Accuracy; Accuracy (%); Accuracy,Jersey Number Recognition,Computer Vision,See [Scene Text Detection](https://paperswithcode.com/task/scene-text-detection) for leaderboards in this task.
Autonomous Vehicles,29,ROAD; Talk2Car; HSD; CARLA; SynthCity; comma 2k19; DADA-2000; INTERACTION Dataset; Car datasets in multiple scenes; RadarScenes,ApolloCar3D,A3DP,CARLA Leaderboard 2.0; Simultaneous Localization and Mapping; Pedestrian Density Estimation; Pedestrian Detection; Traffic Sign Recognition,Computer Vision; Robots; Computer Code,Autonomous vehicles is the task of making a vehicle that can guide itself without human conduction.    Many of the state-of-the-art results can be found at more general task pages such as [3D Object D...
Unsupervised Anomaly Detection,28,MVTecAD; AnoShift; KolektorSDD; SMAP; ECG5000; MIMII; ToyADMOS; SMD; MVTec LOCO AD; STL-10,AeBAD-S; Synthetic; Vehicle Claims; AnoShift; KolektorSDD; SMAP; ECG5000; DAGM2007; TIMo; KolektorSDD2,F1; ROC-AUC-ID (In-Distribution setup); ROC-AUC IID; AUC-ROC; AUC; Detection AP; AUROC; Best F1; Detection AUROC; Recall,Anomaly Detection at Various Anomaly Percentages; Root Cause Ranking; Anomaly Detection at 30% anomaly; Unsupervised Anomaly Detection with Specified Settings -- 30% anomaly; Unsupervised Contextual Anomaly Detection,Miscellaneous; Computer Vision; Graphs,The objective of **Unsupervised Anomaly Detection** is to detect previously unseen rare objects or events without any prior knowledge about these. The only information available is that the percentage...
Object Counting,28,ACCT Data Repository; TRANCOS; SmartCity; FSC147; FSOD; COWC; fluocells; Blue Cells enumeration dataset; HowMany-QA; iWildCam 2021,Omnicount-191; TRANCOS; FSC147; Pascal VOC 2007 count-test; TallyQA-Complex; TallyQA-Simple; CARPK; COCO count-test; PASCAL VOC; HowMany-QA,mRMSE-nz; m-reIRMSE-nz; Accuracy; MAE(val); MAE(test); RMSE(test); MAE; RMSE; MSE; RMSE(val),Exemplar-Free Counting; Open-vocabulary object counting; Few-shot Object Counting and Detection; Training-free Object Counting,Computer Vision,"The goal of **Object Counting** task is to count the number of object instances in a single image or video sequence. It has many real-world applications such as traffic flow monitoring, crowdedness es..."
Visual Place Recognition,28,Oxford RobotCar Dataset; FAS100K; AmsterTime; SF-XL test v1; SF-XL Night; NYU-VPR; Mapillary Vistas Dataset; SF-XL Occlusion; NCLT; Retrieval-SfM,SVOX-Sun; Inside Out; Oxford RobotCar Dataset; AmsterTime; VP Air; SF-XL test v1; SF-XL Night; SF-XL Occlusion; 17 Places; SF-XL test v2,recall@top1%; Recall@10; recall@top1; Average F1; Recall@5; Localization Recall@1 ; Recall@1,Indoor Localization; geo-localization; 3D Place Recognition,Computer Vision,"**Visual Place Recognition** is the task of matching a view of a place with a different view of the same place taken at a different time.    <span class=""description-source"">Source: [Visual place reco..."
Visual Tracking,28,LTFT; TrackingNet; SurgT; OTB-2013; Drunkard's Dataset; CDTB; LaSOT; MDOT; TLP; MobiFace,TrackingNet; Kinetics; OTB-2013; Kubric; LaSOT; OTB-100; RGB-Stacking; DAVIS; Second dialogue state tracking challenge; TNL2K,Normalized Precision; AUC; Average Jaccard; precision; Score; ACCURACY,Real-Time Visual Tracking; RF-based Visual Tracking; Rgb-T Tracking; Point Tracking,Computer Vision,"**Visual Tracking** is an essential and actively researched problem in the field of computer vision with various real-world applications such as robotic services, smart surveillance systems, autonomou..."
Neural Architecture Search,27,HW-NAS-Bench; DTD; Freiburg Groceries; Visual Wake Words; EA-HAS-Bench; LIDC-IDRI; FGVC-Aircraft; NAS-Bench-201; STL-10; Oxford 102 Flower,"NATS-Bench Size, CIFAR-10; NAS-Bench-201, CIFAR-10; DTD; LIDC-IDRI; NAS-Bench-201; STL-10; NAS-Bench-101; NATS-Bench Topology, ImageNet16-120; NAS-Bench-201, CIFAR-100; NATS-Bench Size, ImageNet16-120",Spearman's Rho; Spearman Correlation; MACs; Top-1 Error Rate; Validation Accuracy; Accuracy (%); PARAMS; Percentage Error; Kendall's Tau; Search time (s),Activation Function Synthesis,Methodology,"**Neural architecture search (NAS)** is a technique for automating the design of artificial neural networks (ANN), a widely used model in the field of machine learning. NAS essentially takes the proce..."
2D Human Pose Estimation,27,Complain~How do I complain to Expedia?; Halpe-FullBody; PoPArt; AGORA; Bizarre Pose Dataset; PeopleSansPeople; InfiniteRep; Fitness-AQA; CropCOCO; C2A: Human Detection in Disaster Scenarios,Human-Art; COCO-WholeBody; ExLPose-LL-E; ExLPose-LL-N; ExLPose-OCN-RICOH3; ExLPose-LL-H; Alibaba Cluster Trace; OCHuman; JHMDB (2D poses only); ExLPose-OCN-A7M3,AP (gt bbox); WB; 10-20% Mask PSNR; AP; Validation AP; body; face; hand; PCK; Test AP,Action Anticipation; Community Question Answering; Articles; Semi-Supervised Human Pose Estimation; 3D Face Animation,Computer Vision; Reasoning; Knowledge Base,"What is Human Pose Estimation?  Human pose estimation is the process of estimating the configuration of the body (pose) from a single, typically monocular, image. Background. Human pose estimation is ..."
Emotion Classification,27,nEMO; CMU-MOSEI; AVCAffe; ReactionGIF; VNEMOS; PhyMER; ArmanEmo; ISEAR; MFA; EmoBank,ArmanEmo; ROCStories; SemEval 2018 Task 1E-c; EWALK; CMU-MOSEI; MFA; CAER-Dynamic; ShortPersianEmo; RAVDESS,F1; F-F1 score (NA); Accuracy; Micro-F1; Top-1 Accuracy; F-F1 score (Persian); V-F1 score (Comb.); V-F1 score (Persian); V-F1 score (NA); F-F1 score (Comb.),N/A,Computer Vision; Natural Language Processing,"Emotion classification, or emotion categorization, is the task of recognising emotions to classify them into the corresponding category. Given an input, classify it as 'neutral or no emotion' or as on..."
Visual Localization,27,DeepLoc; Long-term visual localization; Structured3D; KAIST Urban; GRAL; InLoc; FAS100K; PhotoSynth; ROVER; SeasonDepth,Oxford RobotCar Full; RobotCar Seasons v2; Aachen Day-Night v1.1 Benchmark; Extended CMU Seasons; Oxford Radar RobotCar (Full-6),"Acc@5m, 10°; Acc @ .5m, 5°; Acc@0.5m, 5°; Acc @ .25m, 2°; Acc @ 5m, 10°; Acc@0.25m, 2°; Mean Translation Error",N/A,Computer Vision,"**Visual Localization** is the problem of estimating the camera pose of a given image relative to a visual representation of a known scene.   <span class=""description-source"">Source: [Fine-Grained Seg..."
Visual Odometry,26,"Drunkard's Dataset; TartanAir; TUM monoVO; MineNav; Event-Camera Dataset; Virtual KITTI; EuRoC MAV; EndoSLAM; Multi-Spectral Stereo Dataset  (RGB, NIR, thermal images, LiDAR, GPS/IMU); ConsInv Dataset",EuRoC MAV,Relative Position Error Translation [cm],Monocular Visual Odometry; Face Anti-Spoofing,Computer Vision; Robots,"**Visual Odometry** is an important area of information fusion in which the central aim is to estimate the pose of a robot using data collected by visual sensors.   <span class=""description-source"">So..."
Intent Detection,26,ProSLU; BANKING77; HINT3; Persian-ATIS; MDID; diaforge-utc-r-0725; CLINC-Single-Domain-OOS; DialogUSR; SNIPS; HWU64,ProSLU; BANKING77; SNIPS; HWU64; MixATIS; BANKING77 5-shot; BANKING77 10-shot; HWU64 10-shot; CLINC150; CLINC150 10-shot,F1; Accuracy; Acc; f1 macro; Accuarcy; Accuracy (%); Intent Accuracy,Open Intent Detection,Natural Language Processing,**Intent Detection** is a task of determining the underlying purpose or goal behind a user's search query given a context. The task plays a significant role in search and recommendations.   A traditio...
DeepFake Detection,26,GANGen-Detection; DeeperForensics-1.0; FaceForensics; WildDeepfake; GOTCHA; DeepFake MNIST+; TwinSynths; 1; DEEP-VOICE: DeepFake Voice Recognition; LAV-DF,FaceForensics++; FaceForensics; ^(#$!@#$)(()))******; DFDC; CIFAKE: Real and AI-Generated Synthetic Images; DFFD; FakeAVCeleb; วีเค..!! ชมคลิป ‘ไอซ์ ปรีชญา’ ลืมปิดถ่ายทอดสดขณะอาบน้ำ โด; 1; COCOFake,ROC AUC; DF; Accuracy; 0-shot MRR; AUC; FS; LogLoss; AP; Real; FSF,Synthetic Speech Detection; diffusion-generated faces detection; Audio Deepfake Detection; Human Detection of Deepfakes; Multimodal Forgery Detection,Miscellaneous; Computer Vision; Audio; Speech,**DeepFake Detection** is the task of detecting fake videos or images that have been generated using deep learning techniques. Deepfakes are created by using machine learning algorithms to manipulate ...
Music Information Retrieval,26,FMA; URMP; YouTube-100M; GoodSounds; GiantMIDI-Piano; Lakh Pianoroll Dataset; RWC; MTG-Jamendo; MSSD; Fingerprint Dataset,N/A,N/A,N/A,Music,N/A
Video Prediction,25,Shanghai2020; Something-Something V2; MPI Sintel; Cityscapes; Vimeo90K; Kinetics; EarthNet2021; QST; Moving MNIST; SynPick,"CMU Mocap-1; KTH 64x64 cond10 pred30; Something-Something V2; MPI Sintel; Kinetics-600 12 frames, 64x64; Cityscapes; Vimeo90K; Cityscapes 128x128; Moving MNIST; YouTube-8M",Cond; Pred; IS; LPIPS; Test Error; ST-RRED; MS-SSIM; FVD; MAE; MSE,Predict Future Video Frames; Earth Surface Forecasting,Computer Vision; Time Series,Script for Amee Marketing & Trading Company Short Video    *(Duration: 45-60 seconds)*      ---    Opening Scene (0:00-0:05):    - *Visual:* Close-up of fresh organic grains spilling gently into a woo...
Text-to-Image Generation,25,Flickr-8k; TextAtlasEval; Pick-a-Pic; T2I-CompBench; Oxford 102 Flower; Multi-Modal CelebA-HQ; ENTIGEN; GenEval; HRS-Bench; LHQ,DPG; MS-COCO; GeNeVA (i-CLEVR); DrawBench; Colors; Flickr-8k; GeNeVA (CoDraw); COCO; GenEval; CUB,Color; Single Obj.; Shape; FID-8; Counting; Validation Accuracy; Texture; F1-score; Overall; Spatial,DreamBooth Personalized Generation; Text-based Image Editing; Zero-Shot Text-to-Image Generation; text-guided-image-editing; Conditional Text-to-Image Synthesis,Computer Vision; Natural Language Processing,"The development of the brain's blood supply in an embryo involves a complex process with several stages. Initially, there is a network of connections between the carotid and basilar artery systems tha..."
Few-Shot Image Classification,24,mini-Imagenet; tieredImageNet; Meta-Dataset; MineralImage5k; Bongard-HOI; FC100; SUN; Oxford 102 Flower; Stanford Dogs; FewSOL,"ImageNet-FS (1-shot, novel); Stanford Cars 5-way (5-shot); Fewshot-CIFAR100 - 1-Shot Learning; ImageNet-FS (10-shot, all); Oxford 102 Flower; ImageNet - 5-shot; mini-ImageNet - 100-Way; CUB 200 50-way (0-shot); ImageNet-FS (5-shot, all); Mini-Imagenet 5-way (5-shot)",Top-5 Accuracy; Accuracy; Top-1 Accuracy; Mean Rank; AP50; Avg. Accuracy; ACCURACY; 1:1 Accuracy; Top 1 Accuracy; Frame accuracy,Unsupervised Few-Shot Learning; Unsupervised Few-Shot Image Classification; Generalized Few-Shot Classification,Computer Vision,**Few-Shot Image Classification** is a computer vision task that involves training machine learning models to classify images into predefined categories using only a few labeled examples of each categ...
Face Verification,24,IJB-S; LFW; Oulu-CASIA; MS-Celeb-1M; RFW; CK+; CPLFW; CALFW; CASIA-WebFace; BTS3.1,IJB-S; LFW; Oulu-CASIA; Oulu-CASIA NIR-VIS; CASIA NIR-VIS 2.0; CK+; CPLFW; CALFW; YouTube Faces DB; BTS3.1,TAR @ FAR=1e-5; TAR @ FAR=1e-2; TAR @ FAR=1e-6; Rank-1; Accuracy; Rank-1 (Video2Single); model; TAR @ FAR=1e-3; TAR @ FAR=0.001; FRR@FAR(%),Disguised Face Verification,Computer Vision,**Face Verification** is a machine learning task in computer vision that involves determining whether two facial images belong to the same person or not. The task involves extracting features from the...
Out-of-Distribution Detection,24,iSUN; 20 Newsgroups; STL-10; ImageNet-1k vs NINCO; Pano3D; ImageNet-1k vs OpenImage-O; Places365; ImageNet-1k vs Places; ImageNet-1k vs iNaturalist; OpenImage-O,Far-OOD; CIFAR-10 vs iSUN; MS-1M vs. IJB-C; 20 Newsgroups; CIFAR-100 vs ImageNet (C); SVHN vs Uniform; CIFAR-100 vs ImageNet (R); SVHN vs iSUN; CIFAR-10 vs ImageNet (C); STL-10,"Percentage correct; ID ACC; FPR95; FPR@95; AUPR; Latency, ms; AP; AUROC",N/A,Computer Vision,Detect out-of-distribution or anomalous examples.
Cross-Modal Retrieval,24,Flickr-8k; NUS-WIDE; MSCOCO; CTC; SoundingEarth; IAPR TC-12; Song Describer Dataset; CUHK-PEDES; TaxaBench-8k; RSICD,Flickr30k; Flickr-8k; Recipe1M+; SoundingEarth; Recipe1M; MSCOCO-1k; ChEBI-20; CUHK-PEDES; MSCOCO; COCO 2014,text-to-image R@1; Test MRR; Hits@10; Text-to-image Medr; Text-to-image R@5; Hits@1; Image-to-sound R@100; Mean Rank; Text-to-image R@1; Sound-to-image R@100,multilingual cross-modal retrieval; Zero-shot Composed Person Retrieval; Image-text matching; Cross-Modal Retrieval on RSITMD; Cross-modal retrieval with noisy correspondence,Computer Vision; Natural Language Processing,"**Cross-Modal Retrieval (CMR)** is a task of retrieving items across different modalities, such as image, text, video, and audio. The core challenge of CMR is the *heterogeneity gap*, which arises bec..."
Video Generation,24,ChronoMagic-Pro; DropletVideo-10M; Kinetics-700; LAION-400M; OpenS2V-5M; Open-HypermotionX; OpenS2V-Eval; Kinetics; UCF101; QST,"UCF-101 16 frames, Unconditional, Single GPU; UCF-101; Taichi; Sky Time-lapse; BAIR Robot Pushing; YouTube Driving; Kinetics-600 12 frames, 128x128; Kinetics-700; UCF-101 16 frames, 128x128, Unconditional; TrailerFaces",Cond; Pred; FID; FVD128; LPIPS; FVD 16; FVD score; FVD; Inception score; CLIP R-Precision,Image to Video Generation; Unconditional Video Generation,Computer Vision; Natural Language Processing,"<span style=""color:grey; opacity: 0.6"">( Various Video Generation Tasks.  Gif credit: [MaGViT](https://paperswithcode.com/paper/magvit-masked-generative-video-transformer) )</span>"
Speech Enhancement,24,TIMIT; LibriMix; GRID Dataset; RealMAN; WHAM!; WHAMR_ext; L3DAS21; DNS Challenge; EARS-WHAM; EasyCom,WHAMR!; WSJ0 + DEMAND + RNNoise; VoiceBank+DEMAND; TCD-TIMIT corpus (mixed-speech); LibriSpeechDuplicate; DEMAND; Deep Noise Suppression (DNS) Challenge; EARS-WHAM; GRID corpus (mixed-speech); CHiME-3,ViSQOL; SIGMOS; SIIB; POLQA; PESQ-WB; FLOPS (G); COVL; DNSMOS SIG; SNR; CBAK,Bandwidth Extension; Speech Intelligibility Evaluation; Packet Loss Concealment; Speech Dereverberation,Audio; Speech,**Speech Enhancement** is a signal processing task that involves improving the quality of speech signals captured under noisy or degraded conditions. The goal of speech enhancement is to make speech s...
Crowd Counting,23,TRANCOS; SmartCity; Cross-View Cross-Scene Multi-View Crowd Counting Dataset; ShanghaiTech; DroneRGBT; SCUT-HEAD; Crowd in a rally | Crowd Counting | Crowd Human; JHU-CROWD; CityUHK-X-BEV; FDST,N/A,N/A,N/A,N/A,N/A
Human-Object Interaction Detection,23,HICO; BEHAVE; Ambiguous-HOI; RICH; FineGym; HAKE; COUCH; EgoISM-HOI; CHAIRS dataset; DIO,V-COCO; VidHOI; HICO; HICO-DET; MECCANO; Ambiguious-HOI,Time Per Frame(ms); AP(S1); Oracle: Full (mAP@0.5); Oracle: Rare (mAP@0.5); Oracle: Non-Rare (mAP@0.5); Detection: Non-Rare (mAP@0.5); mAP@0.5 role; MAP; Detection: Full (mAP@0.5); Time Per Frame (ms),Affordance Recognition; Hand-Object Interaction Detection,Computer Vision,"Human-Object Interaction (HOI) detection is a task of identifying ""a set of interactions"" in an image, which involves the i) localization of the subject (i.e., humans) and target (i.e., objects) of in..."
Denoising,23,CommitBART; NIND; SIDD; PointDenoisingBenchmark; S2TLD; Fingerprint inpainting and denoising; PINet; Darmstadt Noise Dataset; DIV2K; PixelShift200,CBSD68 sigm75; Darmstadt Noise Dataset; AAPM; DIV2K; iris; DND,Average; PSNR/SSIM; Average PSNR (dB); SSIM (sRGB); SSIM; PSNR; Average PSNR,Salt-And-Pepper Noise Removal; Color Image Denoising; 3D Mesh Denoising; Grayscale Image Denoising; Sar Image Despeckling,Computer Vision,"**Denoising** is a task in image processing and computer vision that aims to remove or reduce noise from an image. Noise can be introduced into an image due to various reasons, such as camera sensor l..."
Low-Light Image Enhancement,23,LOL-v2-synthetic; Sony-Total-Dark; DICM; MEF; LOL-v2; LOL; ExDark; Canon RAW Low Light; LLVIP; SID,N/A,N/A,N/A,N/A,N/A
6D Pose Estimation,23,Drunkard's Dataset; INSANE Cross-Domain UAV Data Set; YCB-Ev 1.1; YCB-Video; NERDS 360; REAL275; HouseCat6D; SMOT; GraspClutter6D; ConSLAM,DTTD-Mobile; LineMOD; YCB-Video; 3D-BSLS-6D; OPT; ApolloCar3D,AR pCH; eTE; Mean ADD-S; A3DP; ADD-S AUC; AUC; Accuracy (ADD); ADDS AUC; eRE; AR CoU,Robot Pose Estimation; hand-object pose,Computer Vision,Image: [Zeng et al](https://arxiv.org/pdf/1609.09475v3.pdf)
Hand Pose Estimation,23,Surgical Hands; 3DPW; RHD; EgoDexter; InterHand2.6M; ThermoHands; BigHand2.2M Benchmark; 3D Hand Pose; HandNet; ContactArt,COCO-WholeBody; Custom FINNgers; HANDS 2019; K2HPD; 3DPW; MSRA Hands; ICVL; HANDS 2017; NYU Hands; ICVL Hands,PDJ@5mm; keypoint AP; 1:1 Accuracy; MPJPE; FPS; Average 3D Error; Error (mm),3D Hand Pose Estimation,Computer Vision; Graphs,"Hand pose estimation is the task of finding the joints of the hand from an image or set of video frames.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Pose-REN](https://github.com/xinghao..."
Scene Classification,23,SEN12MS; RESISC45; MTG-Jamendo; Million-AID; Places365; RSICD; RS_NS92; AIDER; BigEarthNet; TAU Urban Acoustic Scenes 2019,UC Merced Land Use Dataset; Places365-Standard,Top 5 Error; Accuracy (%); Top 1 Error,N/A,Computer Vision,"**Scene Classification** is a task in which scenes from photographs are categorically classified. Unlike object classification, which focuses on classifying prominent objects in the foreground, Scene ..."
Automatic Speech Recognition,22,MultiMed; Common Voice; BERSt; Multilingual LibriSpeech; FLEURS; LibriSpeech; GigaSpeech; Video Dataset; LJSpeech; MNIST,N/A,N/A,N/A,N/A,N/A
Speech Emotion Recognition,22,MSP-Podcast; nEMO; BERSt; RESD; IEMOCAP; AESI; CREMA-D; VNEMOS; Quechua-SER; JTES,LSSED; Quechua-SER; MSP-Podcast (Dominance); MNIST; Dusha Crowd; ShEMO; BERSt; RESD; Dusha Podcast; IEMOCAP,Unweighted Accuracy (UA); F1; Weighted F1; CCC (Arousal); UA CV; WA; Accuracy; F1 Score; CCC (Valence); UA,Cultural Vocal Bursts Intensity Prediction; Vocal Bursts Valence Prediction; Vocal Bursts Intensity Prediction; Vocal Bursts Type Prediction,Speech,**Speech Emotion Recognition** is a task of speech processing and computational paralinguistics that aims to recognize and categorize the emotions expressed in spoken language. The goal is to determin...
Video Classification,22,Breakfast; Bukva; AirLetters; Something-Something V2; Multimodal PISA; Trailers12k; Crowd 11; Kinetics; Video Dataset; COIN,YouTube-8M; Home Action Genome; Kinetics; Hockey Fight Detection Dataset; Breakfast; COIN; MoB; Charades; Something-Something V2; Multimodal PISA,Global Average Precision; Top-1; Accuracy; Hit@1; AUPR; Top-5 Accuracy; PERR; 1:1 Accuracy; Hit@5; mAP,Multi Class Classification (Four-level Video Classification); Student Engagement Level Detection (Four Class Video Classification),Computer Vision,"**Video Classification** is the task of producing a label that is relevant to the video given its frames. A good video level classifier is one that not only provides accurate frame labels, but also be..."
Image Denoising,22,NIND; SEN12MS-CR-TS; FFHQ; SIDD; Fingerprint inpainting and denoising; Nam; SID; Phase 1 dataset; FMD; SEN12MS-CR,SID x300; Urban100 sigma25; FFHQ; SIDD; SID SonyA7S2 x250; FFHQ 64x64 - 4x upscaling; SID SonyA7S2 x100; Nam; ELD SonyA7S2 x100; SID SonyA7S2 x300,LPIPS; SSIM (Raw); PSNR (Raw); ODRMSE; SSIM (sRGB); PSNR (sRGB); SSIM; PSNR; Average PSNR,intensity image denoising; lifetime image denoising,Computer Vision; Medical,"**Image Denoising** is a computer vision task that involves removing noise from an image. Noise can be introduced into an image during acquisition or processing, and can reduce image quality and make ..."
Cell Segmentation,22,uBench; LIVECell; ACCT Data Repository; CoNSeP; Fluo-N2DH-SIM+; YIM Dataset; PanNuke; Fluo-C3DH-A549-SIM; MoNuSeg; Fluo-N2DH-GOWT1,Fluo-N2DL-HeLa; DIC-C2DH-HeLa; LIVECell; CoNSeP; Fluo-C3DL-MDA231; Fluo-N2DH-SIM+; EVICAN; PanNuke; STARE; MoNuSeg,mask AFNR; LIVECell Transferability; AUC; Average Dice; mask AP; LIVECell Extrapolation (A172); mask AP50; LIVECell Extrapolation (A549); SEG (~Mean IoU),Nuclei Segmentation and Classfication,Computer Vision,"**Cell Segmentation** is a task of splitting a microscopic image domain into segments, which represent individual instances of cells. It is a fundamental step in many biomedical studies, and it is reg..."
Speech Synthesis,22,TaL Corpus; Tilde MODEL Corpus; CSS10; RUSLAN; TTSDS Synthetic Speech; PromptSpeech; JIT Dataset; LJSpeech; TTS-Portuguese Corpus; LibriTTS,Mandarin Chinese; LJSpeech; LibriTTS; North American English; Blizzard Challenge 2013,M-STFT; Mean Opinion Score; MCD; PESQ; NLL; V/UV F1; Periodicity,Expressive Speech Synthesis; Speech Synthesis - Bodo; text-to-speech translation; Emotional Speech Synthesis; Speech Synthesis - Hindi,Audio; Speech,"Speech synthesis is the task of generating speech from some other modality like text, lip movements etc.     Please note that the leaderboards here are not really comparable between studies - as they ..."
Super-Resolution,22,PINet; TESTIMAGES; INRIA DLFD; DAVANet; PixelShift200; PROBA-V; SR-CACO-2; CATS; RealSRSet; MuS2,hradis et al dataset,SSIM; Average PSNR,3D Object Super-Resolution; Video Super-Resolution; Reference-based Video Super-Resolution; Image Super-Resolution; Reference-based Super-Resolution,Computer Vision; Graphs,**Super-Resolution** is a task in computer vision that involves increasing the resolution of an image or video by generating missing high-frequency details from low-resolution input. The goal is to pr...
Handwriting Recognition,22,READ 2016; HKR; Ricordi; Bentham; Patzig; Saint Gall; BRUSH; MatriVasha:; Konzil; Burmese Handwritten Digit Dataset (BHDD),BanglaLekha Isolated Dataset; KOHTD; An extensive dataset of handwritten central Kurdish isolated characters,Epochs; Accuracy; 1:1 Accuracy; Cross Entropy Loss; CER,Handwritten Line Segmentation; Handwritten Word Segmentation,Computer Vision,Image source: [Handwriting Recognition of Historical Documents with few labeled data](https://arxiv.org/pdf/1811.07768v1.pdf)
Molecular Property Prediction,21,ESOL (Estimated SOLubility); HIV (Human Immunodeficiency Virus); SIDER; Photoswitch; QM7; MUV; QM8; MD22; PubChem18; BBBP (Blood-Brain Barrier Penetration),ToxCast; Clearance; SIDER; BACE; QM7; MUV; HIV; QM8; MoleculeNet; PCBA,Molecules (M); AUC; MAE; RMSE; R2; ROC-AUC,Odor Descriptor Prediction; 3D Geometry Prediction; mixture property prediction; NMR J-coupling,Miscellaneous; Methodology; Graphs; Medical,Molecular property prediction is the task of predicting the properties of a molecule from its structure.
Simultaneous Localization and Mapping,21,Drunkard's Dataset; TUM monoVO; Virtual KITTI; S3E; Bonn RGB-D Dynamic; Robust e-NeRF Synthetic Event Dataset; ConSLAM; KAIST VIO Dataset; Endomapper; TorWIC,N/A,N/A,Object SLAM; Semantic SLAM,Computer Vision,Simultaneous localization and mapping (SLAM) is the task of constructing or updating a map of an unknown environment while simultaneously keeping track of an agent's location within it.    <span style...
Medical Diagnosis,21,REFLACX; BreastDICOM4; EBHI-Seg; MedConceptsQA; IntrA; PadChest; BreastRates4; FracAtlas; Sakha-TB; LIMUC,BreastDICOM4; Clinical Admission Notes from MIMIC-III,AUROC; Average Precision; Average Recall,CBC TEST; Retinal OCT Disease Classification; Blood Cell Count; Alzheimer's Disease Detection; Thoracic Disease Classification,Computer Vision; Medical,"**Medical Diagnosis** is the process of identifying the disease a patient is affected by, based on the assessment of specific risk factors, signs, symptoms and results of exams.      <span class=""desc..."
Sound Event Detection,20,WildDESED; TAU-NIGENS Spatial Sound Events 2020; TUT Sound Events 2017; L3DAS21; VOICe; DCASE 2017; FSDnoisy18k; TUT-SED Synthetic 2016; DESED; BIOSED-ACPD,WildDESED; Mivia Audio Events; L3DAS21; DESED; Mivia Road Events,SED-score; PSDS2; PSDS1 (Clean); Rank-1 Recognition Rate; event-based F1 score; F-Score; Error Rate; PSDS1 (10dB); PSDS1; PSDS1 (0dB),N/A,Audio,"**Sound Event Detection** (SED) is the task of recognizing the sound events and their respective temporal start and end time in a recording. Sound events in real life do not always occur in isolation,..."
Traffic Prediction,20,LargeST; PeMS08; Q-Traffic; EXPY-TKY; Equilibrium-Traffic-Networks; CTV-Dataset; NYCBike2; PeMSD4; METR-LA; Beijing Traffic,"HZME(outflow); PeMSD8 (10 days' training data, 30min); PeMSD4 (10 days' training data, 15min); LargeST; PeMS08; PeMSD7(L); PeMSD4 (10 days' training data, 60min); Q-Traffic; PeMSD7 (10 days' training data, 30min); EXPY-TKY",MAPE; 6 step MAE; Parameters(K); MAPE (%) @ in; MAE @ 30min; MAE @ 60min; 1 step MAE; SD MAE; MAPE (%) @ out; MAE @ 45min,Traffic Data Imputation,Time Series,"**Traffic Prediction** is a task that involves forecasting traffic conditions, such as the volume of vehicles and travel time, in a specific area or along a particular road. This task is important for..."
Aspect-Based Sentiment Analysis (ABSA),20,Laptop-ACOS; Pars-ABSA; ACOS; DiaASQ; YASO; COVID19-CountryImage; AWARE; UIT-ViSFD; CAIL2019-SCM; SemEval-2014 Task-4,SemEval-2014 Task-4; FABSA; Lap14; Sentihood; ASTE; SemEval 2014 Task 4 Sub Task 1; ASQP;  SemEval 2015 Task 12; Rest14; MAMS,Restaurant (Acc); F1 (Laptop); Restaurant (F1); Aspect; Laptop (F1); Sentiment; F1 (R15); Accuracy (4-way); F1 (Restaurant); Acc,Aspect Category Sentiment Analysis; Aspect Category Sentiment Classification; Aspect-oriented  Opinion Extraction; Extract Aspect; Aspect-Sentiment-Opinion Triplet Extraction,Natural Language Processing,**Aspect-Based Sentiment Analysis (ABSA)** is a Natural Language Processing task that aims to identify and extract the sentiment of specific aspects or components of a product or service. ABSA typical...
Named Entity Recognition,20,MasakhaNER; Mini HAREM; Few-NERD; GUM; OntoNotes 5.0; First HAREM; CoNLL-2012; LatamXIX; NuNER; ARF,N/A,N/A,N/A,N/A,N/A
Change Detection,20,urban_change_monitoring_mariupol_ua; ChangeSim; Changen2-S1-15k; Changen2-S9-27k; CLCD; ChaBuD; BRIGHT; DSIFN-CD; WHU Building Dataset; OSCD,LEVIR-CD; S2Looking; DSIFN-CD; WHU-CD; OSCD - 3ch; EGY-BCD; LEVIR+; SECOND; SYSU-CD; ChangeSim,F1; F1-score; IoU; Fscd; Overall Accuracy; KC; Category mIoU; Recall; Params(M); Prcision,Semi-supervised Change Detection,Computer Vision,"**Change Detection** is a computer vision task that involves detecting changes in an image or video sequence over time. The goal is to identify areas in the image or video that have undergone changes,..."
Stereo Matching,20,Virtual KITTI; UASOL; Middlebury 2001; Middlebury 2006; IMCPT-SparseGM-100; PST900; Middlebury 2005; ETH3D; IRS; VBR,N/A,N/A,N/A,Computer Vision,"**Stereo Matching** is one of the core technologies in computer vision, which recovers 3D structures of real world from 2D images. It has been widely used in areas such as autonomous driving, augmente..."
3D Hand Pose Estimation,20,ExPose; HO-3D v2; AGORA; EgoDexter; InterHand2.6M; ThermoHands; BigHand2.2M Benchmark; 3D Hand Pose; H2O  (2 Hands and Objects); FreiHAND,DexYCB; HO-3D v3; HInt: Hand Interactions in the wild; HO-3D v2; FreiHAND; InterHand2.6M; H3WB,PCK@0.05 (Ego4D) Occ; PCK@0.05 (VISOR) Occ; AUC_J; VAUC; F@15mm; PCK@0.05 (Ego4D) All; PA-F@5mm; PA-VAUC; Procrustes-Aligned MPJPE; PA-MPJPE,3D Canonical Hand Pose Estimation; hand-object pose; Grasp Generation,Computer Vision,Image: [Zimmerman et l](https://arxiv.xsrg/pdf/1705.01389v3.pdf)
Image Manipulation Detection,20,CASIA (OSN-transmitted - Weibo); NIST (OSN-transmitted - Facebook); COVERAGE; Digital Forensics 2023 dataset - DF2023; DSO (OSN-transmitted - Facebook); DIS100k; Columbia (OSN-transmitted - Whatsapp); NIST (OSN-transmitted - Wechat); NIST (OSN-transmitted - Weibo); CASIA (OSN-transmitted - Whatsapp),CASIA (OSN-transmitted - Weibo); NIST (OSN-transmitted - Facebook); COVERAGE; DSO (OSN-transmitted - Facebook); Columbia (OSN-transmitted - Whatsapp); NIST (OSN-transmitted - Wechat); NIST (OSN-transmitted - Weibo); CASIA (OSN-transmitted - Whatsapp); DSO (OSN-transmitted - Weibo); DSO-1,F-score; Intersection over Union; AUC; f-Score; Balanced Accuracy,N/A,Computer Vision,"The task of detecting images or image parts that have been tampered or manipulated (sometimes also referred to as doctored).  This typically encompasses image splicing, copy-move, or image inpainting."
Semantic Textual Similarity,19,GLUE; CARER; SemEval-2014 Task-10; HumanEval; SICK; MRPC; KorNLI; JGLUE; STS Benchmark; PIT,MRPC; MTEB; SICK-R; STS15; STS12; SentEval; STS16; STS Benchmark; STS14; MRPC Dev,F1; Spearman Correlation; MRPC; avg ± std; SICK-R; Accuracy; SICK-E; Dev Pearson Correlation; Pearson Correlation; Dev Spearman Correlation,Paraphrase Identification; Cross-Lingual Semantic Textual Similarity,Natural Language Processing,Semantic textual similarity deals with determining how similar two pieces of texts are.  This can take the form of assigning a score from 1 to 5. Related tasks are paraphrase or duplicate identificati...
Medical Image Classification,19,Galaxy Zoo DECaLS; LymphoMNIST; Heel Dataset; NCT-CRC-HE-100K; CheXphoto; Malaria Dataset; OASIS; NIH-CXR-LT; Sakha-TB; LIMUC,COVIDGR; IDRiD; ImageNet; OASIS 3; CheXphoto; PCOS Classification; ISIC 2020 Challenge Dataset; NCT-CRC-HE-100K; Malaria Dataset; Galaxy10 DECals,Mean AUC; Accuracy; GFLOPs; AUC; Top 1 Accuracy; Accuracy (% ); 1:1 Accuracy; Specificity; Sensitivity; Top-1 Accuracy (%),Semi-supervised Medical Image Classification,Medical,"**Medical Image Classification** is a task in medical image analysis that involves classifying medical images, such as X-rays, MRI scans, and CT scans, into different categories based on the type of i..."
Video Object Segmentation,19,ODMS; VISOR - Semi supervised video object segmentation; VOST; YouTube-VOS 2018; MOSE; SegTrack-v2; Referring Expressions for DAVIS 2016 & 2017; Infinity Spills Basic Dataset; BL30K; PUMaVOS,SegTrack-v2; FBMS; YouTube-VOS 2019; YouTube; DAVIS 2017 (val); DAVIS 2017; DAVIS 2016; DAVIS 2017 (test-dev); DAVIS-2017 (test-dev); M$^3$-VOS,Average; Mean Jaccard & F-Measure; F-Measure (Unseen); Jaccard (Seen); F-Score; Jaccard (Mean); J&F; F-measure; Jaccard (Unseen); mIoU,Referring Video Object Segmentation; Video Salient Object Detection; Semi-Supervised Video Object Segmentation; Video Shadow Detection; Long-tail Video Object Segmentation,Computer Vision,Video object segmentation is a binary labeling problem aiming to separate foreground object(s) from the background region of a video.    For leaderboards please refer to the different subtasks.
Visual Navigation,19,MINOS; House3D Environment; UAV Multiview Navigation; HELP; IQUAD; R2R; HM3DSem; image-goal-nav-dataset; MineRL; AVD,"Dmlab-30; Help, Anna! (HANNA); Cooperative Vision-and-Dialogue Navigation; SOON Test; R2R; AI2-THOR",Medium Human-Normalized Score; SR; dist_to_end_reduction; spl; Success Rate (All); Nav-SPL; SPL (L≥5); SPL (All); Success Rate (L≥5),ObjectGoal Navigation,Computer Vision; Robots,"**Visual Navigation** is the problem of navigating an agent, e.g. a mobile robot, in an environment using camera input only. The agent is given a target image (an image it will see from the target pos..."
Animal Pose Estimation,19,ATRW; MacaquePose; AP-10K; StanfordExtra; Marmoset-8K; SuperAnimal-Quadruped; TriMouse-161; Animal Kingdom; Desert Locust; MBW - Zoo Dataset,Horse-10; Fish-100; Animal-Pose Dataset; AP-10K; StanfordExtra; Marmoset-8K; Animal3D; TriMouse-161,PCK@0.1; Normalized Error (OOD); PA-MPJPE; PCK@0.3 (OOD); AP; mAP,N/A,Computer Vision,"Animal pose estimation is the task of identifying the pose of an animal.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Using DeepLabCut for 3D markerless pose estimation across species an..."
Robot Navigation,19,Sparrow; MLGESTURE DATASET; FPDS; SESIV; Ruralscapes; Habitat Platform; Pose Estimation Lunar Robot; Hyper Drive; PInNED; TAO,Habitat 2020 Point Nav minival; Habitat 2020 Object Nav test-std; Habitat 2020 Object Nav minival; Habitat 2020 Point Nav test-std,SPL; SUCCESS; SOFT_SPL; DISTANCE_TO_GOAL,Sequential Place Learning; Social Navigation; PointGoal Navigation; ObjectGoal Navigation; VNLA,Computer Vision; Robots,The fundamental objective of mobile **Robot Navigation** is to arrive at a goal position without collision. The mobile robot is supposed to be aware of obstacles and move freely in different working s...
Motion Synthesis,19,DD100; KIT Motion-Language; HumanAct12; AIST++; BRACE; HumanML3D; InterHuman; Trinity Speech-Gesture Dataset; LaFAN1; Motion-X,FineDance; BRACE; HumanML3D; InterHuman; AIOZ-GDANCE; KIT Motion-Language; Trinity Speech-Gesture Dataset; Inter-X; LaFAN1; HumanAct12,L2Q@15; Mean Opinion Score; L2P@5; MModality; Frechet Inception Distance; NPSS@15; L2Q@5; Toprock average; Diversity; GMR,Motion Style Transfer; motion in-betweening; Temporal Human Motion Composition,Computer Vision; Computer Code,"Creating a video where people in the images move (such as blinking or smiling) requires specialized AI technology like Deepfake or Motion Synthesis, which I cannot do directly here.      However, if y..."
Transductive Zero-Shot Classification,18,EuroSAT; Food-101; PatternNet; RESISC45; ImageNet; UCF101; Stanford Cars; Oxford-IIIT Pets; DTD; SUN397,N/A,N/A,N/A,N/A,N/A
Object Localization,18,Mall; Infinity Spills Basic Dataset; FracAtlas; Spatial Commonsense Graph Dataset; KITTI; VGG Cell; SIXray; HDD; GRIT; IllusionVQA,Mall; KITTI Pedestrians Moderate; Plant; KITTI Pedestrians Easy; KITTI Cars Hard; PASCAL VOC 2012; KITTI Pedestrian Easy; Pupil; IllusionVQA; KITTI Cars Easy,Accuracy; F-Score; Nav-SPL; AP; Recall; Nav-Length; Nav-Succ; Nav-OSucc; Localization (ablation); Precision,Weakly-Supervised Object Localization; Image-Based Localization; Active Object Localization; Monocular 3D Object Localization; Unsupervised Object Localization,Computer Vision,"**Object Localization** is the task of locating an instance of a particular object category in an image, typically by specifying a tightly cropped bounding box centered on the instance. An object prop..."
Multiple Object Tracking,18,KITTI MOTS; RADIATE; CFC; KITTI; LPW; PersonPath22; Waymo Open Dataset; SportsMOT; HiEve; YouTube-Hands,RADIATE; BDD100K val; KITTI Test (Offline Methods); SportsMOT; Waymo Open Dataset; YouTube-Hands; BDD100K test; UA-DETRAC; GMOT-40; CroHD,IDF1; IDs; mIDF1; ML; mMOTA; AssocA; AssA; MOTA; mHOTA; mAP,Multiple Object Track and Segmentation; Multiple Object Tracking with Transformer,Computer Vision,"**Multiple Object Tracking** is the problem of automatically identifying multiple objects in a video and representing them as a set of trajectories with high accuracy.   <span class=""description-sourc..."
Gaze Estimation,18,RT-GENE; MPIIGaze; LISA Gaze Dataset; UCO-LAEO; GazeCapture; EYEDIAP; EyeInfo; OpenEDS; Gaze360; GAFA,RT-GENE; MPIIGaze; UT Multi-view; EYEDIAP; Gaze360; MPII Gaze; ETH-XGaze; EYEDIAP (screen target); GazeCapture; EYEDIAP (floating target),Euclidean Mean Error (EME); FPS; Angular Error; Mean Angle Error,N/A,Computer Vision,**Gaze Estimation** is a task to predict where a person is looking at given the person’s full face. The task contains two directions: 3-D gaze vector and 2-D gaze position estimation. 3-D gaze vector ...
Action Segmentation,18,SICS-155; Breakfast; JIGSAWS; 50 Salads; EgoExoLearn; COIN; HOI4D; LARa; UW IOM; EgoProceL,JIGSAWS; Breakfast; 50 Salads; COIN; MPII Cooking 2 Dataset; 50Salads; Assembly101; GTEA; Youtube INRIA Instructional,F1; Accuracy; F1@50%; Average F1; F1@25%; F1@25; Acc; Edit Distance; MoF; Edit,Skeleton Based Action Segmentation; Weakly Supervised Action Segmentation (Action Set)); Unsupervised Action Segmentation; Weakly Supervised Action Segmentation (Transcript),Computer Vision,"**Action Segmentation** is a challenging problem in high-level video understanding. In its simplest form, Action Segmentation aims to segment a temporally untrimmed video by time and label each segmen..."
3D Instance Segmentation,18,MultiScan; FOR-instance; KITTI-360; LoTE-Animal; STPLS3D; CREMI; S3DIS; InfiniteRep; ScanNet200; ScanNet++,STPLS3D; S3DIS; ScanNet++; ScanNet200; MitoEM; SceneNN; PartNet; ScanNet; ScanNet(v2),AP75-H-Val; AP50; mAcc; mAP; mCov; mAP50; AP75-H-Test; mWCov; mAP@50; mAP @ 50,Interactive 3D Instance Segmentation,Computer Vision,Image: [OccuSeg](https://arxiv.org/pdf/2003.06537v3.pdf)
Text-To-Speech Synthesis,18,EMOVIE; RyanSpeech; IMaSC; SOMOS; HUI speech corpus; Trinity Speech-Gesture Dataset; GneutralSpeech Male; KazakhTTS; LJSpeech; AISHELL-3,CMUDict 0.7b; HUI speech corpus; Trinity Speech-Gesture Dataset; LJSpeech; 20000 utterances; Thorsten voice 21.02 neutral,MOS; Word Error Rate (WER); Audio Quality MOS; Mean Opinion Score; Pleasantness MOS; WER (%); 10-keyword Speech Commands dataset; Phoneme Error Rate,Zero-Shot Multi-Speaker TTS; Prosody Prediction,Natural Language Processing; Audio; Speech,**Text-To-Speech Synthesis** is a machine learning task that involves converting written text into spoken words. The goal is to generate synthetic speech that sounds natural and resembles human speech...
Zero-Shot Video Question Answer,18,MSVD-QA; TVQA; IntentQA; ActivityNet-QA; Shot2Story20K; NExT-GQA; Neptune; TVQA+; CinePile: A Long Video Question Answering Dataset and Benchmark; MSRVTT-QA,N/A,N/A,N/A,N/A,N/A
Image Restoration,18,TinyPerson; PIRM; CDD-11; Real Rain Dataset; CBSD68; SIDD; HAC; DocUNet; UHDM; HRI,UHDM; ^(#$!@#$)(()))******; CDD-11,PSNR; SSIM; 0L; Average PSNR (dB),Underwater Image Restoration; Flare Removal; JPEG Artifact Removal; JPEG Artifact Correction; Unified Image Restoration,Computer Vision,"**Image Restoration** is a family of inverse problems for obtaining a high quality image from a corrupted input image. Corruption may occur due to the image-capture process (e.g., noise, lens blur), p..."
Image Enhancement,18,ExDark; SQUID; LLVIP; PolarRR; UIEB; PolyU; TIP 2018; Exposure-Errors; CUHK Image Cropping; MIT-Adobe FiveK,MIT-Adobe 5k; TIP 2018; Exposure-Errors; MIT-Adobe FiveK; PSNR; SICE-Mix; SICE-Grad,SSIM on proRGB; DeltaE; LPIPS; FSIM; PSNR on sRGB; SSIM on sRGB; SSIM; PSNR; PSNR on proRGB; Average PSNR,wavelet structure similarity loss; Color Manipulation; Vignetting Removal; Local Color Enhancement; Film Simulation,Computer Vision,**Image Enhancement** is basically improving the interpretability or perception of information in images for human viewers and providing ‘better’ input for other automated image processing techniques....
Semantic Similarity,17,COS960; BIOSSES; SpokenSTS; LatamXIX; Spanish Corpus XIX; Phrase-in-Context; Names pairs dataset; CoNLL 2003; MultiFC; SugarCrepe++,sts17 es en test; BIOSSES; MedSTS; Unknown; sts dev 128; sts17 en ua; sts test 768; sts test 512; sts dev 512; sts dev 64,F1; Spearman Correlation; Pearson Correlation; MSE; Recall; Macro F1; Precision,Similarity Explanation; Semantic Shift Detection,Methodology; Natural Language Processing,"The main objective **Semantic Similarity** is to measure the distance between the semantic meanings of a pair of words, phrases, sentences, or documents. For example, the word “car” is more similar to..."
Face Alignment,17,FaceScape; AFLW-19; CelebA; AFW; LS3D-W; 300-VW; WFLW; LFPW; MERL-RAV; AnimalWeb,N/A,N/A,N/A,N/A,N/A
Image Inpainting,17,CASIA V2; FDF; ImageNet; CelebA; DREAMING Inpainting Dataset; ApolloScape; FVI; FFHQ; Apolloscape Inpainting; Places365,ImageNet; Paris StreetView; CelebA; ApolloScape; Apolloscape Inpainting; Places365; StreetView; FFHQ 512 x 512; CelebA-HQ; Places2 val,40-50% Mask PSNR; LPIPS; FID; PD; 10-20% Mask PSNR; MAE; RMSE; U-IDS; L1 error; SSIM,Cloud Removal; Facial Inpainting; Fine-Grained Image Inpainting,Computer Vision,Remove the preview tag from template
Image Dehazing,17,D-HAZY; RB-Dust; SQUID; Rendered WB dataset; NH-HAZE; I-HAZE; KITTI; MOR-UAV; RESIDE; SEN12MS-CR-TS,RB-Dust; RESIDE-6K; HD-NH-HAZE; NH-HAZE; KITTI; RESIDE; DNH-HAZE; RS-Haze; SOTS Outdoor; I-Haze,PSNR; SSIM,N/A,Computer Vision,"<span style=""color:grey; opacity: 0.6"">( Image credit: [Densely Connected Pyramid Dehazing Network](https://github.com/hezhangsprinter/DCPDN) )</span>"
3D Object Tracking,17,3D-POP; Argoverse 2 Motion Forecasting; K-Radar; KITTI; IBISCape; TruckScenes; DTTD-Mobile; DTTD; SimBEV; The RBO Dataset of Articulated Objects and Interactions,Argoverse CVPR 2020; RTB,Runtime [ms]; AVG-RANK; ADDS AUC,3D Single Object Tracking,Computer Vision,3D Object Tracking is a computer vision task dedicated to monitoring and precisely locating objects as they navigate within a three-dimensional environment. It frequently utilizes 3D object detection ...
Action Recognition In Videos,17,Kinetics-600; THUMOS14; MTL-AQA; PKU-MMD; Kinetics; ActivityNet; UCF101; Jester (Gesture Recognition); CVB; NTU RGB+D,AVA v2.2; Kinetics-600; HMDB-51; FS-Something-Something V2-Small; ActivityNet; PKU-MMD; Kinetics-400; UCF101; Jester (Gesture Recognition); FS-Something-Something V2-Full,Clip Hit@1; Top-1 Accuracy(5-Way-1-Shot); mAP@0.3; 3-fold Accuracy; X-View; mAP (Val); Average accuracy of 3 splits; mAP; mAP@0.4; Video hit@5,Action Anticipation,Computer Vision,**Action Recognition in Videos** is a task in computer vision and pattern recognition where the goal is to identify and categorize human actions performed in a video sequence. The task involves analyz...
Scene Text Detection,17,Indian Number Plates Dataset | Vehicle Number Plates | English OCR Detection; COCO-Text; Chinese Text in the Wild; ICDAR 2015; PKU (License Plate Detection); RCTW-17; UrduDoc; ICDAR 2017; SignboardText; SCUT-CTW1500,COCO-Text; IC19-ReCTs; ICDAR 2015; ICDAR 2017 MLT; IC19-Art; SCUT-CTW1500; MSRA-TD500; Total-Text; ICDAR 2013,Accuracy; F-Measure; H-Mean; Recall; FPS; Precision,Curved Text Detection; Multi-Oriented Scene Text Detection,Computer Vision,**Scene Text Detection** is a computer vision task that involves automatically identifying and localizing text within natural images or videos. The goal of scene text detection is to develop algorithm...
Video Anomaly Detection,17,CUHK Avenue; UCF-Crime; HR-Avenue; Hawk Annotation Dataset; GoodsAD; ShanghaiTech Campus; HR-UBnormal; AeBAD; UCSD Ped2; HR-ShanghaiTech,CUHK Avenue; UCF-Crime; HR-Avenue; ShanghaiTech Campus; HR-UBnormal; UCSD Ped2; HR-ShanghaiTech; Ped2; IITB Corridor; Street Scene,RBDC; AUC; TBDC,Weakly-supervised Video Anomaly Detection,Computer Vision,N/A
Lane Detection,17,TuSimple Lane; TuSimple; CurveLanes; K-Lane; OpenLane; DET; ONCE-3DLanes; BDD100K; LLAMAS; CARLANE Benchmark,tvtLane; CurveLanes; BDD100K val; K-Lane; TuSimple; OpenLane; DET; Caltech Lanes Washington; OpenLane-V2 val; CULane,F1; GFLOPs; Accuracy; IoU; event-based F1 score; IoU (%); Recall; FPS; Params (M); mF1,3D Lane Detection,Computer Vision,**Lane Detection** is a computer vision task that involves identifying the boundaries of driving lanes in a video or image of a road scene. The goal is to accurately locate and track the lane markings...
Face Anti-Spoofing,17,CASIA-MFSD; SiW-Enroll; OULU-NPU; CASIA-SURF; SiW; CelebA-Spoof-Enroll; Replay-Mobile; Replay-Attack; SuHiFiMask; CelebA-Spoof,N/A,N/A,N/A,N/A,N/A
Pedestrian Detection,17,EuroCity Persons; BGVP; CityPersons; NREC Agricultural Person-Detection; LLVIP; SynoClip; CADP; Virtual-Pedcross-4667; UofTPed50; ETH,LLVIP; DVTOD; CityPersons; Caltech; CVC14; MMPD-Dataset; TJU-Ped-campus; TJU-Ped-traffic; Caltech Pedestrian Dataset,AP50; Reasonable Miss Rate; Heavy MR^-2; box mAP; mAP; Medium MR^-2; HO (miss rate); R (miss rate); Partial MR^-2; R+HO (miss rate),Thermal Infrared Pedestrian Detection,Computer Vision,Pedestrian detection is the task of detecting pedestrians from a camera.    Further state-of-the-art results (e.g. on the KITTI dataset) can be found at [3D Object Detection](https://paperswithcode.co...
RGB Salient Object Detection,17,Salient Object Subitizing Dataset; SOD; HRSOD; SBU / SBU-Refine; MD4K; VT5000; DAVIS-S; ECSSD; DUT-OMRON; HS-SOD,HRSOD; SOD; SBU / SBU-Refine; DAVIS-S; UCF; ECSSD; DUT-OMRON; UHRSD; DUTS-TE; SOC,Balanced Error Rate; S-measure; F-Score; MAE; Average MAE; F-measure; mBA; max F-measure; mean F-Measure; S-Measure,Dichotomous Image Segmentation; Co-Salient Object Detection; Video Salient Object Detection,Computer Vision,"RGB Salient object detection is a task-based on a visual attention mechanism, in which algorithms aim to explore objects or regions more attentive than the surrounding areas on the scene or RGB images..."
Salient Object Detection,17,HRSOD; SOD; P3; EORSSD; VQA-OV; MD4K; VT5000; Lytro Illum; ECSSD; DUT-OMRON,SOD; ECSSD; DUT-OMRON; DUTS-TE; PASCAL-S; HKU-IS,Fwβ; max_F1; S-measure; MAE; relaxFbβ; E-measure; Smeasure; {max}Fβ; Sm,RGB-T Salient Object Detection; Saliency Ranking,Computer Vision,N/A
Deblurring,17,REDS; Real Blur Dataset; Rendered WB dataset; RSBlur; DAVANet; YorkTag; Beam-Splitter Deblurring (BSD); Motion Blurred and Defocused Dataset; Deblur-NeRF; GoPro,REDS; RealBlur-R; DVD ; RealBlur-J; RealBlur-R (trained on GoPro); RSBlur; Beam-Splitter Deblurring (BSD); MSU BASED; .; GoPro,"PSNR; LPIPS; ERQAv2.0; SSIM (sRGB); 10 Images, 4*4 Stitching, Exact Accuracy; MAE; Params; PSNR (sRGB); SSIM; Params (M)",Blind Image Deblurring; Single-Image Blind Deblurring,Computer Vision,"**Deblurring** is a computer vision task that involves removing the blurring artifacts from images or videos to restore the original, sharp content. Blurring can be caused by various factors such as c..."
Lesion Segmentation,17,ISIC 2018 Task 3; HAM10000; ISIC 2017 Task 2; CoIR; AutoPET; SD-198; University of Waterloo skin cancer database; ISIC 2018 Task 2; DigestPath; HECKTOR,N/A,N/A,N/A,N/A,N/A
Fact Verification,17,CFEVER; FEVER; FEVEROUS; X-Fact; VitaminC; CREAK; Evidence-based Factual Error Correction; DanFEVER; AVeriTeC; PolitiFact,FEVER; DanFEVER; KILT: FEVER,F1; FEVER; KILT-AC; Accuracy; Recall@5; R-Prec,N/A,Natural Language Processing,"Fact verification, also called ""fact checking"", is a process of verifying facts in natural text against a database of facts."
Beat Tracking,17,JAAH; FibroTUG platforms: Time-lapse microscopy dataset of engineered cardiac microbundles; Groove; Candombe; Filosax; Hainsworth; Beatles; ASAP; Ballroom; HJDB,JAAH; Filosax; Groove; Hainsworth; Candombe; Beatles; ASAP; Ballroom; SMC; HJDB,F1,N/A,Audio,Determine the positions of all beats in a music recording.
Motor Imagery Decoding (left-hand vs right-hand),17,Motor Imagey Dataset from Shin et al 2017; Physionet MI; Motor Imagery dataset from Weibo et al 2014.; Motor Imagery dataset from Cho et al 2017.; BNCI 2014-002 Motor Imagery dataset; BNCI 2015-001 Motor Imagery dataset; Motor Imagery dataset from Zhou et al 2016.; BNCI 2015-004 Motor Imagery dataset; BNCI 2014-001 Motor Imagery dataset.; Motor Imagery dataset from Ofner et al 2017,N/A,N/A,N/A,N/A,N/A
Image Quality Assessment,16,PIQ23; Hephaestus; Fraunhofer Portugal AICOS EDoF Dataset; MSU SR-QA Dataset; TID2013; SACID; CSIQ; SPAQ; KADID-10k; UHD-IQA,SPAQ; KonIQ-10k; KADID10K; KADID-10k; MSU NR VQA Database; MSU FR VQA Database,SRCC; PLCC; KLCC,No-Reference Image Quality Assessment; Document Image Quality Assessment; Full-Reference Image Quality Assessment; Stereoscopic image quality assessment; Image Quality Estimation,Computer Vision,N/A
Scene Recognition,16,KITTI-360-SR; ADE20K; SUN RGB-D; YUP++; HSD; MIT Indoor Scenes; Places365; SUN397; MAI; ADVANCE,N/A,N/A,N/A,N/A,N/A
Facial Landmark Detection,16,CASIA-Face-Africa; COCO-WholeBody; AFW; Toronto NeuroFace Dataset; TFW: Annotated Thermal Faces in the Wild Dataset; 300-VW; WFLW; CatFLW; LFPW; AFLW2000-3D,COCO-WholeBody; AFLW-Full; 300-VW (C); CatFLW; WFLW; COFW; 300W (Full); AFLW2000-3D; 300W; AFLW-Front,NME; Mean NME; FR@10 (inter-ocular); NME (inter-ocular); AUC@10 (inter-ocular); NME (inter-pupil); GTE; Mean Error Rate; keypoint AP; Mean NME ,3D Facial Landmark Localization; Speech to Facial Landmark; Unsupervised Facial Landmark Detection,Computer Vision,"**Facial Landmark Detection** is a computer vision task that involves detecting and localizing specific points or landmarks on a face, such as the eyes, nose, mouth, and chin. The goal is to accuratel..."
Grammatical Error Correction,16,UA-GEC; Kor-Native; Kor-Learner; FCGEC; GitHub Typo Corpus; CoNLL; Kor-Lang8; JFLEG; NaSGEC; The Write & Improve Corpus 2024,EstGEC-L2; UA-GEC; BEA-2019 (test); Restricted; FCGEC; JFLEG; Falko-MERLIN; Unrestricted; MuCGEC; WI-LOCNESS,F0.5; Recall; exact match; Precision; GLEU,Grammatical Error Detection,Natural Language Processing,"Grammatical Error Correction (GEC) is the task of correcting different kinds of errors in text such as spelling, punctuation, grammatical, and word choice errors.     GEC is typically formulated as a ..."
Video Summarization,16,Tour20; Shot2Story20K; TvSum; MVS1K; VideoXum; Multi-Ego; Ego4D-HCap; OSTD; SumMe; Query-Focused Video Summarization Dataset,Shot2Story20K; videoxum; TvSum; SumMe; Query-Focused Video Summarization Dataset; Mr. HiSum,Spearman's Rho; ROUGE; Kendall's Tau; F1 (avg); METEOR; CIDEr; 1 shot Micro-F1; F1-score (Canonical); F1-score (Augmented); MAP (50%),Unsupervised Video Summarization; Supervised Video Summarization,Computer Vision,**Video Summarization** aims to generate a short synopsis that summarizes the video content by selecting its most informative and important parts. The produced summary is usually composed of a set of ...
Active Learning,16,Arxiv GR-QC; COMP6; DeepWeeds; DialoGLUE; FDST; Photoswitch; Groove; L-Bird; HJDataset; Industrial Benchmark,"CIFAR10 (10,000)",Accuracy,Active Object Detection,Methodology; Computer Vision; Natural Language Processing,"**Active Learning** is a paradigm in supervised machine learning which uses fewer training examples to achieve better optimization by iteratively training a predictor, and using the predictor in each ..."
Joint Entity and Relation Extraction,16,2012 i2b2 Temporal Relations; CDR; DocRED-IE; CoNLL; GDA; SemEval-2022 Task-12; ACE 2005; SciERC; CoNLL04; DocRED,N/A,N/A,N/A,N/A,N/A
Event Extraction,16,Phee; Personal Events in Dialogue Corpus; M2E2; SciREX; ChFinAnn; Title2Event; EventNarrative; WikiEvents; ACE 2005; BKEE,Cancer Genetics 2013 (CG); ^(#$!@#$)(()))******; ACE2005; Infectious Diseases 2011 (ID); GENIA 2013; Epigenetics and Post-translational Modifications 2011 (EPI); Multi-Level Event Extraction (MLEE); Pathway Curation 2013 (PC); GENIA,F1; Argument Id; Argument Cl; 0L; Trigger Id; Trigger Cl,Zero-shot Event Extraction; NER; Event Causality Identification,Natural Language Processing,Determine the extent of the events in a text.    Other names: Event Tagging; Event Identification
3D Action Recognition,16,MultiviewC; InfiniteRep; UNIPD-BPE; Florence3D; 3DYoga90; FLAG3D; NTU RGB+D; Fitness-AQA; Navigation Turing Test; BABEL,NTU RGB+D; 100 sleep nights of 8 caregivers; Assembly101,10%; Object Top-1; Cross View Accuracy; Actions Top-1; Verbs Top-1; Cross Subject Accuracy,Zero Shot Skeletal Action Recognition; Model Editing; Image Manipulation Detection; Skeleton Based Action Recognition; motion retargeting,Computer Vision; Natural Language Processing,Image: [Rahmani et al](https://www.cv-foundation.org/openaccess/content_cvpr_2016/papers/Rahmani_3D_Action_Recognition_CVPR_2016_paper.pdf)
Hand Gesture Recognition,16,EgoGesture; IPN Hand; RWTH-PHOENIX Handshapes dev set; LSA16; FDMSE-ISL; OREBA; NVGesture; SHREC; Jester (Gesture Recognition); BdSLW60,N/A,N/A,N/A,N/A,N/A
Trajectory Forecasting,16,FPL; inD Dataset; FollowMe Vehicle Behaviour Prediction Dataset; EgoPAT3D-DT; PIE; JAAD; METEOR; Euro-PVI; NBA SportVU; uniD Dataset,N/A,N/A,N/A,N/A,N/A
Video Super-Resolution,16,VideoLQ; Vimeo90K; Falling Objects; Videezy4K; MSU SR-QA Dataset; SAT-MTB-VSR; RealMCVSR; MSU Super-Resolution for Video Compression; TbD-3D; MSU Video Super Resolution Benchmark: Detail Restoration,UDM10 - 4x upscaling; Falling Objects; SAT-MTB-VSR; Vimeo90K; Vimeo-90K; MSU Super-Resolution for Video Compression; TbD-3D; MSU Video Super Resolution Benchmark: Detail Restoration; REDS4- 4x upscaling; Vid4 - 4x upscaling - BD degradation,BSQ-rate over Subjective Score; LPIPS; MOVIE; BSQ-rate over VMAF; 1 - LPIPS; BSQ-rate over ERQA; Subjective score; ERQAv1.0; TIoU; SSIM,Key-Frame-based Video Super-Resolution (K = 15),Computer Vision,"**Video Super-Resolution** is a computer vision task that aims to increase the resolution of a video sequence, typically from lower to higher resolutions. The goal is to generate high-resolution video..."
Single-View 3D Reconstruction,15,SceneNet; ShapeNetCore; ABO; Drunkard's Dataset; Common Objects in 3D; TransProteus; Parcel2D Real; ABC Dataset; ShapeNet; ShapenetRender,ShapeNetCore; Common Objects in 3D; ATR; SynthEVox3D-Tiny; ShapeNet; Market-HQ; CUB-200-2011; GSO; TransProteus,FID; Chamfer Distance; IoU; A-mIoU; F-Score; R2; 3DIoU; Avg. F1,3D Semantic Scene Completion from a single RGB image,Computer Vision,N/A
Video Frame Interpolation,15,SlowFlow; I2-2000FPS; Vimeo90K; X4K1000FPS; UCF101; LAVIB; Middlebury; VFITex; GoPro; Vid4,Xiph 4k; X4K1000FPS-2K; Middlebury; Vid4 - 4x upscaling; Vimeo90K; X4K1000FPS; UCF101; Xiph-4K (Crop); GoPro; ATD-12K,Speed (ms/f); LPIPS; Parameters; tOF; MS-SSIM; runtime (s); Subjective score; Interpolation Error; PSNR (sRGB); SSIM,Unsupervised Video Frame Interpolation; eXtreme-Video-Frame-Interpolation; 3D Video Frame Interpolation,Computer Vision,The goal of **Video Frame Interpolation** is to synthesize several frames in the middle of two adjacent frames of the original video. Video Frame Interpolation can be applied to generate slow motion v...
Human Activity Recognition,15,MPII Human Pose Descriptions; OAD dataset; WEAR; HASCD; RHM; UCF101; MOSAD; Radar Dataset (DIAT-μRadHAR: Radar micro-Doppler Signature dataset for Human Suspicious Activity Recognition); FLAG3D; MPHOI-72,OAD dataset; RHM; UCF 101; MM-Fit; HMDB51; PAMAP2; Radar Dataset (DIAT-μRadHAR: Radar micro-Doppler Signature dataset for Human Suspicious Activity Recognition); HAR,Accuracy; Accuracy (Top-1); 1:1 Accuracy; F1 - macro; ARI; Macro-F1; Macro F1; F1 Macro; NMI,Sports Activity Recognition,Time Series,Classify various human activities
Gesture Recognition,15,IPN Hand; Kinteract; Slovo: Russian Sign Language Dataset; Aff-Wild; MSRC-12; DVS128 Gesture; SALSA; SHREC; Jester (Gesture Recognition); TIM-Tremor,Ninapro DB-1 8 gestures; Ninapro DB-1 12 gestures; ChaLearn 2016; DVS128 Gesture; SHREC 2017 track on 3D Hand Gesture Recognition; MSRC-12; CapgMyo DB-b; GesturePod; Montalbano; ChaLearn 2013,Accuracy; 14 gestures accuracy; Jaccard (Mean); Recall; Real World Accuracy; Precision; Error rate; Accuracy (%),Hand-Gesture Recognition; Hand Gesture Recognition; RF-based Gesture Recognition,Computer Vision,"**Gesture Recognition** is an active field of research with applications such as automatic recognition of sign language, interaction of humans and robots or for new ways of controlling video games.   ..."
2D Pose Estimation,15,3D-POP; Vinegar Fly; HARPER; ConSLAM; MacaquePose; Animal Kingdom; Human3.6M; MP-100; [[Easy~complain@file]]How to file a complaint against Expedia; 300W,Vinegar Fly; HARPER; MacaquePose; Animal Kingdom; Human3.6M; MP-100; iRodent; 300W; Desert Locust,PCK@0.05; Mean PCK@0.2; Average mAP; AP; Mean PCK@0.2 - 5shot; Mean PCK@0.2 - 1shot; EPE; PCK,Overlapping Pose Estimation; Category-Agnostic Pose Estimation,Computer Vision,detective pose
Computed Tomography (CT),15,LUNA16; LiTS17; CPCXR; 2DeteCT; COVID-19-CT-CXR; BIMCV COVID-19; COVID-CT; PadChest; MosMedData; Large COVID-19 CT scan slice dataset,N/A,N/A,Stroke Classification,Methodology,"The term “computed tomography”, or CT, refers to a computerized x-ray imaging procedure in which a narrow beam of x-rays is aimed at a patient and quickly rotated around the body, producing signals th..."
Saliency Detection,15,iSUN; CAT; CAT2000; PASCAL Context; Lytro Illum; ECSSD; DUT-OMRON; HS-SOD; ReDWeb-S; COME15K,CAT2000; PASCAL Context; ECSSD; DUT-OMRON; DUTS-test; PASCAL-S; HKU-IS,Fwβ; max_F1; AUC; MAE; relaxFbβ; NSS; {max}Fβ; Sm,Video Saliency Detection; Co-Salient Object Detection; Saliency Prediction; Unsupervised Saliency Detection,Computer Vision,"**Saliency Detection** is a preprocessing step in computer vision which aims at finding salient objects in an image.   <span class=""description-source"">Source: [An Unsupervised Game-Theoretic Approach..."
Video Inpainting,15,VPData; VPBench; DTTD-Mobile; FVI; QST; Apolloscape Inpainting; VideoRemoval4K; EgoHOS; How2Sign; KITTI360-EX,HQVI (480p); YouTube-VOS; HQVI (240p); HQVI (2K); How2Sign; DAVIS; YouTube-VOS 2018,LPIPS; SSIM (square); VFID; PSNR (square); SSIM; LPIPS (square); SSIM (object); LPIPS (object); PSNR; L1 error,N/A,Computer Vision,"The goal of **Video Inpainting** is to fill in missing regions of a given video sequence with contents that are both spatially and temporally coherent. Video Inpainting, also known as video completion..."
Sarcasm Detection,15,Headlines dataset; SPIRS; BIG-bench; MUStARD++; iSarcasmEval; ArSarcasm; iSarcasm; MMSD2.0; WITS; Reddit,SARC (all-bal); MUStARD++; FigLang 2020 Reddit Dataset; iSarcasm; FigLang 2020 Twitter Dataset; BIG-bench (SNARKS); WITS; SARC (pol-bal); SARC (pol-unbal),F1; Avg F1; Accuracy; R1; Recall; F1-Score; Precision,N/A,Natural Language Processing,"The goal of **Sarcasm Detection** is to determine whether a sentence is sarcastic or non-sarcastic. Sarcasm is a type of phenomenon with specific perlocutionary effects on the hearer, such as to break..."
License Plate Recognition,15,Caltech Cars; CLPD; EnglishLP; Vehicle-Rear; CCPD; ChineseLP; CD-HARD; OpenALPR-EU; UFPR-VCR Dataset; AOLP,N/A,N/A,N/A,N/A,N/A
Tumor Segmentation,15,LiTS17; The ULS23 Challenge Test Set; BraTS 2016; AutoPET; CSAW-S; DigestPath; HECKTOR; BraTS 2015; CoCaHis; NSCLC-Radiogenomics,N/A,N/A,N/A,N/A,N/A
3D Human Reconstruction,15,BEHAVE; HBW; MMBody; CAPE; TikTok Dataset; SIZER; SSP-3D; AGORA; MoVi; Synthetic Human Model Dataset,N/A,N/A,N/A,N/A,N/A
3D Classification,15,Teeth3DS+; InfiniteRep; FER2013 Blendshapes; Corn Seeds Dataset; CVB; RAD-ChestCT Dataset; ADHD-200; 3D-Point Cloud dataset of various geometrical terrains; InLUT3D; U-10: United-10 COVID19 CT Dataset,U-10: United-10 COVID19 CT Dataset,AUC,3D Object Classification; MRI classification,Computer Vision; Medical,N/A
Semi-Supervised Image Classification,14,Semi-iNat; ImageNet; Caltech-256; LIMUC; BIOSCAN-5M; SVHN; Salinas; CIFAR-100; Caltech-101; HeriGraph,"CIFAR-100 (10000 Labels, ImageNet-100 Unlabeled); cifar-100, 10000 Labels; CIFAR-10, 4000 Labels; ImageNet - 1% labeled data; EuroSAT, 100 Labels; SVHN, 2000 Labels; CIFAR-100, 5000 Labels; CIFAR-10 (250 Labels, ImageNet-100 Unlabeled); STL-10; CIFAR-100, 4000 Labels",Percentage correct; Number of params; Top 5 Accuracy; Accuracy; ImageNet Top-1 Accuracy; Overall Accuracy; Percentage error; Accuracy (Test); Accuarcy; Top 1 Accuracy,Semi-Supervised Image Classification (Cold Start); Open-World Semi-Supervised Learning,Computer Vision,Semi-supervised image classification leverages unlabelled data as well as labelled data to increase classification performance.    You may want to read some blog posts to get an overview before readin...
Weakly Supervised Object Detection,14,IconArt; ImageNet; Watercolor2k; Foggy Cityscapes; PASCAL VOC 2012 test; LeukemiaAttri; Charades; HICO-DET; PASCAL VOC 2007; MSCOCO,N/A,N/A,N/A,N/A,N/A
Open Information Extraction,14,SemTabNet; BenchIE; LSOIE; CaRB; QA-SRL; GenericsKB; DocOIE; New York Times Annotated Corpus; OPIEC; OIE2016,BenchIE; LSOIE; OpenIE; CaRB; NYT; WiRe57; DocOIE-transportation; CaRB OIE benchmark (Greek Use-case); Web; OIE2016,F1; AUC; EN-F1; Recall; Precision; EN-AUC,Event Extraction,Natural Language Processing,"In natural language processing, open information extraction is the task of generating a structured, machine-readable representation of the information in text, usually in the form of triples or n-ary ..."
Text-to-Video Generation,14,ChronoMagic-Pro; ConsisID-preview-Data; ChronoMagic; Kinetics; UCF101; Vript; WebVid; EvalCrafter Text-to-Video (ECTV) Dataset; OpenS2V-5M; Something-Something V2,UCF-101; Kinetics; WebVid; EvalCrafter Text-to-Video (ECTV) Dataset; Something-Something V2; MSR-VTT,FID; Total Score; Accuracy; Visual Quality; FVD; Temporal Consistency; CLIPSIM; CLIP-FID; Text-to-Video Alignment; FVD16,Text-to-Video Editing; Subject-driven Video Generation,Computer Vision; Natural Language Processing,"Ma grand-mère m’a raconté que quand elle était étudiante, elle avait un petit-ami. À l’âge de 18 ans, il a dû partir pour le service militaire, elle ne l’a pas attendu et elle a épousé quelqu’un d’aut..."
Emotion Recognition in Conversation,14,EmoWOZ; SEMAINE; EmoContext; KD-EmoR; EmotionLines; CMU-MOSI; EmoryNLP; MaSaC_ERC; IEMOCAP; DailyDialog,N/A,N/A,N/A,N/A,N/A
Boundary Detection,14,RoFT-chatgpt; NYUv2; Kinetics; UruDendro; TriBERT; SoccerNet-v2; PASCAL Context; CoAuthor; CaFFe; Contour Drawing Dataset,NYU-Depth V2; RoFT-chatgpt; UruDendro; Kinetics-400; PASCAL Context; CoAuthor; TriBERT (in-domain); RoFT,F1-score; Average Precision; Cohen’s Kappa score; MSE; Recall; Average Recall; FScore; Pairwise F1; Precision; Accuracy (%),Junction Detection,Computer Vision,"**Boundary Detection** is a vital part of extracting information encoded in images, allowing for the computation of quantities of interest including density, velocity, pressure, etc.      <span class=..."
Nested Named Entity Recognition,14,AMALGUM; ACE 2004; DaN+; LegalNERo; RuTermEval (Track 3); ACE 2005; The QUAERO French Medical Corpus; GENIA; GUM; RuTermEval (Track 1),N/A,N/A,N/A,N/A,N/A
Semantic Role Labeling,14,X-SRL; CoNLL-2012; NomBank; RuSRL; CoNLL; GePaDe; FrameNet; QA-SRL; PropBank-PT; Product Reviews 2017,CoNLL12; OntoNotes; CoNLL 2012; CoNLL05 WSJ; CoNLL05 Brown; CoNLL-2009; CoNLL 2005,F1; Avg. F1; F1 (Arg.); F1 (Prd.),Predicate Detection; Semantic Role Labeling (predicted predicates); Textual Analogy Parsing,Natural Language Processing,"Semantic role labeling aims to model the predicate-argument structure of a sentence  and is often described as answering ""Who did what to whom"". BIO notation is typically  used for semantic role label..."
Network Intrusion Detection,14,UNSW-NB15; IoT Network Intrusion Dataset; IoT Benign and Attack Traces; Kitsune Network Attack Dataset; WEB-IDS23 Dataset; CIC IoT Dataset 2022; UQ NIDS Datasets (FlowMeter Format); CICIDS2017; ToN_IoT; UQ NIDS Datasets,N/A,N/A,N/A,N/A,N/A
motion prediction,14,Occ-Traj120; Freiburg Street Crossing; Argoverse 2 Motion Forecasting; FollowMe Vehicle Behaviour Prediction Dataset; I2-2000FPS; GTA-IM Dataset; CITR & DUT; NTU RGB+D 2D; Waymo Open Motion Dataset; Expi,N/A,N/A,OPD: Single-view 3D Openable Part Detection,Computer Vision,N/A
Handwritten Text Recognition,14,BN-HTRd; READ 2016; READ2016(line-level); MatriVasha:; Digital Peter; Burmese Handwritten Digit Dataset (BHDD); HKR; IAM(line-level); Bentham; SIMARA,READ2016(line-level); READ 2016; Digital Peter; HKR; IAM-B; IAM(line-level); Bentham; IAM-D; SIMARA; Saint Gall,Test WER; Test CER; CER (%); WER (%); WER; CER,Handwritten Document Recognition; Unsupervised Text Recognition,Computer Vision; Adversarial,Handwritten Text Recognition (HTR) is the task of automatically identifying and transcribing handwritten text from images or scanned documents into machine-readable text. The goal is to develop a syst...
Pose Tracking,14,"VRMocap: VR Mocap Dataset for Pose Reconstruction; Drunkard's Dataset; InfiniteRep; HOI4D; 10,000 People - Human Pose Recognition Data; ConSLAM; YCBInEOAT Dataset; VR Mocap Dataset for Pose/Orientation Prediction; PoseTrack; SLAM2REF",PoseTrack2018; PoseTrack2017; Multi-Person PoseTrack,IDF1; MOTP; MOTA; mAP; IDs,3D Human Pose Tracking,Computer Vision,**Pose Tracking** is the task of estimating multi-person human poses in videos and assigning unique instance IDs for each keypoint across frames. Accurate estimation of human keypoint-trajectories is ...
Human Detection,14,TinyPerson; MIAP; NREC Agricultural Person-Detection; PeopleSansPeople; Human faces with mixed-race & various emotions; TikTok Dataset; JRDB; UCF-CC-50; SoccerNet-GSR; PP-HumanSeg14K,N/A,N/A,N/A,Computer Vision,"DEFINICIÓN   EL Granada SOUND es un festival de música india de ámbito nacional e internacional que se celebra en Granada, España. Desde su primera edición en 2012, el evento ha ido creciendo en popul..."
Event Detection,14,MAVEN-Arg; ROAD; OpenTTGames; BIRDeep; BKEE; Brazilian Protest; MUSIED; LEVEN; EDT; LEMONADE,N/A,N/A,N/A,N/A,N/A
Indoor Localization,14,University; DATOR-synth; Structured3D; LuViRA; DATOR-lab; ZInd; dichasus-cf0x; ConSLAM; NILoc; RISEdb,"Structured3D (perspective, emtpy); Structured3D (perspective, furnished); Structured3D (perspective, empty)",N/A,"Indoor Localization (6-DoF Pose); Indoor Localization (3-DoF Pose: X, Y, Yaw)",Computer Vision,Indoor localization is a fundamental problem in indoor location-based applications.
Instruction Following,14,IFEval; GSCAN; SurgeGlobal/Orca; Sequential Instructions; SurgeGlobal/LaMini; MIMIC-IT; UGIF; Tamil Alpaca; Tamil Alpaca Orca; Bactrian-X,IFEval,Prompt-level strict-accuracy; Inst-level loose-accuracy; Inst-level strict-accuracy; Prompt-level loose-accuracy,visual instruction following,Computer Vision; Natural Language Processing,Instruction following is the basic task of the model. This task is dedicated to evaluating the ability of the large model to follow human instructions. It is hoped that the model can generate controll...
Keypoint Detection,13,ViCoS Towel Dataset; PASCAL3D+; Fish Keypoints Detection; MPII; TAMPAR; COCO (Common Objects in Context); OCHuman; KeypointNet; AwA Pose; RadioGalaxyNET,N/A,N/A,N/A,N/A,N/A
Video Object Tracking,13,TREK-150; BL30K; RF100; NT-VOT211; CATER; GOT-10k; DTTD-Mobile; SoccerNet-v2; VideoCube; SOTVerse,N/A,N/A,N/A,N/A,N/A
Dialogue State Tracking,13,CoSQL; Taskmaster-1; MultiWOZ; CrossWOZ; MMConv; RiSAWOZ; SIMMC2.0; diaforge-utc-r-0725; IndirectRequests; Dialogue State Tracking Challenge,N/A,N/A,N/A,N/A,N/A
Small Object Detection,13,SOD4SB; SODA-A; Apron Dataset; iSAID; Aircraft Context Dataset; SODA-D; Bee4Exp Honeybee Detection; Blood Cell Detection Dataset; SFCHD; EVD4UAV,SOD4SB Private Test; Bee4Exp Honeybee Detection; SODA-D; SOD4SB Public Test,mAP@0.5:0.95; AP50; Average F1,Rice Grain Disease Detection,Computer Vision,**Small Object Detection** is a computer vision task that involves detecting and localizing small objects in images or videos. This task is challenging due to the small size and low resolution of the ...
Video Object Detection,13,"THGP; VISEM-Tracking; 5,011 Images – Human Frontal face Data (Male); Waymo Open Dataset; DTTD-Mobile; ImageNet VID; OAK; EPIC-KITCHENS-55; Underwater Trash Detection; YT-BB",N/A,N/A,N/A,N/A,N/A
Semi-Supervised Video Object Segmentation,13,Referring Expressions for DAVIS 2016 & 2017; VOT2020; BL30K; Long Video Dataset; Long Video Dataset (3X); BURST; PUMaVOS; DAVIS 2017; DAVIS 2016; VOTChallenge,VOT2020; Long Video Dataset (3X); Long Video Dataset; YouTube-VOS 2019; YouTube; DAVIS-2017; DAVIS-2016; BURST-val; DAVIS 2017 (val); DAVIS 2017,D17 test (G); F-Measure (Unseen); D16 val (F); Jaccard (Mean); Speed  (FPS); Overall; D17 val (F); F-measure (Decay); Speed (FPS); Jaccard (Unseen),One-shot visual object segmentation,Computer Vision,The semi-supervised scenario assumes the user inputs a full mask of the object(s) of interest in the first frame of a video sequence. Methods have to produce the segmentation mask for that object(s) i...
Semi-Supervised Semantic Segmentation,13,Dronescapes; Kvasir-Instrument; SUIM; WoodScape; ScribbleKITTI; SemanticKITTI; ImageNet-S; 2D-3D-S; KiTS19; 2017 Robotic Instrument Segmentation Challenge,N/A,N/A,N/A,N/A,N/A
Hyperspectral Image Classification,13,Indian Pines; Botswana; HSI-Drive v2.0; Pavia University; LIB-HSI; WHU-Hi; Salinas; Hyperspectral City; Kennedy Space Center; HyKo2-VIS,N/A,N/A,N/A,N/A,N/A
Community Detection,13,SNAP; HLA-Chat; Citeseer; A collection of LFR benchmark graphs; DBLP; Placenta; Twitter-HyDrug; Email-EU; Orkut; Yeast,Facebook TV Show; Citeseer; DBLP; Facebook Celebrities; Facebook Companies; Amazon; Facebook Media; Twitter-HyDrug; Facebook Politicians; Facebook Athletes,Jaccard; Accuracy-NE; F1-Score; Modularity; NMI; ACC; F1-score,Local Community Detection; Online Community Detection; Network Community Partition,Graphs,"**Community Detection** is one of the fundamental problems in network analysis, where the goal is to find groups of nodes that are, in some sense, more similar to each other than to the other nodes.  ..."
Downbeat Tracking,13,JAAH; Filosax; Groove; Candombe; Hainsworth; Beatles; ASAP; Ballroom; HJDB; GTZAN,JAAH; Filosax; Candombe; Hainsworth; Groove; Beatles; ASAP; Ballroom; HJDB; GTZAN,F1,N/A,Audio,Determine the positions of all downbeats in a music recording.
Vision and Language Navigation,13,CHALET; MINOS; Talk the Walk; StreetLearn; RxR; ArraMon; Fine-Grained R2R; robo-vln; Touchdown Dataset; Talk2Nav,VLN Challenge; RxR; robo-vln; Touchdown Dataset; map2seq,SPL (Sucess Weighted by Path Length); success; error; oracle success; spl; length; ndtw; Task Completion (TC),N/A,Robots,N/A
Multiple Instance Learning,13,UCF-Crime; Colorectal Adenoma; Wiki-en; SPOT; Wiki-zh; AVE; Musk v2; CamNuvem Dataset; Elephant; CAMELYON16,Musk v2; Elephant; CAMELYON16; TCGA; Musk v1,Expected Calibration Error; AUC; FROC; Patch AUC; ACC,N/A,Methodology,"**Multiple Instance Learning** is a type of weakly supervised learning algorithm where training data is arranged in bags, where each bag contains a set of instances $X=\\{x_1,x_2, \ldots,x_M\\}$, and ..."
Image Registration,13,BRIGHT; LLVIP; Fetoscopy Placenta Data; FIRE; SegTHOR; ALTO; Medical Segmentation Decathlon; Learn2Reg; L1BSR; DIR-LAB COPDgene,Unpaired-abdomen-CT; FIRE;  Osteoarthritis Initiative; Unpaired-lung-CT; DIR-LAB COPDgene,landmarks; mAUC; ASD; Dice; DSC,Unsupervised Image Registration; distortion correction,Computer Vision,"Image registration is the process of transforming different sets of data into one coordinate system. Data may be multiple photographs, data from different sensors, times, depths, or viewpoints. It is ..."
Image Reconstruction,12,CED; SEN12MS-CR; 2DeteCT; ImageNet; CBCT Walnut; SEN12MS-CR-TS; OADAT; Spike-X4K; Ultra-High Resolution Image Reconstruction Benchmark; RGB-DAVIS Dataset,ImageNet 256x256; Edge-to-Handbags; Edge-to-Shoes; ImageNet; Spike-X4K; Ultra-High Resolution Image Reconstruction Benchmark; Edge-to-Clothes; Audio Set,LPIPS; FID; MMD; HP; SSIM; PSNR; rFID; Average PSNR,MRI Reconstruction; Film Removal; Blind Super-Resolution; WiFi CSI-based Image Reconstruction; CT Reconstruction,Miscellaneous; Computer Vision; Medical,N/A
Image Compression,12,Food-101; PCam; BSDS500; ImageNet-32; OSN-transmission_mini_CelebA; FER2013; Oxford-IIIT Pet Dataset; Oxford-IIIT Pets; UPIQ; PINet,Food-101; Cars-196; Caltech101; PCam; BSDS500; kodak; FER2013; Oxford-IIIT Pet Dataset; ImageNet32; STL-10,bpsp; 10%; Bit rate; BD-Rate over VTM-17.0; SSIM; Average PSNR,Color Image Compression Artifact Reduction; Lossy-Compression Artifact Reduction; Jpeg Compression Artifact Reduction; Feature Compression,Computer Vision,"**Image Compression** is an application of data compression for digital images to lower their storage and/or transmission requirements.      <span class=""description-source"">Source: [Variable Rate Dee..."
Point Cloud Registration,12,4DMatch; KITTI; FAUST-partial; UrbanLoco; DeformingThings4D; ScanNet++; FPv1; 3DMatch; 3DCSR dataset; CODD,3DLoMatch (10-30% overlap); ScanNet++ (trained on 3DMatch); FP-O-H; FP-T-M; KITTI (FCGF setting); 3DMatch (at least 30% overlapped - sample 5k interest points); FPv1; FP-R-E; FP-O-E; FP-R-M,"mRR @ Normal Criterion (1.5°&0.3m); RR @ Loose Criterion (5°&2m), on LoKITTI; Recall ( correspondence RMSE below 0.2); CD; Recall (30cm, 5 degrees); RRE (degrees); Recall (0.6m, 5 degrees); Recall (3cm, 10 degrees); Success Rate; Feature Matching Recall",Image to Point Cloud Registration,Computer Vision,"**Point Cloud Registration** is a fundamental problem in 3D computer vision and photogrammetry. Given several sets of points in different coordinate systems, the aim of registration is to find the tra..."
Real-Time Semantic Segmentation,12,COCO-Stuff; NYUv2; Kvasir-SEG; Kvasir-Instrument; Endotect Polyp Segmentation Challenge Dataset; KvasirCapsule-SEG; HelixNet; Kvasir; CamVid; Medico automatic polyp segmentation challenge (dataset),N/A,N/A,N/A,N/A,N/A
Fine-Grained Image Recognition,12,"Crowd Activity Dataset; OmniBenchmark; CUB-GHA; WikiChurches; OVEN; CNFOOD-241-Chen; Ultra Fine-Grained Leaves (Cotton, SoyAgeing, SoyGene, SoyGlobal, SoyLocal); WebFG-496; DAPlankton; CUB-200-2011",N/A,N/A,N/A,N/A,N/A
Object Detection In Aerial Images,12,SODA-A; DIOR; xView; iSAID; VME & CDSI; LandCover.ai; DOTA 2.0; AID; C2A: Human Detection in Disaster Scenarios; FMARS,N/A,N/A,N/A,N/A,N/A
Activity Detection,12,Home Action Genome; TSU; ROAD; InfiniteRep; DAHLIA; UCLA Protest Image; MEVA; AVA-Speech; IITB Corridor; AVA,AVA-Speech,ROC-AUC,N/A,Computer Vision,Detecting activities in extended videos.
Fraud Detection,12,BAF; Yelp-Fraud; Vehicle Claims; IBM Transactions for Anti Money Laundering; FDCompCN; Amazon-Fraud; Elliptic Dataset; Healthcare Provider Fraud Detection Analysis; CIDII Dataset; Yelp,BAF – Variant V; BAF – Variant II; Yelp-Fraud; BAF – Variant I; BAF – Variant III; BAF – Variant IV; Elliptic Dataset; Healthcare Provider Fraud Detection Analysis; Amazon-Fraud; FDCompCN,Accuracy; AUC-ROC; AUC; Average Precision; Recall @ 1% FPR; AUPRC; G-mean; Recall @ 5% FPR; Averaged Precision; F1 Macro,N/A,Miscellaneous,"**Fraud Detection** is a vital topic that applies to many industries including the financial sectors, banking, government agencies, insurance, and law enforcement, and more. Fraud endeavors have detec..."
Video Segmentation,12,SegTrack-v2; Infinity Spills Basic Dataset; MOMA-LRG; TikTok Dataset; Dynamic Replica; LSDBench; MM-OR; EgoProceL; BDD100K; PP-HumanSeg14K,SegTrack v2,Accuracy,Open-World Video Segmentation; Camera shot boundary detection; Open-Vocabulary Video Segmentation,Computer Vision,N/A
Surface Normals Estimation,12,SynFoot; Pano3D; IBims-1; NYUv2; 3D Ken Burns Dataset; TransProteus; GRIT; PASCAL Context; Taskonomy; ScanNet,IBims-1; PCPNet; PASCAL Context; ScanNetV2; NYU Depth v2; NYU-Depth V2 Surface Normals; Taskonomy; Stanford-ORB,Cosine Distance; % < 11.25; RMSE; % < 30; L1 error; % < 22.5; Mean; RMSE ; Mean Angle Error,N/A,Computer Vision,Surface normal estimation deals with the task of predicting the surface orientation of the objects present inside a scene. Refer to [Designing Deep Networks for Surface Normal Estimation (Wang et al.)...
Conversational Response Selection,12,Reddit Corpus; DSTC7 Task 1; UDC; Douban; RRS; RRS Ranking Test; BBAI Dataset; Reddit; Advising Corpus; Douban Conversation Corpus,"Ubuntu Dialogue (v1, Ranking); PolyAI AmazonQA; Douban; Persona-Chat; personachat; Ubuntu IRC; RRS; RRS Ranking Test; PolyAI OpenSubtitles; Advising Corpus",R@10; R10@1; Accuracy; NDCG@3; MAP; R2@1; NDCG@5; R20@1; R@50; R10@5,N/A,Natural Language Processing,Conversational response selection refers to the task of identifying the most relevant response to a given input sentence from a collection of sentences.
Motion Forecasting,12,FPL; Argoverse 2 Motion Forecasting; FollowMe Vehicle Behaviour Prediction Dataset; Argoverse-HD; Argoverse 2 Sensor; Argoverse 2 Lidar; Autonomous-driving Streaming Perception Benchmarrk; Waymo Open Motion Dataset; SinD; Argoverse,Argoverse CVPR 2020,brier-minFDE (K=6); minADE (K=1); minADE (K=6); MR (K=1); DAC (K=6); minFDE (K=6); minFDE (K=1); MR (K=6),Multi-Person Pose forecasting; Multiple Object Forecasting,Computer Vision,Motion forecasting is the task of predicting the location of a tracked object in the future
License Plate Detection,12,Caltech Cars; CLPD; Common Voice; Vehicle-Rear; ChineseLP; CD-HARD; SSIG-SegPlate; RodoSol-ALPR; PKU (License Plate Detection); UFPR-ALPR,Common Voice Estonian,0S,N/A,Computer Vision,License Plate Detection is an image-processing technology used to identify vehicles by their license plates. This technology is used in various security and traffic applications.
Video Quality Assessment,12,LIVE-VQC; LIVE-ETRI; MSU SR-QA Dataset; KoNViD-1k; YouTube-UGC; Video Call MOS Set; Raw_-Subjective-Scores-120-videos; LIVE-YT-HFR; LIVE Livestream; LIVE-FB LSVQ,LIVE-VQC; LIVE-ETRI; MSU SR-QA Dataset; KoNViD-1k; YouTube-UGC; LIVE-YT-HFR; LIVE Livestream; LIVE-FB LSVQ; MSU NR VQA Database; MSU FR VQA Database,Type; PLCC; SROCC; SRCC; KLCC,N/A,Computer Vision; Time Series,"Video Quality Assessment is a computer vision task aiming to mimic video-based human subjective perception. The goal is to produce a mos score, where higher score indicates better perceptual quality. ..."
Time Series Prediction,12,Box-Jenkins; Extreme Events > Natural Disasters > Hurricane; STREETS; EarthNet2021; Hotel; ExtMarker; Data Collected with Package Delivery Quadcopter Drone; Air Quality Index; NBA player performance prediction dataset; Beijing Traffic,Sunspot; Data Collected with Package Delivery Quadcopter Drone,RMSE; Average mean absolute error,N/A,Time Series,"The goal of **Time Series Prediction** is to infer the future values of a time series from the past.      <span class=""description-source"">Source: [Orthogonal Echo State Networks and stochastic evalua..."
Fact Checking,12,Politi Hop; Stanceosaurus; VitaminC; AVeriTeC; CoVERT; PANACEA; Spiced; STVD-FC; Mocheg; CFEVER,CDCD; ^(#$!@#$)(()))******; FEVER (BEIR); AVeriTeC; SciFact (BEIR); CLIMATE-FEVER (BEIR); LIAR2,AveriTeC; Question + Answer score; F1-Micro (Test); Recall; 0..5sec; Accuracy (Test); nDCG@10; Question Only score; Precision; F1-Macro (Test),Misconceptions; FEVER (2-way); Sentence Ambiguity; FEVER (3-way); Known Unknowns,Miscellaneous,N/A
Key Information Extraction,12,POIE; CORD; SROIE; Kleister NDA; DocILE; ARF; EPHOIE; SIMARA; SIBR; ETD500,SROIE; CORD; Kleister NDA; EPHOIE; SIMARA; ETD500,F1; Average F1; F1 (%); Accuracy,Key-value Pair Extraction,Computer Vision; Natural Language Processing,"Key Information Extraction (KIE) is aimed at extracting structured information (e.g. key-value pairs) from form-style documents (e.g. invoices), which makes an important step towards intelligent docum..."
Zero-Shot Composed Image Retrieval (ZS-CIR),11,NICO++; ImageNet; WebVid-CoVR; GeneCIS; CIRR; ImageNet-R; Fashion IQ; CIRCO; COCO (Common Objects in Context); Large Time Lags Location (LTLL),N/A,N/A,N/A,N/A,N/A
Outlier Detection,11,MVTecAD; WikiSem500; ADFI; Epinion; ECG5000; Ecoli; Vistas-NP; pathbased; SKAB; ImageNet-O,Hepatitis; Ionosphere_class b; Heart-C; Glass identification; ECG5000; Balance scale_class 1; SKAB; Breast cancer Wisconsin_class 2; Breast cancer Wisconsin_class 4; Fashion-MNIST,Accuracy; AUC-ROC; AUC; Average Accuracy; Average F1; AUROC,Graph Outlier Detection; outlier ensembles; One-class classifier; Outlier Interpretation,Methodology; Graphs,**Outlier Detection** is a task of identifying a subset of a given data set which are considered anomalous in that they are unusual from other instances. It is one of the core data mining tasks and is...
Scene Graph Generation,11,VRD; SpaceSGG; GQA; 3DSSG; MM-OR; Haystack; COCO (Common Objects in Context); Visual Genome; PSG Dataset; 3RScan,VRD; MS-COCO; GQA; MM-OR; 3R-Scan; Visual Genome; 4D-OR,F1; mR@50; mR@100; Top-5 Accuracy; R@100; Recall@20; Recall@100; zR@20; R@20; mean Recall @100,Panoptic Scene Graph Generation; Unbiased Scene Graph Generation,Computer Vision,"A scene graph is a structured representation of an image, where nodes in a scene graph correspond to object bounding boxes with their object categories, and edges correspond to their pairwise relation..."
Depth Completion,11,"VOID; Multi-Spectral Stereo Dataset  (RGB, NIR, thermal images, LiDAR, GPS/IMU); NYUv2; KITTI; TransCG; Matterport3D; ConSLAM; BIDCD; PLAD; SuperCaustics",VOID; KITTI Depth Completion Eigen Split; KITTI Depth Completion; NYU-Depth V2; KITTI Depth Completion 500 points; KITTI; VOID-150; Matterport3D; PLAD,iMAE; MAE; Runtime [ms]; RMSE; iRMSE; REL; RMSE ,N/A,Computer Vision,"The **Depth Completion** task is a sub-problem of depth estimation. In the sparse-to-dense depth completion problem, one wants to infer the dense depth map of a 3-D scene given an RGB image and its co..."
Graph Matching,11,BirdSong; Occluded REID; SPair-71k; IMCPT-SparseGM-100; Linux; CUB-200-2011; IMCPT-SparseGM-50; DispScenes; Mindboggle; PASCAL VOC,SPair-71k; Willow Object Class; IMCPT-SparseGM-100; CUB; IMCPT-SparseGM-50; PASCAL VOC; RARE,Spearman Correlation; F1 score; matching accuracy,N/A,Graphs,**Graph Matching** is the problem of finding correspondences between two sets of vertices while preserving complex relational information among them. Since the graph structure has a strong capacity to...
Multimodal Emotion Recognition,11,EMOTIC; Werewolf-XL; Video Dataset; CMU-MOSEI; RESD; DEAP; IEMOCAP; SES; AESI; MELD,Expressive hands and faces dataset (EHF).; IEMOCAP; CMU-MOSEI-Sentiment-3; MELD-Sentiment; MELD; IEMOCAP-4; CMU-MOSEI-Sentiment,Weighted F1; F1; Accuracy; Weighted Recall; v2v error,Video Emotion Detection,Computer Vision,This is a leaderboard for multimodal emotion recognition on the IEMOCAP dataset. The modality abbreviations are  A: Acoustic  T: Text  V: Visual    Please include the modality in the bracket after the...
Head Pose Estimation,11,HPD; Panoptic; WFLW; DAD-3DHeads; AFLW2000-3D; AFLW; ICT-3DHP; BIWI; COFW; ARKitFace,N/A,N/A,N/A,N/A,N/A
Unsupervised Object Segmentation,11,SegTrack-v2; FBMS; Shelf&Tote Training Dataset; Multi-dSprites; ClevrTex; ECSSD; DAVIS 2016; ShapeStacks; FBMS-59; DUTS,SegTrack-v2; Shelf&Tote Training Dataset; ClevrTex; ECSSD; DAVIS 2016; ShapeStacks; FBMS-59; DUTS; ObjectsRoom,J score; MSE; ARI; mIoU; ARI-FG,N/A,Computer Vision,Image credit: [ClevrTex: A Texture-Rich Benchmark for Unsupervised Multi-Object Segmentation](https://paperswithcode.com/paper/clevrtex-a-texture-rich-benchmark-for)
Referring Expression Segmentation,11,JHMDB; Referring Expressions for DAVIS 2016 & 2017; Refer-YouTube-VOS; CLEVR-Ref+; A2D Sentences; Google Refexp; DAVIS 2017; A2Dre+; RefCOCO; A2Dre,Refer-YouTube-VOS; ReferIt; Refer-YouTube-VOS (2021 public validation); RefCOCO testA; RefCOCO+ testA; Referring Expressions for DAVIS 2016 & 2017; RefCOCO testB; RefCOCO+ test B; RefCOCO+ val; G-Ref val,Precision@0.5; Overall IoU; Pr@0.7; Precision@0.8; Zero-Shot Transfer; IoU; IoU (%); IoU overall; J&F 1st frame; IoU mean,Weakly Supervised Referring Expression Segmentation; Generalized Referring Expression Segmentation,Computer Vision,"The task aims at labeling the pixels of an image or video that represent an object instance referred by a linguistic expression. In particular, the referring expression (RE) must allow the identificat..."
3D Face Reconstruction,11,FaceScape; ExPose; NoW Benchmark; REALY; FaMoS; FFHQ-UV; DAD-3DHeads; AFLW2000-3D; Florence; FaceWarehouse,13.8; !(()&&!|*|*|; REALY; Stirling-HQ (FG2018 3D face reconstruction challenge); NoW Benchmark; AFLW2000-3D; Florence; REALY (side-view); Stirling-LQ (FG2018 3D face reconstruction challenge),@mouth; Mean NME; RMSE Cooperative; @cheek; Median Reconstruction Error; @nose; all; Stdev Reconstruction Error (mm); Mean NME ; RMSE Indoor,Facial Recognition and Modelling,Computer Vision,**3D Face Reconstruction** is a computer vision task that involves creating a 3D model of a human face from a 2D image or a set of images. The goal of 3D face reconstruction is to reconstruct a digita...
Action Anticipation,11,CP2A dataset; EgoExoLearn; EPIC-KITCHENS-100; OST; Ego4D; DARai; MM-OR; Assembly101; TVSeries; VIENA2,EgoExoLearn; EPIC-KITCHENS-55 (Unseen test set (S2); EPIC-KITCHENS-100; EPIC-KITCHENS-55 (Seen test set (S1)); EPIC-KITCHENS-100 (test); Assembly101; 50-Salads; EGTEA,Top-5 Verb; Verbs Recall@5; Top 5 Accuracy - Act.; Top-1 Accuracy; Accuracy; Top 1 Accuracy - Verb; Top 1 Accuracy - Noun; Top 1 Accuracy - Act.; Top 5 Accuracy - Verb; Top 5 Accuracy - Noun,N/A,Computer Vision,"Next action anticipation is defined as observing 1, ... , T frames and predicting the action that happens after a gap of T_a seconds. It is important to note that a new action starts after T_a seconds..."
Bias Detection,11,BASIL; Grep-BiasIR; TwinViews-13k; NewB; WEATHub; Filipino CrowS-Pairs and Filipino WinoQueer; CLEAR-Bias; rt-inod-bias; CI-MNIST; StereoSet,PlantVillage_8px; ICAT LLM bias; Wiki Neutrality Corpus; rt-inod-bias; StereoSet,F1; SS; ICAT Score; LMS; Best-of; Accuracy (%),Selection bias,Natural Language Processing,"Bias detection is the task of detecting and measuring racism, sexism and otherwise discriminatory behavior in a model (Source: https://stereoset.mit.edu/)"
Medical Visual Question Answering,11,MedTrinity-25M; VQA-RAD; QUILT-1M; SLAKE; PMC-VQA; Kvasir-VQA; MedPromptX-VQA; MediConfusion; SLAKE-English; PathVQA,N/A,N/A,N/A,N/A,N/A
Image Manipulation,11,Satire Dataset; CASIA V2; CASIA (OSN-transmitted - Whatsapp); NIST (OSN-transmitted - Facebook); CASIA (OSN-transmitted - Weibo); CASIA (OSN-transmitted - Facebook); LRS2; CelebAMask-HQ; Digital Forensics 2023 dataset - DF2023; DSO (OSN-transmitted - Facebook),LRS2,SIFID (S3); LPIPS (S2); SIFID (S1); SIFID (S2); LPIPS (S5); LPIPS (S1); SIFID (S5); LPIPS (S4); SIFID (S4); LPIPS (S3),N/A,Computer Vision,"Image Manipulation is the process of altering or transforming an existing image to achieve a desired effect or to modify its content. This can involve various techniques and tools to enhance, modify, ..."
Dimensionality Reduction,11,SoF; CN-CELEB; HolStep; AtariARI; Deep Fakes Dataset; GoodSounds; ALGAD; STRING; Oxford-Affine; EMNIST,MCA; EMNIST,Classification Accuracy,Supervised dimensionality reduction; Online nonnegative CP decomposition,Methodology; Computer Vision,"Dimensionality reduction is the task of reducing the dimensionality of a dataset.    <span style=""color:grey; opacity: 0.6"">( Image credit: [openTSNE](https://github.com/pavlin-policar/openTSNE) )</sp..."
Event-based vision,11,CED; TUM-VIE; COESOT; MS-EVS Dataset; N-ImageNet; Robust e-NeRF Synthetic Event Dataset; Spike-X4K; FE108; RGB-DAVIS Dataset; EKubric,1 Megapixel Automotive Detection Dataset,mAP,Event-based Optical Flow; Event-based Motion Estimation; Event-Based Video Reconstruction,Computer Vision,"An event camera, also known as a neuromorphic camera, silicon retina or dynamic vision sensor, is an imaging sensor that responds to local changes in brightness. Event cameras do not capture images us..."
Material Recognition,11,MatSim; LAS&T: Large Shape & Texture Dataset; SpectroVision; MINC; LabPics; DMS; MCubeS; MatSeg; OpenSurfaces; Matador,N/A,N/A,N/A,Computer Vision,"Material recognition focuses on identifying classes, types, states, and properties of materials."
Visual Grounding,11,SkyEye-968k; A Game Of Sorts; VizWiz Answer Grounding; Mono3DRefer; VisArgs; DIOR-RSVG; AutomotiveUI-Bench-4K; SK-VG; Infinity-MM; MRR-Benchmark,RefCOCO+ val; RefCOCO+ testA; RefCOCO+ test B; RefCOCO testA,IoU; Accuracy (%),Phrase Extraction and Grounding (PEG); Person-centric Visual Grounding; 3D visual grounding,Computer Vision,"Visual Grounding (VG) aims to locate the most relevant object or region in an image, based on a natural language query. The query can be a phrase, a sentence, or even a multi-round dialogue. There are..."
Multi-Label Image Classification,10,VOC-MLT; Sewer-ML; BigEarthNet; Bengali.AI Handwritten Graphemes; dacl10k; MSCOCO; VizWiz-Classification; COCO (Common Objects in Context); LADI v2; COCO-MLT,BigEarthNet (official test set); BigEarthNet; VOC2007; VizWiz-Classification; MSCOCO; BigEarthNet-S1 (official test set); BigEarthNet-10%,mean average precision; mAP (macro); Accuracy; F1 Score; mAP (micro); official split; MAP; FScore; mAP,Multi-label Image Recognition with Partial Labels,Computer Vision,The Multi-Label Image Classification focuses on predicting labels for images in a multi-class classification problem where each image may belong to more than one class.
Human Part Segmentation,10,CIHP; PASCAL-Part; TikTok Dataset; VESSEL12; CCIHP; Human3.6M; MHP; Cityscapes Panoptic Parts; AeroPath; Pascal Panoptic Parts,CIHP; PASCAL-Part; MHP v2.0; ATR; Human3.6M; PASCAL-Person-Part,mIoU; Mean IoU; pACC,N/A,Computer Vision,N/A
Unsupervised Object Detection,10,Objects365; KITTI; Watercolor2k; LeukemiaAttri; UVO; PASCAL VOC 2007; LVIS; OpenImages-v6; Clipart1k; Comic2k,N/A,N/A,N/A,N/A,N/A
Table Detection,10,SciTSR; FUNSD; TNCR Dataset; IIIT-AR-13K; PubTables-1M; ICDAR 2019; TableBank; CISOL; STDW; ICDAR 2013,ICDAR2013; ICDAR 2019; STDW,IoU; Avg F1; Weighted Average F1-score; AP,N/A,Miscellaneous,Image credit:[Table Detection in the Wild: A Novel Diverse Table Detection Dataset and Method](https://paperswithcode.com/paper/table-detection-in-the-wild-a-novel-diverse)
Passage Retrieval,10,CSPRD; CoreSearch; PeerQA; DAPFAM; Natural Questions; EntityQuestions; QAMPARI; BEIR; GermanDPR; MS MARCO,N/A,N/A,N/A,N/A,N/A
Out of Distribution (OOD) Detection,10,ImageNet-1k vs NINCO; OpenImage-O; MUAD; SNIPS; RMOT-223; ATIS; Persian-ATIS; FathomNet2023; ImageNet-O; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Video Grounding,10,MAD; Kinetics; DTTD-Mobile; QVHighlights; Vript; STAR Benchmark; LongVALE; YouwikiHow; Animal Kingdom; Kinetics-GEB+,MAD; QVHighlights,"R@5,IoU=0.1; R@10,IoU=0.1; R@1,IoU=0.3; R@1,IoU=0.5; R@1,IoU=0.1; R@5,IoU=0.3; R@100,IoU=0.1; R@50,IoU=0.1; R@1,IoU=0.7",Video Narrative Grounding; Boundary Grounding,Computer Vision,"**Video grounding** is the task of linking spoken language descriptions to specific video segments. In video grounding, the model is given a video and a natural language description, such as a sentenc..."
Video Recognition,10,Crowd 11; Kinetics; Imbalanced-MiniKinetics200; 3DYoga90; MOD++; RAISE-LPBF; Win-Fail Action Understanding; MIDV-2019; BosphorusSign22k; L-SVD,N/A,N/A,N/A,N/A,N/A
Online Multi-Object Tracking,10,MOT17; MOT16; BEE23; PersonPath22; SportsMOT; MOT15; Oxford Town Center; MMPTRACK; MOTChallenge; CholecTrack20,N/A,N/A,N/A,N/A,N/A
Scene Segmentation,10,NYUv2; SUN RGB-D; UAVid; MovieNet; StreetHazards; Berkeley DeepDrive Video; USIS10K; Mila Simulated Floods; ScanNet; DARai,SUN-RGBD; UAVid; MovieNet; NYU Depth v2; StreetHazards; ScanNet,Average Accuracy; Category mIoU; AP; 3DIoU; Mean IoU; Open-mIoU,Thermal Image Segmentation,Computer Vision,Scene segmentation is the task of splitting a scene into its various object components.    Image adapted from [Temporally coherent 4D reconstruction of complex dynamic scenes](https://paperswithcode.c...
Face Swapping,10,FaceForensics++; DeeperForensics-1.0; WildDeepfake; DFDC; VGGFace2 HQ; Celeb-DF; VideoForensicsHQ; DFDM; AFLW2000-3D; HOD,N/A,N/A,N/A,N/A,N/A
Facial Attribute Classification,10,A View From Somewhere (AVFS); bFFHQ; UTKFace; FairFace; DiveFace; CelebV-HQ; LFWA; MORPH; LAOFIW Dataset; IMDB-Clean,N/A,N/A,N/A,N/A,N/A
Answer Selection,10,WikiQA; UDC; WikiHowQA; SelQA; WikiQAar; TrecQA; ASNQ; MilkQA; CICERO; InsuranceQA,N/A,N/A,N/A,N/A,N/A
LIDAR Semantic Segmentation,10,ULS labeled data; S.MID; Paris-Lille-3D; SemanticSTF; ScribbleKITTI; A Curb Dataset; nuScenes (Cross-City UDA); WildScenes; SemanticKITTI; nuScenes,ULS labeled data; S.MID; SemanticSTF; Paris-Lille-3D; SemanticKITTI; nuScenes,mIOU; val mIoU; Specificity; G-mean; Mean IoU; test mIoU; Binary Accuracy,N/A,Computer Vision,N/A
Visual Dialog,10,VisDial; Wizard of Wikipedia; GuessWhat?!; PhotoBook; Blended Skill Talk; Image-Chat; CLEVR-Dialog; EmpatheticDialogues; VisPro; ConvAI2,Wizard of Wikipedia; Image-Chat; VisDial v0.9 val; BlendedSkillTalk; EmpatheticDialogues; VisDial v1.0 test-std; Visual Dialog v1.0 test-std; ConvAI2,F1; R@10; NDCG (x 100); MRR (x 100); NDCG; Mean Rank; ROUGE-L; Mean; R@1; BLEU-4,N/A,Computer Vision,"Visual Dialog requires an AI agent to hold a meaningful dialog with humans in natural, conversational language about visual content. Specifically, given an image, a dialog history, and a follow-up que..."
Motion Estimation,10,Fisheye; DDD17; Retinal Microsurgery; SegTHOR; PST900; ADVIO; Headcam; X-ray and Visible Spectra Circular Motion Images Dataset; EV-IMO; VBR,N/A,N/A,N/A,Computer Vision,"**Motion Estimation** is used to determine the block-wise or pixel-wise motion vectors between two frames.      <span class=""description-source"">Source: [MEMC-Net: Motion Estimation and Motion Compens..."
Acoustic Scene Classification,10,TUT Acoustic Scenes 2017; DCASE 2016; MTG-Jamendo; TUT Sound Events 2018; LITIS Rouen; DCASE 2013; TAU Urban Acoustic Scenes 2019; TUT Urban Acoustic Scenes 2018; DCASE 2019 Mobile; CochlScene,TUT Acoustic Scenes 2017; TAU Urban Acoustic Scenes 2019; TUT Urban Acoustic Scenes 2018; DCASE 2019 Mobile; CochlScene,Acc; 1:1 Accuracy; Accuracy,N/A,Audio,The goal of acoustic scene classification is to classify a test recording into one of the provided predefined classes that characterizes the environment in which it was recorded.    Source: [DCASE 201...
Object Detection In Indoor Scenes,10,Transparent Object Images | Indoor Object Dataset; SUN RGB-D; Electronics Object Image Dataset | Computer Parts; Bottles and Cups Dataset | Household Objects; Stairs Image Dataset | Parts of House | Indoor; Kitchen Scenes; ISOD; Mobile Phone Dataset | Smartphone & Feature Phone; Suitcase/Luggage Dataset Indoor Object Image; Masks Dataset | Unattended Mask Images,N/A,N/A,N/A,N/A,N/A
Semantic SLAM,10,KITTI-360; TUM RGB-D; Bonn RGB-D Dynamic; DTTD-Mobile; ConsInv Dataset; ConSLAM; WildScenes; TorWIC; SLAM2REF; ViViD++,N/A,N/A,N/A,N/A,N/A
10-shot image generation,10,((Speak))How do I speak with someone at Expedia?; FQL-Driving; How do you request an upgrade to first class?; ####How do i ask a question at Expedia?; [[Ask!!Question]]How do I ask a question on Expedia?; FlyingThings3D; [FaQ's--Help]How do I speak to someone on Expedia?; Music21; MEAD; Gun Detection Dataset,FQL-Driving; .; FlyingThings3D; Music21; Babies; MEAD,FID; 12k; 0-shot MRR; 10-20% Mask PSNR; 0..5sec,Text to Video Retrieval; Talking Head Generation; Image Deblurring; Semantic Segmentation; Audio Super-Resolution,Music; Time Series; Knowledge Base; Computer Vision; Computer Code; Playing Games; Audio; Adversarial; Medical; Miscellaneous; Robots; Graphs,Generate 10 image of famous arts
Multiview Detection,10,CityStreet; Home Action Genome; MultiviewC; Three-view Synthetic data; DOLPHINS; MultiviewX; Wildtrack; CVCS; GMVD; RailEye3D Dataset,N/A,N/A,N/A,N/A,N/A
Remote Sensing Image Classification,10,Million-AID; DRIFT; OpenStreetMap Multi-Sensor Scene Classification; GDIT; WHU-RS19; RTI Rwanda Drone Crop Types; FireRisk; AID; HRPlanesV2; Remote Flash LiDAR Vehicles Dataset,FireRisk,Accuracy (%),Sentinel-1 SAR processing; Webcam (RGB) image classification,Computer Code,N/A
3D Depth Estimation,10,Pano3D; IBISCape; DTTD-Mobile; Relative Human; HUMAN4D; WinSyn; Aria Digital Twin Dataset; DurLAR; EUEN17037_Daylight_and_View_Standard_TestDataSet; DRACO20K,Relative Human,PCDR-Baby; PCDR; PCDR-Adult; mPCDK; PCDR-Kid; PCDR-Teen,Transparent Object Depth Estimation,Computer Vision,Image: [monodepth2](https://github.com/nianticlabs/monodepth2)
Font Recognition,10,MRR-Benchmark; VFR-Wild; Persian Text Image Segmentation (PTI SEG); AdobeVFR syn; Dafonts Free; AdobeVFR real; Persian Font Recognition (PFR); VFR-2420; VFR-447; Explor_all,Persian Text Image Segmentation (PTI SEG); VFR-Wild; AdobeVFR syn; AdobeVFR real; Persian Font Recognition (PFR); VFR-2420; VFR-447; Explor_all,IOU50; Top 5 Accuracy; Top 10 Accuracy; Top 5 Error Rate; Top 1 Accuracy; Top-1 Error Rate,N/A,Computer Vision,Font recognition (also called *visual font recognition* or *optical font recognition*) is the task of identifying the font family or families used in images containing text. Understanding which fonts ...
Time Series Anomaly Detection,10,ODDS; ATMs fault prediction; edeniss2020; FedTADBench; Paderbone University Bearing Fault Benckmark; MSL; SMAP; UCR Anomaly Archive; SMD; Consumer Spendings,MSL; SMAP; WADI; UCR Anomaly Archive; SWaT; SMD; Yahoo A1; KPI,AUPR; F1 Score; Recall; precision; accuracy; F1 score,N/A,Time Series,N/A
Within-Session Motor Imagery (left hand vs. right hand),10,Shin2017A MOABB; BNCI2014-001 MOABB; Schirrmeister2017 MOABB; Cho2017 MOABB; Zhou2016 MOABB; GrosseWentrup2009 MOABB; Weibo2014 MOABB; BNCI2014-004 MOABB; Lee2019-MI MOABB; PhysionetMotorImagery MOABB,N/A,N/A,N/A,N/A,N/A
Color Image Denoising,9,RENOIR; Darmstadt Noise Dataset; ImageNet; McMaster; BSD; Cell; CBSD68; Fingerprint inpainting and denoising; Urban100,N/A,N/A,N/A,N/A,N/A
Video Description,9,Flickr30k; ViDAS; ActivityNet Entities; YouCook; M-VAD Names; DeVAn; EDUB-Seg; VideoCC3M; TACoS Multi-Level Corpus,N/A,N/A,N/A,N/A,N/A
Unsupervised Semantic Segmentation,9,COCO-Stuff; ACDC (Adverse Conditions Dataset with Correspondences); Dark Zurich; SUIM; Nighttime Driving; KITTI-STEP; COCO (Common Objects in Context); ImageNet-S; Cityscapes,Potsdam-3; COCO-Stuff-81; ACDC (Adverse Conditions Dataset with Correspondences); ImageNet-S-50; Dark Zurich; SUIM; COCO-Stuff-27; Nighttime Driving; COCO-Stuff-3; Cityscapes val,Linear Classifier [mIoU]; Accuracy; Pixel Accuracy; mIoU (val); Clustering [Accuracy]; Linear Classifier [Accuracy]; mIoU (test); Clustering [mIoU]; mIoU; FCN [mIoU],Unsupervised Semantic Segmentation with Language-image Pre-training,Computer Vision,"Models that learn to segment each image (i.e. assign a class to every pixel) without seeing the ground truth labels.    <span style=""color:grey; opacity: 0.6"">( Image credit: [SegSort: Segmentation by..."
Conditional Image Generation,9,ImageNet-LT; Human-Art; Tiny ImageNet; CelebAMask-HQ; ArtBench-10 (32x32); CIFAR-100; COCO (Common Objects in Context); Large Labelled Logo Dataset (L3D); CIFAR-10,ImageNet-LT; ImageNet 256x256; ImageNet 128x128; Tiny ImageNet; CelebAMask-HQ; ArtBench-10 (32x32); CIFAR-100; ImageNet 64x64; COCO-Animals; CIFAR-10,IS; LPIPS; FID; Inception score; Inception Score; mIoU; Intra-FID,Human-Object Interaction Generation; Noisy Semantic Image Synthesis; Image-Guided Composition,Computer Vision,"Conditional image generation is the task of generating new images from a dataset conditional on their class.    <span style=""color:grey; opacity: 0.6"">( Image credit: [PixelCNN++](https://github.com/o..."
Interactive Segmentation,9,RClicks; DAVIS-585; ssTEM; PUMaVOS; SBD; DAVIS; COCO (Common Objects in Context); PASCAL VOC; Cityscapes,Rooftop; COCO minival; PascalVOC; GrabCut; DAVIS-585; ssTEM; DRIONS-DB; PASCAL2COCO(Unseen); Cityscapes val; SBD,NoC@90; NoC@80; NoC@85; Instance Average IoU; NoC@95,N/A,Computer Vision,N/A
Real-Time Object Detection,9,Kvasir-SEG; Kvasir-Instrument; Endotect Polyp Segmentation Challenge Dataset; Argoverse-HD; PASCAL VOC 2007; Kvasir; SFCHD; COCO (Common Objects in Context); Hyper-Kvasir Dataset,N/A,N/A,N/A,N/A,N/A
Robust Object Detection,9,Apron Dataset; Aircraft Context Dataset; Separated COCO; DWD; PASCAL VOC 2007; Occluded COCO; COCO (Common Objects in Context); RF100; Cityscapes,N/A,N/A,N/A,N/A,N/A
Edge Detection,9,CID; UDED; BSDS500; SBD; BIPED; BRIND; MDBD; INRIA-Horse; Cityscapes,CID; BSDS500; SBD; BIPED; Cityscapes test; BRIND; MDBD; UDED,F1; Number of parameters (M); ODS; AP; Maximum F-measure,N/A,Computer Vision,**Edge Detection** is a fundamental image processing technique which involves computing an image gradient to quantify the magnitude and direction of edges in an image. Image gradients are used in vari...
Scene Generation,9,OSM; CoDraw; VizDoom; KITTI; GoogleEarth; InstaOrder; Replica; 3D FRONT HUMAN; AVD,OSM; VizDoom; GoogleEarth; KITTI; Replica; AVD,Average FID; FID; KID; SwAV-FID; Camera Error; Depth Error,N/A,Computer Vision,make to t shirt an Ad with a little bit of action
Vehicle Re-Identification,9,VeRi-Wild; Vehicle-1M; Vehicle-Rear; VehicleID; VRAI; Car datasets in multiple scenes; VeRi-776; VehicleX; CityFlow,VeRi-Wild Large; VRAI test; VehicleID Small; VeRi-Wild Medium; VRAI test-dev; VeRi-Wild Small; VehicleID; VehicleID Large; VehicleID Medium; VeRi-776,Rank1; Rank-5; MAP; CMC10; Rank5; CMC1; CMC5; mAP; Rank-10; Rank-1,N/A,Computer Vision,"Vehicle re-identification is the task of identifying the same vehicle across multiple cameras.    <span style=""color:grey; opacity: 0.6"">( Image credit: [A Two-Stream Siamese Neural Network for Vehicl..."
Small Data Image Classification,9,UCF-Crime; ImageNet 50 samples per class; DEIC Benchmark; Ecoli; CIFAR-100; CUB-200-2011; TMED; WikiChurches; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Fine-Grained Visual Recognition,9,WHOI-Plankton; New Plant Diseases Dataset; MTL-AQA; WikiChurches; FeathersV1; Stanford Dogs; WebFG-496; CUB-200-2011; FGVC-Aircraft,N/A,N/A,N/A,N/A,N/A
Gait Recognition,9,TUM-GAID; CCGR; OUMVLP-Pose; OUMVLP; CASIA-B; Gait3D; USF; PsyMo; Gait3D-Parsing,Gait3D; OUMVLP,Averaged rank-1 acc(%); Rank-5; mINP; mAP; Rank-1,Multiview Gait Recognition; Gait Recognition in the Wild,Computer Vision,"<span style=""color:grey; opacity: 0.6"">( Image credit: [GaitSet: Regarding Gait as a Set for Cross-View Gait Recognition](https://github.com/AbnerHqC/GaitSet) )</span>"
6D Pose Estimation using RGBD,9,Drunkard's Dataset; LM; 6IMPOSE; T-LESS; YCB-Ev 1.1; YCB-Video; UW Indoor Scenes (UW-IS) Occluded dataset; REAL275; SLAM2REF,N/A,N/A,N/A,N/A,N/A
3D Shape Reconstruction,9,3D-POP; Foot3D; LAS&T: Large Shape & Texture Dataset; FewSOL; HUMAN4D; Hypersim; ApolloCar3D; 4DFAB; Pix3D,ApolloCar3D; Pix3D,EMD; IoU; CD; A3DP,3D Shape Reconstruction From A Single 2D Image,Computer Vision,"Image credit: [GSNet: Joint Vehicle Pose and Shape Reconstruction with Geometrical and Scene-aware Supervision  , ECCV'20](https://www.ecva.net/papers/eccv_2020/papers_ECCV/papers/123600511.pdf)"
Pedestrian Attribute Recognition,9,UPAR; PETA; PA-100K; DukeMTMC-attribute; PEARL30K; RAP; Market1501-Attributes; CAR; UAV-Human,UPAR; PETA; PA-100K; DukeMTMC-attribute; RAP; Market1501-Attributes; RAPv2; UAV-Human,UCS; Accuracy; Gender; Hat; UCC; Accuracy ; LCS; F1 score; LCC; Backpack,N/A,Computer Vision,"Pedestrian attribution recognition is the task of recognizing pedestrian features - such as whether they are talking on a phone, whether they have a backpack, and so on.    <span style=""color:grey; op..."
Action Quality Assessment,9,AQA-7; MTL-AQA; JIGSAWS; Rhythmic Gymnastic; EgoExoLearn; FineDiving; UI-PRMD; Fitness-AQA; Multimodal PISA,AQA-7; MTL-AQA; Rhythmic Gymnastic; JIGSAWS; EgoExoLearn; FineDiving; UI-PRMD; KIMORE,Accuracy; Spearman Correlation; RL2(*100); Average mean absolute error,N/A,Computer Vision,Assessing/analyzing/quantifying how well an action was performed.
Video Reconstruction,9,I2-2000FPS; VoxCeleb1; MVSEC; Videezy4K; MGif; SEN12MS-CR-TS; Tai-Chi-HD; Event-Camera Dataset; TED-talks,UVG; MVSEC; MGif; Tai-Chi-HD; Event-Camera Dataset; VoxCeleb; Tai-Chi-HD (256); TED-talks; Tai-Chi-HD (512),LPIPS; AKD; Average PSNR (dB); MKR; AED; Model Size (M); Mean Squared Error; L1,N/A,Computer Vision,"<span class=""description-source"">Source: [Deep-SloMo](https://github.com/avinashpaliwal/Deep-SloMo)</span>"
Trajectory Planning,9,inD Dataset; short-MetaWorld; ToolBench; uniD Dataset; exiD Dataset; rounD Dataset; highD Dataset; DARai; nuScenes,ToolBench; nuScenes,L2-3s; Collision-Avg; L2-2s; Win rate; L2-1s; L2-Avg; Collision-2s; Collision-3s; Collision-1s,Sokoban,Playing Games,Trajectory planning for industrial robots consists of moving the tool center point from point A to point B while avoiding body collisions over time.  Trajectory planning is sometimes referred to as mo...
Defect Detection,9,Regressors-Regressions Dataset; ISP-AD; Deep PCB; KolektorSDD; DAGM2007; KolektorSDD2; Honeycombs in Concrete; ARMBench; CodeXGLUE,N/A,N/A,N/A,N/A,N/A
Vision-Language Navigation,9,BnB; SDN; Talk the Walk; ReALFRED; ReaSCAN; R2R; XL-R2R; PInNED; TEACh,Room2Room,spl,Vision-Language-Action,Computer Vision; Robots,"Vision-language navigation (VLN) is the task of navigating an embodied agent to carry out natural language instructions inside real 3D environments.    <span style=""color:grey; opacity: 0.6"">( Image c..."
Dense Video Captioning,9,YouCook2; YouCook; MTL-AQA; Sieve & Swap - HowTo100M (Cooking); ActivityNet Captions; Vript; VidChapters-7M; LongVALE; ViTT,ActivityNet Captions; VidChapters-7M; YouCook2; ViTT,F1; DIV-1; DIV-2; ROUGE-L; BLEU-3; BLEU4; METEOR; CIDEr; SODA; Recall,Zero-shot dense video captioning,Computer Vision,"Most natural videos contain numerous events. For example, in a video of a “man playing a piano”, the video might also contain “another man dancing” or “a crowd clapping”. The task of dense video capti..."
Saliency Prediction,9,MSU Video Saliency Prediction; UIEB; iSUN; SUIM; CAT2000; AViMoS; Salient-KITTI; SALICON; CapMIT1003,CAT2000; MIT300; SALICON; SALECI,CC; AUC-Judd; IG; AUC; KLD; SIM; NSS; sAUC; KL,Few-Shot Transfer Learning for Saliency Prediction; Aerial Video Saliency Prediction,Computer Vision,A saliency map is a model that predicts eye fixations on a visual scene. Saliency prediction is informed by the human visual attention mechanism and predicts the possibility of the human eyes to stay ...
Blind Super-Resolution,9,BSD; BSD100; KID-F; Manga109; Set5; DIV2KRK; DRealSR; Set14; Urban100,N/A,N/A,N/A,N/A,N/A
Intrusion Detection,9,UNSW-NB15; Kitsune Network Attack Dataset; 20NewsGroups; CICIDS2017; IoT ENVIRONMENT DATASET; EDGE-IIOTSET; ARINC 429 Voltage Data; IoT Network Intrusion Dataset; CIC,UNSW-NB15; CIC-DDoS; ^(#$!@#$)(()))******; CIC-DoS; 20NewsGroups; CICIDS2017,F1 Score (Macro Avg); Precision (Macro Avg); Actions Top-1 (S2); Recall (Macro Avg); AUC; Accuracy (%); 0..5sec,Network Intrusion Detection,Miscellaneous; Natural Language Processing,"**Intrusion Detection** is the process of dynamically monitoring events occurring in a computer system or network, analyzing them for signs of possible incidents and often interdicting the unauthorize..."
Medical Report Generation,9,MedTrinity-25M; HistGen WSI-Report Dataset; IU X-Ray; CASIA-CXR; RaTE-NER; MIMIC-CXR; SMR IU X-Ray; PathVQA; LLaVA-Rad MIMIC-CXR Annotations,HistGen WSI-Report Dataset; IU X-Ray; MIMIC-CXR,ROUGE; BLEU-1; ROUGE-L; Micro-F1-5; Micro-Precision-5; METEOR; CIDEr; Example-F1-14; F1 RadGraph; Micro-Recall-5,N/A,Methodology; Medical,Medical report generation (MRG) is a task which focus on training AI to automatically generate professional report according the input image data. This can help clinicians make faster and more accurat...
Change Point Detection,9,TSSB; HASCD; Labelling for Explosions and Road accidents from UCF-Crime; Epinion; MOSAD; SKAB; Turing Change Point Dataset; CSTS; TEP,TEP; TSSB; SKAB,Relative Change Point Distance; Covering; NAB (LowFN); NAB (lowFP); NAB (standard),N/A,Time Series,**Change Point Detection** is concerned with the accurate detection of abrupt and significant changes in the behavior of a time series.    Change point detection is the task of finding changes in the ...
Surface Reconstruction,9,SynFoot; VIRDO Dataset; P2S; SceneNet; OmniObject3D; OMMO; ANIM; Foot3D; Stanford-ORB,N/A,N/A,N/A,N/A,N/A
Food Recognition,9,Recipe1M+; ChineseFoodNet; CNFOOD-241-Chen; CNFOOD-241; KenyanFood13; Indian Food Image Dataset; FoodX-251; ISIA Food-500; Food Image Classification Dataset,N/A,N/A,N/A,Computer Vision,N/A
Camera Localization,9,CrossLoc Benchmark Datasets; Aachen Day-Night; ZInd; ConSLAM; HPS; SpaGBOL; PEnG; 12 Scenes; SLAM2REF,Oxford RobotCar Full; Aachen Day-Night benchmark,"Acc @ 0.5m, 2°; Acc @ 5m, 10°; Acc @ 1m, 5°; Mean Rotation Error; Mean Translation Error",Camera Relocalization; Cross-View Geo-Localisation,Computer Vision,N/A
object-detection,9,Underwater Object Detection Dataset; Tea sickness - object detection; IP102; AODRaw; Br35H :: Brain Tumor Detection 2020; M5-Malaria Dataset; DiaMOS Plant; RF100; GenSC-6G,N/A,N/A,N/A,N/A,N/A
Sound Event Localization and Detection,9,BGG dataset; STARSS23; PodcastFillers; STARSS22; TAU-NIGENS Spatial Sound Events 2020; L3DAS22; TAU-NIGENS Spatial Sound Events 2021; RWCP Sound Scene Database; L3DAS21,PodcastFillers; STARSS22; TAU-NIGENS Spatial Sound Events 2021; RWCP Sound Scene Database; L3DAS21,Class-dependent localization error; location-dependent F1-score (macro); LE-CD; event-based F1 score; SELD score; Localization-dependent error rate (20°); ER≤20°; location-dependent F1-score (micro); Class-dependent localization recall; F1≤20°,N/A,Audio,"Given multichannel audio input, a sound event detection and localization (SELD) system outputs a temporal activation track for each of the target sound classes, along with one or more corresponding sp..."
3D Medical Imaging Segmentation,9,SKM-TEA; EPISURG; BHSD; MRSpineSeg Challenge; HaN-Seg; VerSe; KiTS19; RAD-ChestCT Dataset; ATLAS v2.0,TCIA Pancreas-CT,Dice Score,Pancreas Segmentation,Medical,"3D medical imaging segmentation is the task of segmenting medical objects of interest from 3D medical imaging.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Elastic Boundary Projection fo..."
Within-Session Motor Imagery (right hand vs. feet),9,BNCI2014-001 MOABB; Schirrmeister2017 MOABB; Zhou2016 MOABB; BNCI2014-002 MOABB; AlexandreMotorImagery MOABB; Weibo2014 MOABB; BNCI2015-004 MOABB; PhysionetMotorImagery MOABB; BNCI2015-001 MOABB,N/A,N/A,N/A,N/A,N/A
Zero-Shot Transfer Image Classification,8,ObjectNet; Food-101; ImageNet; ImageNet-Sketch; ImageNet-A; ImageNet-R; SUN; ImageNet-S,aYahoo; ObjectNet; Food-101; CN-ImageNet; ImageNet; ImageNet V2; CN-ImageNet-A; ImageNet-Sketch; ImageNet-R; ImageNet-A,Accuracy (Private); Top 5 Accuracy; Accuracy; Param; Top 1 Accuracy; Accuracy (Public),N/A,Computer Vision,N/A
3D Object Reconstruction,8,BEHAVE; A collection of 131 CT datasets of pieces of modeling clay containing stones; DTTD-Mobile; Corn Seeds Dataset; ShapeNet; X3D; The RBO Dataset of Articulated Objects and Interactions; Parcel3D,ShapeNet; Data3D−R2N2; RenderPeople; BEHAVE,Point-to-surface distance (cm); Chamfer (cm); Avg F1; Chamfer Distance; Surface normal consistency; 3DIoU,3D Object Reconstruction From A Single Image; Feature Splatting; Simulated Gaussian Manipulation; CAD Reconstruction,Computer Vision,Image: [Choy et al](https://arxiv.org/pdf/1604.00449v1.pdf)
Image-to-Text Retrieval,8,Flickr30k; WHOOPS!; LAION-400M; FewSOL; FETA Car-Manuals; XM 3600; COCO (Common Objects in Context); RSICD,Flickr30k; WHOOPS!; COCO; AIC-ICC; FETA Car-Manuals; COCO (Common Objects in Context); RSICD; RUC-CAS-WenLan,R@10; Recall@10; Recall@Sum; Image to Text Recall@1; Specificity; Recall@5; R@1; R@5; Recall@1,N/A,Natural Language Processing,"**Image-text retrieval** is the process of retrieving relevant images based on textual descriptions or finding corresponding textual descriptions for a given image. This task is interdisciplinary, com..."
Few-Shot Object Detection,8,VizWiz-FewShot; BankNote-Net; FSOD; LVIS; CAMO-FS; COCO (Common Objects in Context); ELEVATER; LOGO-Net,COCO 2017; LVIS v1.0 val; MS-COCO (1-shot); MS-COCO (10-shot); MS-COCO (5-shot); MS-COCO (30-shot); LVIS v1.0 test-dev; ODinW-35; ODinW-13; CAMO-FS,Average Score; APc; APr; AP50; box AP; APf; AP; AP75,Cross-Domain Few-Shot Object Detection,Computer Vision,**Few-Shot Object Detection** is a computer vision task that involves detecting objects in images with limited training data. The goal is to train a model on a few examples of each object class and th...
Layout-to-Image Generation,8,COCO-Stuff; LayoutBench-COCO - Combination; LayoutBench; COCO (Common Objects in Context); Visual Genome; LayoutBench-COCO - Number; LayoutBench-COCO - Size; LayoutBench-COCO - Position,N/A,N/A,N/A,N/A,N/A
Scene Flow Estimation,8,Argoverse 2; KITTI; DeformingThings4D; Middlebury 2003; FlyingThings3D; Spring; VBR; EKubric,Scene Flow; Spring; KITTI 2015 Scene Flow Training; KITTI 2015 Scene Flow Test; Argoverse 2,D2-all; Fl-all; EPE Foreground Dynamic; Runtime (s); D1-all; EPE 3-Way; EPE Background Static; 1px total; EPE Foreground Static;  Runtime (s),Self-supervised Scene Flow Estimation,Computer Vision,"Optical flow is a two-dimensional motion field in the image plane. It is the projection of the three-dimensional motion of the world. If the world is completely non-rigid, the motions of the points in..."
No-Reference Image Quality Assessment,8,LIVE; TID2013; SPAQ; UHD-IQA; KonIQ-10k; KADID-10k; MSU NR VQA Database; CSIQ,LIVE; TID2013; SPAQ; KADID-10k; UHD-IQA; KonIQ-10k; 200k Short Texts for Humor Detection; CSIQ,14 gestures accuracy; SRCC; PLCC,Blind Image Quality Assessment; NR-IQA,Computer Vision,An Image Quality Assessment approach where no reference image information is available to the model. Sometimes referred to as Blind Image Quality Assessment (BIQA).
Video-Based Person Re-Identification,8,MARS; AG-VPReID; TVPReid; iLIDS-VID; DukeMTMC-VideoReID; P-DESTRE; Airport; MARS-DL,N/A,N/A,N/A,N/A,N/A
Semantic correspondence,8,SPair-71k; PF-WILLOW; PF-PASCAL; AP-10K; FunKPoint; CUB-200-2011; AVMIT; Caltech-101,SPair-71k; PF-WILLOW; AP-10K; PF-PASCAL; CUB-200-2011; Caltech-101,LT-ACC; PCK (weak); IoU; IoU (weak); Mean PCK@0.05; LT-ACC (weak); Mean PCK@0.1; PCK,Interspecies Facial Keypoint Transfer,Computer Vision,The task of semantic correspondence aims to establish reliable visual correspondence between different instances of the same object category.
Visual Commonsense Reasoning,8,Visual Commonsense Immorality benchmark; IconQA; WHOOPS!; GD-VCR; ScienceQA; PolyMATH; VCR; QLEVR,N/A,N/A,N/A,N/A,N/A
Seizure Detection,8,Siena Scalp EEG Database; SPaRCNet; CHB-MIT; BIDS Siena Scalp EEG Database; SeizeIT1; TUH EEG Seizure Corpus; BIDS CHB-MIT Scalp EEG Database; A dataset of neonatal EEG recordings with seizures annotations,TUH EEG Seizure Corpus; CHB-MIT,AUROC; Accuracy,N/A,Medical,"**Seizure Detection** is a binary supervised classification problem with the aim of classifying between seizure and non-seizure states of a patient.   <span class=""description-source"">Source: [ResOT: ..."
Image/Document Clustering,8,JAFFE; warpPIE10P; Wine; australian; iris; pathbased; BA; pixraw10P,JAFFE; warpPIE10P; Wine; australian; iris; pendigits; BA; pixraw10P,NMI; Accuracy (%); runtime (s),Self-Organized Clustering,Miscellaneous; Computer Vision,N/A
Anomaly Detection In Surveillance Videos,8,UCF-Crime; ShanghaiTech Campus; VFP290K; UBI-Fights; ShanghaiTech; VADD; CamNuvem Dataset; XD-Violence,N/A,N/A,N/A,N/A,N/A
Video Instance Segmentation,8,YouTube-VIS 2019; BURST; UVO; BDD100K; HQ-YTVIS; Youtube-VIS 2022 Validation; OVIS; YouTube-VIS 2021,Youtube-VIS (trained with no video masks); BDD100K val; OVIS validation; HQ-YTVIS; Youtube-VIS 2022 Validation; YouTube-VIS validation; YouTube-VIS; YouTube-VIS 2021,mMOTSA; AR1; AR1_L; AP50; Tube-Boundary AP; AP; AP75; APmo; mask AP; mAP_L,N/A,Computer Vision,"The goal of video instance segmentation is simultaneous detection, segmentation and tracking of instances in videos. In words, it is the first time that the image instance segmentation problem is exte..."
Click-Through Rate Prediction,8,CBT; TripClick; KDD12; MovieLens; MerRec; KKBox; Criteo; iPinYou,Dianping; Amazon; Frappe; Huawei App Store; Android Malware Dataset; Avazu; MovieLens 20M; iPinYou; Children's Book Test Common noun; Amazon Dataset,F1; Accuracy; AUC; LogLoss; Log Loss,N/A,Miscellaneous,"Click-through rate prediction is the task of predicting the likelihood that something on a website (such as an advertisement) will be clicked.    <span style=""color:grey; opacity: 0.6"">( Image credit:..."
Unsupervised Video Object Segmentation,8,SegTrack-v2; FBMS; Referring Expressions for DAVIS 2016 & 2017; BL30K; DAVIS 2017; DAVIS 2016; FBMS-59; MOSE,N/A,N/A,N/A,N/A,N/A
Video Semantic Segmentation,8,ODMS; SegTrack-v2; CamVid; VSPW; VLOG Dataset; MOSE; Cityscapes; LaRS,Cityscapes val; Multispectral Video Semantic Segmentation; VSPW; CamVid; LaRS,F1; Q; μ; mIoU; Mean IoU,Camera shot segmentation,Computer Vision,The goal of video semantic segmentation is to assign a predefined class to each pixel in all frames of a video. This requires the model not only to predict accurate segmentation masks but also to ensu...
Weakly-Supervised Semantic Segmentation,8,ACDC Scribbles; SEN12MS; ScribbleSup; PASCAL VOC 2012 test; CheXlocalize; ScribbleKITTI; ADE20K; Cityscapes,N/A,N/A,N/A,N/A,N/A
3D Absolute Human Pose Estimation,8,[[Talk!!Person]]How do I get a person on Expedia?; SportsPose; InfiniteRep; TotalCapture; Human3.6M; HPS; Relative Human; [[Free@cancellation]]Is Expedia really free cancellation?,Surreal; Human3.6M; Total Capture,MRPE; Average MPJPE (mm); PA-MPJPE; MPJPE,Image to 3D; 3D Face Animation; 3D Human Shape Estimation; Text-to-Face Generation,Computer Vision; Robots; Knowledge Base,"This task aims to solve absolute (camera-centric not root-relative) 3D human pose estimation.     <span style=""color:grey; opacity: 0.6"">( Image credit: [RootNet](https://github.com/mks0601/3DMPPE_ROO..."
Human action generation,8,NTU RGB+D 120; NTU RGB+D 2D; FLAG3D; Human3.6M; NTU RGB+D; UESTC RGB-D; HumanAct12; PHSPD,NTU RGB+D 120; CMU Mocap; NTU RGB+D 2D; NTU RGB+D; Human3.6M; UESTC RGB-D; HumanAct12,Multimodality; FID (CV); MMDs (CS); FID; MMDs; MMDs (CV); FID (CS); Accuracy; MMDa (CS); MMDa,Action Generation,Computer Vision,"Yan et al. (2019) CSGN:    ""When the dancer is stepping, jumping and spinning on the  stage, attentions of all audiences are attracted by the streamof the fluent and graceful movements. Building a  mo..."
Retinal Vessel Segmentation,8,CHASE_DB1; DRIVE; HRF; ROSE; SMDG; UZLF; STARE; INSPIRE-AVR (LUNet subset),ROSE-1 SVC-DVC; CHASE_DB1; DRIVE; HRF; ROSE-1 DVC; ROSE-2; UZLF; STARE; INSPIRE-AVR (LUNet subset); ROSE-1 SVC,Dice Score; Accuracy; MCC; AUC; mIOU; Average Dice (0.5*Dice_a + 0.5*Dice_v); Average Dice; 1:1 Accuracy; Acc; Specificity,Artery/Veins Retinal Vessel Segmentation,Computer Vision; Medical,"Retinal vessel segmentation is the task of segmenting vessels in retina imagery.    <span style=""color:grey; opacity: 0.6"">( Image credit: [LadderNet](https://github.com/juntang-zhuang/LadderNet) )</s..."
Dynamic Link Prediction,8,Enron Emails; ML25m (TGN Style); DGraphFin (TGN Style); DBLP Temporal; WN18; Reddit; HeriGraph; Taobao (TGN Style),N/A,N/A,N/A,N/A,N/A
Human Interaction Recognition,8,SBU / SBU-Refine; UT-Interaction; NTU RGB+D 120; NTU RGB+D; EPIC-SOUNDS; H²O Interaction; NuiSI Dataset; H2O,SBU / SBU-Refine; UT-Interaction; SBU; NTU RGB+D 120; NTU RGB+D; EPIC-SOUNDS; BIT; UT,Accuracy (Cross-Subject); Accuracy (Cross-Setup); Accuracy; Accuracy (Cross-View); Accuracy (Set 2); Accuracy (Set 1); Top-1 accuracy %,Dense contact estimation; One-Shot 3D Action Recognition; Mutual Gaze,Computer Vision,"Human Interaction Recognition (HIR) is a field of study that involves the development of computer algorithms to detect and recognize human interactions in videos, images, or other multimedia content. ..."
Pose Prediction,8,G3D; Drunkard's Dataset; SportsPose; InfiniteRep; VR Mocap Dataset for Pose/Orientation Prediction; NTU RGB+D; Expi; VRMocap: VR Mocap Dataset for Pose Reconstruction,N/A,N/A,N/A,N/A,N/A
geo-localization,8,PDFM Embeddings; ShipSG; PEnG; GTA-UAV; University-1652; SpaGBOL; CV-Cities; DenseUAV,N/A,N/A,N/A,N/A,N/A
Image Matting,8,AIM-500; P3M-10k; AM-2K; withoutbg100 dataset; Agriculture-Vision; Distinctions-646; PPM-100; Composition-1K,Adobe Matting; AIM-500; P3M-10k; AM-2K; Distinctions-646; PPM-100; AMD; Composition-1K,MAD; SAD; Trimap; Conn.; MSE; Conn; Grad.; Grad,Semantic Image Matting,Computer Vision,"**Image Matting** is the process of accurately estimating the foreground object in images and videos. It is a very important technique in image and video editing applications, particularly in film pro..."
Colorectal Polyps Characterization,8,Kvasir-SEG; PolypGen; KvasirCapsule-SEG; Kvasir-Capsule; Kvasir; UNITOPATHO; Kvasir-Sessile dataset; CRC,N/A,N/A,N/A,N/A,N/A
Zero-shot Image Retrieval,8,XTD10; Flickr30k-CNA; X-TransferBench; WebLI; COCO-CN; QUILT-1M; ImageNet-R; simco-comco,N/A,N/A,N/A,N/A,N/A
Surgical phase recognition,8,SICS-155; GraSP; MultiBypass140; MM-OR; GJ; MISAW; Cholec80; HeiChole Benchmark,Cholec80; GraSP; HeiChole Benchmark; MISAW,Acc; F1; mAP,Offline surgical phase recognition; Online surgical phase recognition,Computer Vision,"The first 40 videos are used for training, the last 40 videos are used for testing."
Satellite Image Classification,8,WorldStrat; Cross-View Time Dataset; UV6K; SECO; NASA Worldview; DRIFT; Cross-View Time Dataset (Cross-Camera Split); HRPlanesV2,N/A,N/A,N/A,N/A,N/A
Single Image Dehazing,8,D-HAZY; RB-Dust; SQUID; NH-HAZE; I-HAZE; UIEB; RESIDE; SMOKE,N/A,N/A,N/A,N/A,N/A
3D Object Recognition,8,DOORS; Corn Seeds Dataset; SHREC; Cube++; The RBO Dataset of Articulated Objects and Interactions; ModelNet; CY101 Dataset; Washington RGB-D,N/A,N/A,N/A,N/A,N/A
Intrinsic Image Decomposition,8,FutureHouse; ShapeNet Intrinsic Images v1.0; IBL-NeRF; MID Intrinsics; MPI Sintel; ShapeNet Intrinsic Images v2.0 Extended; Hypersim; Doc3DShade,N/A,N/A,N/A,Computer Vision,"**Intrinsic Image Decomposition** is the process of separating an image into its formation components such as reflectance (albedo) and shading (illumination). Reflectance is the color of the object, i..."
Keyphrase Extraction,8,Keyphrases CS&Math Russian; KP20k; Krapivin; EUROPA; Inspec; NUS; KPTimes; SemEval-2017 Task-10,KP20k; Krapivin; Inspec; NUS; KPTimes; SemEval-2017 Task-10,Recall; F1@10,N/A,Natural Language Processing,"A classic task to extract salient phrases that best summarize a document, which essentially has two stages: candidate generation and keyphrase ranking."
Monocular Visual Odometry,8,EndoSLAM; Drunkard's Dataset; ConsInv Dataset; TUM monoVO; ConSLAM; VBR; Bike and Car Odometer Dataset ! Speedometer OCR; SLAM2REF,N/A,N/A,N/A,N/A,N/A
Ad-hoc video search,8,TRECVID-AVS21 (V3C1); TRECVID; TRECVID-AVS18 (IACC.3); TRECVID-AVS20 (V3C1); TRECVID-AVS17 (IACC.3); TRECVID-AVS19 (V3C1); TRECVID-AVS16 (IACC.3); IACC.3,TRECVID-AVS20 (V3C1); TRECVID-AVS18 (IACC.3); TRECVID-AVS17 (IACC.3); TRECVID-AVS19 (V3C1); TRECVID-AVS16 (IACC.3),infAP,N/A,Computer Vision,"The Ad-hoc search task ended a 3 year cycle from 2016-2018 with a goal to model the end user search use-case, who is searching (using textual sentence queries) for segments of video containing persons..."
Medical Image Registration,8,Niramai Oncho Dataset; BreastDICOM4; IXI; PPMI; OASIS; SR-Reg; Full-Spectral Autofluorescence Lifetime Microscopic Images; Learn2Reg,SR-Reg; OASIS+ADIBE+ADHD200+MCIC+PPMI+HABS+HarvardGSP; IXI; OASIS,Dice (Average); val dsc; Dice Score; DSC,BIRL; Diffeomorphic Medical Image Registration,Medical,"Image registration, also known as image fusion or image matching, is the process of aligning two or more images based on image appearances. **Medical Image Registration** seeks to find an optimal spat..."
Skin Lesion Classification,8,HAM10000; BCN_20000; SD-198; ISIC 2020 Challenge Dataset; PAD-UFES-20; MSK; MCSI; ISIC 2019,N/A,N/A,N/A,N/A,N/A
Brain Tumor Segmentation,8,BraTs Peds 2024; BraTS 2013; BraTS 2017; BraTS 2015; FeTS2022; BraTS 2014; BRISC; BraTS-Africa,N/A,N/A,N/A,N/A,N/A
Entity Alignment,8,Weibo-Douban; MMKG; DBP-5L (Greek); DBP2.0 zh-en; EventEA; UMVM; DBP1M FR-EN; DBP15K,DBP1M DE-EN; DICEWS-1K; FBDB15k; dbp15k ja-en; DBP2.0 zh-en; dbp15k fr-en; FBYG15k; YAGO-WIKI50K; DBP1M FR-EN; DBP15k zh-en,Hit@1; Entity Alignment (Consolidated) F1; Hits@1; dangling entity detection F1,Multi-modal Entity Alignment,Natural Language Processing; Knowledge Base,**Entity Alignment** is the task of finding entities in two knowledge bases that refer to the same real-world object. It plays a vital role in automatically integrating multiple knowledge bases.    No...
Gender Prediction,8,inaGVAD; UIT-ViNames; M-VAD Names; AgeDB; Age and Gender; BN-AuthProf; LAGENDA; IMDB-Clean,N/A,N/A,N/A,N/A,N/A
Image Forensics,8,Satire Dataset; StreetStyle; CASIA (OSN-transmitted - Whatsapp); NIST (OSN-transmitted - Facebook); CASIA (OSN-transmitted - Facebook); Celeb-DF; DSO (OSN-transmitted - Facebook); Columbia (OSN-transmitted - Facebook),N/A,N/A,N/A,Computer Vision,N/A
Colorization,8,ImageNet ctest10k; AnimeCeleb; SPair-71k; NCD; QST; SketchyScene; MHMD; NIR2RGB VCIP Challange Dataset,ImageNet ctest10k; ImageNet val,FID; FID-5K,Color Mismatch Correction; Line Art Colorization; Point-interactive Image Colorization,Computer Vision,"**Colorization** is the process of adding plausible color information to monochrome photographs or videos. Colorization is a highly undetermined problem, requiring mapping a real-valued luminance imag..."
Image Manipulation Localization,8,CASIA (OSN-transmitted - Weibo); Casia V1+; NIST (OSN-transmitted - Facebook); CASIA (OSN-transmitted - Whatsapp); COVERAGE; Digital Forensics 2023 dataset - DF2023; DSO (OSN-transmitted - Facebook); DIS100k,Casia V1+; COVERAGE(Protocol-CAT); Columbia(Protocol-CAT); CocoGlide; CASIAv1(Protoclo-CAT); COVERAGE; DSO-1; Columbia; NIST16(Protocol-CAT),Average Pixel F1(Fixed threshold); Pixel Binary F1,N/A,Computer Vision,"The task of segmenting parts of images or image parts that have been tampered with or manipulated (sometimes also referred to as doctored). This typically encompasses image splicing, copy-move, or ima..."
Product Recommendation,8,Logo-2K+; Exact Street2Shop; Coveo Data Challenge Dataset; WANDS; EXTREME CLASSIFICATION; OTTO Recommender Systems Dataset; VISUELLE2.0; LSEC,Coveo Data Challenge Dataset,F1; MRR,Context Aware Product Recommendation,Miscellaneous,N/A
Breast Cancer Detection,8,BRACS; BCI; CAMELYON16; BreastClassifications4; Breast Lesion Detection in Ultrasound Videos (CVA-Net); CBIS-DDSM; CMMD; BreakHis,N/A,N/A,N/A,N/A,N/A
Dialogue Act Classification,8,EMOTyDA; Switchboard-1 Corpus; SDN; Switchboard Dialog Act Corpus; Doc2Dial; MRDA; Emotional Dialogue Acts; CPED,N/A,N/A,N/A,N/A,N/A
Handwritten Digit Recognition,7,MNIST-MIX; MatriVasha:; NumtaDB; Burmese Handwritten Digit Dataset (BHDD); Digits; MNIST; DigiLeTs,N/A,N/A,N/A,N/A,N/A
Zero-shot Text-to-Image Retrieval,7,Flickr30k; ILIAS; COCO-CN; FewSOL; XM 3600; COCO (Common Objects in Context); COCO-Facet,N/A,N/A,N/A,N/A,N/A
Multi-Person Pose Estimation,7,COCO-WholeBody; PoPArt; PoseTrack; CrowdPose; MPII; COCO (Common Objects in Context); OCHuman,PoseTrack2018; WAF; COCO-WholeBody; COCO minival; COCO test-dev; Multi-Person PoseTrack; MPII Multi-Person; PoseTrack21; COCO; PoseTrack2017,AOP; AP Hard; APL; AP50; AR50; AP Medium; keypoint AP; AP; APM; AP75,Semi-Supervised Human Pose Estimation,Computer Vision,"Multi-person pose estimation is the task of estimating the pose of multiple people in one frame.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Human Pose Estimation with TensorFlow  ](htt..."
Unsupervised Semantic Segmentation with Language-image Pre-training,7,COCO-Stuff; KITTI-STEP; PASCAL VOC 2007; COCO (Common Objects in Context); ADE20K; PASCAL VOC; Cityscapes,N/A,N/A,N/A,N/A,N/A
Open Vocabulary Object Detection,7,Objects365; OVAD benchmark; Description Detection Dataset; OVIC Datasets; LVIS; MSCOCO; COCO (Common Objects in Context),Objects365; OpenImages-v4; MSCOCO; LVIS v1.0,AP novel-LVIS base training; AP novel-Unrestricted open-vocabulary training; AP 0.5; mask AP50,Open Vocabulary Attribute Detection,Computer Vision,Open-vocabulary detection (OVD) aims to generalize beyond the limited number of base classes labeled during the training phase. The goal is to detect novel classes defined by an unbounded  (open) voca...
Audio-Visual Speech Recognition,7,CAS-VSR-W1k (LRW-1000); MISP2021; LRS2; LRS3-TED; How2; CAS-VSR-S101; LRW,CAS-VSR-S101; LRW; LRS2; LRS3-TED,Top-1 Accuracy; Word Error Rate (WER); Test WER,Text to Speech,Speech,Audio-visual speech recognition is the task of transcribing a paired audio and visual stream into text.
Monocular 3D Object Detection,7,KITTI; SUN RGB-D; Mono3DRefer; Virtual KITTI; Virtual KITTI 2; OPV2V; nuScenes,N/A,N/A,N/A,N/A,N/A
Stereo Disparity Estimation,7,KITTI; Middlebury 2014; GUISS dataset; SERV-CT; Spring; VBR; L1BSR,Middlebury 2014; KITTI 2015; Scene Flow,one pixel error; D1-all; EPE; three pixel error; D1 Error (2px),Stereo Matching,Computer Vision,N/A
Medical Code Prediction,7,MIMIC-III; MIMIC-IV ICD-9; MIMIC-IV-ICD9-top50; MIMIC-IV-ICD10-top50; MIMIC-IV ICD-10; MIMIC-IV-ICD9-full; MIMIC-IV-ICD-10-full,MIMIC-III; MIMIC-IV ICD-9; MIMIC-IV-ICD9-top50; MIMIC-IV-ICD10-top50; MIMIC-IV ICD-10; MIMIC-IV-ICD9-full; MIMIC-IV-ICD-10-full,Micro-F1; Macro AUC; R-Prec; mAP; AUC Micro; Micro AUC; AUC (Macro); Macro-AUC; F1 Micro; Exact Match Ratio,N/A,Medical,Context: Prediction of medical codes from clinical notes is both a practical and essential need for every healthcare delivery organization within current medical systems. Automating annotation will sa...
Zero-Shot Video Retrieval,7,YouCook2; ActivityNet; MSR-VTT; VATEX; MSVD; LSMDC; DiDeMo,YouCook2; VATEX; MSR-VTT; ActivityNet; MSVD; MSR-VTT-full; LSMDC; DiDeMo,text-to-video Mean Rank; text-to-video Median Rank; text-to-video R@10; video-to-text R@1; text-to-video R@5; text-to-video R@1; video-to-text Median Rank; video-to-text R@10; video-to-text R@5,N/A,Computer Vision,Zero-shot video retrieval is the task of retrieving relevant videos based on a query (usually in text form) without any prior training on specific examples of those videos. Unlike traditional retrieva...
Text to Video Retrieval,7,MSVD-Indonesian; Sakuga-42M; Kinetics; MVK; MSR-VTT; ChinaOpen-1k; Kinetics-GEB+,MSVD-Indonesian; Kinetics-GEB+; MSR-VTT,R@10; text-to-video R@1; Mean Rank; mAP; text-to-video R@10; text-to-video R@5; Median Rank; R@1; R@5; text-to-video R@50,Partially Relevant Video Retrieval,Computer Vision,She's gone   I can't find her anywhere   I'm looking everywhere for her  Everywhere is dark
6D Pose Estimation using RGB,7,Fraunhofer IPA Bin-Picking; Drunkard's Dataset; LM; T-LESS; YCB-Video; UW Indoor Scenes (UW-IS) Occluded dataset; ApolloCar3D,N/A,N/A,N/A,N/A,N/A
Human Pose Forecasting,7,GTA-IM Dataset; HARPER; 3DPW; Human3.6M; Expi; PATS; AMASS,N/A,N/A,N/A,N/A,N/A
Face Identification,7,PetFace; LTFT; DroneSURF; IJB-A; MS-Celeb-1M; MegaFace; IJB-B,N/A,N/A,N/A,N/A,N/A
Traffic Sign Recognition,7,TopLogo-10; CURE-TSD; FlickrLogos-32; Tsinghua-Tencent 100K; BelgaLogos; CURE-TSR; GTSRB,N/A,N/A,N/A,N/A,N/A
Motion Segmentation,7,ApolloScape; KT3DMoSeg; KITTI'15 MSplus; HOI4D; MOD++; Hopkins155; EV-IMO,Hopkins155; ApolloScape; KT3DMoSeg; MTPV62,Classification Error; Accuracy; Error,N/A,Computer Vision,"**Motion Segmentation** is an essential task in many applications in Computer Vision and Robotics, such as surveillance, action recognition and scene understanding. The classic way to state the proble..."
Facial Expression Recognition,7,CMU-MOSEI; RAF-DB; FER2013; AffectNet; Aff-Wild2; MELD; FER+,CMU-MOSEI; RAF-DB; FER2013; AffectNet; Aff-Wild2; MELD; FER+,Accuracy (7 emotion); Overall Accuracy; Accuracy; Weighted Accuracy,Zero-Shot Facial Expression Recognition; Cross-Domain Facial Expression Recognition,Computer Vision,N/A
3D Shape Modeling,7,SynFoot; PartNet-Mobility; DrivAerNet; CADBench; PARIS Dataset; BlendNet; Pix3D,Pix3D S2; Pix3D S1,mesh AP; mask AP; box AP,N/A,Computer Vision,Image: [Gkioxari et al](https://arxiv.org/pdf/1906.02739v2.pdf)
Video Denoising,7,VideoLQ; I2-2000FPS; Videezy4K; SC_burst; CRVD; SEPE 8K; DAVIS,N/A,N/A,N/A,N/A,N/A
Camouflaged Object Segmentation,7,R2C7K; CAMO; Camouflaged Animal Dataset; COD10K; MoCA-Mask; NC4K; CAMO++,CAMO; COD; CHAMELEON; Camouflaged Animal Dataset; MoCA-Mask; NC4K; PCOD_1200,S-measure; mDice; MAE; weighted F-measure; S-Measure; mIoU; Weighted F-Measure,Camouflaged Object Segmentation with a Single Task-generic Prompt,Computer Vision,"Camouflaged object segmentation (COS) or Camouflaged object detection (COD), which was originally promoted by [T.-N. Le et al.](https://www.sciencedirect.com/science/article/abs/pii/S1077314219300608)..."
Electroencephalogram (EEG),7,K-EmoCon; SEED; SPaRCNet; mEBAL; CWL EEG/fMRI Dataset; MUTLA; ICLabel,HS-SSVEP; SEED-IV; 　SEED,Accuracy (5-fold); Accuracy,Attention Score Prediction; LWR Classification; EEG Denoising; Eeg Decoding; EEG,Methodology; Time Series; Medical,"**Electroencephalogram (EEG)** is a method of recording brain activity using electrophysiological indexes. When the brain is active, a large number of postsynaptic potentials generated synchronously b..."
Keyword Extraction,7,MAKED; Keyphrases CS&Math Russian; CSL (Chinese Scientific Literature); Inspec; KPTimes; SemEval-2017 Task-10; MPQA Opinion Corpus,SemEval-2017 Task-10; SemEval 2010 Task 8; Inspec,Precision@10; F1 score; Recall@10; Recall @ 10,N/A,Natural Language Processing,Keyword extraction is tasked with the automatic identification of terms that best describe the subject of a document (Source: Wikipedia).
3D Object Classification,7,Cyclone Data; UR5 Tool Dataset; ModelNet; CY101 Dataset; 3RScan; EUEN17037_Daylight_and_View_Standard_TestDataSet; Remote Flash LiDAR Vehicles Dataset,ModelNet40; Remote Flash LiDAR Vehicles Dataset; 3R-Scan; ModelNet10,mean average precision; Top-5 Accuracy; Accuracy; Classification Accuracy; Top-10 Accuracy,Generative 3D Object Classification; Cube Engraving Classification,Computer Vision,3D Object Classification is the task of predicting the class of a 3D object point cloud.  It is a voxel level prediction where each voxel is classified into a category. The popular benchmark for this ...
Camera Relocalization,7,Niantic Map-free Relocalization Dataset; DeepLoc; ConSLAM; Cambridge Landmarks; PEnG; University; SLAM2REF,N/A,N/A,camera absolute pose regression,Computer Vision,"""Camera relocalization, or image-based localization is a fundamental problem in robotics and computer vision. It refers to the process of determining camera pose from the visual scene representation a..."
Lesion Classification,7,HAM10000; BCN_20000; ISIC 2017 Task 2; MSK; ISIC 2017 Task 1; Retinal-Lesions; ISIC 2018 Task 1,N/A,N/A,N/A,N/A,N/A
Text Segmentation,7,CoNLL 2017 Shared Task - Automatically Annotated Raw Texts and Word Embeddings; CareerCoach 2022; CoNLL; SPMRL Hebrew segmentation data; UrduDoc; YTSeg; Wiki5K Hebrew segmentation,N/A,N/A,N/A,N/A,N/A
Natural Language Visual Grounding,7,MAD; ReferIt3D; METU-VIREF Dataset; Google Refexp; ImageCoDe; Spot-the-diff; ScreenSpot,ScreenSpot,Accuracy (%),N/A,Reasoning,N/A
Text Matching,7,DuLeMon; PSM; COVID-19 Twitter Chatter Dataset; Composed Quora; MedICaT; PolitiFact; Snopes,N/A,N/A,N/A,Natural Language Processing,Matching a target text to a source text based on their meaning.
Traffic Sign Detection,7,CURE-TSD; CCTSDB-AUG; aiMotive 3D Traffic Light and Traffic Sign Dataset; CVL Traffic Signs Dataset; CURE-TSR; TT100K; CCTSDB2021,CCTSDB-AUG; GTSDB; TT100K; CCTSDB2021,mAP@0.5; Averaged Precision; avg-mAP (0.1-0.5); mAP,N/A,Computer Vision,N/A
Gesture Generation,7,BiGe; LLMafia; DVS128 Gesture; Talking With Hands 16.2M; BEAT2; TED Gesture Dataset; BEAT,N/A,N/A,N/A,N/A,N/A
Hyperspectral Image Segmentation,7,HSI-Drive v2.0; HSIRS; LIB-HSI; Hyperspectral City; HyKo2-VIS; Tecnalia WEEE HYPERSPECTRAL DATASET; Hyper Drive,N/A,N/A,Hyperspectral,Computer Vision,N/A
Person Recognition,7,iCartoonFace; CN-Celeb-AV; MotionID: IMU all motions part1; MotionID: IMU all motions part2; MotionID: IMU all motions part3; PRW; MotionID: IMU specific motion,N/A,N/A,N/A,Computer Vision,N/A
3D Human Shape Estimation,7,HBW; SSP-3D; UNIPD-BPE; AGORA; MoVi; HUMAN4D; BEDLAM,N/A,N/A,N/A,N/A,N/A
Generalizable Novel View Synthesis,7,LLFF; RealEstate10K; ACID; NERDS 360; ZJU-MoCap; Spaces; Shiny dataset,N/A,N/A,N/A,N/A,N/A
Neural Rendering,7,FutureHouse; DONeRF: Evaluation Dataset; BUP20; MatrixCity; SyntheticFur; ThermoScenes; SB20,N/A,N/A,Neural Radiance Caching,Computer Vision,"Given a representation of a 3D scene of some kind (point cloud, mesh, voxels, etc.), the task is to create an algorithm that can produce photorealistic renderings of this scene from an arbitrary viewp..."
Gender Bias Detection,7,Grep-BiasIR; inaGVAD; BUG; Filipino CrowS-Pairs and Filipino WinoQueer; international faces; CI-MNIST; IMDB-Clean,N/A,N/A,N/A,Miscellaneous; Natural Language Processing,N/A
Table Recognition,7,PubTabNet; TNCR Dataset; WikiTableSet; PubTables-1M; CISOL; WTW; FinTabNet,ICDAR2013 table structure recognition; PubTabNet; Table Recognition Challenge mini-test; Table Recognition Challenge test; WTW,F1; TEDS (all samples); TEDS (simple samples); F-Measure; TEDS-Struct; TEDS (complex samples),N/A,Computer Vision,"Table recognition refers to the process of automatically identifying and extracting tabular structures from unstructured data sources such as text documents, images, or scanned documents. The goal of ..."
Fault Detection,7,IMS Bearing Dataset; Sound-based drone fault classification using multitask learning; Paderbone University Bearing Fault Benckmark; PRONOSTIA Bearing Dataset; Centrifugal Pump Fault Detection; PRONTO; Unbalance Classification Using Vibration Data,N/A,N/A,N/A,Miscellaneous,N/A
Vulnerability Detection,7,CVEfixes; VulScribeR; Vulnerable Verified Smart Contracts; RealVul; CASTLE Benchmark; IoTvulCode; Vulnerability Java Dataset,VulScribeR; Vulnerability Java Dataset,F1 Score; F1; AUC,N/A,Miscellaneous,Vulnerability detection plays a crucial role in safeguarding against these threats by identifying weaknesses and potential entry points that malicious actors could exploit. Through advanced scanning t...
Infrared And Visible Image Fusion,7,Uncorrelated Corrupted Dataset; LLVIP; Correlated Corrupted Dataset; AWMM-100k; InfraParis; ThermoScenes; NIR2RGB VCIP Challange Dataset,N/A,N/A,N/A,Computer Vision,Image fusion with paired infrared and visible images
Term Extraction,7,PET; PET: A new Dataset for Process Extraction from Natural Language Text; AWARE; IEE; RuTermEval (Track 1); RuTermEval (Track 2); RuTermEval (Track 3),SemEval 2014 Task 4 Laptop; AWARE,F1-Score,Nested Term Extraction; Nested Term Recognition,Natural Language Processing,"Term Extraction, or Automated Term Extraction (ATE), is about extraction domain-specific terms from natural language text.   For example, the sentence “We meta-analyzed mortality using random-effect m..."
MRI segmentation,7,SKM-TEA; BraTs Peds 2024; ABIDE; SMILE-UHURA; CUTS; UPenn-GBM; BRISC,N/A,N/A,Brain Tumor Classification,Computer Vision,N/A
Video Restoration,7,I2-2000FPS; Videezy4K; TAPE; BS-RSC; Vident-lab; SEPE 8K; VRDS,UVG; SEPE 8K,Average PSNR (dB),Analog Video Restoration,Computer Vision,N/A
2D Cyclist Detection,7,"[[FAQs~charge]]How much does Expedia charge for cancellation?; How to fix QuickBooks Error 6123, 0 – Causes & Solutions; How  to fix  QuickBooks Error 30159 – Causes & Fixes; [Travel@Guide® ]What is the 24 hour rule for KLM?; CTCyclistDetectionDataset; Why is Allegiant phone number busy?; CIMAT-Cyclist",N/A,N/A,N/A,N/A,N/A
Text-based Image Editing,7,GEdit-Bench-EN; PIE-Bench; NHR-Edit; ImgEdit; ManiCups; ImgEdit-Data; OIR-Bench,N/A,N/A,N/A,N/A,N/A
Image to Video Generation,7,ChronoMagic-Pro; DropletVideo-10M; ChronoMagic; OpenS2V-5M; ConsisID-preview-Data; ChronoMagic-ProH; OpenS2V-Eval,N/A,N/A,Open-Domain Subject-to-Video,Computer Vision,**Image to Video Generation** refers to the task of generating a sequence of video frames based on a single still image or a set of still images. The goal is to produce a video that is coherent and co...
Unsupervised Image Classification,6,ObjectNet; ImageNet; SVHN; MNIST; STL-10; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Structured Prediction,6,SciREX; CFQ; ListOps; WBM; MNIST; WIKIOG,MNIST,Negative CLL,N/A,Methodology,"**Structured Prediction** is an area of machine learning focusing on representations of spaces with combinatorial structure, and algorithms for inference and parameter estimation over these structures..."
Image Deblurring,6,Real Blur Dataset; ImageNet; CelebA; Leishmania parasite dataset; GoPro; HIDE,RealBlur-R; ImageNet; RealBlur-J; CelebA; RealBlur-R(trained on GoPro); GoPro; Real-world Dataset; HIDE (trained on GOPRO); HIDE,LPIPS; FID; SSIM; Params (M); PSNR,Low-light Image Deblurring and Enhancement,Computer Vision,file:///var/mobile/Library/SMS/Attachments/cb/11/8A26E2FC-7464-4692-8F2A-3F1B3E23BCD5/IMG_4630.jpeg
Lightweight Face Recognition,6,BTS3.1; LFW; CPLFW; IJB-C; CALFW; IJB-B,N/A,N/A,N/A,N/A,N/A
3D Part Segmentation,6,Teeth3DS+; LiDAR-MOS; IntrA; ShapeNet; TomoSAM; Workshop Tools Dataset,N/A,N/A,N/A,N/A,N/A
Real-time Instance Segmentation,6,KITTI; Multi30K; MSCOCO; MEIS; COCO (Common Objects in Context); Cityscapes,N/A,N/A,N/A,N/A,N/A
Zero-Shot Object Detection,6,RF100; PASCAL VOC 2007; LVIS; MSCOCO; COCO (Common Objects in Context); ELEVATER,N/A,N/A,N/A,N/A,N/A
Open World Object Detection,6,UVO; COCO-OOD; PASCAL VOC 2007; SFCHD; COCO-Mix; COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Talking Face Generation,6,VOCASET; AnimeCeleb; GLips; LRW; PASCAL VOC; CREMA-D,LRW; CREMA-D,FID; LSE-C; SSIM; LMD; EmoAcc,Face  Dubbing; Constrained Lip-synchronization,Computer Vision,"Talking face generation aims to synthesize a sequence of face images that correspond to given speech semantics      <span style=""color:grey; opacity: 0.6"">( Image credit: [Talking Face Generation by A..."
Stereo Depth Estimation,6,"Multi-Spectral Stereo Dataset  (RGB, NIR, thermal images, LiDAR, GPS/IMU); KITTI; GUISS dataset; Spring; VBR; Helvipad",KITTI2012; KITTI2015; Spring; KITTI 2015; sceneflow,1px total; D1-all All;  three pixel error; Average End-Point Error; three pixel error; D1-all Noc; EPE,Omnnidirectional Stereo Depth Estimation,Computer Vision,N/A
Egocentric Pose Estimation,6,xR-EgoPose; GlobalEgoMocap Test Dataset; SceneEgo; EgoPW-Scene; KITTI; UnrealEgo,N/A,N/A,N/A,N/A,N/A
Depth Prediction,6,"Multi-Spectral Stereo Dataset  (RGB, NIR, thermal images, LiDAR, GPS/IMU); KITTI; Matterport3D; Coastal Inundation Maps with Floodwater Depth Values; MagicBathyNet; Stanford-ORB",N/A,N/A,N/A,N/A,N/A
Zero-Shot Action Recognition,6,THUMOS14; Kinetics; ActivityNet; UCF101; Charades; HMDB51,Kinetics; ActivityNet; UCF101; THUMOS' 14; Charades; HMDB51; Olympics,Top-5 Accuracy; Accuracy; Top-1 Accuracy; Top-5 accuracy; mAP,N/A,Computer Vision,N/A
Few Shot Action Recognition,6,MOMA-LRG; Something-Something-100; Kinetics; UCF101; HMDB51; Kinetics-100,N/A,N/A,N/A,N/A,N/A
Scene Graph Detection,6,VRD; MOMA-LRG; Home Action Genome; ImageCLEF-DA; Haystack; Visual Genome,N/A,N/A,N/A,N/A,N/A
Open Vocabulary Semantic Segmentation,6,COCO-Stuff; iSAID; ISPRS Potsdam; ADE20K; PASCAL VOC; Cityscapes,PascalVOC-20b; PascalVOC-20; SIOR; iSAID; PASCAL Context-459; 	ADE20K-150; ADE20K-150; FAST; PASCAL Context-59; ISPRS Potsdam,mIoU; HIoU; hIoU; mIoU-,Zero-Guidance Segmentation,Computer Vision,Open-vocabulary semantic segmentation models aim to accurately assign a semantic label to each pixel in an image from a set of arbitrary open-vocabulary texts.
Face Sketch Synthesis,6,Multi-Modal CelebA-HQ; FS2K; CUFSF; SKSF-A; CUFS; CUHK03,N/A,N/A,N/A,N/A,N/A
Medical Named Entity Recognition,6,Species-800; The QUAERO French Medical Corpus; 2010 i2b2/VA; JNLPBA; BC2GM; RadGraph,N/A,N/A,N/A,N/A,N/A
Moment Retrieval,6,MAD; Charades-STA; QVHighlights; LongVALE; HiREST; Goal,QVHighlights; Charades-STA,mAP@0.5; R@1 IoU=0.5; mAP@0.75; R@5 IoU=0.7; R@1 IoU=0.3; R@1 IoU=0.7; mAP; mIoU; R@5 IoU=0.5,Zero-shot Moment Retrieval,Computer Vision,"Moment retrieval can de defined as the task of ""localizing moments in a video given a user query"".    Description from: [QVHIGHLIGHTS: Detecting Moments and Highlights in Videos via Natural Language Q..."
Grammatical Error Detection,6,Cleaned_Lang8; FCGEC; CoNLL; The Write & Improve Corpus 2024; JFLEG; FCE,N/A,N/A,N/A,N/A,N/A
Facial Emotion Recognition,6,JAFFE; HEADSET; MH-FED; CANDOR Corpus; Thermal Face Database; RAVDESS,N/A,N/A,N/A,N/A,N/A
Multimodal Activity Recognition,6,UTD-MHAD; Home Action Genome; UCSD Ped2; MSRDailyActivity3D; MMAct; UT-Kinect,N/A,N/A,N/A,N/A,N/A
Aesthetics Quality Assessment,6,RPCD; AADB; Image Aesthetics dataset; Aesthetic Visual Analysis; AVA; CADB,N/A,N/A,N/A,N/A,N/A
Spatio-Temporal Action Localization,6,JRDB-Act; Kinetics; VidHOI; AVA; LIRIS human activities dataset; MultiSports,N/A,N/A,N/A,N/A,N/A
3D Facial Landmark Localization,6,FER2013 Blendshapes; DAD-3DHeads; AFLW2000-3D; 3DFAW; H3WB; Urban Hyperspectral Image,N/A,N/A,N/A,N/A,N/A
Chinese Named Entity Recognition,6,CLUENER2020; Weibo NER; OntoNotes 4.0; MSRA CN NER; OntoNotes 5.0; Resume NER,N/A,N/A,N/A,N/A,N/A
Weakly-Supervised Named Entity Recognition,6,CoNLL++; CoNLL; CoNLL 2003; BC5CDR; ShARe/CLEF 2014: Task 2 Disorders; OntoNotes 5.0,N/A,N/A,N/A,N/A,N/A
Body Detection,6,DCM; Watercolor2k; DermSynth3D; Manga109; Clipart1k; Comic2k,N/A,N/A,N/A,N/A,N/A
Speaker Recognition,6,VoxCeleb1; CN-CELEB; MAVS; ASR-RAMC-BIGCCSC: A CHINESE CONVERSATIONAL SPEECH CORPUS; FKD; VGG-Sound,VoxCeleb1,EER,N/A,Speech,"**Speaker Recognition** is the process of identifying or confirming the identity of a person given his speech segments.   <span class=""description-source"">Source: [Margin Matters: Towards More Discrim..."
Medical Image Generation,6,ChestX-ray14; BCI; Leishmania parasite dataset; Kvasir-VQA; Chest X-Ray Images (Pneumonia); ACDC,ChestX-ray14; SLIVER07; ChestXray14 1024x1024; Chest X-Ray Images (Pneumonia); ACDC,FID; Frechet Inception Distance,Radiologist Binary Classification,Medical,"Medical image generation is the task of synthesising new medical images.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Towards Adversarial Retinal Image Synthesis](https://arxiv.org/pdf/1..."
3D Multi-Object Tracking,6,3D-ZeF; nuScenes LiDAR only; Waymo Open Dataset; CVB; MOTFront; nuScenes,Waymo Open Dataset: Vehicle (Online Methods); nuScenes Camera Only; Waymo Open Dataset; nuScenes LiDAR only; nuscenes Camera-Radar; nuScenes,FP/L2; MOTA/L1; MOTA; Recall; MOTA/L2; AMOTA,N/A,Computer Vision,Image: [Weng et al](https://arxiv.org/pdf/1907.03961v4.pdf)
Sleep Stage Detection,6,ISRUC-Sleep; Montreal Archive of Sleep Studies; SHHS; PhysioNet Challenge 2018; MaSS; Sleep-EDF,DODH; MASS (single-channel); ISRUC-Sleep; ISRUC-Sleep (single-channel); Montreal Archive of Sleep Studies; Sleep-EDFx; SHHS; PhysioNet Challenge 2018 (single-channel); SHHS (single-channel); MASS SS3,Kappa; Accuracy; Cohen's Kappa; Cohen's kappa; Macro-averaged Accuracy; Macro-F1; AUROC,Sleep Staging,Medical,Human Sleep Staging into W-N1-N2-N3-REM classes from multiple or single polysomnography signals
Unified Image Restoration,6,BSD; Synthetic Rain Datasets; RESIDE; GoPro; AWMM-100k; LOL,BSD68 sigma25; RESIDE; GoPro; Rain100L; LOL,Average PSNR (dB),Blind All-in-One Image Restoration,Computer Vision,Using a single model to restore inputs with different degradation types.
Image-Based Localization,6,Cross-View Time Dataset; University-1652; Cross-View Time Dataset (Cross-Camera Split); VIGOR; NAVER LABS Localization Datasets; SpaGBOL,N/A,N/A,N/A,N/A,N/A
3D Point Cloud Classification,6,Teeth3DS+; ScanObjectNN; IntrA; ModelNet; Sydney Urban Objects; ModelNet40-C,ModelNet40; ScanObjectNN; IntrA; Sydney Urban Objects; ModelNet40-C,OBJ-BG (OA); F1 score (5-fold); Mean class accuracy; Number of params; F1; Overall Accuracy; Error Rate; Mean Accuracy; OBJ-ONLY (OA); FLOPs,Supervised Only 3D Point Cloud Classification; Zero-Shot Transfer 3D Point Cloud Classification; Few-Shot 3D Point Cloud Classification; 3D Object Classification,Computer Vision,N/A
Visual Speech Recognition,6,Watch Your Mouth: Point Clouds based Speech Recognition Dataset; CAS-VSR-W1k (LRW-1000); AV Digits Database; LRS2; LRS3-TED; GLips,LRS2; LRS3-TED,Word Error Rate (WER),Lip to Speech Synthesis,Computer Vision; Music; Speech,N/A
Line Segment Detection,6,Wireframe; ICDAR 2021; York Urban Line Segment Database; BN-HTRd; WaterScenes; KAIST Urban,wireframe dataset; York Urban Dataset,sAP5; FH; sAP15; sAP10,N/A,Computer Vision,N/A
3D Face Animation,6,Biwi 3D Audiovisual Corpus of Affective Communication - B3D(AC)^2; FEAFA+; Human Optical Flow; BEAT2; VOCASET; PASCAL VOC,Biwi 3D Audiovisual Corpus of Affective Communication - B3D(AC)^2; VOCASET; BEAT2,Lip Vertex Error; FDD; MSE,Video Super-Resolution,Computer Vision; Playing Games,Image: [Cudeiro et al](https://arxiv.org/pdf/1905.03079v1.pdf)
Dictionary Learning,6,FMD; Extended Yale B; Partial-REID; SALSA; Partial-iLIDS; LoDoPaB-CT,N/A,N/A,N/A,Methodology,"**Dictionary Learning** is an important problem in multiple areas, ranging from computational neuroscience, machine learning, to computer vision and image processing. The general goal is to find a goo..."
Robust Face Recognition,6,FacesInThings; DroneSURF; OCFR-LFW; UFDD; MALF; MeGlass,N/A,N/A,N/A,N/A,N/A
Stereo Matching Hand,6,ICubWorld; CATS; Middlebury 2001; Middlebury 2006; Middlebury 2005; PhotoTour,N/A,N/A,N/A,Computer Vision,N/A
Zero-Shot Image Classification,6,MineralImage5k; OVIC Datasets; Corn Seeds Dataset; QUILT-1M; Country211; TaxaBench-8k,ODinW; Country211; ICinW,Top-1 accuracy; Average Score,Open Vocabulary Image Classification,Computer Vision,Zero-shot image classification is a technique in computer vision where a model can classify images into categories that were not present during training. This is achieved by leveraging semantic inform...
Scene Change Detection,6,ChangeVPR; ChangeSim; MSU Shot Boundary Detection Benchmark; Unaligned-VL-CMU-CD (neighbor distance 2); 3RScan; PCD,ChangeVPR; VL-CMU-CD; ChangeSim; Semantic Scene Understanding Challenge (passive actuation & ground-truth localisation); Unaligned-VL-CMU-CD (neighbor distance 2); PCD,avg_fp_quality; F1-score; avg_state_quality; Category mIoU; OMQ; macro F1; avg_spatial; avg_pairwise; F1 score; avg_label,N/A,Computer Vision,Scene change detection (SCD) refers to the task  of localizing changes and identifying change-categories given two scenes. A scene can be either an RGB (+D) image or a 3D reconstruction (point cloud)....
Video Enhancement,6,I2-2000FPS; LDV; MFQE v2; Vident-lab; SEPE 8K; BVI-DVC,MFQE v2,Incremental PSNR; Parameters(M),N/A,Computer Vision,N/A
Gaze Prediction,6,GOO; OST; EyeInfo; EgoMon; OpenEDS2020; EGO-CH-Gaze,N/A,N/A,N/A,Computer Vision,N/A
Emotion Recognition in Context,6,KD-EmoR; EMOTIC; BoLD; CAER; CAER-Dynamic; Emotional Dialogue Acts,N/A,N/A,N/A,N/A,N/A
Composed Image Retrieval (CoIR),6,WebVid-CoVR; LaSCo; CIRR; Fashion IQ; CIRCO; PatternCom,CIRR; Fashion IQ,R@10; R@50; R@1; R@5; (Recall@10+Recall@50)/2,Zero-Shot Composed Image Retrieval (ZS-CIR),Computer Vision,"**Composed Image Retrieval (CoIR)** is the task involves retrieving images from a large database based on a query composed of multiple elements, such as text, images, and sketches. The goal is to deve..."
CAD Reconstruction,6,Fusion 360 Gallery; Text2CAD; CC3D; DeepCAD; CADBench; BlendNet,N/A,N/A,N/A,N/A,N/A
Autonomous Navigation,6,MIDGARD; RELLIS-3D; JRDB; LDDRS; Hyper Drive; IN2LAAMA,N/A,N/A,Sequential Place Recognition; Autonomous Web Navigation; Autonomous Flight (Dense Forest),Computer Vision; Reasoning; Robots,"Autonomous navigation is the task of autonomously navigating a vehicle or robot to or around a location without human guidance.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Approximate L..."
Single Image Deraining,6,RainCityscapes; Synthetic Rain Datasets; Real Rain Dataset; HRI; AWMM-100k; Raindrop,N/A,N/A,N/A,N/A,N/A
Abstractive Dialogue Summarization,6,SummZoo; ICSI Meeting Corpus; SAMSum; AMI Meeting Corpus; ELITR Minuting Corpus; ConvoSumm,N/A,N/A,N/A,N/A,N/A
The Semantic Segmentation Of Remote Sensing Imagery,6,SAMRS; UV6K; Five-Billion-Pixels; MSAW; DRIFT; GID,MSAW; UV6K,IoU (%); F1 score,Lake Ice Monitoring,Computer Vision,N/A
Trajectory Modeling,6,inD Dataset; NBA SportVU; uniD Dataset; exiD Dataset; rounD Dataset; highD Dataset,NBA SportVU,1x1 NLL,N/A,Time Series,The equivalent of language modeling but for trajectories.
Video Panoptic Segmentation,6,KITTI-STEP; MM-OR; Cityscapes-VPS; 4D-OR; VIPSeg; LaRS,N/A,N/A,N/A,N/A,N/A
Video-Text Retrieval,6,SYMON; Youku-mPLUG; Test-of-Time; WebVid; wonderbread; VTC,N/A,N/A,N/A,N/A,N/A
Multiple People Tracking,6,LTFT; PersonPath22; SOMPT22; MMPTRACK; Crowd in a rally | Crowd Counting | Crowd Human; 2024 AI City Challenge,N/A,N/A,N/A,Computer Vision,N/A
Object SLAM,6,ConsInv Dataset; ConSLAM; TorWIC; CODD; YCB-Slide; SLAM2REF,N/A,N/A,N/A,N/A,N/A
Medical Object Detection,6,SKM-TEA; GRAZPEDWRI-DX; DeepLesion; MP-IDB; RF100; ENSeg,N/A,N/A,N/A,N/A,N/A
Attribute Value Extraction,6,MAVE; OA-Mine - annotations; Caselaw4; AE-110k; WDC-PAVE; MAVE - Attribute: Black Tea Variety,OA-Mine - annotations; AE-110k; MAVE; WDC-PAVE,F1-Score; F1-score,N/A,Natural Language Processing,**Attribute Value Extraction** is the task of extracting values for a given set of attributes of interest from free text input. Attribute value extraction is for example applied in the context of e-co...
Within-Session Motor Imagery (all classes),6,BNCI2014-001 MOABB; Schirrmeister2017 MOABB; Zhou2016 MOABB; AlexandreMotorImagery MOABB; Weibo2014 MOABB; PhysionetMotorImagery MOABB,N/A,N/A,N/A,N/A,N/A
Automatic Phoneme Recognition,6,VibraVox (soft in-ear microphone); VibraVox (throat microphone); VibraVox (headset microphone); VibraVox (temple vibration pickup); VibraVox (forehead accelerometer); VibraVox (rigid in-ear microphone),N/A,N/A,N/A,N/A,N/A
Unsupervised Anomaly Detection with Specified Settings -- 20% anomaly,5,CIFAR-10; MNIST; STL-10; Fashion-MNIST; Cats and Dogs,cifar10; MNIST; STL-10; Fashion-MNIST; Cats and Dogs,AUC-ROC,N/A,Computer Vision,N/A
Unsupervised Anomaly Detection with Specified Settings -- 1% anomaly,5,CIFAR-10; MNIST; STL-10; Fashion-MNIST; Cats and Dogs,CIFAR-10; MNIST; STL-10; Fashion-MNIST; Cats and Dogs,AUC-ROC,N/A,Computer Vision,N/A
Unsupervised Anomaly Detection with Specified Settings -- 0.1% anomaly,5,CIFAR-10; MNIST; STL-10; Fashion-MNIST; Cats and Dogs,CIFAR-10; MNIST; STL-10; Fashion-MNIST; Cats and Dogs,AUC-ROC,N/A,Computer Vision,N/A
Unsupervised Anomaly Detection with Specified Settings -- 10% anomaly,5,CIFAR-10; MNIST; STL-10; Fashion-MNIST; Cats and Dogs,CIFAR-10; MNIST; STL-10; Fashion-MNIST; Cats and Dogs,AUC-ROC,N/A,Computer Vision,N/A
Blind Face Restoration,5,LFW; CelebA; Wider-Test-200; CelebA-HQ; WIDER,CelebA-Test; CelebA-HQ; LFW; WIDER,NIQE; LPIPS; FID; Deg.; SSIM; PSNR; IDS,N/A,Computer Vision,"Blind face restoration aims at recovering high-quality faces from the low-quality counterparts suffering from unknown degradation, such as low-resolution, noise, blur, compression artifacts, etc. When..."
3D Face Modelling,5,Voxceleb-3D; LFW; FaMoS; THVD; Human Optical Flow,Voxceleb-3D; LFW,ARE-MR; Mean ARE; ARE-FR; ARE-ER; ARE-CR; 1-of-100 Accuracy,Facial Recognition and Modelling; Continuous Control,Computer Vision; Playing Games; Medical,N/A
Zero-Shot Cross-Modal Retrieval,5,Flickr30k; ImageNet_CN; Earth on Canvas; IMPACT Patent; COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
JPEG Artifact Correction,5,LIVE (Public-Domain Subjective Image Quality Database); DIV2K; BSDS500; Classic5; ICB,N/A,N/A,N/A,N/A,N/A
Stereo Image Super-Resolution,5,KITTI; Flickr1024; Middlebury; VBR; L1BSR,N/A,N/A,N/A,N/A,N/A
Unsupervised Panoptic Segmentation,5,KITTI; Waymo Open Dataset; BDD100K; MUSES: MUlti-SEnsor Semantic perception dataset; Cityscapes,COCO val2017; KITTI; BDD100K val; Waymo Open Dataset; MUSES: MUlti-SEnsor Semantic perception dataset; Cityscapes,SQ; PQ; RQ,Unsupervised Zero-Shot Panoptic Segmentation,Computer Vision,**Unsupervised Panoptic Segmentation** aims to partition an image into semantically meaningful regions and distinct object instances without training using any manually annotated images.  <span style=...
Unsupervised Monocular Depth Estimation,5,KITTI-C; KITTI; CamlessVideosFromTheWild; WeatherKITTI; Cityscapes,N/A,N/A,N/A,N/A,N/A
Self-Supervised Action Recognition,5,Kinetics-600; Kinetics; UCF101; HMDB51; Kinetics 400,N/A,N/A,N/A,N/A,N/A
Visual Relationship Detection,5,VRD; TexRel; Visual Relationship Detection Dataset; Visual Genome; Open Images V7,VRD; VRD Phrase Detection; VRD Predicate Detection; VRD Relationship Detection; Visual Genome,mR@50; mR@100; R@100; R@50; R@50 k=1,Video Visual Relation Detection; Human-Object Relationship Detection,Computer Vision,Visual relationship detection (VRD) is one newly developed computer vision task aiming to recognize relations or interactions between objects in an image. It is a further learning task after object re...
Sketch-to-Image Translation,5,COCO-Stuff; FS2K; SketchyCOCO; SKSF-A; Scribble,COCO-Stuff; Scribble; SketchyCOCO,FID-C; FID; Human (%); Accuracy,N/A,Computer Vision,N/A
Pose Transfer,5,CelebAMask-HQ; Market-1501; DeepFashion; ADE20K; iDesigner,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Action Localization,5,THUMOS14; ActivityNet; BEOID; FineAction; GTEA,THUMOS14; ActivityNet-1.3; THUMOS' 14; THUMOS’14; BEOID; THUMOS 2014; FineAction; GTEA; ActivityNet-1.2,mAP@0.5; mAP@0.1:0.5; avg-mAP (0.1:0.7); mAP IOU@0.75; mAP IOU@0.95; mAP IOU@0.5; mAP@0.1:0.7; avg-mAP (0.3-0.7); avg-mAP (0.1-0.5); mAP,N/A,Computer Vision,"In this task, the training data consists of videos with a list of activities in them without any temporal boundary annotations. However, while testing, given a video, the algorithm should recognize th..."
Oriented Object Detection,5,SODA-A; DOTA 2.0; TimberVision; EVD4UAV; DOTA,N/A,N/A,N/A,N/A,N/A
Aspect Extraction,5,Casino Reviews; SemEval-2014 Task-4; YASO; ROAST; Yelp,SemEval-2014 Task-4; SemEval 2016 Task 5 Sub Task 1 Slot 2; SemEval 2014 Task 4 Sub Task 1;  SemEval 2015 Task 12; YASO - YELP; SemEval 2015 Task 12,F1; Mean F1 (Laptop + Restaurant); Laptop (F1); Restaurant (F1),Hidden Aspect Detection; Latent Aspect Detection,Natural Language Processing,"Aspect extraction is the task of identifying and extracting terms relevant for opinion mining and sentiment analysis, for example terms for product attributes or features."
Sports Ball Detection and Tracking,5,Volleyball; Badminton; Basketball; Tennis; Soccer,N/A,N/A,N/A,N/A,N/A
Symmetry Detection,5,Symmetric Solids; NYU Symmetry Database; YCB-Video; AvaSym; Symbrain,YCB-Video,PR AUC,N/A,Computer Vision,N/A
Interactive Video Object Segmentation,5,BL30K; PUMaVOS; DAVIS 2017; DAVIS; MOSE,N/A,N/A,N/A,N/A,N/A
Monocular 3D Human Pose Estimation,5,HBW; AGORA; 3DOH50K; Human3.6M; H3WB,N/A,N/A,N/A,N/A,N/A
2D Panoptic Segmentation,5,depression interview dataset; [[Easy~refund]]How can i get a refund from expedia; MM-OR; ScanNet; 4D-OR,4D-OR; ScanNetV2; MM-OR,VPQ; PQ,Unsupervised Panoptic Segmentation,Computer Vision,N/A
Zero-shot 3D Point Cloud Classification,5,ScanObjectNN; ModelNet40 (Pretrained on ShapeNet); OmniObject3D; ModelNet; ScanNet,N/A,N/A,N/A,N/A,N/A
Facial Inpainting,5,FFHQ; MFSD; DREAMING Inpainting Dataset; VGGFace2; CASIA-WebFace,N/A,N/A,N/A,N/A,N/A
Optic Disc Segmentation,5,SMDG; REFUGE Challenge; ADAM; MESSIDOR; STARE,N/A,N/A,N/A,N/A,N/A
Aspect Category Detection,5,Casino Reviews; SemEval-2014 Task-4; ROAST; AWARE; FABSA,SemEval-2014 Task-4; SemEval 2014 Task 4 Subtask 3; AWARE; Citysearch,F-measure (%); NDCG; Hit@5; MRR; Recall; Average Recall; Precision; F1 score; F1-score,N/A,Natural Language Processing,Aspect category detection (ACD) in sentiment analysis aims to identify the aspect categories mentioned in a sentence.
Grasp Contact Prediction,5,ContactPose; GAS; A Billion Ways to Grasp; ContactDB; GRAB,N/A,N/A,N/A,N/A,N/A
Natural Language Moment Retrieval,5,MAD; ActivityNet Captions; LongVALE; DiDeMo; TACoS Multi-Level Corpus,N/A,N/A,N/A,N/A,N/A
3D Multi-Person Pose Estimation,5,Panoptic; Store dataset; MuPoTS-3D; Campus & Shelf; AGORA,Panoptic; MuPoTS-3D; AGORA; Shelf; Campus,B-MVE; B-NMJE; 3DPCK; PCP3D; Average MPJPE (mm); MPJPE; B-NMVE; B-MPJPE; Mean mAP,3D Multi-Person Pose Estimation (root-relative); 3D Multi-Person Pose Estimation (absolute); 3D Multi-Person Mesh Recovery,Computer Vision,"This task aims to solve root-relative 3D multi-person pose estimation. No human bounding box and root joint coordinate groundtruth are used in testing time.    <span style=""color:grey; opacity: 0.6"">(..."
Face Presentation Attack Detection,5,OULU-NPU; CASIA-SURF; Replay-Mobile; WMCA; SWAX,N/A,N/A,N/A,N/A,N/A
Joint Demosaicing and Denoising,5,REDS; Videezy4K; McMaster; BSD100; Urban100,N/A,N/A,N/A,N/A,N/A
Motion Planning,5,ThreeDWorld Transport Challenge; Motion Policy Networks; PushWorld; HouseExpo; nuScenes,nuScenes,L2; Collision,N/A,Robots,"<span style=""color:grey; opacity: 0.6"">( Image credit: [Motion Planning Among Dynamic, Decision-Making Agents with Deep Reinforcement Learning](https://arxiv.org/pdf/1805.01956v1.pdf) )</span>"
3D Semantic Scene Completion,5,SSCBench; KITTI-360; NYUv2; PRO-teXt; SemanticKITTI,SemanticKITTI; KITTI-360; PRO-teXt; NYUv2,CMD; F1; CD; mIoU,3D Semantic Scene Completion from a single RGB image,Computer Vision,"This task was introduced in ""Semantic Scene Completion from a Single Depth Image"" (https://arxiv.org/abs/1611.08974) at CVPR 2017 . The target is to infer the dense 3D voxelized semantic scene from an..."
Zeroshot Video Question Answer,5,MSVD-QA; CausalChaos!; MSRVTT-QA; VCG+112K; TGIF-QA,N/A,N/A,N/A,N/A,N/A
Depression Detection,5,SMHD; Well-being Dataset; eRisk 2017; Distress Analysis Interview Corpus/Wizard-of-Oz set (DAIC-WOZ); Perla Dataset,N/A,N/A,N/A,N/A,N/A
Fine-Grained Visual Categorization,5,MTL-AQA; FeathersV1; L-Bird; VegFru; FoodX-251,N/A,N/A,N/A,N/A,N/A
RGB-D Salient Object Detection,5,NLPR; ReDWeb-S; NJU2K; LFSD; SIP,N/A,N/A,N/A,N/A,N/A
Extractive Text Summarization,5,DebateSum; SubSumE; GovReport; DUC 2004; CNN/Daily Mail,DebateSum; DUC 2004 Task 1; GovReport; CNN / Daily Mail; DUC 2004,ROUGE-1; Avg. Test RougeLsum; Avg. Test Rouge2; ROUGE-L; Test ROGUE-2; Test ROGUE-1; Avg. Test Rouge1; ROUGE-2,Reader-Aware Summarization,Natural Language Processing,"Given a document, selecting a subset of the words or sentences which best represents a summary of the document."
Stock Market Prediction,5,Astock; stocknet; FinSen; EDT; Dhaka Stock Exchange Historical Data,Astock; stocknet; S&P 500,F1; Recall; Average daily returns; Precision; Accuray; F1-score,Stock Trend Prediction; Stock Prediction; Stock Price Prediction,Time Series,N/A
Cross-Domain Few-Shot Object Detection,5,DIOR; UODD; Artaxor; NEU-DET; DeepFish,N/A,N/A,N/A,N/A,N/A
Surgical tool detection,5,CholecT50; Cholec80; PWISeg; HeiChole Benchmark; CholecTrack20,N/A,N/A,N/A,N/A,N/A
Hand-Gesture Recognition,5,IPN Hand; TIM-Tremor; MLGESTURE DATASET; Human Palm and Gloves Dataset | Human Body Parts Dataset; VIVA,N/A,N/A,N/A,N/A,N/A
Surface Normal Estimation,5,FutureHouse; NYUv2; 3D Ken Burns Dataset; CARLA2Real; GRIT,N/A,N/A,N/A,N/A,N/A
Face Parsing,5,EasyPortrait; CelebAMask-HQ; iBugMask; LaPa; Helen,N/A,N/A,N/A,N/A,N/A
Face Model,5,FaceScape; HUMBI; Procedural Human Action Videos; FaceWarehouse; MeGlass,N/A,N/A,N/A,Computer Vision,N/A
Content-Based Image Retrieval,5,INRIA Holidays Dataset; PS-Battles; Oxford5k; GPR1200; European Flood 2013 Dataset,INRIA Holidays Dataset,MAP,Drone navigation; Drone-view target localization,Computer Vision,"**Content-Based Image Retrieval** is a well studied problem in computer vision, with retrieval problems generally divided into two groups: category-level retrieval and instance-level retrieval. Given ..."
Medical Relation Extraction,5,DDI; GAD; CMeIE; EU-ADR; RadGraph,CMeIE; DDI extraction 2013 corpus,F1; Micro F1,N/A,Medical,Biomedical relation extraction is the task of detecting and classifying semantic relationships from biomedical text.
Text to Audio Retrieval,5,Localized Narratives; WavCaps; AudioCaps; Clotho; SoundDescs,Clotho; SoundDescs; Localized Narratives; AudioCaps,R@10; Text-to-audio R@1; Text-to-audio R@5; mAP@10; R@1; R@5; Text-to-audio R@10,audio moment retrieval,Audio,N/A
Video Emotion Recognition,5,Ekman6; CANDOR Corpus; L-SVD; CREMA-D; RAVDESS,N/A,N/A,N/A,N/A,N/A
3D Feature Matching,5,4DMatch; PartNet-Mobility; DeformingThings4D; CPNet; 3DMatch,N/A,N/A,N/A,N/A,N/A
Low Resource Named Entity Recognition,5,Broad Twitter Corpus; CoNLL; Few-NERD; CoNLL 2003; Rare Diseases Mentions in MIMIC-III,N/A,N/A,N/A,N/A,N/A
Video Compression,5,YT-UGC; Deep Fakes Dataset; Vinoground; SEPE 8K; BVI-DVC,N/A,N/A,N/A,N/A,N/A
Program Synthesis,5,PSB2; CONCODE; SPoC; xCodeEval; SketchGraphs,SPoC TestW; AlgoLisp; SPoC TestP,Success rate @budget 100; Accuracy,Enumerative Search; Type prediction; SQL Synthesis; Program Repair; Value prediction,Reasoning; Computer Code,Program synthesis is the process of automatically generating a program or code snippet that satisfies a given specification or set of requirements. This can include generating code from a formal speci...
Malware Detection,5,EMBER; AutoRobust; BODMAS; MalNet; IoT-23,N/A,N/A,N/A,N/A,N/A
Human motion prediction,5,GTA-IM Dataset; EMDB; NuiSI Dataset; VIENA2; MoCapAct,N/A,N/A,Stochastic Human Motion Prediction,Computer Vision,"Action prediction is a pre-fact video understanding task, which focuses on future states, in other words, it needs to reason about future states or infer action labels before the end of action executi..."
Face Generation,5,iFakeFaceDB; VGGFace2 HQ; DFDM; FLUXSynID; Face dataset by Generated Photos,N/A,N/A,Talking Head Generation; Facial expression generation; Kinship face generation; Face Age Editing; Talking Face Generation,Computer Vision,Face generation is the task of generating (or interpolating) new faces from an existing dataset.    The state-of-the-art results for this task are located in the Image Generation parent.    <span styl...
whole slide images,5,MITOS_WSI_CMC; LKS; CAMELYON16; PanNuke; CryoNuSeg,N/A,N/A,N/A,Computer Vision,N/A
Logo Recognition,5,Indian Signboard Image Dataset | Text in Image; WiRLD_; WiRLD; OSLD; LOGO-Net,N/A,N/A,N/A,Computer Vision,N/A
3D Place Recognition,5,Wild-Places; CS-Campus3D; Oxford RobotCar Dataset; In-house; VBR,N/A,N/A,N/A,N/A,N/A
Cell Detection,5,Fluo-N2DL-HeLa; uBench; PanNuke; Fluo-N2DH-GOWT1; PhC-C2DH-U373,N/A,N/A,N/A,N/A,N/A
Spatial Relation Recognition,5,TexRel; ResQ; Rel3D; SpatialSense Benchmark; MRR-Benchmark,Rel3D,Acc,N/A,Computer Vision,N/A
Image Relighting,5,NRHints-RealCapture; Dynamic OLAT Dataset; NRHints-Synthetic; VIDIT; Stanford-ORB,N/A,N/A,N/A,N/A,N/A
Trajectory Clustering,5,inD Dataset; uniD Dataset; exiD Dataset; rounD Dataset; highD Dataset,N/A,N/A,N/A,N/A,N/A
Music Emotion Recognition,5,4Q audio emotion dataset (Russell's model); PIAST; VGMIDI; RAVDESS; XMIDI,N/A,N/A,N/A,Music,N/A
Medical X-Ray Image Segmentation,5,FracAtlas; CheXlocalize; A collection of X-ray projections of 131 pieces of modeling clay containing stones for machine learning-driven object detection; CheXmask; ChestX-Det,N/A,N/A,N/A,N/A,N/A
3D Assembly,5,3D design files; RePAIR Dataset; Breaking Bad; SBA; DeepCAD,DeepCAD,1-1,N/A,N/A,N/A
Brain Segmentation,5,BRATS 2021; Tc1 Mouse cerebellum atlas; CUTS; CrossMoDA; Multi-template MRI mouse brain atlas,N/A,N/A,N/A,N/A,N/A
Medical Concept Normalization,5,BB-norm-habitat; BB-norm-phenotype; BB; CoNECo; CHIP-CDN,N/A,N/A,N/A,N/A,N/A
Road Segmentation,5,Pothole Mix; ChesapeakeRSC; DeepGlobe; Massachusetts Roads Dataset; Polarimetric Imaging for Perception,Massachusetts Roads Dataset; DeepGlobe; ChesapeakeRSC,F1; DWR; IoU; APLS; mIoU,Lane Labeling,Computer Vision,Road Segmentation is a pixel wise binary classification in order to extract underlying road network. Various Heuristic and data driven models are proposed. Continuity and robustness still remains one ...
Photoplethysmography (PPG),5,UBFC-rPPG; MTHS; MIMIC PERform Testing Dataset; MMSE-HR; VitalDB,N/A,N/A,Heart rate estimation; Photoplethysmography (PPG) heart rate estimation; Blood pressure estimation; Photoplethysmography (PPG) beat detection,Medical,"**Photoplethysmography (PPG)** is a non-invasive light-based method that has been used since the 1930s for monitoring cardiovascular activity.      <span class=""description-source"">Source: [Non-contac..."
3D Anomaly Detection,5,Real 3D-AD; WALT; How do I Contact Expedia Customer Service 24/7 hours; NovelCraft; IoT-23,Anomaly-ShapeNet; Anomaly-ShapeNet10; Real 3D-AD,P-AUROC; O-AUROC; Point AUROC; Mean Performance of P. and O. ; Object AUROC,Video Anomaly Detection; Artifact Detection,Computer Vision,3D-only Anomaly Detection.  Structures out of normal distribution are detected from the 3D-only point cloud.
Photoplethysmography (PPG) heart rate estimation,5,UBFC-rPPG; MMPD; MIMIC PERform Testing Dataset; BUAA-MIHR dataset; MMSE-HR,N/A,N/A,N/A,N/A,N/A
3D Question Answering (3D-QA),5,MSQA; 3D MM-Vet; SQA3D; Beacon3D; ScanQA,N/A,N/A,N/A,N/A,N/A
Text to 3D,5,Text2CAD; BlendNet; StableText2Lego; T$^3$Bench; CADBench,T$^3$Bench,Avg,N/A,Computer Vision,Task involves generating 3D objects based on the text prompt provided to the system.
Video-Adverb Retrieval,5,AIR; MSR-VTT Adverbs; VATEX Adverbs; HowTo100M Adverbs; ActivityNet Adverbs,AIR; MSR-VTT Adverbs; VATEX Adverbs; HowTo100M Adverbs; ActivityNet Adverbs,Acc-A; mAP W; mAP M,Video-Adverb Retrieval (Unseen Compositions),Computer Vision,The bidirectional video-adverb retrieval task aims at retrieving adverbs that match an action in a video and vice versa.
Image Forgery Detection,5,CASIA (OSN-transmitted - Weibo); NIST (OSN-transmitted - Facebook); Digital Forensics 2023 dataset - DF2023; DIS100k; FairFD,N/A,N/A,N/A,Computer Vision,N/A
Cross-modal retrieval with noisy correspondence,5,NoW; Flickr30K-Noisy; CC152K; COCO-Noisy; Noise of Web,N/A,N/A,N/A,N/A,N/A
3D Generation,5,E.T. the Exceptional Trajectories; StableText2Lego; MeshFLeet; Mpm-Verse-Large; HumanRig,E.T. the Exceptional Trajectories,Classifier-F1; ClaTr-Score; FD_ClaTr,Garment sewing pattern generation,Graphs,N/A
Handwritten Mathmatical Expression Recognition,5,CROHME 2014; CROHME 2019; HME100K; CROHME 2023; CROHME 2016,CROHME 2014; CROHME 2019; CROHME 2016; HME100K,ExpRate,N/A,Computer Vision,Offline Handwritten mathematical Expression Recognition aims to convert 2D images into a 1D structured sequences(LaTeX or MathML)
Image-text Retrieval,5,InpaintCOCO; NoW; IMPACT Patent; Noise of Web; LeafNet,N/A,N/A,N/A,N/A,N/A
Multiview Clustering,4,NUS-WIDE; MNIST; Fashion-MNIST; Multilingual Reuters,N/A,N/A,N/A,N/A,N/A
Unsupervised Anomaly Detection with Specified Settings -- 30% anomaly,4,MNIST; STL-10; Fashion-MNIST; CIFAR-10,ASSIRA Cat Vs Dog; MNIST; STL-10; Fashion-MNIST; CIFAR-10,AUC-ROC,N/A,Computer Vision,N/A
Image Compressed Sensing,4,CelebA; Set11; CBSD68; ImageNet,N/A,N/A,N/A,N/A,N/A
Physical Attribute Prediction,4,CelebA; CAR; FAD; Sound of Water 50,Sound of Water 50,Mean Squared Error,N/A,Computer Vision,N/A
Multimodal Text and Image Classification,4,Food-101; CUB-200-2011; CD18; QUILT-1M,Food-101; CUB-200-2011; CD18,F-measure (%); Accuracy (%); Accuracy,image-sentence alignment; Open-World Social Event Classification,Natural Language Processing,Classification with both source Image and Text
Image Outpainting,4,COCO (Common Objects in Context); LHQ; MSCOCO; Places365,Places365-Standard; MSCOCO; LHQC,Block-FID (Down Extend); FID; Block-FID (Up Extend); CLIP Similarity; Inception score; Adversarial; Block-FID (Left Extend); MSE; Block-FID  (Right Extend); L1,N/A,Computer Vision,Predicting the visual context of an image beyond its boundary.    Image credit: [NUWA-Infinity: Autoregressive over Autoregressive Generation for Infinite Visual Synthesis](https://paperswithcode.com/...
Vehicle Pose Estimation,4,VBR; CarFusion; KITTI; ApolloCar3D,N/A,N/A,N/A,N/A,N/A
3D Object Detection From Stereo Images,4,3D-POP; 3D-ZeF; KITTI; IBISCape,N/A,N/A,N/A,N/A,N/A
Prediction Of Occupancy Grid Maps,4,Occ3D; EviLOG; KITTI; nuScenes,Occ3D-nuScenes; nuScenes,mIoU,N/A,Computer Vision,N/A
Self-Supervised Action Recognition Linear,4,UCF101; HMDB51; Kinetics; Kinetics 400,N/A,N/A,N/A,N/A,N/A
Mortality Prediction,4,MIMIC-III; Clinical Admission Notes from MIMIC-III; eICU-CRD; MeDAL,MIMIC-III; Clinical Admission Notes from MIMIC-III,Accuracy; Recall; Precision; F1 score; AUROC,ICU Mortality,Medical,"<span style=""color:grey; opacity: 0.6"">( Image credit: [Early hospital mortality prediction using vital signals](https://arxiv.org/pdf/1803.06589v2.pdf) )</span>"
Unsupervised Image Segmentation,4,COCO-Stuff; BN-HTRd; Oxford 102 Flower; TYC Dataset,N/A,N/A,N/A,N/A,N/A
Patch Matching,4,ETH SfM; VIPeR; PhotoSynth; HPatches,Brown Dataset; HPatches,Patch Verification; Patch Retrieval; Patch Matching; FPR95,Multimodal Patch Matching,Computer Vision,N/A
Facial Action Unit Detection,4,DISFA; Thermal Face Database; CANDOR Corpus; BP4D,N/A,N/A,N/A,N/A,N/A
Clinical Concept Extraction,4,Adverse Drug Events (ADE) Corpus; Toronto NeuroFace Dataset; 2010 i2b2/VA; 2018 n2c2 (Track 2) - Adverse Drug Events and Medication Extraction,2010 i2b2/VA,Exact Span F1,Clinical Information Retreival,Natural Language Processing; Medical,"Automatic extraction of clinical named entities such as clinical problems, treatments, tests and anatomical parts from clinical notes.    ( [Source](https://arxiv.org/pdf/2012.04005v1.pdf) )"
Temporal Localization,4,VidSTG; TUMTraffic-VideoQA; AVE; Charades-STA,N/A,N/A,Temporal Defect Localization; Language-Based Temporal Localization,Computer Vision,N/A
Temporal Action Proposal Generation,4,ActivityNet Captions; FineAction; ActivityNet; THUMOS14,N/A,N/A,N/A,N/A,N/A
Weakly-supervised Temporal Action Localization,4,THUMOS14; UCF101-24; FineAction; ActivityNet,ActivityNet-1.3; UCF101-24; THUMOS’14,mAP@AVG(0.1:0.9); mAP IOU@0.75; mAP IOU@0.1; mAP IOU@0.9; mAP IOU@0.95; mAP IOU@0.4; mAP IOU@0.3; mAP IOU@0.5; mAP IOU@0.8; mAP IOU@0.6,Weakly Supervised Temporal Action Localization,Computer Vision,Temporal Action Localization with weak supervision where only video-level labels are given for training
Abnormal Event Detection In Video,4,UCSD Ped2; UBI-Fights; ShanghaiTech; ShanghaiTech Campus,UCSD Ped2; UBI-Fights,Decidability; AUC; EER,Semi-supervised Anomaly Detection,Computer Vision,"**Abnormal Event Detection In Video** is a challenging task in computer vision, as the definition of what an abnormal event looks like depends very much on the context. For instance, a car driving by ..."
Egocentric Activity Recognition,4,DECADE; EPIC-KITCHENS-55; EGOK360; EGTEA,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Instance Segmentation,4,BDD100K; ADE20K; COCO 10% labeled data; Cityscapes,COCO 1% labeled data; COCO 10% labeled data; COCO 5% labeled data; COCO 2% labeled data; ADE20K; Cityscapes,mask AP; AP,N/A,Computer Vision,N/A
Multimodal Unsupervised Image-To-Image Translation,4,FFHQ-Aging; CATS; CelebA-HQ; AFHQ,N/A,N/A,N/A,N/A,N/A
Unsupervised Action Segmentation,4,Breakfast; IKEA ASM; 50 Salads; Youtube INRIA Instructional,N/A,N/A,N/A,N/A,N/A
Supervised Video Summarization,4,Query-Focused Video Summarization Dataset; Mr. HiSum; SumMe; TvSum,N/A,N/A,N/A,N/A,N/A
Video Salient Object Detection,4,ViSal; SegTrack-v2; FBMS; FBMS-59,N/A,N/A,N/A,N/A,N/A
Image Classification with Label Noise,4,WikiChurches; CIFAR-100; Corn Seeds Dataset; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Reconstruction,4,iDesigner; ADE20K; CelebAMask-HQ; PPMI,CelebAMask-HQ; PPMI; HCP; ADE20K; iDesigner,PSNR; SSIM; R-FID; runtime (s),Single-View 3D Reconstruction; Single-Image-Based Hdr Reconstruction; 3D Human Reconstruction; 4D reconstruction,Computer Vision; Reasoning,N/A
Pose Retrieval,4,HARPER; Human3.6M; MPI-INF-3DHP; SLAM2REF,Human3.6M; MPI-INF-3DHP,Hit@10; Hit@1,N/A,Computer Vision,Retrieval of similar human poses from images or videos
Document Image Classification,4,RVL-CDIP; Tobacco-3482; S-VED; SUT,n-MNIST; SUT; RVL-CDIP; AIP; Noisy MNIST; Tobacco-3482; Noisy Bangla Numeral; Noisy Bangla Characters,Memory; Parameters; Top 1 Accuracy - Verb; Accuracy,N/A,Computer Vision,"Document image classification is the task of classifying documents based on images of their contents.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Real-Time Document Image Classification..."
3D-Aware Image Synthesis,4,SynthRAD2023; 3D-Point Cloud dataset of various geometrical terrains; CelebAMask-HQ; FFHQ,N/A,N/A,N/A,N/A,N/A
Video Polyp Segmentation,4,SUN-SEG-Hard (Unseen); PolypGen; STARE; SUN-SEG-Easy (Unseen),N/A,N/A,N/A,N/A,N/A
Unsupervised Extractive Summarization,4,FacetSum; Pubmed; XWikiRef; arXiv Summarization Dataset,N/A,N/A,N/A,N/A,N/A
Nested Mention Recognition,4,ACE 2004; AMALGUM; GUM; ACE 2005,ACE 2004; ACE 2005,F1,N/A,Natural Language Processing,Nested mention recognition is the task of correctly modeling the nested structure of mentions.
Event Argument Extraction,4,WikiEvents; MAVEN-Arg; ACE 2005; LEMONADE,N/A,N/A,N/A,N/A,N/A
Image Stitching,4,PhotoSynth; HPatches; UDIS-D; CROSS,HPatches,0..5sec,N/A,Computer Vision,"**Image Stitching** is a process of composing multiple images with narrow but overlapping fields of view to create a larger image with a wider field of view.      <span class=""description-source"">Sour..."
3D Open-Vocabulary Instance Segmentation,4,STPLS3D; Replica; S3DIS; ScanNet200,STPLS3D; Replica; S3DIS; ScanNet200,AP50 Base B6/N6; AP50; AP25; AP50 Base B8/N4 ; AP Common; AP Tail; mAP; AP Head; AP50 Novel B6/N6; AP50 Novel B8/N4,N/A,Computer Vision,Open-vocabulary 3D instance segmentation is a computer vision task that involves identifying and delineating individual objects or instances within a three-dimensional (3D) scene without prior knowled...
Cross-Domain Named Entity Recognition,4,CrossNER; CoNLL04; RuTermEval (Track 3); CoNLL,N/A,N/A,N/A,N/A,N/A
Cross-View Image-to-Image Translation,4,Ego2Top; PEnG; Dayton; Cam2BEV,N/A,N/A,N/A,N/A,N/A
Shadow Detection,4,SOBA; CUHK-Shadow; INS Dataset; SBU / SBU-Refine,CUHK-Shadow; SBU / SBU-Refine,BER,Shadow Detection And Removal,Computer Vision,N/A
Weakly supervised Semantic Segmentation,4,SemanticPOSS; CheXlocalize; SemanticKITTI; nuScenes,N/A,N/A,N/A,N/A,N/A
3D Semantic Scene Completion from a single RGB image,4,SSCBench; KITTI-360; NYUv2; SemanticKITTI,N/A,N/A,N/A,N/A,N/A
Few-Shot Semantic Segmentation,4,GAS; FewSOL; PASCAL-5i; FSS-1000,COCO-20i -> Pascal VOC (1-shot); Pascal5i; COCO-20i (5-shot); FSS-1000 (1-shot); PASCAL-5i (1-Shot); PASCAL-5i (10-Shot); COCO-20i (10-shot); COCO-20i -> Pascal VOC (5-shot); COCO-20i (1-shot); FSS-1000,learnable parameters (million); FB-IoU; mIoU; Mean IoU; meanIOU,Generalized Few-Shot Semantic Segmentation,Computer Vision,Few-shot semantic segmentation (FSS) learns to segment target objects in query image given few pixel-wise annotated support image.
Artery/Veins Retinal Vessel Segmentation,4,INSPIRE-AVR (LUNet subset); LES-AV; HRF; UZLF,N/A,N/A,N/A,N/A,N/A
Drone-view target localization,4,University-1652; BuckTales; CODrone; GTA-UAV,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Defect Detection,4,KolektorSDD; KolektorSDD2; DAGM2007; ISP-AD,N/A,N/A,N/A,N/A,N/A
Nuclear Segmentation,4,CoNSeP; Cell; 2018 Data Science Bowl; MoNuSAC,N/A,N/A,N/A,N/A,N/A
Action Localization,4,CVB; COIN; GraSP; HACS,N/A,N/A,Unusual Activity Localization; Action Segmentation; Temporal Action Localization; Spatio-Temporal Action Localization,Computer Vision,Action Localization is finding the spatial and temporal co ordinates for an action in a video. An action localization model will identify which frame an action start and ends in video and return the x...
Sketch-Based Image Retrieval,4,"QuickDraw-Extended; ShoeV2; Quick, Draw! Dataset; Chairs",Handbags; Shoes; Chairs,R@10; R@1,On-the-Fly Sketch Based Image Retrieval,Computer Vision,N/A
Visual Storytelling,4,Creative Visual Storytelling Anthology; VIST-Edit; Visual Writing Prompts; VIST,VIST,MLTD; BLEU-1; ROUGE-L; METEOR; CIDEr; BLEU-2; SPICE; BLEU-3; BLEU-4; BLEURT,Image-guided Story Ending Generation,Natural Language Processing,"<span style=""color:grey; opacity: 0.6"">( Image credit: [No Metrics Are Perfect](https://github.com/eric-xw/AREL) )</span>"
Building change detection for remote sensing images,4,LEVIR-CD; SECOND; BANDON; WHU Building Dataset,N/A,N/A,N/A,N/A,N/A
Photo geolocation estimation,4,GeoDE; OpenStreetView-5M; MM-Locate-News; Im2GPS,N/A,N/A,N/A,N/A,N/A
Action Understanding,4,Fitness-AQA; Win-Fail Action Understanding; MTL-AQA; W-Oops,Win-Fail Action Understanding,2-Class Accuracy,N/A,Computer Vision,N/A
Multiview Learning,4,Multimodal PISA; MTL-AQA; MNIST Multiview Datasets; 3 Sources,N/A,N/A,N/A,Computer Vision,N/A
3D Human Pose Tracking,4,Panoptic; InfiniteRep; UNIPD-BPE; VIDIMU: Multimodal video and IMU kinematic dataset on daily life activities using affordable devices,Panoptic,3DMOTA,Motion Synthesis,Audio,N/A
MRI Reconstruction,4,SKM-TEA; M4Raw; IXI; fastMRI,fastMRI Knee 8x; fastMRI Brain 8x; IXI; fastMRI Knee Val 8x ; fastMRI Knee 4x; fastMRI Brain 4x,NMSE; DSSIM; MSE; SSIM; Params (M); PSNR,Quantitative MRI; Magnetic Resonance Fingerprinting,Medical,"In its most basic form, MRI reconstruction consists in retrieving a complex-valued image from its under-sampled Fourier coefficients.   Besides, it can be addressed as a encoder-decoder task, in which..."
Stock Prediction,4,Astock; stocknet; EDT; Dhaka Stock Exchange Historical Data,N/A,N/A,Text-Based Stock Prediction; PAIR TRADING; Event-Driven Trading,Time Series; Natural Language Processing,N/A
Action Triplet Recognition,4,CholecT50; CholecT45; PETRAW; CholecT40,N/A,N/A,N/A,N/A,N/A
Event Coreference Resolution,4,LegalCore; MultiReQA; ECB+; Gun Violence Corpus,N/A,N/A,N/A,N/A,N/A
Change detection for remote sensing images,4,GVLM; SECOND; ChaBuD; CDD Dataset (season-varying),N/A,N/A,N/A,N/A,N/A
Face Reconstruction,4,FaceWarehouse; ICT-3DHP; FaceScape; FLUXSynID,N/A,N/A,3D Face Reconstruction,Computer Vision,"Face reconstruction is the task of recovering the facial geometry of a face from an image.    <span style=""color:grey; opacity: 0.6"">( Image credit: Microsoft [Deep3DFaceReconstruction](https://github..."
Multi-future Trajectory Prediction,4,ETH; JAAD; TrajAir: A General Aviation Trajectory Dataset; PIE,N/A,N/A,N/A,N/A,N/A
Distant Speech Recognition,4,DIRHA; BERSt; DiPCo; ReVerb Challenge,N/A,N/A,N/A,N/A,N/A
Video Alignment,4,Penn Action; MSU Video Alignment and Retrieval Benchmark Suite; IAW Dataset; N-Digit MNIST,N/A,N/A,N/A,N/A,N/A
EEG Emotion Recognition,4,SEED; MEEG; DEAP; PhyMER,N/A,N/A,N/A,N/A,N/A
Instrument Recognition,4,YouTube-100M; NSynth; Kvasir-Instrument; OpenMIC-2018,IRMAS; NSynth; OpenMIC-2018,mean average precision; Accuracy; Recall; Precision; F1-score,N/A,Audio,N/A
Word Alignment,4,WMT 2016 News; Bangla Word Analogy; Europarl ConcoDisco Dataset; MUSE,MUSE en-pt; en-es; en-it; en-fr; MUSE en-de; es-en; fr-en,P@1,N/A,Natural Language Processing,"**Word Alignment** is the task of finding the correspondence between source and target words in a pair of sentences that are translations of each other.      <span class=""description-source"">Source: [..."
Scientific Results Extraction,4,ArxivPapers; LinkedResults; SegmentedTables; PWC Leaderboards,N/A,N/A,N/A,N/A,N/A
Iris Recognition,4,CASIA-Iris-Complex; UFPR-Periocular; NDPSID - WACV 2019; UBIRIS.v2,N/A,N/A,Pupil Dilation,Computer Vision; Medical,N/A
Supervised Anomaly Detection,4,BTAD; MVTecAD; CHAD; ISP-AD,N/A,N/A,N/A,N/A,N/A
Scene Parsing,4,Stanford Background; PGDP5K; UNDD; Cityscapes,PGDP5K; Cityscapes test,mIoU; Total Accuracy,Scene Text Recognition; Indoor Scene Reconstruction; Scene Graph Generation; Scene Recognition; Scene Understanding,Computer Vision,"Scene parsing is to segment and parse an image into different image regions associated with semantic categories, such as sky, road, person, and bed. [MIT Description](http://sceneparsing.csail.mit.edu..."
3D Object Retrieval,4,The RBO Dataset of Articulated Objects and Interactions; Visual 3D shape matching dataset; ModelNet; LAS&T: Large Shape & Texture Dataset,N/A,N/A,N/A,N/A,N/A
Temporal View Synthesis,4,IISc VEED-Dynamic; SceneNet RGB-D; IISc VEED; MPI Sintel,N/A,N/A,N/A,N/A,N/A
Camera shot boundary detection,4,ClipShots; TRECVID; SoccerNet-v2; MSU Shot Boundary Detection Benchmark,N/A,N/A,N/A,N/A,N/A
Indoor Monocular Depth Estimation,4,InSpaceType; HUMAN4D; ISOD; DIODE,N/A,N/A,N/A,N/A,N/A
Audio to Text Retrieval,4,SoundDescs; Clotho; Localized Narratives; AudioCaps,N/A,N/A,N/A,N/A,N/A
Multi-modal Entity Alignment,4,MMKG; UMVM; OpenEA Benchmark; DBP15K,UMVM-dbp-ja-en; UMVM-oea-d-w-v1; MMKG; UMVM-dbp-fr-en; UMVM-dbp-zh-en; UMVM-oea-d-w-v2; UMVM-oea-en-fr; UMVM-oea-en-de,H@1; Hits@1,N/A,Knowledge Base,N/A
Online Beat Tracking,4,ASAP; Ballroom; Rock Corpus; GTZAN,Ballroom; Rock Corpus; GTZAN,F1,Inference Optimization,Audio,N/A
Online Downbeat Tracking,4,ASAP; Ballroom; Rock Corpus; GTZAN,N/A,N/A,N/A,N/A,N/A
Spelling Correction,4,MCSCSet; Viwiki-Spelling; CSCD-IME; GitHub Typo Corpus,N/A,N/A,Bangla Spelling Error Correction,Natural Language Processing,Spelling correction is the task of detecting and correcting spelling mistakes.
Zero-shot Named Entity Recognition (NER),4,HarveyNER; CrossNER; Broad Twitter Corpus; WikiEvents,N/A,N/A,N/A,N/A,N/A
Image Cropping,4,AADB; Flickr Cropping Dataset; CUHK Image Cropping; GNMC,FLMS,IoU; BDE,N/A,Computer Vision,"**Image Cropping** is a common photo manipulation process, which improves the overall composition by removing unwanted regions. Image Cropping is widely used in photographic, film processing, graphic ..."
graph construction,4,IPRE; CovidQA; RoadTracer; Perfume Co-Preference Network,N/A,N/A,N/A,Graphs,N/A
Document-level Relation Extraction,4,DWIE; DocRED-IE; Bc8; Re-DocRED,DWIE; DocRED-IE; Bc8; Re-DocRED,F1; Evaluation Macro F1; Relation F1,Document-level RE with incomplete labeling,Natural Language Processing,Document-level RE aim to identify the relations of various entity pairs expressed across multiple sentences.
3D Shape Representation,4,KeypointNet; Objectron; Foot3D; Dynamic FAUST,N/A,N/A,3D Dense Shape Correspondence,Computer Vision,Image: [MeshNet](https://arxiv.org/pdf/1811.11424v1.pdf)
Hand Segmentation,4,ObMan-Ego; HandNet; EgoHands; EYTH,N/A,N/A,N/A,N/A,N/A
Landmark Recognition,4,Aerial Landmarks Recognition Dataset; Google Landmarks Dataset v2; ZuBuD+; Chest x-ray landmark dataset,"Google Landmarks Dataset v2 (recognition, validation); Google Landmarks Dataset v2 (recognition, testing)",microAP,Brain landmark detection,Computer Vision,N/A
Abuse Detection,4,WikiConv; AbuseAnalyzer Dataset; CoRAL dataset; Hate Speech and Offensive Language,N/A,N/A,Hate Speech Detection,Natural Language Processing,"Abuse detection is the task of identifying abusive behaviors, such as hate speech, offensive language, sexism and racism, in utterances from social media platforms (Source: https://arxiv.org/abs/1802...."
Multispectral Object Detection,4,MS-EVS Dataset; NII-CU MAPD; LLVIP; KAIST Multispectral Pedestrian Detection Benchmark,NII-CU MAPD; LLVIP; KAIST Multispectral Pedestrian Detection Benchmark; FLIR,AP@0.5; All Miss Rate; Reasonable Miss Rate; mAP; mAP@0.5:0.95; AP@0.75; mAP50,N/A,Computer Vision,"Only using RGB cameras for automatic outdoor scene analysis is challenging when, for example, facing insufficient illumination or adverse weather. To improve the recognition reliability, multispectral..."
Medical Image Enhancement,4,Human Protein Atlas; SimNICT; Brain Tumor MRI Dataset; LoDoPaB-CT,Human Protein Atlas Image; Brain Tumor MRI Dataset; LoDoPaB-CT,SSIM; Average PSNR,N/A,Computer Vision,Aims to improve the perceptual quality of low-quality medical images
Action Parsing,4,Home Action Genome; DARai; JerichoWorld; TAPOS,JerichoWorld,Set accuracy,N/A,Natural Language Processing,"Action parsing is the task of, given a video or still image, assigning each frame or image a label describing the action in that frame or image."
Humor Detection,4,UR-FUNNY; Multimodal Humor Dataset; PHINC; Cards Against Humanity,200k Short Texts for Humor Detection,F1-score,N/A,Natural Language Processing,Humor detection is the task of identifying comical or amusing elements.
Thermal Image Segmentation,4,"MFNet; Charlotte-ThermalFace; Multi-Spectral Stereo Dataset  (RGB, NIR, thermal images, LiDAR, GPS/IMU); PST900",N/A,N/A,N/A,N/A,N/A
Sketch Recognition,4,"AtyPict; ShoeV2; Quick, Draw! Dataset; Im4Sketch",N/A,N/A,Image to sketch recognition,Computer Vision,N/A
Cross-View Geo-Localisation,4,StreetLearn; PEnG; SpaGBOL; VIGOR,N/A,N/A,N/A,N/A,N/A
3D Scene Reconstruction,4,The RobotriX; WayveScenes101; ENRICH; IBISCape,N/A,N/A,3D Semantic Scene Completion from a single RGB image,Computer Vision,Creating 3D scene either using conventional SFM pipelines or latest deep learning approaches.
Image-based Automatic Meter Reading,4,UFPR-ADMR-v1; Copel-AMR; UFPR-ADMR-v2; UFPR-AMR,N/A,N/A,Dial Meter Reading,Computer Vision,N/A
Cross-Lingual Abstractive Summarization,4,M3LS; WikiMulti; XWikiRef; WikiLingua,N/A,N/A,N/A,N/A,N/A
Extracting Buildings In Remote Sensing Images,4,WHU Building Dataset; MapAI Dataset; Massachusetts building dataset; xBD,N/A,N/A,N/A,N/A,N/A
Heart Segmentation,4,Echonet-Dynamic; ACDC Scribbles; CheXmask; MM-WHS 2017,N/A,N/A,N/A,N/A,N/A
Action Generation,4,LLMafia; PHSPD; Two4Two; HumanAct12,N/A,N/A,N/A,Computer Vision,N/A
Malware Family Detection,4,AutoRobust; MOTIF; Malimg; MalNet,N/A,N/A,N/A,N/A,N/A
Negation Detection,4,ShARe/CLEF 2014: Task 2 Disorders; EMC Dutch Clinical Corpus; This is not a Dataset; THYME-2016,N/A,N/A,Negation Scope Resolution,Natural Language Processing,Negation detection is the task of identifying negation cues in text.
hand-object pose,4,HO-3D v2; DexYCB; Human Palm and Gloves Dataset | Human Body Parts Dataset; ARCTIC,N/A,N/A,N/A,N/A,N/A
Biomedical Information Retrieval,4,NFCorpus; BEIR; TripClick; SuMe,NFCorpus (BEIR); BioASQ (BEIR); TREC-COVID (BEIR),nDCG@10,PICO; SpO2 estimation,Natural Language Processing; Medical,N/A
Bird Audio Detection,4,Warblr; Western Mediterranean Wetlands Birds - Version 2; BIRDeep; TinyChirp,N/A,N/A,N/A,N/A,N/A
Entity Retrieval,4,BEIR; Product Reviews 2017; SE-PEF; ZESHEL,N/A,N/A,N/A,N/A,N/A
Rumour Detection,4,1; CIDII Dataset; Sepehr_RumTel01; FTR-18,1; Sepehr_RumTel01,F-Measure; 0..5sec,N/A,Natural Language Processing,"Rumor detection is the task of identifying rumors, i.e. statements whose veracity is not quickly or ever confirmed, in utterances on social media platforms."
Semantic Image-Text Similarity,4,RGZ EMU: Semantic Taxonomy; CxC; LAION-400M; fruit-SALAD,N/A,N/A,N/A,N/A,N/A
Stock Trend Prediction,4,Astock; TRADES-LOB; EDT; CSI 300 Pair Trading,FI-2010,F1 (H50); Accuracy (H50),Stock Market Prediction,Time Series,N/A
Crack Segmentation,4,CrackVision12K; Khanhha's dataset; CRACK500; CFD,N/A,N/A,N/A,N/A,N/A
Hand Detection,4,Slovo: Russian Sign Language Dataset; Human Palm and Gloves Dataset | Human Body Parts Dataset; TI1K Dataset; ThermoHands,N/A,N/A,N/A,Computer Vision,"As an important subject in the field of computer vision, hand detection plays an important role in many tasks such as human-computer interaction, automatic driving, virtual reality and so on."
Blood Cell Detection,4,Raw-Microscopy and Raw-Drone; uBench; LeukemiaAttri; BBBC041,N/A,N/A,N/A,Medical,N/A
Referring Video Object Segmentation,4,Long-RVOS; MeViS; Refer-YouTube-VOS; ReVOS,N/A,N/A,N/A,N/A,N/A
Fake Image Detection,4,DF40; ArtiFact; Corn Seeds Dataset; TwinSynths,N/A,N/A,GAN image forensics; Fake Image Attribution,Computer Vision,"<span style=""color:grey; opacity: 0.6"">( Image credit: [FaceForensics++](https://github.com/ondyari/FaceForensics) )</span>"
Audio-Visual Synchronization,4,Acappella; AVSync15; VGGSound-Sparse; AVS Benchmark,N/A,N/A,N/A,Computer Vision; Audio,N/A
audio-visual learning,4,GLips; Acappella; AVMIT; IS3 (Interactive-Synthetic Sound Source) Dataset,N/A,N/A,N/A,N/A,N/A
Real-Time Multi-Object Tracking,4,MMPTRACK; LTFT; Argoverse-HD; CholecTrack20,N/A,N/A,N/A,N/A,N/A
Intent Recognition,4,BIG-bench; OIR; diaforge-utc-r-0725; IndirectRequests,BIG-bench,Accuracy ,Multimodal Intent Recognition,Miscellaneous,N/A
Temporal Forgery Localization,4,AV-Deepfake1M; TVIL; ForgeryNet; LAV-DF,N/A,N/A,N/A,N/A,N/A
Discourse Segmentation,4,DISRPT2021; AMALGUM; GUM; DISRPT2019,N/A,N/A,N/A,N/A,N/A
Temporal Relation Extraction,4,2012 i2b2 Temporal Relations; French Timebank; Catalan TimeBank 1.0; Vinoground,Vinoground,Text Score; Video Score; Group Score,Temporal Relation Classification,Natural Language Processing,"Temporal relation extraction systems aim to identify and classify the temporal relation between a pair of entities provided in a text. For instance, in the sentence ""Bob sent a message to Alice while ..."
UNET Segmentation,4,Pothole Mix; MGPFD; AbdomenCT-1K; Munich Sentinel2 Crop Segmentation,N/A,N/A,N/A,N/A,N/A
3D Lane Detection,4,OpenLane-V2 val; OpenLane; OpenLane-V2 test; ONCE-3DLanes,N/A,N/A,N/A,N/A,N/A
Camera Calibration,4,CVGL; SoccerNet-GSR; DeepSportRadar-v1; Cross-View Cross-Scene Multi-View Crowd Counting Dataset,N/A,N/A,N/A,Computer Vision,"Camera calibration involves estimating camera parameters(including camera intrinsics and extrinsics) to infer geometric features from captured sequences, which is crucial for computer vision and robot..."
Robust Speech Recognition,4,Google Speech Commands - Musan; FSC-P2; Speech Robust Bench; DiPCo,N/A,N/A,N/A,N/A,N/A
Optical Charater Recogntion,4,Indian Number Plates Dataset | Vehicle Number Plates | English OCR Detection; Oximeter Image Dataset | Medical Device Reading; Bike and Car Odometer Dataset ! Speedometer OCR; MatriVasha:,N/A,N/A,Bangla Text Detection,Natural Language Processing,N/A
Image Defocus Deblurring,4,RealDOF; SDD; Motion Blurred and Defocused Dataset; DPD (Dual-view),N/A,N/A,N/A,N/A,N/A
3D Facial Expression Recognition,4,"Florence 4D; FER2013 Blendshapes; 4,458 People - 3D Facial Expressions Recognition Data; HEADSET",N/A,N/A,N/A,N/A,N/A
Cell Entity Annotation,4,Tough Tables; WikiTables-TURL; BiodivTab; WikipediaGS,N/A,N/A,N/A,N/A,N/A
Person Retrieval,4,TVPReid; Occluded-PoseTrack-ReID; ITCPR dataset; RSTPReid,SoftBioSearch,Average IOU,N/A,Computer Vision,N/A
Lesion Detection,4,Duke Breast Cancer MRI; AutoPET; HECKTOR; Breast Lesion Detection in Ultrasound Videos (CVA-Net),N/A,N/A,N/A,N/A,N/A
Dynamic Facial Expression Recognition,4,DFEW; FERV39k; MEAD; MAFW,N/A,N/A,N/A,N/A,N/A
Point Tracking,4,TAP-Vid; TAPVid-3D: A Benchmark for Tracking Any Point in 3D; PointOdyssey; Perception Test,Perception Test; TAP-Vid; TAP-Vid-Kinetics-First; TAP-Vid-DAVIS-First; TAP-Vid-Kinetics; PointOdyssey; TAP-Vid-RGB-Stacking; TAP-Vid-DAVIS,MTE; δ; Occlusion Accuracy; Survival; Average PCK; Average Jaccard,N/A,Computer Vision,"Point Tracking, often referred to as Tracking any Point (TAP) involves acquiring, focusing on, and continuously tracking specific target point/points across video frames. The system identifies the tar..."
Financial Relation Extraction,4,REFinD; FinArg; FinRED; Financial Dynamic Knowledge Graph,N/A,N/A,N/A,N/A,N/A
Social Navigation,4,Social-HM3D; HabiCrowd; Social-MP3D; Vid2RealHRI online video and results dataset,N/A,N/A,N/A,N/A,N/A
Synthetic Speech Detection,4,SONICS; Synthetic Speech Attribution; DEEP-VOICE: DeepFake Voice Recognition; ShiftySpeech,N/A,N/A,N/A,N/A,N/A
medical image detection,4,GRAZPEDWRI-DX; MP-IDB; OCT5k; Heel Dataset,N/A,N/A,N/A,N/A,N/A
Spectral Reconstruction,4,ARAD-1K; Real HSI; KAIST; CAVE,N/A,N/A,N/A,N/A,N/A
Event-based Object Segmentation,4,DDD17-SEG; RGBE-SEG; DSEC-SEG; MVSEC-SEG,DDD17-SEG; RGBE-SEG; DSEC-SEG; MVSEC-SEG,mIoU,N/A,N/A,N/A
Deformable Object Manipulation,4,VIRDO Dataset; DeformPAM-Dataset; Branched Deformable Linear Objects (BDLOs) Dataset; Deformable Linear Objects  (DLOs) Dataset,N/A,N/A,N/A,Robots,N/A
2D Tiny Object Detection,4,Bone Fracture Multi-Region X-ray Dataset; SIRST-UAVB; ((Refund~option))What is the refundable option on Expedia?; Bone Fracture Multi-Region X-ray Data,N/A,N/A,Insulator Defect Detection,Computer Vision,N/A
Hyperspectral image analysis,4,MaNGA; Hyper Drive; HSIRS; Tecnalia WEEE HYPERSPECTRAL DATASET,complex refractive index through reflection,RMSPE; Pearson correlation coefficient (PCC),N/A,N/A,N/A
Video Editing,4,VPData; VPBench; FiVE; V2VBench,N/A,N/A,Video Temporal Consistency,Computer Vision; Graphs,N/A
Sequential Image Classification,3,MNIST; Mudestreda; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Image Colorization,3,CelebA; NIR2RGB VCIP Challange Dataset; ImageNet,CelebA; NIR2RGB VCIP Challange Dataset; ImageNet,PSNR; FID; Consistency,Sketch Colorization,Computer Vision,N/A
Image Attribution,3,CelebA; CUB-200-2011; VGGFace2,CelebA; CUB-200-2011; VGGFace2,Insertion AUC score (ResNet-101); Deletion AUC score (ArcFace ResNet-101); Deletion AUC score (ResNet-101); Insertion AUC score (ArcFace ResNet-101),N/A,Computer Vision,Image attribution algorithms aim to identify important regions that are highly relevant to model decisions.
Model Compression,3,QNLI; ImageNet; GLUE,QNLI; ImageNet,Accuracy; Top-1,Neural Network Compression,Miscellaneous; Methodology,**Model Compression** is an actively pursued area of research over the last few years with the goal of deploying state-of-the-art deep networks in low-power and resource limited devices without signif...
Weakly-Supervised Object Localization,3,Tiny ImageNet; CUB-200-2011; ImageNet,N/A,N/A,N/A,N/A,N/A
Self-Supervised Image Classification,3,Chest X-ray images; BIOSCAN-5M; ImageNet,N/A,N/A,N/A,N/A,N/A
Synthetic Face Recognition,3,CALFW; CPLFW; LFW,N/A,N/A,N/A,N/A,N/A
Face Quality Assessement,3,mebeblurf; Color FERET; LFW,mebeblurf; Color FERET; LFW,Equal Error Rate; Pearson Correlation,Face Image Quality,Computer Vision,N/A
Face Anonymization,3,FDF; LFW; FDH,LFW; 2019_test set,negated ID retrieval; Temporal ID consistency; ID retrieval; 10%,N/A,Computer Vision,N/A
Semi Supervised Learning for Image Captioning,3,Flickr30k; COCO (Common Objects in Context); FlickrStyle10K,Flickr30k; COCO (Common Objects in Context); FlickrStyle10K,CIDEr,Pseudo Label,Miscellaneous,N/A
mage-to-Text Retrieval,3,Flickr30k; MSCOCO; COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Single-object discovery,3,COCO (Common Objects in Context); Object Discovery; PASCAL VOC,VOC_all; VOC12; VOC_6x2; Object Discovery; COCO_20k,CorLoc,N/A,Computer Vision,N/A
Multi-object discovery,3,COCO (Common Objects in Context); RF100; PASCAL VOC,VOC_all; VOC12; COCO_20k,Detection Rate,N/A,Computer Vision,N/A
Object Proposal Generation,3,COCO (Common Objects in Context); CocoDoom; Comic2k,N/A,N/A,N/A,N/A,N/A
Visual Keyword Spotting,3,LRW; LRS2; LRS3-TED,N/A,N/A,N/A,N/A,N/A
Early Action Prediction,3,Something-Something V2; NTU RGB+D; UCF101,N/A,N/A,N/A,N/A,N/A
Image Generation from Scene Graphs,3,Visual Genome; Home Action Genome; BDD100K-Subsets,N/A,N/A,N/A,N/A,N/A
Scene Graph Classification,3,ImageCLEF-DA; Visual Genome; HRI Simple Tasks,N/A,N/A,N/A,N/A,N/A
Zero-Shot Semantic Segmentation,3,COCO-Stuff; ADE20K; PASCAL VOC,COCO-Stuff; MESS; PASCAL VOC; ADE20K-847,Mean IoU; Transductive Setting hIoU; unseen mIoU; Inductive Setting hIoU,N/A,Computer Vision,N/A
Defocus Blur Detection,3,HistoArtifacts; CUHK03; EBD,DUT ; CTCUG; CUHK; SZU blur detection; EBD,IoU; Mean absolute error; MAE,N/A,Computer Vision,N/A
Action Unit Detection,3,Aff-Wild; 4DFAB; BP4D,N/A,N/A,N/A,N/A,N/A
Point-interactive Image Colorization,3,ImageNet ctest10k; CUB-200-2011; Oxford 102 Flower,N/A,N/A,N/A,N/A,N/A
Temporal Information Extraction,3,THYME-2016; TempEval-3; TimeBank,TempEval-3; TimeBank,F1 score; Temporal awareness,Temporal Tagging,Natural Language Processing,"Temporal information extraction is the identification of chunks/tokens corresponding to temporal intervals, and the extraction and determination of the temporal relations between those. The entities e..."
Partially Relevant Video Retrieval,3,ActivityNet Captions; TVR; Charades-STA,N/A,N/A,N/A,N/A,N/A
Dense Object Detection,3,xView3-SAR; SKU110K; DOTA,N/A,N/A,N/A,N/A,N/A
Audio-Visual Active Speaker Detection,3,AVA; VPCD; AVA-ActiveSpeaker,N/A,N/A,N/A,N/A,N/A
Graph Anomaly Detection,3,Amazon-Fraud; Yelp; Yelp-Fraud,N/A,N/A,N/A,N/A,N/A
Multi-Object Tracking and Segmentation,3,BURST; BDD100K; KITTI MOTS,BDD100K val; KITTI MOTS,HOTA; AssA; mMOTSA; DetA,N/A,Computer Vision,"Multiple object tracking and segmentation requires detecting, tracking, and segmenting objects belonging to a set of given classes.     (Image and definition credit: [Prototypical Cross-Attention Netw..."
Semantic Object Interaction Classification,3,SPACE; Kinetics; Kinetics-700,N/A,N/A,N/A,N/A,N/A
Event Segmentation,3,EgoProceL; Kinetics; Kinetics 400,Kinetics-400,F1,Generic Event Boundary Detection,Computer Vision,N/A
Unconditional Image Generation,3,Large Labelled Logo Dataset (L3D); CelebA-HQ; ArtBench-10 (32x32),N/A,N/A,N/A,N/A,N/A
Unsupervised Facial Landmark Detection,3,AFLW; MAFL; 300W,N/A,N/A,N/A,N/A,N/A
Unsupervised Video Summarization,3,OSTD; SumMe; TvSum,N/A,N/A,N/A,N/A,N/A
Multi-Hypotheses 3D Human Pose Estimation,3,Human3.6M; AH36M; MPI-INF-3DHP,N/A,N/A,N/A,N/A,N/A
Unsupervised Human Pose Estimation,3,Human3.6M; DeepFashion; Tai-Chi-HD,N/A,N/A,N/A,N/A,N/A
3D Face Alignment,3,FaceWarehouse; FaMoS; AFLW2000-3D,N/A,N/A,N/A,N/A,N/A
Face Hallucination,3,KID-F; EDFace-Celeb-1M; FFHQ,N/A,N/A,N/A,N/A,N/A
Colorectal Gland Segmentation:,3,Kvasir; STARE; Kvasir-SEG,CRAG; STARE,Hausdorff Distance (mm); AUC; Dice; F1-score,N/A,Medical,N/A
Lung Nodule Detection,3,LUNA16; LUNA; LIDC-IDRI,LUNA2016 FPRED; LIDC-IDRI,mean average precision; AUC; Sensitivity,Lung Nodule 3D Detection,Computer Vision; Medical,N/A
Molecular Property Prediction (1-shot)),3,SIDER; Tox21; PubChem18,N/A,N/A,N/A,N/A,N/A
Graph Property Prediction,3,QM9; OGB; PCQM4Mv2-LSC,ogbg-molpcba; ogbg-ppa; QM9; ogbg-molhiv; ogbg-code2,Test ROC-AUC; Number of params; gap (meV); Validation ROC-AUC; logMAE; Validation F1 score; Test F1 score; Validation AP; alpha (ma); Ext. data,XRD regression; SAXS regression; Neutron PDF; Neutron PDF regression; X-ray PDF regression,Graphs,N/A
Prediction,3,QM9; Bone Cement Removal with Audio-Monitoring; SDWPF,QM9; Synthetic,Edit Distance; RMSE,N/A,Time Series,N/A
Supervised Text Retrieval,3,Reuters-21578; 20 Newsgroups; COVID-19 Twitter Chatter Dataset,N/A,N/A,N/A,N/A,N/A
Human Instance Segmentation,3,OCHuman; TikTok Dataset; PeopleSansPeople,OCHuman,AP,Pose-Based Human Instance Segmentation,Computer Vision,Instance segmentation is the task of detecting and delineating each distinct object of interest appearing in an image.    Image Credit: [Deep Occlusion-Aware Instance Segmentation with Overlapping BiL...
3D Room Layouts From A Single RGB Panorama,3,Rent3D; 2D-3D-S; PanoContext,N/A,N/A,N/A,N/A,N/A
Unsupervised Saliency Detection,3,ECSSD; DUT-OMRON; DUTS,N/A,N/A,N/A,N/A,N/A
Image to sketch recognition,3,PACS; Sketchy; Im4Sketch,N/A,N/A,N/A,N/A,N/A
Zero Shot Skeletal Action Recognition,3,NTU RGB+D; NTU RGB+D 120; PKU-MMD,N/A,N/A,N/A,N/A,N/A
Generalized Zero Shot skeletal action recognition,3,NTU RGB+D; NTU RGB+D 120; PKU-MMD,N/A,N/A,N/A,N/A,N/A
Unsupervised Skeleton Based Action Recognition,3,NTU RGB+D; NTU RGB+D 120; PKU-MMD,N/A,N/A,N/A,N/A,N/A
Bird's-Eye View Semantic Segmentation,3,LandCover.ai; SimBEV; nuScenes,Lyft Level 5; SimBEV; nuScenes,IoU lane - 224x480 - 100x100 at 0.5; IoU veh - 224x480 - No vis filter - 100x100 at 0.5; IoU vehicle - 224x480 - Short; car; rider; IoU veh - 224x480 - Vis filter. - 100x100 at 0.5; IoU veh - 224x480 - No vis filter - 100x50 at 0.25; IoU veh - 448x800 - No vis filter - 100x100 at 0.5; IoU ped - 224x480 - Vis filter. - 100x100 at 0.5; road,N/A,Computer Vision,N/A
Skin Cancer Segmentation,3,SD-198; ISIC 2020 Challenge Dataset; PH2,N/A,N/A,N/A,N/A,N/A
4D Panoptic Segmentation,3,4D-OR; SemanticKITTI; MM-OR,SemanticKITTI,LSTQ,N/A,Computer Vision,"**4D Panoptic Segmentation** is a computer vision task that extends [video panoptic segmentation](https://paperswithcode.com/task/video-panoptic-segmentation) to point cloud sequences. That is, given ..."
Twitter Bot Detection,3,Crypto related tweets from 10.10.2020 to 3.3.2021; MGTAB; MIB Dataset,MGTAB; MIB Dataset,Acc; F1; Accuracy,N/A,Miscellaneous,Academic studies estimate that up to 15% of Twitter users are automated bot accounts [1]. The prevalence of Twitter bots coupled with the ability of some bots to give seemingly human responses has ena...
Multi-Frame Super-Resolution,3,BurstSR; Inter4K; PROBA-V,N/A,N/A,N/A,N/A,N/A
Video Emotion Detection,3,CMU-MOSEI; L-SVD; Ekman6,N/A,N/A,N/A,N/A,N/A
Continuous Affect Estimation,3,NEMO; AffectNet; AMIGOS,AffectNet; AMIGOS; 	AffectNet,CCC (Arousal); PCC (Valence); Concordance correlation coefficient (CCC); CCC (Valence); PCC (Arousal),N/A,Computer Vision,N/A
Event data classification,3,N-Caltech 101; DVS128 Gesture; CIFAR10-DVS,N-Caltech 101; DVS128 Gesture; CIFAR10-DVS,Accuracy; Accuracy (% ),N/A,Computer Vision,N/A
Open Vocabulary Action Detection,3,UCF101-24; JHMDB; MultiSports,UCF101-24; JHMDB; MultiSports,val mAP,N/A,Computer Vision,N/A
Target Sound Extraction,3,AudioSet; AudioCaps; FSDSoundScapes,AudioSet; AudioCaps; FSDSoundScapes,SDRi; SI-SDRi; SI-SNRi,Streaming Target Sound Extraction,Audio,Target Sound Extraction is the task of extracting a sound corresponding to a given class from an audio mixture. The audio mixture may contain background noise with a relatively low amplitude compared ...
Traffic Accident Detection,3,TAP; CAP-DATA; A3D,A3D; SA; custom,AUC; Average F1,Accident Anticipation,Computer Vision,N/A
Recognizing And Localizing Human Actions,3,Capture-24; UESTC RGB-D; HAR,N/A,N/A,N/A,N/A,N/A
Document-level Closed Information Extraction,3,DWIE; DocRED-IE; DocRED,N/A,N/A,N/A,N/A,N/A
Multi-tissue Nucleus Segmentation,3,Kumar; PanNuke; CoNSeP,CoNSeP; PanNuke; Kumar,Hausdorff Distance (mm); PQ; Jaccard Index; Dice,N/A,Medical,N/A
Clique Prediction,3,Arxiv GR-QC; arXiv Astro-Ph; Arxiv HEP-TH citation graph,N/A,N/A,N/A,N/A,N/A
Polyp Segmentation,3,PolypGen; Kvasir; Kvasir-SEG,N/A,N/A,N/A,N/A,N/A
Drone Pose Estimation,3,UAVA; Drunkard's Dataset; VID Dataset,N/A,N/A,N/A,N/A,N/A
Video Saliency Detection,3,AViMoS; DHF1K; MSU Video Saliency Prediction,N/A,N/A,N/A,N/A,N/A
Video Saliency Prediction,3,AViMoS; DHF1K; EgoMon,N/A,N/A,N/A,N/A,N/A
Multimodal Abstractive Text Summarization,3,M3LS; FINDSum; How2,N/A,N/A,N/A,N/A,N/A
Text based Person Retrieval,3,ICFG-PEDES; CUHK-PEDES; RSTPReid,ICFG-PEDES; CUHK-PEDES; RSTPReid,R@10; Rank-5; Rank-10; mAP; mINP; Rank-1; R@1; R@5,N/A,Computer Vision,N/A
Text-based Person Retrieval with Noisy Correspondence,3,ICFG-PEDES; CUHK-PEDES; RSTPReid,ICFG-PEDES; CUHK-PEDES; RSTPReid,Rank 5; Rank 1; mAP; Rank-5; mINP; Rank-10; Rank-1; Rank 10,N/A,Computer Vision,This is a benchmark about text-based person retrieval with noisy correspondence. All recorded values ​​are the results under 20% noise rate.
Semantic Retrieval,3,Speech Brown; Phrase-in-Context; Contract Discovery,Contract Discovery,Soft-F1,N/A,Natural Language Processing,N/A
3D Point Cloud Data Augmentation,3,ModelNet40-C; ModelNet; ScanObjectNN,N/A,N/A,N/A,N/A,N/A
Recognizing Emotion Cause in Conversations,3,RECCON; EmoCause; CANDOR Corpus,RECCON; EmoCause,Top-5 Recall; F1; Top-3 Recall; F1(Neg); F1(Pos); Top-1 Recall; Exact Span F1,Causal Emotion Entailment,Natural Language Processing,"Given an utterance U, labeled with emotion E, the task is to extract the causal spans S from the conversational history H (including utterance U) that sufficiently represent the causes of emotion E."
Text-Based Stock Prediction,3,Astock; stocknet; EDT,N/A,N/A,N/A,N/A,N/A
Audio Super-Resolution,3,DSD100; VCTK; MedleyDB 2.0,Voice Bank corpus (VCTK); DSD100; VCTK Multi-Speaker; Piano,SNR; Log-Spectral Distance,N/A,Audio,"Audio super-resolution, especially speech, refers to the process of reconstructing high-resolution music signals from their low-resolution counterparts. Essentially, it enhances the quality of a speec..."
Multi Future Trajectory Prediction,3,ETH; KITTI-trajectory-prediction; TrajAir: A General Aviation Trajectory Dataset,N/A,N/A,N/A,N/A,N/A
Grayscale Image Denoising,3,BSD; Urban100; Set12,N/A,N/A,N/A,N/A,N/A
Multi-class Anomaly Detection,3,ITDD; MVTecAD; VisA,N/A,N/A,N/A,N/A,N/A
Loop Closure Detection,3,KAIST Urban; New College; AUT-VI,N/A,N/A,N/A,N/A,N/A
Drug–drug Interaction Extraction,3,DrugBank; DDI; Drug Combination Extraction Dataset,N/A,N/A,N/A,N/A,N/A
Online Action Detection,3,THUMOS14; FineAction; TVSeries,N/A,N/A,N/A,N/A,N/A
Chord Recognition,3,JAAH; RWC; Filosax,N/A,N/A,N/A,Audio,N/A
Direction of Arrival Estimation,3,LOCATA; TAU-NIGENS Spatial Sound Events 2020; TAU-NIGENS Spatial Sound Events 2021,SOFA,Angular Error,N/A,Audio,Estimating the direction-of-arrival (DOA) of a sound source from multi-channel recordings.
Volumetric Medical Image Segmentation,3,RAOS; Radio-Freqency Ultrasound volume dataset for pre-clinical liver tumors; PROMISE12,N/A,N/A,N/A,N/A,N/A
speech-recognition,3,VoxPopuli; Common Voice; OpenSLR,N/A,N/A,N/A,N/A,N/A
Predicate Detection,3,CoNLL-2012; Product Reviews 2017; CoNLL,N/A,N/A,N/A,N/A,N/A
Semantic Role Labeling (predicted predicates),3,CoNLL-2012; Product Reviews 2017; CoNLL,N/A,N/A,N/A,N/A,N/A
Sentence segmentation,3,MASC; CoNLL 2017 Shared Task - Automatically Annotated Raw Texts and Word Embeddings; CoNLL,N/A,N/A,N/A,N/A,N/A
Binary Relation Extraction,3,SciREX; BioRED; BB,N/A,N/A,N/A,N/A,N/A
Repetitive Action Counting,3,RepCount; UCFRep; Countix,RepCount; UCFRep; Countix,OBO; OBZ; MAE; RMSE,N/A,Computer Vision,Repetitive action counting aims to count the number of repetitive actions in a video.
Gaze Target Estimation,3,VideoAttentionTarget; GazeFollow; EyeInfo,VideoAttentionTarget; GazeFollow,AUC; AP; Average Distance,N/A,Computer Vision,Gaze Target Estimation refers to predicting the image 2D gaze location of a person in the image.
3D Point Cloud Matching,3,2D-3D Match Dataset; DeformingThings4D; 4DMatch,N/A,N/A,N/A,N/A,N/A
Reflection Removal,3,SlowFlow; CDR; PolarRR,Real20; SIR^2(Wild); SIR^2(Objects); SIR^2(Postcard); Nature,PSNR; SSIM,N/A,Computer Vision,Remove the spots from mirror and clear the picture
Definition Extraction,3,DEFT Corpus; ProNCI; UJ-CS/Math/Phy,N/A,N/A,N/A,N/A,N/A
Action Spotting,3,SoccerNet-v2; GolfDB; SoccerNet,N/A,N/A,N/A,N/A,N/A
Inverse Rendering,3,Hypersim; MID Intrinsics; Stanford-ORB,Stanford-ORB,HDR-PSNR,N/A,Computer Vision,"**Inverse Rendering** is the task of recovering the properties of a scene, such as shape, material, and lighting, from an image or a video. The goal of inverse rendering is to determine the properties..."
3D Panoptic Segmentation,3,Hypersim; 4D-OR; MM-OR,N/A,N/A,N/A,N/A,N/A
Hyperspectral Semantic Segmentation,3,Hyperspectral City; HyKo2-VIS; HSI-Drive v2.0,N/A,N/A,N/A,N/A,N/A
Chinese Word Segmentation,3,MSRA CN NER; LSICC; CUGE,N/A,N/A,N/A,N/A,N/A
Spindle Detection,3,Montreal Archive of Sleep Studies; MODA dataset; MaSS,N/A,N/A,N/A,N/A,N/A
Face Clustering,3,A View From Somewhere (AVFS); EasyCom; MovieGraphs,N/A,N/A,N/A,N/A,N/A
Event-based Optical Flow,3,EKubric; MVSEC; DSEC,N/A,N/A,N/A,N/A,N/A
lidar absolute pose regression,3,Oxford Radar RobotCar Dataset; ConSLAM; SLAM2REF,vReLoc (Seq-06); vReLoc (Seq-07); vReLoc (Seq-05); Oxford Radar RobotCar (Full-8); vReLoc (Seq-14); Oxford Radar RobotCar (Full-9); Oxford Radar RobotCar (Full-6); Oxford Radar RobotCar (Full-7),Mean Translation/Rotation Error (m/degree); Median Translation/Rotation Error (m/degree),N/A,Computer Vision,N/A
Fovea Detection,3,ADAM; IDRiD; REFUGE Challenge,ADAM; IDRiD; REFUGE Challenge; REFUGE,Euclidean Distance (ED),N/A,Medical,N/A
Optic Disc Detection,3,ADAM; IDRiD; REFUGE Challenge,IDRiD; REFUGE Challenge,IoU; Euclidean Distance (ED),N/A,Medical,Region proposal for optic disc
Object Discovery,3,Infinity-MM; ShapeStacks; UVO,N/A,N/A,N/A,Computer Vision,"**Object Discovery** is the task of identifying previously unseen objects.   <span class=""description-source"">Source: [Unsupervised Object Discovery and Segmentation of RGBD-images ](https://arxiv.org..."
Visual Entailment,3,e-SNLI-VE; VSR; SNLI-VE,N/A,N/A,N/A,N/A,N/A
Spatio-Temporal Video Grounding,3,VidSTG; HC-STVG2; HC-STVG1,VidSTG; HC-STVG2; HC-STVG1,vIoU@0.3; vIoU@0.5; Declarative vIoU@0.5; Interrogative vIoU@0.3; m_vIoU; Val m_vIoU; Val vIoU@0.5; Interrogative vIoU@0.5; Declarative vIoU@0.3; Declarative m_vIoU,N/A,Computer Vision,Spatio-temporal video grounding is a computer vision and natural language processing (NLP) task that involves linking textual descriptions to specific spatio-temporal regions or moments in a video. In...
3D Object Detection From Monocular Images,3,3D-POP; Waymo Open Dataset; KITTI-360,N/A,N/A,N/A,N/A,N/A
Aspect Term Extraction and Sentiment Classification,3,Casino Reviews; AWARE; YASO,N/A,N/A,N/A,N/A,N/A
Road Damage Detection,3,Pothole Mix; NPO; RDD-2020,NPO,mIoU,N/A,Computer Vision,"Road damage detection is the task of detecting damage in roads.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Road Damage Detection And Classification In Smartphone Captured Images Using ..."
Texture Synthesis,3,CBTex; 3D-FUTURE; QST,N/A,N/A,N/A,Computer Vision,"The fundamental goal of example-based **Texture Synthesis** is to generate a texture, usually larger than the input, that faithfully captures all the visual characteristics of the exemplar, yet is nei..."
Breast Cancer Histology Image Classification,3,BCI; BreakHis; ICIAR 2018 Grand Challenge on Breast Cancer Histology Images,BreakHis; ICIAR 2018 Grand Challenge on Breast Cancer Histology Images,1:1 Accuracy; Accuracy (%); Accuracy (Inter-Patient); Accuracy (% ),Breast Cancer Histology Image Classification (20% labels); Breast Cancer Detection,Computer Vision; Knowledge Base,N/A
Medical Image Retrieval,3,BreastDICOM4; BreastRates4; BreakHis,N/A,N/A,N/A,N/A,N/A
Pedestrian Trajectory Prediction,3,Euro-PVI; JAAD; PIE,N/A,N/A,N/A,N/A,N/A
Face Morphing Attack Detection,3,FRLL-Morphs; SMDD; FLUXSynID,N/A,N/A,N/A,N/A,N/A
Voice Query Recognition,3,TAT; Fluent Speech Commands; Banglish,Banglish,Accuracy (%),N/A,Speech,N/A
Surgical Gesture Recognition,3,MultiBypass140; MISAW; PETRAW,N/A,N/A,N/A,Medical,N/A
Affordance Detection,3,Visual Affordance Learning; PADv2; 3D AffordanceNet,3D AffordanceNet Partial View; 3D AffordanceNet Rotate SO(3); 3D AffordanceNet Rotate z; 3D AffordanceNet,AIOU; mAP,N/A,Computer Vision,"Affordance detection refers to identifying the potential action possibilities of objects in an image, which is an important ability for robot perception and manipulation.    Image source: [Object-Base..."
Human-Object-interaction motion tracking,3,MPHOI-72; BEHAVE; Win-Fail Action Understanding,N/A,N/A,N/A,N/A,N/A
Cardiac Segmentation,3,Echonet-Dynamic; ACDC Scribbles; CAMUS,N/A,N/A,N/A,N/A,N/A
Weakly supervised segmentation,3,CheXlocalize; ACDC Scribbles; MARIDA,N/A,N/A,N/A,Computer Vision,N/A
Sign Language Production,3,How2Sign; LSFB Datasets; Mediapi-RGB,N/A,N/A,N/A,Natural Language Processing,"Sign Language Production (SLP) is the automatically translation from spoken language sentences into sign language sequences. Whilst Sign language Translation translates from sign to text, SLP is the o..."
Texture Image Retrieval,3,ISOD; ElBa; LAS&T: Large Shape & Texture Dataset,N/A,N/A,N/A,N/A,N/A
Supervised Defect Detection,3,SensumSODF; KolektorSDD2; ISP-AD,N/A,N/A,N/A,N/A,N/A
Argument Retrieval,3,BEIR; FinArg; Webis-Touché-2020,N/A,N/A,N/A,N/A,N/A
Personality Trait Recognition,3,SynthPAI; Essays; CPED,SynthPAI; Essays,Accuracy; F-Measure; Recall; Average accuracy in %; Precision,N/A,Computer Vision,N/A
Semantic Image Similarity,3,CxC; NIGHTS; fruit-SALAD,N/A,N/A,N/A,N/A,N/A
Histopathological Image Classification,3,BCNB; DiagSet; Chaoyang,N/A,N/A,N/A,Medical,N/A
Radar Object Detection,3,RaDelft; BAAI-VANJEE; CRUW,N/A,N/A,N/A,Robots,The radar object detection (ROD) task aims to classify and localize the objects in 3D purely from radar's radio frequency (RF) images.
Activity Prediction,3,InfiniteRep; MODIS AOD (imputed); Home Action Genome,ActEV,mAP,Cyber Attack Detection; Sequential skip prediction; motion prediction,Methodology; Computer Vision; Time Series,Predict human activities in videos
Satire Detection,3,CIDII Dataset; SaRoCo; YesBut,N/A,N/A,N/A,N/A,N/A
Segmentation Of Remote Sensing Imagery,3,Five-Billion-Pixels; GID; CaBuAr,N/A,N/A,Lake Ice Monitoring,Computer Vision,N/A
3D Point Cloud Reconstruction,3,Boombox; HUMAN4D; IBISCape,N/A,N/A,3D Point Cloud Classification,Computer Vision,Encoding and reconstruction of 3D point clouds.
Review Generation,3,ICLR Database; MOPRD; ReviewRobot Dataset,N/A,N/A,N/A,Natural Language Processing,N/A
Photo Retouching,3,MIT-Adobe FiveK; IntHarmony; NILUT,MIT-Adobe 5k; MIT-Adobe 5k (1080p); MIT-Adobe 5k (480p),PSNR; SSIM,N/A,Computer Vision,N/A
Stock Price Prediction,3,Astock; TRADES-LOB; EDT,N/A,N/A,N/A,N/A,N/A
Semantic Frame Parsing,3,ProSLU; MixATIS; MixSNIPS,N/A,N/A,N/A,N/A,N/A
Multilingual Named Entity Recognition,3,UNER v1; WikiNEuRal; XFUND,N/A,N/A,N/A,N/A,N/A
Video Matting,3,VideoMatting108; VideoMatte240K; PhotoMatte85,N/A,N/A,N/A,Computer Vision,Image credit: [https://arxiv.org/pdf/2012.07810v1.pdf](https://arxiv.org/pdf/2012.07810v1.pdf)
Camera Auto-Calibration,3,BrnoCompSpeed; CVGL Camera Calibration Dataset; OV,N/A,N/A,N/A,Computer Vision,N/A
Thermal Infrared Pedestrian Detection,3,NII-CU MAPD; LLVIP; InfraParis,N/A,N/A,N/A,N/A,N/A
Reading Order Detection,3,ROOR; opendataset; ReadingBank,ROOR; ReadingBank,Average Page-level BLEU; Segment-level F1; Average Relative Distance (ARD),N/A,Natural Language Processing,Reading order detection aims to capture the word sequence which can be naturally comprehended by human readers from visually-rich documents. It is a fundamental task for visually-rich document underst...
Scientific Concept Extraction,3,SemOpenAlex; SemEval-2021 Task-11; unarXive,N/A,N/A,N/A,N/A,N/A
3D Geometry Perception,3,FEE Corridor; OmniObject3D; EUEN17037_Daylight_and_View_Standard_TestDataSet,N/A,N/A,N/A,N/A,N/A
2D Semantic Segmentation task 3 (25 classes),3,CaDIS; ((Claim~in-easy~way))How to make a claim against Expedia?; IBISCape,CaDIS,Mean IoU (test); Mean IoU (val),Document Enhancement,Computer Vision,N/A
Aspect-Category-Opinion-Sentiment Quadruple Extraction,3,DiaASQ; Laptop-ACOS; Restaurant-ACOS,Laptop-ACOS; Restaurant-ACOS,F1,Conversational Sentiment Quadruple Extraction,Natural Language Processing,Aspect-Category-Opinion-Sentiment (ACOS) Quadruple Extraction is the task with the goal to extract all aspect-category-opinion-sentiment quadruples in a review sentence. ( and provide full support for...
Multiobjective Optimization,3,Database of axial impact simulations of the crash box; Deep Sea Treasure Pareto-Front; RSM-based multi-objective optimization using desirability functions,N/A,N/A,Crashworthiness optimisation,Methodology,"Multi-objective optimization (also known as multi-objective programming, vector optimization, multicriteria optimization, multiattribute optimization or Pareto optimization) is an area of multiple cri..."
3D Shape Retrieval,3,SketchyVR; HUMAN4D; LAS&T: Large Shape & Texture Dataset,N/A,N/A,N/A,N/A,N/A
Parameter Prediction,3,DrivAerNet; MODIS AOD (imputed); DeepNets-1M,CIFAR10,Classification Accuracy (ViT); Classification Accuracy (Wide); Classification Accuracy (ID-test); Classification Accuracy (ResNet-50); Classification Accuracy (Dense); Classification Accuracy (Deep); Classification Accuracy (BN-free),N/A,Miscellaneous,N/A
Unfairness Detection,3,DemogPairs; Multilingual Terms of Service; FairFD,N/A,N/A,N/A,N/A,N/A
Extractive Summarization,3,SubSumE; Bengali Curated News Summary Dataset; OpenDebateEvidence,N/A,N/A,N/A,N/A,N/A
Key Point Matching,3,FewSOL; ArgKP-2021; ENRICH,N/A,N/A,N/A,N/A,N/A
Portrait Segmentation,3,P3M-10k; PP-HumanSeg14K; EasyPortrait,N/A,N/A,N/A,N/A,N/A
Underwater Image Restoration,3,Underwater Object Detection Dataset; LSUI; MVK,N/A,N/A,N/A,N/A,N/A
Transparent Object Depth Estimation,3,TransProteus; TransCG; SuperCaustics,N/A,N/A,N/A,N/A,N/A
Atrial Fibrillation Detection,3,CPSC2021; IRIDIA-AF; AF Classification from a Short Single Lead ECG Recording - The PhysioNet Computing in Cardiology Challenge 2017,N/A,N/A,N/A,N/A,N/A
Joint Event and Temporal Relation Extraction,3,2012 i2b2 Temporal Relations; TimeBankPT; Spanish TimeBank 1.0,TB-Dense,Event Detection F-score,N/A,N/A,N/A
Aspect Category Polarity,3,Casino Reviews; FABSA; AWARE,AWARE,Accuracy (%),N/A,Natural Language Processing,N/A
Speech Denoising,3,EmoSpeech; WHAMR_ext; mDRT,LRS3+VGGSound; LRS2+VGGSound,STOI; PESQ; CBAK; CSIG; COVL,N/A,Speech,Obtain the clean speech of the target speaker by suppressing the background noise. Recent representative github platform [ClearerVoice-Studio](https://github.com/modelscope/ClearerVoice-Studio)
Inductive Link Prediction,3,Wikidata5M-SI; ILPC22-Large; ILPC22-Small,N/A,N/A,N/A,N/A,N/A
Video Domain Adapation,3,RoCoG-v2; Human-Animal-Cartoon; ActorShift,N/A,N/A,N/A,N/A,N/A
Line Detection,3,UrduDoc; NKL; SEL,NKL; SEL,HIoU; F_measure (EA); AUC_F,N/A,Computer Vision,N/A
Speech Extraction,3,WHAMR_ext; WSJ0-2mix-extr; MC_GRID,N/A,N/A,N/A,N/A,N/A
Animal Action Recognition,3,CVB; LoTE-Animal; Animal Kingdom,N/A,N/A,cow identification,Computer Vision,"Cross-species (intra-class, inter-class) action recognition"
Multi-Animal Tracking with identification,3,CVB; Animals-10; 3D-POP,N/A,N/A,N/A,N/A,N/A
Zero-shot Text Retrieval,3,Flickr30k-CNA; WebLI; simco-comco,N/A,N/A,N/A,N/A,N/A
Point Cloud Segmentation,3,PointCloud-C; WaterScenes; WildScenes,PointCloud-C,mean Corruption Error (mCE),N/A,Computer Vision,"3D point cloud segmentation is the process of  classifying point clouds into multiple homogeneous regions, the  points in the same region will have the same properties. The  segmentation is challengin..."
Audio Emotion Recognition,3,ARAUS; nEMO; Soundscape Attributes Translation Project (SATP) Dataset,N/A,N/A,N/A,N/A,N/A
HDR Reconstruction,3,SI-HDR; HDRT; MSU HDR Video Reconstruction Benchmark,N/A,N/A,Multi-Exposure Image Fusion,Computer Vision,N/A
Dialect Identification,3,FreCDo; ArSarcasm-v2; ArSarcasm,N/A,N/A,N/A,N/A,N/A
Blind Image Quality Assessment,3,UHD-IQA; PIQ23; MSU NR VQA Database,N/A,N/A,N/A,N/A,N/A
Photoplethysmography (PPG) beat detection,3,UBFC-rPPG; MIMIC PERform Testing Dataset; MMSE-HR,N/A,N/A,N/A,N/A,N/A
3D Volumetric Reconstruction,3,NMC Li-ion Battery Cathode Energies and Charge Densities; Ethylene Carbonate Molecular Dynamics; HIV (Human Immunodeficiency Virus),N/A,N/A,N/A,N/A,N/A
Multimodal Intent Recognition,3,MIntRec; MMDialog; PhotoChat,N/A,N/A,N/A,N/A,N/A
Extractive Question-Answering,3,M2QA; HR Extractive Question Answering; CodeQueries,N/A,N/A,N/A,N/A,N/A
Crop Yield Prediction,3,CropAndWeed; SICKLE; SemanticSugarBeets,SICKLE; 2018 Syngenta (2016 val),MAPE (%); RMSE,N/A,Miscellaneous; Computer Vision,N/A
Low-Dose X-Ray Ct Reconstruction,3,X3D; A collection of 131 CT datasets of pieces of modeling clay containing stones; 2DeteCT,N/A,N/A,N/A,N/A,N/A
Organ Segmentation,3,Extended Task10_Colon Medical Decathlon; BTCV; CUTS,N/A,N/A,N/A,N/A,N/A
Robust 3D Semantic Segmentation,3,nuScenes-C; WOD-C; SemanticKITTI-C,N/A,N/A,N/A,N/A,N/A
Text Detection,3,UrduDoc; BFRD; Media-Text,UrduDoc,Precision; Recall,N/A,Computer Vision,Detecting the text in the image and localise it using a bounding box. The text can be in any shape and size. We need to localise all such instances of text in the entire image along with bounding box ...
Zero Shot Segmentation,3,Segmentation in the Wild; MatSeg; TomoSAM,Segmentation in the Wild; ADE20K training-free zero-shot segmentation,mIoU; Mean AP,N/A,Computer Vision,N/A
Open Intent Detection,3,Banking_CG; OOS_CG; StackOverflow_CG,N/A,N/A,N/A,N/A,N/A
LLM-generated Text Detection,3,GenSC-6G; WaterBench; OUTFOX,N/A,N/A,N/A,N/A,N/A
Video-Adverb Retrieval (Unseen Compositions),3,ActivityNet Adverbs; MSR-VTT Adverbs; VATEX Adverbs,N/A,N/A,N/A,N/A,N/A
Robot Pose Estimation,3,SLAM2REF; ConSLAM; DREAM-dataset,N/A,N/A,N/A,N/A,N/A
Image to 3D,3,Coil100-Augmented; Bone Fracture Multi-Region X-ray Dataset; VBR,N/A,N/A,N/A,Computer Vision,N/A
Audio Deepfake Detection,3,ASVspoof 2021; FakeMusicCaps; SONICS,N/A,N/A,N/A,N/A,N/A
Camera Pose Estimation,3,KITTI Odometry Benchmark; ConSLAM; SLAM2REF,KITTI Odometry Benchmark,Average Translational Error et[%]; Average Rotational Error er[%]; Absolute Trajectory Error [m],Panorama Pose Estimation (N-view),Computer Vision,Camera pose estimation is a crucial task in computer vision and robotics that involves determining the position and orientation (pose) of a camera relative to a given reference frame. This task is ess...
Attribute Mining,3,OA-Mine - annotations; AE-110k; MAVE,OA-Mine - annotations; AE-110k; MAVE,F1-score,N/A,Natural Language Processing,"The attribute mining task assumes an open-world setting in which novel attributes and their values are extracted from free text.  The task is mainly studied in the e-commerce context, where new attrib..."
Noisy Semantic Image Synthesis,3,noisy-ADE20K-Random; noisy-ADE20K-DS; noisy-ADE20K-Edge,N/A,N/A,N/A,N/A,N/A
Key-value Pair Extraction,3,SIBR; RFUND-EN; RFUND,N/A,N/A,N/A,N/A,N/A
Open-Domain Subject-to-Video,3,CoIR; OpenS2V-5M; OpenS2V-Eval,N/A,N/A,N/A,N/A,N/A
"1 Image, 2*2 Stitchi",3,[FaQ-s::Expert-Guide]How do I speak to an Expedia agent?; FQL-Driving; [[Human!!!Support]] How do I get a human at Expedia?,FQL-Driving,0..5sec,Voice Conversion; Image Deblurring; Pose Estimation; Image to Video Generation; Talking Face Generation,Music; Time Series; Knowledge Base; Computer Vision; Audio; Natural Language Processing; Speech; Medical; Reasoning; Robots,N/A
Image to text,3,CII-Bench; PsOCR; Urdu Text Scene Images,N/A,N/A,N/A,N/A,N/A
Medical Image Analysis,3,Liver-US; ICIAR 2018 Grand Challenge on Breast Cancer Histology Images; MP-IDB,N/A,N/A,Nuclei Classification; Whole Mammogram Classification; Shadow Confidence Maps In Ultrasound Imaging; Multi-Focus Microscopical Images Fusion; Colon Cancer Detection In Confocal Laser Microscopy Images,Medical,N/A
RGB-D Instance Segmentation,3,SUN-RGBD-IS; Box-IS; NYUDv2-IS,N/A,N/A,N/A,N/A,N/A
Antibody-antigen binding prediction,3,MIPE; PECAN; Paragraph Expanded,N/A,N/A,N/A,N/A,N/A
Sentence Retrieval,3,WangchanX-Legal-ThaiCCL-RAG; NitiBench; PeerQA,N/A,N/A,N/A,N/A,N/A
Nested Term Extraction,3,RuTermEval (Track 1); RuTermEval (Track 2); RuTermEval (Track 3),N/A,N/A,N/A,N/A,N/A
Nested Term Recognition from Flat Supervision,3,RuTermEval (Track 1); RuTermEval (Track 2); RuTermEval (Track 3),N/A,N/A,N/A,N/A,N/A
Unsupervised Image-To-Image Translation,2,MNIST; Freiburg Forest,SVNH-to-MNIST; Freiburg Forest Dataset,PSNR; Classification Accuracy,Sensor Modeling,Computer Vision,"Unsupervised image-to-image translation is the task of doing image-to-image translation without ground truth image-to-image pairings.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Unpaire..."
Superpixel Image Classification,2,MNIST; MPI FAUST Dataset,N/A,N/A,N/A,N/A,N/A
Semantic Textual Similarity within Bi-Encoder,2,MRPC; GLUE,N/A,N/A,N/A,N/A,N/A
Constituency Grammar Induction,2,Penn Treebank; PTB Diagnostic ECG Database,N/A,N/A,N/A,N/A,N/A
Unsupervised Object Localization,2,COCO (Common Objects in Context); PASCAL VOC 2007,N/A,N/A,N/A,N/A,N/A
One-Shot Object Detection,2,COCO (Common Objects in Context); SKU110K,N/A,N/A,N/A,N/A,N/A
One-Shot Instance Segmentation,2,COCO (Common Objects in Context); UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
Few Shot Open Set Object Detection,2,COCO (Common Objects in Context); MSCOCO,MSCOCO,AR_U,N/A,Computer Vision,N/A
Lip to Speech Synthesis,2,GLips; LRW,LRW,PESQ; STOI; ESTOI,Speaker-Specific Lip to Speech Synthesis,Computer Vision,"Given a silent video of a speaker, generate the corresponding speech that matches the lip movements."
Landmark-based Lipreading,2,LRW; LRS2,N/A,N/A,N/A,N/A,N/A
Birds Eye View Object Detection,2,SILK; KITTI,KITTI Pedestrian Hard; KITTI Pedestrian Easy; KITTI Cyclists Hard; KITTI Cars Easy val; KITTI Cyclists Moderate; KITTI Cars Moderate val; KITTI Pedestrian Hard val; KITTI Pedestrian Easy val; KITTI Cars Easy; KITTI Pedestrian Moderate,Average Precision; mAP; AP,N/A,Computer Vision,KITTI birds eye view detection task
Monocular Cross-View Road Scene Parsing(Road),2,KITTI; Argoverse,N/A,N/A,N/A,N/A,N/A
Monocular Cross-View Road Scene Parsing(Vehicle),2,KITTI; Argoverse,N/A,N/A,N/A,N/A,N/A
Image to Point Cloud Registration,2,VBR; KITTI,N/A,N/A,N/A,N/A,N/A
Self-supervised Video Retrieval,2,UCF101; HMDB51,N/A,N/A,N/A,N/A,N/A
Length-of-Stay prediction,2,MIMIC-III; Clinical Admission Notes from MIMIC-III,MIMIC-III; Clinical Admission Notes from MIMIC-III,AUROC; Accuracy (LOS>7 Days); Accuracy (LOS>3 Days),Remaining Length of Stay,Medical,N/A
Multi-label Image Recognition with Partial Labels,2,Visual Genome; PASCAL VOC 2007,N/A,N/A,N/A,N/A,N/A
Unsupervised semantic parsing,2,Visual Genome; WebNLG,N/A,N/A,N/A,N/A,N/A
Bird Species Classification With Audio-Visual Data,2,BIRDeep; CUB-200-2011,N/A,N/A,N/A,N/A,N/A
Zero-shot Relation Triplet Extraction,2,Wiki-ZSL; FewRel,N/A,N/A,N/A,N/A,N/A
Video Story QA,2,MovieQA; CinePile: A Long Video Question Answering Dataset and Benchmark,N/A,N/A,N/A,N/A,N/A
Handwritten Chinese Text Recognition,2,CASIA-HWDB; ICDAR 2013,N/A,N/A,N/A,N/A,N/A
Distractor Generation,2,SciQ; RACE,N/A,N/A,N/A,N/A,N/A
Few Shot Temporal Action Localization,2,THUMOS14; ActivityNet,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Action Detection,2,THUMOS14; ActivityNet,N/A,N/A,N/A,N/A,N/A
Zero-Shot Action Detection,2,THUMOS14; ActivityNet,N/A,N/A,N/A,N/A,N/A
One-stage Anchor-free Oriented Object Detection,2,HRSC2016; SKU110K-R,N/A,N/A,N/A,N/A,N/A
Subjectivity Analysis,2,SUBJ; Czech Subjectivity Dataset,SUBJ; Czech Subjectivity Dataset,Accuracy,N/A,Natural Language Processing,A related task to sentiment analysis is the subjectivity analysis with the goal of labeling an opinion as either subjective or objective.
Traffic Object Detection,2,BDD100K; Car datasets in multiple scenes,N/A,N/A,N/A,N/A,N/A
Zero-Shot Human-Object Interaction Detection,2,HICO; HICO-DET,N/A,N/A,N/A,N/A,N/A
Generic Event Boundary Detection,2,TAPOS; Kinetics,N/A,N/A,N/A,N/A,N/A
Speaker-Specific Lip to Speech Synthesis,2,GRID Dataset; TIMIT,N/A,N/A,N/A,N/A,N/A
Group Activity Recognition,2,Volleyball; Collective Activity,N/A,N/A,N/A,N/A,N/A
Fine-Grained Action Detection,2,GTEA; MultiSports,N/A,N/A,N/A,N/A,N/A
Image Rescaling,2,DIV2K; Set5,N/A,N/A,N/A,N/A,N/A
3D Canonical Hand Pose Estimation,2,HO-3D v2; STB,STB; Ego3DHands; RHP,AUC,N/A,Computer Vision,Image: [Lin et al](https://arxiv.org/pdf/2006.01320v1.pdf)
3D Shape Reconstruction From A Single 2D Image,2,LLFF; ApolloCar3D,LLFF; ApolloCar3D,CLIP; A3DP,Shape from Texture,Computer Vision,Image: [Liao et al](https://arxiv.org/pdf/1811.12016v1.pdf)
3D Car Instance Understanding,2,CarFusion; ApolloCar3D,N/A,N/A,N/A,N/A,N/A
Highlight Detection,2,QVHighlights; TvSum,YouTube Highlights; arabiska; QVHighlights; TvSum,Hit@1; 0..5sec; mAP,N/A,Computer Vision,https://youtu.be/pJ0auP7dbcY?si=vSiZevfJ57YUKC2q
Continual Semantic Segmentation,2,ScanNet; ADE20K,PASCAL VOC 2012; ScanNet; ADE20K,mIoU,Overlapped 25-25; Overlapped 5-3,Computer Vision,Continual learning in semantic segmentation.
Weakly-supervised 3D Human Pose Estimation,2,Human3.6M; MPI-INF-3DHP,N/A,N/A,N/A,N/A,N/A
Unsupervised 3D Human Pose Estimation,2,Human3.6M; MPI-INF-3DHP,N/A,N/A,N/A,N/A,N/A
Disguised Face Verification,2,MegaFace; DFW,N/A,N/A,N/A,N/A,N/A
Age-Invariant Face Recognition,2,FG-NET; MORPH,N/A,N/A,N/A,N/A,N/A
Dichotomous Image Segmentation,2,DIS5K; STARE,N/A,N/A,N/A,N/A,N/A
Lung Nodule Segmentation,2,LUNA; LIDC-IDRI,N/A,N/A,N/A,N/A,N/A
Graph structure learning,2,Cora; ChEMBL,N/A,N/A,N/A,N/A,N/A
Ancestor-descendant prediction,2,WN18RR; WN18,WN18RR,mAP-100%; mAP-50%; mAP-0%,N/A,Graphs,"Given two entities, make a binary prediction if they have ancestor-descendant relationship, based on existing and missing hierarchical edges in the graph."
Aspect-Based Sentiment Analysis,2,SemEval-2014 Task-4; FABSA,N/A,N/A,N/A,N/A,N/A
Aspect-oriented  Opinion Extraction,2,Casino Reviews; SemEval-2014 Task-4,N/A,N/A,N/A,N/A,N/A
Curved Text Detection,2,UrduDoc; SCUT-CTW1500,N/A,N/A,N/A,N/A,N/A
Brain Image Segmentation,2,CREMI; BRISC,N/A,N/A,N/A,N/A,N/A
Link Sign Prediction,2,Slashdot; Epinions,Slashdot; Epinions; Bitcoin-Alpha,AUC; Macro-F1; Accuracy,N/A,Graphs,N/A
Resynthesis,2,LibriSpeech; LJSpeech,N/A,N/A,N/A,N/A,N/A
One-Shot Segmentation,2,Cluttered Omniglot; UMD-i Affrodance Dataset,Cluttered Omniglot,IoU [4 distractors]; IoU [32 distractors]; IoU [256 distractors],Patient-Specific Segmentation,Computer Vision,"<span style=""color:grey; opacity: 0.6"">( Image credit: [One-Shot Learning for Semantic  Segmentation](https://arxiv.org/pdf/1709.03410v1.pdf) )</span>"
Self-supervised Skeleton-based Action Recognition,2,NTU RGB+D; NTU RGB+D 120,N/A,N/A,N/A,N/A,N/A
Zero-Shot Video-Audio Retrieval,2,YouCook2; MSR-VTT,N/A,N/A,N/A,N/A,N/A
3D Multi-Person Pose Estimation (absolute),2,Relative Human; MuPoTS-3D,MuPoTS-3D,MPJPE; 3DPCK,N/A,Computer Vision,This task aims to solve absolute 3D multi-person pose Estimation (camera-centric coordinates). No ground truth human bounding box and human root joint coordinates are used during testing stage.    <sp...
Prosody Prediction,2,CANDOR Corpus; Helsinki Prosody Corpus,N/A,N/A,N/A,N/A,N/A
Pneumonia Detection,2,ChestX-ray14; Chest X-ray images,ChestX-ray14; COVID-19 CXR Dataset; Chest X-ray images,Accuracy; F-Score; Params; FLOPS; AUROC,N/A,Medical,N/A
Table-based Fact Verification,2,InfoTabS; TabFact,TabFact,Val; Test,N/A,Natural Language Processing,Verifying facts given semi-structured data.
Cross-domain 3D Human Pose Estimation,2,3DPW; MPI-INF-3DHP,N/A,N/A,N/A,N/A,N/A
HD semantic map learning,2,TbV Dataset; nuScenes,Argoverse2; nuScenes,Chamfer AP; Frechet AP,N/A,Computer Vision,"The goal of task is to generate map elements in a vectorized form using data from onboard sensors, e.g., RGB cameras and/or LiDARs. These map elements include but are not limited to : Road boundaries,..."
Motion Detection,2,KITTI'15 MSplus; nuScenes,nuScenes,F1 (%),N/A,Computer Vision,**Motion Detection** is a process to detect the presence of any moving entity in an area of interest. Motion Detection is of great importance due to its application in various areas such as surveillan...
satellite image super-resolution,2,WorldStrat; PROBA-V,N/A,N/A,vehicle detection,Computer Vision,N/A
Arrhythmia Detection,2,The China Physiological Signal Challenge 2018; MIT-BIH Arrhythmia Database,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Action Segmentation (Transcript),2,EgoProceL; Breakfast,N/A,N/A,N/A,N/A,N/A
Multiple Object Tracking with Transformer,2,MOT20; MOTChallenge,N/A,N/A,N/A,N/A,N/A
Image Retrieval with Multi-Modal Query,2,MIT-States; Fashion IQ,MIT-States; FashionIQ; Fashion200k,Recall@5; Recall@10; Recall@1; Recall@50,Cross-Modal Retrieval; Cross-Modal Information Retrieval; Multi-Modal Person Identification; Zero-Shot Cross-Modal Retrieval,Miscellaneous,"The problem of retrieving images from a database based on a multi-modal (image- text) query. Specifically, the query text prompts some modification in the query image and the task is to retrieve image..."
Chinese Semantic Role Labeling,2,CoNLL-2009; CoNLL,N/A,N/A,N/A,N/A,N/A
Zero-shot dense video captioning,2,YouCook2; ViTT,N/A,N/A,N/A,N/A,N/A
Text-Line Extraction,2,UrduDoc; DIVA-HisDB,N/A,N/A,N/A,N/A,N/A
3D Object Reconstruction From A Single Image,2,3D-POP; BUFF,N/A,N/A,N/A,N/A,N/A
Scanpath prediction,2,FixaTons; CapMIT1003,FixaTons; CapMIT1003; Coutrot Dataset 1,SBTDE; String-edit distance; Scaled time-delay embeddings,N/A,Computer Vision,Learning to Predict Sequences of Human Fixations.
Document Enhancement,2,DocUNet; StainDoc,N/A,N/A,N/A,N/A,N/A
Camouflaged Object Segmentation with a Single Task-generic Prompt,2,CAMO; COD10K,N/A,N/A,N/A,N/A,N/A
Fine-Grained Urban Flow Inference,2,TaxiBJ; PeMS04,TaxiBJ-P1; TaxiBJ-P4; TaxiBJ-P2; TaxiBJ-P3,MSE ; MAE; MAPE; MSE,N/A,Miscellaneous,Fine-grained urban flow inference (FUFI) aims to infer the fine-grained urban flow map from the coarse-grained one.
Co-Salient Object Detection,2,CoSal2015; CAT: Context Adjustment Training,N/A,N/A,N/A,N/A,N/A
Single-object colocalization,2,Object Discovery; PASCAL VOC,N/A,N/A,N/A,N/A,N/A
Action Assessment,2,UI-PRMD; EHE,UI-PRMD; KIMORE; EHE,Prediction Accuracy,N/A,Computer Vision,N/A
Dialog Relation Extraction,2,DDRel; DialogRE,N/A,N/A,N/A,N/A,N/A
Zero-Shot Transfer 3D Point Cloud Classification,2,ModelNet; ScanObjectNN,N/A,N/A,N/A,N/A,N/A
3D Point Cloud Linear Classification,2,ModelNet; ScanObjectNN,ScanObjectNN; ModelNet40,Overall Accuracy,N/A,Computer Vision,Training a linear classifier(e.g. SVM) on the embeddings/representations of 3D point clouds. The embeddings/representations are usually trained in an unsupervised manner.
Training-free 3D Point Cloud Classification,2,ModelNet; ScanObjectNN,ScanObjectNN; ModelNet40,Need 3D Data?; Parameters; Accuracy (%),N/A,Computer Vision,Evaluation on target datasets for 3D Point Cloud Classification without any training
3D Parameter-Efficient Fine-Tuning for Classification,2,ModelNet; ScanObjectNN,N/A,N/A,N/A,N/A,N/A
Conditional Text-to-Image Synthesis,2,COCO-MIG; MIMIC-CXR,N/A,N/A,N/A,N/A,N/A
Query-Based Extractive Summarization,2,SubSumE; DebateSum,N/A,N/A,N/A,N/A,N/A
Inductive logic programming,2,Kinship; RuDaS,RuDaS,R-Score; H-Score,N/A,Methodology,N/A
Single-Image Portrait Relighting,2,Multi-PIE; Dynamic OLAT Dataset,Multi-PIE,Si-MSE; Si-L2,N/A,Computer Code,N/A
Fact-based Text Editing,2,WebEdit; RotoEdit,N/A,N/A,N/A,N/A,N/A
Multi-domain Dialogue State Tracking,2,SGD; MultiWOZ,N/A,N/A,N/A,N/A,N/A
Iris Segmentation,2,CASIA-Iris-Complex; UBIRIS.v2,N/A,N/A,N/A,N/A,N/A
Foggy Scene Segmentation,2,Foggy Cityscapes; ACDC (Adverse Conditions Dataset with Correspondences),Foggy Cityscapes; Cityscapes-to-FoggyDriving; ACDC (Adverse Conditions Dataset with Correspondences); foggy zurich,mIoU; mIoU (val),N/A,N/A,N/A
Source Free Object Detection,2,InBreast; Foggy Cityscapes,N/A,N/A,N/A,N/A,N/A
Unsupervised Few-Shot Image Classification,2,mini-Imagenet; tieredImageNet,N/A,N/A,N/A,N/A,N/A
zero-shot anomaly detection,2,MVTecAD; VisA,N/A,N/A,N/A,N/A,N/A
Scene Classification (unified classes),2,NYUv2; SUN RGB-D,N/A,N/A,N/A,N/A,N/A
Robust Semi-Supervised RGBD Semantic Segmentation,2,2D-3D-S; SUN RGB-D,N/A,N/A,N/A,N/A,N/A
Generative 3D Object Classification,2,Objaverse; ModelNet,Objaverse; ModelNet40,Objaverse (Average); ModelNet40 (C); ModelNet40 (I); ModelNet40 (Average); Objaverse (I); Objaverse (C),N/A,Computer Vision,"The task of generative 3D object classification involves prompting the model to generate the object type from its point cloud, distinguishing it from discriminative models that directly classify objec..."
Single Image Haze Removal,2,D-HAZY; RESIDE,N/A,N/A,N/A,N/A,N/A
Depth Map Super-Resolution,2,Middlebury 2005; RGB-D-D,N/A,N/A,N/A,N/A,N/A
Prediction Intervals,2,Friedman1; aGender,N/A,N/A,N/A,Miscellaneous,"A prediction interval is an estimate of an interval in which a future observation will fall, with a certain probability, given what has already been observed. Prediction intervals are often used in re..."
Zero-shot Text to Audio Retrieval,2,Clotho; AudioCaps,N/A,N/A,N/A,N/A,N/A
Diffeomorphic Medical Image Registration,2,PPMI; ACDC,N/A,N/A,N/A,N/A,N/A
Liver Segmentation,2,LiTS17; Radio-Freqency Ultrasound volume dataset for pre-clinical liver tumors,N/A,N/A,N/A,N/A,N/A
Extractive Document Summarization,2,SubSumE; CNN/Daily Mail,N/A,N/A,N/A,N/A,N/A
Sequential sentence segmentation,2,CoNLL 2017 Shared Task - Automatically Annotated Raw Texts and Word Embeddings; CoNLL,N/A,N/A,N/A,N/A,N/A
Sentence Compression,2,Sentence Compression; Google,Google Dataset,F1; CR,Unsupervised Sentence Compression,Natural Language Processing,**Sentence Compression** is the task of reducing the length of text by removing non-essential content while preserving important facts and grammaticality.
Unseen Object Instance Segmentation,2,OCID; WISDOM,N/A,N/A,N/A,N/A,N/A
Nonhomogeneous Image Dehazing,2,SMOKE; NH-HAZE,N/A,N/A,N/A,N/A,N/A
Video Forensics,2,EarthNet2021; DeeperForensics-1.0,N/A,N/A,N/A,Computer Vision,N/A
Sleep spindles detection,2,DREAM; MODA dataset,DREAMS sleep spindles,MCC,N/A,Time Series,N/A
Product Categorization,2,SCG; Atlas,N/A,N/A,N/A,N/A,N/A
Handwritten Word Segmentation,2,BN-HTRd; BanglaWriting,N/A,N/A,N/A,N/A,N/A
Atomic action recognition,2,PSI-AVA; CATER,CATER,Average-mAP,Composite action recognition,Computer Vision,N/A
Semantic Composition,2,Cryptics; Polish CDSCorpus,N/A,N/A,N/A,Natural Language Processing,Understanding the meaning of text by composing the meanings of the individual words in the text (Source: https://arxiv.org/pdf/1405.7908.pdf)
Part-aware Panoptic Segmentation,2,Pascal Panoptic Parts; Cityscapes Panoptic Parts,Pascal Panoptic Parts; Cityscapes Panoptic Parts,PartPQ,N/A,Computer Vision,Panoptic segmentation with part-aware predictions.
Single Image Desnowing,2,CSD; AWMM-100k,N/A,N/A,N/A,N/A,N/A
Fish Detection,2,Fishnet Open Images; DeepFish,N/A,N/A,N/A,N/A,N/A
Heterogeneous Face Recognition,2,MCXFACE; DFW,N/A,N/A,N/A,N/A,N/A
Diabetic Retinopathy Detection,2,Retina Benchmark; Diabetic Retinopathy Detection Dataset,Diabetic Retinopathy Debrecen Data Set,Mean Accuracy,N/A,Medical,N/A
Paper generation (Title-to-abstract),2,"PubMed Term, Abstract, Conclusion, Title Dataset; Elsevier OA CC-BY",N/A,N/A,N/A,N/A,N/A
Paper generation (abstract-to-conclusion),2,"PubMed Term, Abstract, Conclusion, Title Dataset; Elsevier OA CC-BY",N/A,N/A,N/A,N/A,N/A
Multi-View 3D Reconstruction,2,ETH3D; Synthetic Soccer NeRF Dataset,ETH3D,F1 score,N/A,Computer Vision,N/A
Face Age Editing,2,VGGFace2 HQ; FFHQ-Aging,N/A,N/A,N/A,N/A,N/A
Semantic entity labeling,2,EC-FUNSD; FUNSD,EC-FUNSD; FUNSD,F1,N/A,Natural Language Processing,"- One of Form Understanding task (Word grouping, Semantic entity labeling, Entity linking)  - Classifying entities into one of four pre-defined categories: question, answer, header and, other.    cite..."
PointGoal Navigation,2,Habitat Platform; MIDGARD,N/A,N/A,N/A,N/A,N/A
3D Shape Recognition,2,Hypersim; LAS&T: Large Shape & Texture Dataset,N/A,N/A,N/A,N/A,N/A
Image Harmonization,2,FFHQH; iHarmony4,N/A,N/A,N/A,N/A,N/A
Motion Captioning,2,HumanML3D; KIT Motion-Language,HumanML3D; KIT Motion-Language,BLEU-4; BERTScore,N/A,Computer Vision,Generating textual description for human motion.
Active Speaker Detection,2,UniTalk; LRS3-TED,LRS3-TED,Accuracy,Fraud Detection,Robots,N/A
Deception Detection,2,Mafiascum; The Mafia Dataset,N/A,N/A,Deception Detection In Videos,Miscellaneous; Computer Vision,N/A
K-complex detection,2,Montreal Archive of Sleep Studies; MaSS,N/A,N/A,N/A,N/A,N/A
Depth Aleatoric Uncertainty Estimation,2,Mid-Air Dataset; MUAD,N/A,N/A,N/A,N/A,N/A
Head Detection,2,MinneApple; SCUT-HEAD,N/A,N/A,N/A,N/A,N/A
Selection bias,2,ICLR Database; PanNuke,N/A,N/A,N/A,N/A,N/A
Thermal Infrared Object Tracking,2,RF100; PTB-TIR,N/A,N/A,N/A,N/A,N/A
Optic Cup Segmentation,2,SMDG; REFUGE Challenge,REFUGE Challenge,Dice,N/A,Medical,"Optic cup segmentation, concentric with optic disc, useful for glaucoma management (ophthalmology)"
Classify 3D Point Clouds,2,ModelNet40-C; RobustPointSet,N/A,N/A,N/A,N/A,N/A
Micro Expression Recognition,2,SMIC; SAMM Long Videos,N/A,N/A,N/A,N/A,N/A
3D dense captioning,2,ReferIt3D; ScanRefer Dataset,N/A,N/A,N/A,N/A,N/A
Protein Structure Prediction,2,CAMEO; SidechainNet,PaenSeq; CASPSimSeq; UniProtSeq; CASPSeq,Validation perplexity,Protein complex prediction; Protein Interface Prediction,Miscellaneous; Medical,Image credit: [FastFold: Reducing AlphaFold Training Time from 11 Days to 67 Hours](https://arxiv.org/pdf/2203.00854v1.pdf)
Vector Graphics,2,ColorSVG-100K; SPARE3D,N/A,N/A,N/A,N/A,N/A
self-supervised scene text recognition,2,TextZoom; TextSeg,TextZoom; Scene Text Recognition Benchmarks; TextSeg,IoU (%); SSIM; Average Accuracy; Average PSNR (dB),N/A,Computer Vision,scene text recognition task    self-supervised scene text recognition task    text segmentation task    text image super-resolution task
Counterfactual Inference,2,TimeTravel; NVD,N/A,N/A,N/A,N/A,N/A
Factual probe,2,BEAR-probe; T-REx,N/A,N/A,N/A,N/A,N/A
Semi-supervised Medical Image Segmentation,2,ACDC; MM-WHS 2017,N/A,N/A,N/A,N/A,N/A
Object Reconstruction,2,HOWS; A Large Dataset of Object Scans,N/A,N/A,3D Object Reconstruction,Computer Vision,N/A
Visual Social Relationship Recognition,2,PISC; PIPA,PISC; PIPA,mAP (Coarse); Accuracy (domain); Accuracy; mAP,N/A,Computer Vision,N/A
EMG Gesture Recognition,2,Ninapro DB5; NinaPro DB2,N/A,N/A,N/A,Medical,Electromyographic Gesture Recognition
Medical Procedure,2,MedConceptsQA; Clinical Admission Notes from MIMIC-III,Clinical Admission Notes from MIMIC-III,AUROC,N/A,Medical,Predicting medical procedures performed during a hospital admission.
Open Vocabulary Action Recognition,2,Assembly101; EPIC-KITCHENS-100,N/A,N/A,N/A,N/A,N/A
Electron Microscopy Image Segmentation,2,uBench; 3D Platelet EM,N/A,N/A,N/A,N/A,N/A
Pulmorary Vessel Segmentation,2,VESSEL12; Microscopy Image Dataset of Pulmonary Vascular Changes,N/A,N/A,Pulmonary Artery–Vein Classification,Computer Vision,N/A
Semi-supervised Anomaly Detection,2,UBI-Fights; MVTec LOCO AD,UBI-Fights,Decidability; AUC; EER,General Action Video Anomaly Detection; Physical Video Anomaly Detection,Computer Vision,N/A
Commonsense Knowledge Base Construction,2,Quasimodo; Ascent KB,N/A,N/A,N/A,Knowledge Base,N/A
Text-to-video search,2,VALUE; Win-Fail Action Understanding,N/A,N/A,N/A,Natural Language Processing,N/A
Self-Supervised Anomaly Detection,2,KolektorSDD2; ISP-AD,N/A,N/A,N/A,N/A,N/A
Hope Speech Detection,2,HopeEDI; KanHope,HopeEDI; KanHope,F1-score (Weighted); Weighted Average F1-score,Hope Speech Detection for Tamil; Hope Speech Detection for Malayalam; Hope Speech Detection for English,Natural Language Processing,"Detecting speech associated with positive, uplifting,  promise, potential, support, reassurance, suggestions, or inspiration."
Speech Synthesis - Gujarati,2,IndicTTS; ToN_IoT,N/A,N/A,N/A,N/A,N/A
Citation Prediction,2,REFCAT; BEIR,N/A,N/A,N/A,N/A,N/A
Tweet Retrieval,2,BEIR; COVID-19-TweetIDs,N/A,N/A,N/A,N/A,N/A
News Retrieval,2,BEIR; PolyNewsParallel,N/A,N/A,N/A,N/A,N/A
severity prediction,2,AbuseAnalyzer Dataset; Burned Area Delineation from Satellite Imagery,Burned Area Delineation from Satellite Imagery,RMSE,Intubation Support Prediction,Computer Vision,N/A
Anomaly Segmentation,2,RoadAnomaly21; GoodsAD,N/A,N/A,N/A,N/A,N/A
Zero-Shot Audio Retrieval,2,VocSim; AudioCaps,N/A,N/A,N/A,N/A,N/A
drone-based object tracking,2,DroneCrowd; BioDrone,N/A,N/A,N/A,Computer Vision,drone-based object tracking
Partial Point Cloud Matching,2,4DMatch; DeformingThings4D,4DMatch,IR; NFMR,N/A,Computer Vision,N/A
Span-Extraction MRC,2,RuOpinionNE; ExpMRC,N/A,N/A,N/A,N/A,N/A
Graph Reconstruction,2,Netzschleuder; NBA player performance prediction dataset,N/A,N/A,N/A,Graphs,N/A
Dialog Act Classification,2,SILICONE Benchmark; CPED,Switchboard dialogue act corpus,Accuracy (%),N/A,Natural Language Processing,N/A
Time-to-Event Prediction,2,GBSG2; PBC,N/A,N/A,N/A,Time Series,N/A
Joint Vertebrae Identification And Localization In Spinal Ct Images,2,CTSpine1K; Spinal Vertebrae Segmentation Dataset,N/A,N/A,N/A,N/A,N/A
Model extraction,2,Data Collected with Package Delivery Quadcopter Drone; UML Classes With Specs,UML Classes With Specs,Exact Match,N/A,Methodology; Adversarial,"Model extraction attacks, aka model stealing attacks, are used to extract the parameters from the target model. Ideally, the adversary will be able to steal and replicate a model that will have a very..."
Single-Image-Based Hdr Reconstruction,2,NTIRE 2021 HDR; SI-HDR,City Scene Dataset,PSNR; SSIM; HDR-VDP2 Q SCORE,Tone Mapping,Computer Vision,N/A
Sentiment Analysis (Product + User),2,UIT-ViSFD; Synthetic Product Desirability Datasets for Sentiment Analysis Testing,N/A,N/A,N/A,N/A,N/A
Indoor Scene Synthesis,2,PRO-teXt; Rent3D++,N/A,N/A,N/A,N/A,N/A
Time Series Alignment,2,MTic; Artificial signal data for signal alignment testing,N/A,N/A,N/A,Time Series,N/A
Survival Prediction,2,Titanic; Survival Analysis of Heart Failure Patients,N/A,N/A,N/A,N/A,N/A
Emotional Speech Synthesis,2,EmoDB Dataset; EMOVIE,N/A,N/A,N/A,N/A,N/A
Image Similarity Detection,2,DISC21; fruit-SALAD,DISC21 dev,Time (ms); w/o normalization; hardware; dimension; with normalization,N/A,Computer Vision,A fundamental computer vision task to determine whether a part of an image has been copied from another image.    Description from: [The 2021 Image Similarity Dataset and Challenge](https://paperswith...
Image Similarity Search,2,DISC21; fruit-SALAD,N/A,N/A,N/A,Computer Vision,Image credit: [The 2021 Image Similarity Dataset and Challenge](https://paperswithcode.com/paper/the-2021-image-similarity-dataset-and)
3D Multi-Person Mesh Recovery,2,AGORA; Relative Human,N/A,N/A,N/A,N/A,N/A
Multimodal Text Prediction,2,SciGraphQA; MultiSubs,MultiSubs,Word similarity; Accuracy,N/A,Natural Language Processing,"**Multimodal text prediction** is a type of natural language processing that involves predicting the next word or sequence of words in a sentence, given multiple modalities or types of input. In tradi..."
Active Speaker Localization,2,Tragic Talkers; EasyCom,EasyCom,ASL mAP,N/A,Audio,"Active Speaker Localization (ASL) is the process of spatially localizing an active speaker (talker) in an environment using either audio, vision or both."
Factual Visual Question Answering,2,ZS-F-VQA; ViQuAE,N/A,N/A,N/A,N/A,N/A
Image Matching,2,ZEB; IMC PhotoTourism,ZEB; IMC PhotoTourism,mean average accuracy @ 10; Mean AUC@5°,Semantic correspondence; Matching Disparate Images; Patch Matching; set matching,Computer Vision,N/A
Vehicle Speed Estimation,2,BrnoCompSpeed; VBR,BrnoCompSpeed,99-th Percentile Speed Measurement Error (km/h); Median Speed Measurement Error (km/h); Mean Speed Measurement Error (km/h); 95-th Percentile Speed Measurement Error (km/h),N/A,Computer Vision,Vehicle speed estimation is the task of detecting and tracking vehicles whose real-world speeds are then estimated. The task is usually evaluated with recall and precision of the detected vehicle trac...
Fine-Grained Vehicle Classification,2,Vehicle-Rear; UFPR-VCR Dataset,N/A,N/A,Vehicle Color Recognition,Computer Vision,N/A
QRS Complex Detection,2,QR Code Detection YOLO; MIT-BIH Arrhythmia Database,N/A,N/A,N/A,N/A,N/A
Video Visual Relation Detection,2,ImageNet-VidVRD; VidOR,N/A,N/A,N/A,N/A,N/A
Video Visual Relation Tagging,2,ImageNet-VidVRD; VidOR,N/A,N/A,N/A,N/A,N/A
Video scene graph generation,2,STAR Benchmark; ImageNet-VidVRD,ImageNet-VidVRD,Recall@50,N/A,N/A,N/A
Activity Recognition In Videos,2,InfiniteRep; DAHLIA,DogCentric,Accuracy,Activity Prediction,Computer Vision; Time Series,N/A
Low-light Pedestrian Detection,2,LLVIP; LoLI-Street,N/A,N/A,N/A,N/A,N/A
Point Cloud Quality Assessment,2,M-PCCD; WPC,M-PCCD; WPC; SJTU-PCQA,PLCC; Pearson Correlation Coefficient ; SROCC; RMSE; KROCC,N/A,N/A,"### Background  A large and dense collection of points in three-dimensional space, collected by sensors such as LiDAR, is known as a point cloud. Points in the point cloud consist of geometric propert..."
Aspect Sentiment Triplet Extraction,2,MuseASTE; ASTE-Data-V2,N/A,N/A,N/A,N/A,N/A
Document-level Event Extraction,2,ChFinAnn; LEMONADE,N/A,N/A,N/A,N/A,N/A
New Product Sales Forecasting,2,VISUELLE2.0; VISUELLE,N/A,N/A,N/A,N/A,N/A
"Classification of toxic, engaging, fact-claiming comments",2,GermEval; THAR Dataset,N/A,N/A,N/A,N/A,N/A
Zero-Shot Cross-Lingual Visual Reasoning,2,MaRVL; IGLUE,N/A,N/A,N/A,N/A,N/A
Max-Shot Cross-Lingual Visual Reasoning,2,MaRVL; IGLUE,N/A,N/A,N/A,N/A,N/A
Moving Object Detection,2,RF100; DVSMOTION20,N/A,N/A,N/A,N/A,N/A
2D Semantic Segmentation task 1 (8 classes),2,CaDIS; RWD-10K,N/A,N/A,N/A,N/A,N/A
3D Geometry Prediction,2,DrivAerNet; Molecule3D,N/A,N/A,N/A,N/A,N/A
Audio Effects Modeling,2,DiffVox; SignalTrain LA2A Dataset,N/A,N/A,Timbre Interpolation; Pitch control,Audio,"Modeling of audio effects such as reverberation, compression, distortion, etc."
3D Dense Shape Correspondence,2,TOSCA; SHREC'19,N/A,N/A,N/A,N/A,N/A
Acoustic echo cancellation,2,ICASSP 2021 Acoustic Echo Cancellation Challenge; INTERSPEECH 2021 Acoustic Echo Cancellation Challenge,N/A,N/A,N/A,Speech; Medical,N/A
Emotional Dialogue Acts,2,Emotional Dialogue Acts; CPED,N/A,N/A,N/A,N/A,N/A
Video Synchronization,2,MSU Video Alignment and Retrieval Benchmark Suite; VGGSound-Sparse,N/A,N/A,N/A,N/A,N/A
Medical Image Denoising,2,Human Protein Atlas; BCNB,FMD Confocal Mice; Human Protein Atlas Image; Dermatologist level dermoscopy skin cancer classification using different deep learning convolutional neural networks algorithms; FMD Confocal Fish; FMD Two-Photon Mice; LGG Segmentation Dataset,PSNR; SSIM;  SSIM; Average PSNR,N/A,Computer Vision,Image credit: [Learning Medical Image Denoising with Deep Dynamic Residual Attention Network](https://paperswithcode.com/paper/learning-medical-image-denoising-with-deep)
Transparent Object Detection,2,TransProteus; SuperCaustics,N/A,N/A,Transparent objects,Computer Vision,Detecting transparent objects in 2D or 3D
Seismic Detection,2,Southern California Seismic Network Data; INSTANCE,N/A,N/A,N/A,N/A,N/A
Dark Humor Detection,2,BIG-bench; Cards Against Humanity,N/A,N/A,N/A,N/A,N/A
Similarities Abstraction,2,BIG-bench; fruit-SALAD,N/A,N/A,N/A,N/A,N/A
Muscle Tendon Junction Identification,2,deepMTJ_IEEEtbme; deepMTJ,N/A,N/A,N/A,N/A,N/A
3D human pose and shape estimation,2,EgoBody; 3DOH50K,EgoBody,PA-MPVPE; Average MPJPE (mm); PA-MPJPE; MPVPE,N/A,N/A,Estimate 3D human pose and shape (e.g. SMPL) from images
Infrared image super-resolution,2,results-C; results-A,results-C; results-A,Average PSNR,N/A,Computer Vision,Aims at upsampling the IR image and create the high resolution image with help of a low resolution image.
Rgb-T Tracking,2,RGBT234; LasHeR,N/A,N/A,N/A,N/A,N/A
Type prediction,2,ManyTypes4TypeScript; Single Point Corn Yield Data,DeepTyper; ManyTypes4TypeScript; Py150,Average Precision; Average Accuracy; Average F1; Average Recall; Accuracy@5; MRR,N/A,Computer Code,N/A
Kinematic Based Workflow Recognition,2,VIDIMU: Multimodal video and IMU kinematic dataset on daily life activities using affordable devices; PETRAW,PETRAW,Average AD-Accuracy,N/A,Computer Vision,N/A
Tomographic Reconstructions,2,CBCT Walnut; 2DeteCT,N/A,N/A,N/A,N/A,N/A
COVID-19 Tracking,2,CIRO experimental results; SMCOVID19-CT,N/A,N/A,N/A,N/A,N/A
Temporal Metadata Manipulation Detection,2,Cross-View Time Dataset (Cross-Camera Split); Cross-View Time Dataset,N/A,N/A,N/A,N/A,N/A
3D Anomaly Detection and Segmentation,2,DrivAerNet; MVTEC 3D-AD,N/A,N/A,N/A,N/A,N/A
Pancreas Segmentation,2,StepGame; Pancreas-CT,N/A,N/A,N/A,N/A,N/A
Event-based Motion Estimation,2,COESOT; FE108,N/A,N/A,N/A,N/A,N/A
Claim Extraction with Stance Classification (CESC),2,Press Briefing Claim Dataset; IAM Dataset,N/A,N/A,N/A,N/A,N/A
Multiview Contextual Commonsense Inference,2,CICEROv2; CICERO,N/A,N/A,N/A,N/A,N/A
Hyperspectral Image Super-Resolution,2,HSIRS; Multispectral Image Database,N/A,N/A,N/A,N/A,N/A
Mistake Detection,2,ImageNet-X; Assembly101,N/A,N/A,Online Mistake Detection,Computer Vision,Mistakes are natural occurrences in many tasks and an opportunity for an AR assistant to provide help. Identifying such mistakes requires modelling procedural knowledge and retaining long-range sequen...
AbbreviationDetection,2,PLOD-unfiltered; PLOD-filtered,N/A,N/A,N/A,N/A,N/A
Object Categorization,2,GRIT; UR5 Tool Dataset,GRIT,Categorization (test); Categorization (ablation),N/A,Computer Vision,"Object categorization identifies which label, from a  given set, best corresponds to an image region defined by  an input image and bounding box."
Retinal OCT Disease Classification,2,OCTID; RETOUCH,N/A,N/A,N/A,N/A,N/A
Data Visualization,2,PJM(AEP); MIMI dataset,N/A,N/A,Tree Map Layout,Miscellaneous; Methodology; Graphs,N/A
Copy Detection,2,VCSL; STVD-PVCD,N/A,N/A,N/A,N/A,N/A
Audio-visual Question Answering,2,MUSIC-AVQA; MUSIC-AVQA v2.0,MUSIC-AVQA,Acc,AUDIO-VISUAL QUESTION ANSWERING (MUSIC-AVQA-v2.0),Computer Vision,N/A
Weakly-supervised Video Anomaly Detection,2,ShanghaiTech Campus; UBnormal,N/A,N/A,N/A,N/A,N/A
Burst Image Super-Resolution,2,BurstSR; Videezy4K,N/A,N/A,N/A,N/A,N/A
Multi-Oriented Scene Text Detection,2,UrduDoc; Indian Number Plates Dataset | Vehicle Number Plates | English OCR Detection,ICDAR2015,F-Measure,Natural Image Orientation Angle Detection,Computer Vision,N/A
Transparent objects,2,Transparent Object Images | Indoor Object Dataset; TRansPose,N/A,N/A,N/A,N/A,N/A
Dialogue Safety Prediction,2,ProsocialDialog; rt-inod-jailbreaking,N/A,N/A,N/A,N/A,N/A
Amodal Instance Segmentation,2,WALT; MOViD-Amodal,N/A,N/A,N/A,N/A,N/A
Variable Detection,2,SV-Ident; SILD,N/A,N/A,N/A,N/A,N/A
Spam detection,2,Traditional and Context-specific Spam Twitter; ViSpamReviews,Traditional and Context-specific Spam Twitter,Avg F1,Context-specific Spam Detection; Traditional Spam Detection,Natural Language Processing,N/A
Brain Tumor Classification,2,BRISC; Brain Tumor MRI Dataset,N/A,N/A,N/A,N/A,N/A
Motion Generation,2,HumanML3D; NuiSI Dataset,N/A,N/A,N/A,N/A,N/A
Electrocardiography (ECG),2,MIMIC-IV-ECG; MIMIC PERform Testing Dataset,N/A,N/A,ECG Wave Delineation; ECG Patient Identification; Arrhythmia Detection; ECG Denoising; ECG Classification,Methodology; Medical,N/A
Burst Image Reconstruction,2,Videezy4K; SC_burst,N/A,N/A,N/A,N/A,N/A
Training-free Object Counting,2,Omnicount-191; FSC147,N/A,N/A,N/A,N/A,N/A
Skin Lesion Segmentation,2,University of Waterloo skin cancer database; ISIC2016,N/A,N/A,N/A,N/A,N/A
Ad-Hoc Information Retrieval,2,Robust04; SciRepEval,TREC Robust04,P@20; nDCG@20; MAP,Document Ranking,Natural Language Processing,Ad-hoc information retrieval refers to the task of returning information resources related to a user query formulated in natural language.
Semantic Communication,2,Europarl; MVX,Europarl,0..5sec,N/A,N/A,N/A
Drug Response Prediction,2,DrugBank; GDSC,N/A,N/A,N/A,N/A,N/A
Twitter Event Detection,2,Brazilian Protest; Crypto related tweets from 10.10.2020 to 3.3.2021,Events2012 - Oct 11 to Oct 17,Precision; Duplicate Event Rate (DERate); Number of Events,N/A,Natural Language Processing,"Detection of worldwide events from categories like Sports, Politics, Entertainment, Science & Technology, etc. by analyzing Twitter Tweets."
Punctuation Restoration,2,LEPISZCZE; Turkish Punctuation Restoration,N/A,N/A,N/A,Natural Language Processing,Punctuation Restoration
Zero-shot 3D classification,2,Objaverse; OmniObject3D,N/A,N/A,N/A,N/A,N/A
3D Object Captioning,2,Objaverse; TUMTraffic-VideoQA,Objaverse,Hallucination;  Sentence-BERT; GPT-4; SimCSE; Precision; Correctness,N/A,Computer Vision,"3D object captioning involves generating a natural language description of an object, given its point cloud representation."
Emotional Intelligence,2,EQ-Bench; Cards Against Humanity,EQ-Bench,EQ-Bench Score,Ruin Names; Dark Humor Detection; SNARKS,Natural Language Processing,"Emotional Intelligence (EI) is a measure of ""The ability to monitor one’s own and others’ feelings, to discriminate among them, and to use this information to guide one’s thinking and action."" (Salove..."
Depth-aware Video Panoptic Segmentation,2,Cityscapes-DVPS; SemKITTI-DVPS,N/A,N/A,N/A,N/A,N/A
Connectivity Estimation,2,Berlin V2X; iV2V and iV2I+,N/A,N/A,N/A,Graphs,N/A
Audio Synthesis,2,Trinity Speech-Gesture Dataset; mDRT,N/A,N/A,N/A,N/A,N/A
Semi-supervised Medical Image Classification,2,LymphoMNIST; OLIVES Dataset,N/A,N/A,N/A,N/A,N/A
Fake Image Attribution,2,GANGen-Detection; ArtiFact,N/A,N/A,N/A,N/A,N/A
Group Anomaly Detection,2,GADformer Trajectory Datasets; CHAD,N/A,N/A,N/A,N/A,N/A
legal outcome extraction,2,Caselaw4; Reglamento_Aeronautico_Colombiano_2024,N/A,N/A,N/A,N/A,N/A
Hyperspectral Image Denoising,2,ICVL-HSI; Hyper Drive,N/A,N/A,N/A,N/A,N/A
Video-to-image Affordance Grounding,2,OPRA; EPIC-Hotspot,OPRA; EPIC-Hotspot; OPRA (28x28),SIM; AUC-J; Top-1 Action Accuracy; KLD,N/A,Computer Vision,"Given a demonstration video V and a target image I, the goal of video-to-image affordance grounding predict an affordance heatmap over the target image according to the hand-interacted region in the v..."
Robust 3D Object Detection,2,KITTI-C; nuScenes-C,N/A,N/A,N/A,N/A,N/A
audio-visual event localization,2,LongVALE; UnAV-100,UnAV-100, mAP; AP@IOU0.5,N/A,N/A,N/A
Table Retrieval,2,Statcan Dialogue Dataset; CompMix-IR,N/A,N/A,N/A,N/A,N/A
Preference Mapping,2,Summarize from Feedback; Pick-a-Pic,N/A,N/A,N/A,N/A,N/A
3D Point Cloud Interpolation,2,DHB Dataset; NL-Drive,DHB Dataset; NL-Drive,EMD; CD,Point Cloud Registration,Computer Vision,"Point cloud interpolation is a fundamental problem for 3D computer vision. Given a low temporal resolution (frame rate) point cloud sequence, the target of interpolation is to generate a smooth point ..."
Video to Text Retrieval,2,MSVD-Indonesian; Sakuga-42M,N/A,N/A,N/A,N/A,N/A
Irregular Text Recognition,2,UTRSet-Synth; UTRSet-Real,N/A,N/A,N/A,N/A,N/A
Protein Function Prediction,2,"FLIP; FLIP -- AAV, Designed vs mutant",PaenSeq; CASPSimSeq; UniProtSeq,ROUGE-L,Antibody-antigen binding prediction,Medical,"For GO terms prediction, given the specific function prediction instruction and a protein sequence, models characterize the protein functions using the GO terms presented in three different domains (c..."
Occluded Face Detection,2,OCFR-LFW; HEADSET,N/A,N/A,N/A,N/A,N/A
Video-based Generative Performance Benchmarking,2,VideoInstruct; VCG+112K,VideoInstruct,Temporal Understanding; mean; Correctness of Information; Contextual Understanding; Detail Orientation; Consistency,Video-based Generative Performance Benchmarking (Temporal Understanding); Video-based Generative Performance Benchmarking (Consistency); Video-based Generative Performance Benchmarking (Correctness of Information); Video-based Generative Performance Benchmarking (Detail Orientation)); Video-based Generative Performance Benchmarking (Contextual Understanding),Reasoning,The benchmark evaluates a generative Video Conversational Model and covers five key aspects:    - Correctness of Information  - Detailed Orientation  - Contextual Understanding  - Temporal Understandi...
Image Shadow Removal,2,INS Dataset; SD7K,INS Dataset,Average PSNR (dB),N/A,Computer Vision,Merge with the Shadow Removal
Image-guided Story Ending Generation,2,LSMDC-E; VIST-E,N/A,N/A,N/A,N/A,N/A
Zero-shot Composed Person Retrieval,2,ITCPR dataset; GeneCIS,N/A,N/A,N/A,N/A,N/A
visual instruction following,2,LLaVA-Bench; VSTaR-1M,N/A,N/A,N/A,N/A,N/A
Explanatory Visual Question Answering,2,SME; GQA-REX,GQA-REX,ROUGE-L; METEOR; CIDEr; GQA-val; GQA-test; SPICE; BLEU-4; Grounding,FS-MEVQA,Computer Vision,Explanatory Visual Question Answering (EVQA) requires answering visual questions and generating multimodal explanations for the reasoning processes.
Music Performance Rendering,2,ASAP; ATEPP,N/A,N/A,N/A,N/A,N/A
Low-light Image Deblurring and Enhancement,2,LoLI-Street; LOL-Blur,N/A,N/A,N/A,N/A,N/A
X-ray PDF regression,2,CHILI-3K; CHILI-100K,N/A,N/A,N/A,N/A,N/A
named-entity-recognition,2,NuNER; SOMD,N/A,N/A,N/A,N/A,N/A
Multispectral Image Super-resolution,2,Sen2venus; OLI2MSI,N/A,N/A,N/A,N/A,N/A
zero-shot long video question answering,2,SF20K; CinePile: A Long Video Question Answering Dataset and Benchmark,N/A,N/A,N/A,N/A,N/A
3D visual grounding,2,Mono3DRefer; Beacon3D,N/A,N/A,N/A,N/A,N/A
Counterfactual Reasoning,2,ACCORD CSQA 0-5; Vinoground,N/A,N/A,N/A,N/A,N/A
Flood Inundation Mapping,2,STURM-Flood; Coastal Inundation Maps with Floodwater Depth Values,Coastal Inundation Maps with Floodwater Depth Values,Average MAE; Zero detection rate,N/A,Miscellaneous,N/A
Articulated Object modelling,2,PartNet-Mobility; RealArt-6,N/A,N/A,N/A,N/A,N/A
Action Classification (1-shot),2,WiFall; WiGesture,N/A,N/A,N/A,N/A,N/A
Fault localization,2,Discrete-Time Modeling of Interturn Short Circuits in Interior PMSMs - Data and Models; Paderbone University Bearing Fault Benckmark,N/A,N/A,N/A,N/A,N/A
Detecting Image Manipulation,2,CASIA (OSN-transmitted - Weibo); Columbia (OSN-transmitted - Facebook),N/A,N/A,N/A,N/A,N/A
Garment Reconstruction,2,4D-DRESS; GarmentCodeData,N/A,N/A,N/A,N/A,N/A
Marine Animal Segmentation,2,RMAS; MAS3K,N/A,N/A,N/A,Computer Vision,N/A
Mirror Detection,2,MSD (Mirror Segmentation Dataset); PMD,N/A,N/A,N/A,Computer Vision,N/A
Keypoint detection and image matching,2,Niantic Map-free Relocalization Dataset; ZEB,N/A,N/A,N/A,Computer Vision,keypoint detection in retinal images followed by image registration
Causal Discovery in Video Reasoning,2,MECD; CausalChaos!,N/A,N/A,N/A,N/A,N/A
Audio Denoising,2,Biodenoising datasets; Biodenoising_validation,AV-Bench - Violin Yanni; AV-Bench - Wooden Horse; AV-Bench - Guitar Solo,NSDR,N/A,Audio,N/A
Video Deblurring,2,Beam-Splitter Deblurring (BSD); DAVIDE,N/A,N/A,N/A,N/A,N/A
Video deraining,2,Video Waterdrop Removal Dataset; VRDS,Video Waterdrop Removal Dataset; VRDS,PSNR; SSIM,N/A,Computer Vision,N/A
Music Genre Recognition,2,PIAST; XMIDI,chords; 1B Words,10 Hops; Accuracy,N/A,Music,"Recognizing the genre (e.g. rock, pop, jazz, etc.) of a piece of music."
Text-to-Video Editing,2,DAVIS-Edit; V2VBench,N/A,N/A,N/A,N/A,N/A
Outdoor Localization,2,PEnG; SpaGBOL,N/A,N/A,N/A,Robots,N/A
Synthetic Image Detection,2,SuSy Dataset; Gap Pattern Detection,N/A,N/A,N/A,Computer Vision,Identify if the image is real or generated/manipulated by any generative models (GAN or Diffusion).
Malaria Falciparum Detection,2,MP-IDB; M5-Malaria Dataset,N/A,N/A,N/A,N/A,N/A
Text within image generation,2,TextAtlasEval; TextAtlas5M,N/A,N/A,N/A,N/A,N/A
Water3D_Long,2,MPM-Verse; Mpm-Verse-Large,N/A,N/A,N/A,N/A,N/A
Sand3D_Long,2,MPM-Verse; Mpm-Verse-Large,N/A,N/A,N/A,N/A,N/A
Plasticine3D,2,MPM-Verse; Mpm-Verse-Large,N/A,N/A,N/A,N/A,N/A
3D geometry,2,MPM-Verse; Mpm-Verse-Large,N/A,N/A,N/A,Graphs,N/A
Eyeblink detection,2,MPEblink; HUST-LEBW,MPEblink; HUST-LEBW,Avg. F1 ; Blink-AP50,N/A,Computer Vision,Localize eyeblinks in videos.
Audio-Video Question Answering (AVQA),2,MUSIC-AVQA-R; FortisAVQA,N/A,N/A,N/A,Computer Vision,N/A
Damaged Building Detection,2,mwBTFreddy; BRIGHT,N/A,N/A,N/A,Computer Vision,N/A
Foreground Segmentation,2,FCoT; UIIS10K,N/A,N/A,N/A,N/A,N/A
Image Editing,2,GEdit-Bench-EN; ImgEdit-Data,GEdit-Bench-EN; ImgEdit-Data,Action; Overall; Adjust; Style; Hybrid; Semantic Consistency; Replace; Remove; Background; Perceptual Quality,Multimodel-guided image editing; Rolling Shutter Correction; Multimodal fashion image editing; Joint Deblur and Frame Interpolation; Shadow Removal,Computer Vision,N/A
"1 Image, 2*2 Stitching",2,####How do i ask a question at Expedia?; [[FAQs~Communication]]How Do I Communicate to Expedia?,N/A,N/A,Image-to-Image Translation; Fake Image Detection,Miscellaneous; Computer Vision,N/A
Malicious Detection,1,MNIST,N/A,N/A,N/A,N/A,N/A
Iloko Speech Recognition,1,MNIST,N/A,N/A,N/A,N/A,N/A
PD-L1 Tumor Proportion Score Prediction,1,MNIST,N/A,N/A,N/A,N/A,N/A
HairColor/Bias-conflicting,1,CelebA,N/A,N/A,N/A,N/A,N/A
HeavyMakeup/Bias-conflicting,1,CelebA,N/A,N/A,N/A,N/A,N/A
JPEG Decompression,1,ImageNet,ImageNet,IS; PD; FID-5K; CA,N/A,Computer Vision,Image credit: [Palette: Image-to-Image Diffusion Models](https://paperswithcode.com/paper/palette-image-to-image-diffusion-models)
Image Classification with Differential Privacy,1,ImageNet,N/A,N/A,N/A,N/A,N/A
Zero-Shot Transfer Image Classification (CN),1,ImageNet,N/A,N/A,N/A,N/A,N/A
Unsupervised face recognition,1,LFW,N/A,N/A,N/A,N/A,N/A
Training-free 3D Part Segmentation,1,ShapeNet,ShapeNet-Part,mIoU; Need 3D Data?; Parameters,N/A,Computer Vision,Evaluation on target datasets for 3D Part Segmentation without any training
Weakly-supervised instance segmentation,1,COCO (Common Objects in Context),COCO 2017 val; PASCAL VOC 2012 val; COCO test-dev,mAP@0.5; AP@75; AP@L; Average Best Overlap; mAP@0.25; AP@M; AP; AP@S; AP@50; mAP@0.75,N/A,Computer Vision,N/A
Image-level Supervised Instance Segmentation,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Point-Supervised Instance Segmentation,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Active Object Detection,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Box-supervised Instance Segmentation,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Activeness Detection,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
Generalized Zero-Shot Object Detection,1,COCO (Common Objects in Context),N/A,N/A,N/A,N/A,N/A
3D Single Object Tracking,1,KITTI,N/A,N/A,N/A,N/A,N/A
Open Set Action Recognition,1,UCF101,N/A,N/A,N/A,N/A,N/A
Multi-Label Classification Of Biomedical Texts,1,MIMIC-III,MIMIC-III; MIMIC-III ,1:3 Accuracy; Micro F1,N/A,Medical,N/A
Bidirectional Relationship Classification,1,Visual Genome,N/A,N/A,N/A,N/A,N/A
Unbiased Scene Graph Generation,1,Visual Genome,N/A,N/A,N/A,N/A,N/A
Smile Recognition,1,DISFA,N/A,N/A,N/A,N/A,N/A
Unsupervised Keypoint Estimation,1,CUB-200-2011,N/A,N/A,N/A,N/A,N/A
Image Comprehension,1,Visual7W,N/A,N/A,N/A,Computer Vision,N/A
Clinical Assertion Status Detection,1,2010 i2b2/VA,2010 i2b2/VA,Micro F1,N/A,Natural Language Processing,"Classifying the assertions made on given medical concepts as being present, absent, or possible in the patient, conditionally present in the patient under certain circumstances, hypothetically present..."
GZSL Video Classification,1,ActivityNet,N/A,N/A,N/A,N/A,N/A
ZSL Video Classification,1,ActivityNet,N/A,N/A,N/A,N/A,N/A
Cross-Part Crowd Counting,1,ShanghaiTech,N/A,N/A,N/A,N/A,N/A
Zero-Shot Single Object Tracking,1,LaSOT,N/A,N/A,N/A,N/A,N/A
Drivable Area Detection,1,BDD100K,BDD100K val,mIoU; Params (M),N/A,Computer Vision,The drivable area detection is a subset topic of object detection. The model marks the safe and legal roads for regular driving in color blocks shaped by area.
Multiple Object Track and Segmentation,1,BDD100K,N/A,N/A,N/A,N/A,N/A
Amodal Panoptic Segmentation,1,BDD100K,N/A,N/A,N/A,N/A,N/A
Affordance Recognition,1,HICO-DET,HICO-DET; HICO-DET(Unknown Concepts),Novel Classes; HICO; Obj365; COCO-Val2017; Novel classes; Object365,N/A,Computer Vision,Affordance recognition from Human-Object Interaction
Human-Object Interaction Concept Discovery,1,HICO-DET,HICO-DET,Unknown (AP),N/A,Computer Vision,"Discovering the reasonable HOI concepts/categories from known categories and their instances. Actually, it is also a matrix (verb-object matrix) complementation problem."
Phoneme Recognition,1,TIMIT,N/A,N/A,N/A,N/A,N/A
Jpeg Compression Artifact Reduction,1,DIV2K,N/A,N/A,N/A,N/A,N/A
Multiview Gait Recognition,1,CASIA-B,N/A,N/A,N/A,N/A,N/A
Occluded 3D Object Symmetry Detection,1,YCB-Video,YCB-Video,PR AUC,N/A,Computer Vision,N/A
Vehicle Key-Point and Orientation Estimation,1,ApolloCar3D,ApolloCar3D,A3DP,N/A,Computer Vision,N/A
Car Pose Estimation,1,ApolloCar3D,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Image Classification (Cold Start),1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
Neural Network Compression,1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
Image Classification with Human Noise,1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
Supervised Image Retrieval,1,CIFAR-10,CIFAR-10,Precision@100,N/A,Computer Vision,N/A
Open Vocabulary Panoptic Segmentation,1,ADE20K,ADE20K,PQ,N/A,Computer Vision,N/A
Speech Prompted Semantic Segmentation,1,ADE20K,N/A,N/A,N/A,N/A,N/A
Sound Prompted Semantic Segmentation,1,ADE20K,N/A,N/A,N/A,N/A,N/A
Open-Vocabulary Semantic Segmentation,1,ADE20K,ADE20K-150,N/A,Open-Vocabulary Panoramic Semantic Segmentation,Computer Vision,N/A
Root Joint Localization,1,Human3.6M,N/A,N/A,N/A,N/A,N/A
3D Human Pose Estimation in Limited Data,1,Human3.6M,N/A,N/A,N/A,N/A,N/A
3D Semantic Instance Segmentation,1,ScanNet,N/A,N/A,N/A,N/A,N/A
Interactive 3D Instance Segmentation,1,ScanNet,ScanNetV2,NoC@80,Interactive 3D Instance Segmentation -Trained on Scannet40 - Evaluated on Scannet40,Computer Vision,N/A
Unsupervised 3D Semantic Segmentation,1,ScanNet,N/A,N/A,N/A,N/A,N/A
Interactive 3D Instance Segmentation -Trained on Scannet40 - Evaluated on Scannet40,1,ScanNet,N/A,N/A,N/A,N/A,N/A
Semantic Contour Prediction,1,SBD,N/A,N/A,N/A,N/A,N/A
Object Skeleton Detection,1,SK-LARGE,N/A,N/A,N/A,N/A,N/A
Hyperspectral Image Inpainting,1,Indian Pines,N/A,N/A,N/A,N/A,N/A
Semantic Part Detection,1,PASCAL-Part,N/A,N/A,N/A,N/A,N/A
Age/Bias-conflicting,1,UTKFace,N/A,N/A,N/A,N/A,N/A
Race/Bias-conflicting,1,UTKFace,N/A,N/A,N/A,N/A,N/A
Participant Intervention Comparison Outcome Extraction,1,EBM-NLP,EBM-NLP,F1,N/A,Medical,"PICO recognition is an information extraction task for identifying Participant, Intervention, Comparator, and Outcome (PICO elements) information from clinical literature."
Pose-Based Human Instance Segmentation,1,OCHuman,N/A,N/A,N/A,N/A,N/A
Geometric Matching,1,HPatches,HPatches,Average End-Point Error,N/A,Computer Vision,N/A
Class-agnostic Object Detection,1,Comic2k,N/A,N/A,N/A,N/A,N/A
Relationship Detection,1,VRD,N/A,N/A,N/A,N/A,N/A
Few-shot 3D Point Cloud Semantic Segmentation,1,S3DIS,N/A,N/A,N/A,N/A,N/A
Photo to Rest Generalization,1,PACS,N/A,N/A,N/A,N/A,N/A
Gene Interaction Prediction,1,BioGRID,BioGRID(yeast); BioGRID (human),Average Precision,N/A,Graphs,N/A
3D Multi-Person Pose Estimation (root-relative),1,MuPoTS-3D,MuPoTS-3D,MPJPE; AUC; 3DPCK,N/A,Computer Vision,This task aims to solve root-relative 3D multi-person pose estimation (person-centric coordinate system). No ground truth human bounding box and human root joint coordinates are used during testing st...
Unsupervised 3D Multi-Person Pose Estimation,1,MuPoTS-3D,N/A,N/A,N/A,N/A,N/A
General Action Video Anomaly Detection,1,Something-Something V2,N/A,N/A,N/A,N/A,N/A
One-Shot 3D Action Recognition,1,NTU RGB+D 120,N/A,N/A,N/A,N/A,N/A
Self-Supervised Human Action Recognition,1,NTU RGB+D 120,N/A,N/A,N/A,N/A,N/A
Few-Shot Skeleton-Based Action Recognition,1,NTU RGB+D 120,N/A,N/A,N/A,N/A,N/A
One-shot visual object segmentation,1,YouTube-VOS 2018,N/A,N/A,N/A,N/A,N/A
Multi-view 3D Human Pose Estimation,1,MPI-INF-3DHP,N/A,N/A,N/A,N/A,N/A
Online Vectorized HD Map Construction,1,nuScenes,nuScenes Camera Only,Average mAP,N/A,Computer Vision,N/A
Sleep Arousal Detection,1,MESA,N/A,N/A,N/A,N/A,N/A
Multimodal Sleep Stage Detection,1,Sleep-EDF,N/A,N/A,N/A,N/A,N/A
Myocardial infarction detection,1,PTB Diagnostic ECG Database,N/A,N/A,N/A,N/A,N/A
Lung Nodule 3D Classification,1,LIDC-IDRI,LIDC-IDRI,AUC,Radiomics-based Classification,Medical,N/A
Lung Nodule 3D Detection,1,LIDC-IDRI,N/A,N/A,N/A,N/A,N/A
Multi-view Subspace Clustering,1,ORL,N/A,N/A,N/A,N/A,N/A
Video-to-Video Synthesis,1,Street Scene,N/A,N/A,N/A,N/A,N/A
Real-Time 3D Semantic Segmentation,1,SemanticKITTI,N/A,N/A,N/A,N/A,N/A
Lidar Scene Completion,1,SemanticKITTI,SemanticKITTI,Chamfer Distance; Voxel IoU 0.1m; Voxel IoU 0.5m; Voxel IoU 0.2m; JSD BEV; JSD 3D,N/A,Computer Vision,Obtaining dense scene representation from a sparse lidar point cloud.
Text-to-3D-Human Generation,1,DeepFashion,SHHQ; DeepFashion,Percentage of Correct Keypoints; Fashion Accuracy; Frechet Inception Distance; CLIP Score; Depth Error,N/A,Miscellaneous,3D avatars generation from text prompts
Visual Question Answering (VQA) Split A,1,CLEVR,N/A,N/A,N/A,N/A,N/A
Visual Question Answering (VQA) Split B,1,CLEVR,N/A,N/A,N/A,N/A,N/A
Drone navigation,1,University-1652,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Action Segmentation (Action Set)),1,Breakfast,N/A,N/A,N/A,N/A,N/A
Long-video Activity Recognition,1,Breakfast,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Video Action Detection,1,UCF101-24,N/A,N/A,N/A,N/A,N/A
Long Video Retrieval (Background Removed),1,YouCook2,YouCook2,Cap. Avg. R@1; DTW R@1; Cap. Avg. R@5; OTAM R@10; DTW R@10; DTW R@5; Cap. Avg. R@10; OTAM R@1; OTAM R@5,N/A,Computer Vision,Retrieve the long videos given all subtitles.
3D Shape Classification,1,Pix3D,N/A,N/A,N/A,N/A,N/A
Left Ventricle Segmentation,1,SUN09,N/A,N/A,N/A,N/A,N/A
Live Video Captioning,1,ActivityNet Captions,N/A,N/A,N/A,N/A,N/A
Complimentary Image Retrieval,1,iMaterialist,N/A,N/A,N/A,N/A,N/A
Sentence Embeddings For Biomedical Texts,1,BIOSSES,N/A,N/A,N/A,N/A,N/A
Emotion Cause Extraction,1,ECE,N/A,N/A,N/A,N/A,N/A
Semanticity prediction,1,PhyAAt,N/A,N/A,N/A,N/A,N/A
Attention Score Prediction,1,PhyAAt,N/A,N/A,N/A,N/A,N/A
Noise Level Prediction,1,PhyAAt,N/A,N/A,N/A,N/A,N/A
Method name prediction,1,CodeSearchNet,N/A,N/A,N/A,N/A,N/A
Fine-grained Action Recognition,1,MTL-AQA,N/A,N/A,N/A,N/A,N/A
Vector Graphics Animation,1,SVG-Icons8,N/A,N/A,N/A,N/A,N/A
Unsupervised Vehicle Re-Identification,1,VeRi-776,N/A,N/A,N/A,N/A,N/A
Pedestrian Image Caption,1,CUHK-PEDES,N/A,N/A,N/A,N/A,N/A
Video Corpus Moment Retrieval,1,TVR,N/A,N/A,N/A,N/A,N/A
Zero-shot 3D Point Cloud Classificationclassification,1,ScanObjectNN,N/A,N/A,N/A,N/A,N/A
Supervised Only 3D Point Cloud Classification,1,ScanObjectNN,N/A,N/A,N/A,N/A,N/A
Diabetes Prediction,1,Diabetes,Diabetes,Accuracy,N/A,Medical,N/A
Scene-Aware Dialogue,1,AVSD,AVSD,CIDEr,N/A,Computer Vision,N/A
Causal Emotion Entailment,1,RECCON,N/A,N/A,N/A,N/A,N/A
Online surgical phase recognition,1,Cholec80,N/A,N/A,N/A,N/A,N/A
Offline surgical phase recognition,1,Cholec80,N/A,N/A,N/A,N/A,N/A
Event Cross-Document Coreference Resolution,1,ECB+,N/A,N/A,N/A,N/A,N/A
Directional Hearing,1,VCTK,VCTK,SI-SDRi,Real-time Directional Hearing,Audio,Extremely low-latency audio source separation from a known direction of arrival.
Real-time Directional Hearing,1,VCTK,N/A,N/A,N/A,N/A,N/A
Accented Speech Recognition,1,VoxForge,VoxForge Commonwealth; VoxForge Indian; VoxForge European; VoxForge American-Canadian,Percentage error,Speech Synthesis,Audio,N/A
Crime Prediction,1,Foursquare,N/A,N/A,N/A,Miscellaneous,N/A
Research Performance Prediction,1,AMiner,N/A,N/A,N/A,N/A,N/A
Relationship Extraction (Distant Supervised),1,New York Times Annotated Corpus,New York Times Corpus; NYT,P@10%; AUC; Average Precision; PR AUC; P@200; P@100; P@300; P@30%,N/A,Natural Language Processing,"Relationship extraction is the task of extracting semantic relationships from a text. Extracted relationships usually  occur between two or more entities of a certain type (e.g. Person, Organisation, ..."
Chat-based Image Retrieval,1,VisDial,N/A,N/A,N/A,N/A,N/A
Few-Shot Transfer Learning for Saliency Prediction,1,SALICON,SALICON->WebpageSaliency - 1-shot; SALICON->WebpageSaliency - EUB; SALICON->WebpageSaliency - 10-shot ; SALICON->WebpageSaliency - 5-shot ,CC; AUC; NSS,Saliency Prediction,Computer Vision,Saliency prediction aims to predict important locations in a visual scene. It is a per-pixel regression task with predicted values ranging from 0 to 1.    Benefiting from deep learning research and la...
Multi-Person Pose Estimation and Tracking,1,PoseTrack,PoseTrack2018,MOTA,N/A,Computer Vision,"Joint multi-person pose estimation and tracking following the PoseTrack benchmark.   https://posetrack.net/    <span style=""color:grey; opacity: 0.6"">( Image credit: [PoseTrack](https://github.com/iqb..."
Multi-object colocalization,1,PASCAL VOC,VOC_all; VOC12,Detection Rate,N/A,Computer Vision,N/A
Plane Instance Segmentation,1,NYUv2,N/A,N/A,N/A,N/A,N/A
Zero-shot Scene Classification (unified classes),1,NYUv2,N/A,N/A,N/A,N/A,N/A
Viewpoint Estimation,1,PASCAL3D+,N/A,N/A,N/A,Computer Vision,N/A
Panoptic Segmentation (PanopticNDT instances),1,SUN RGB-D,N/A,N/A,N/A,N/A,N/A
Few-Shot 3D Point Cloud Classification,1,ModelNet,N/A,N/A,N/A,N/A,N/A
Time-interval Prediction,1,YAGO,N/A,N/A,N/A,N/A,N/A
Scene Labeling,1,Stanford Background,N/A,N/A,N/A,N/A,N/A
Instance Search,1,Oxford105k,N/A,N/A,Audio Fingerprint,Computer Vision; Audio,Visual **Instance Search** is the task of retrieving from a database of images the ones that contain an instance of a visual query. It is typically much more challenging than finding images from the d...
Matching Disparate Images,1,DispScenes,N/A,N/A,N/A,N/A,N/A
Hand Joint Reconstruction,1,HIC,N/A,N/A,N/A,N/A,N/A
Semi-Supervised RGBD Semantic Segmentation,1,2D-3D-S,N/A,N/A,N/A,N/A,N/A
Anomaly Detection in Edge Streams,1,DARPA,N/A,N/A,N/A,N/A,N/A
Offline Handwritten Chinese Character Recognition,1,CASIA-HWDB,N/A,N/A,N/A,N/A,N/A
TGIF-Action,1,TGIF-QA,N/A,N/A,N/A,N/A,N/A
Space-time Video Super-resolution,1,Vid4,Vimeo90K-Medium; Vimeo90K-Fast,PSNR; SSIM,N/A,Computer Vision,N/A
Key-Frame-based Video Super-Resolution (K = 15),1,Vid4,N/A,N/A,N/A,N/A,N/A
Federated Learning (Video Super-Resolution),1,Vid4,N/A,N/A,N/A,N/A,N/A
4-ary Relation Extraction,1,SciREX,N/A,N/A,N/A,N/A,N/A
3D Shape Generation,1,Combinatorial 3D Shape Dataset,N/A,N/A,Gesture Generation,Robots,Image: [Mo et al](https://arxiv.org/pdf/1908.00575v1.pdf)
Electromyography (EMG),1,Silent Speech EMG,N/A,N/A,Muscle Force Prediction; ALS Detection; EMG Gesture Recognition; EMG Signal Prediction; Medial knee JRF Prediction,Medical,N/A
Sequential skip prediction,1,MSSD,N/A,N/A,N/A,N/A,N/A
3D Interacting Hand Pose Estimation,1,InterHand2.6M,InterHand2.6M,MPJPE Test; MRRPE Test; MPVPE Test,Superpixels,Graphs,N/A
Melody Extraction,1,ErhuPT,N/A,N/A,N/A,Music,N/A
Micro-Expression Recognition,1,CASME II,N/A,N/A,N/A,N/A,N/A
Program induction,1,AQUA-RAT,N/A,N/A,N/A,Computer Code,Generating program code for domain-specific tasks
Word Sense Induction,1,BRWAC,SemEval 2010 WSI,V-Measure; F-Score; AVG,N/A,Natural Language Processing,"Word sense induction (WSI) is widely known as the “unsupervised version” of WSD. The problem states as: Given a target word (e.g., “cold”) and a collection of sentences (e.g., “I caught a cold”, “The ..."
Semantic Text Matching,1,CAIL2019-SCM,N/A,N/A,N/A,N/A,N/A
Composite action recognition,1,CATER,N/A,N/A,N/A,N/A,N/A
Future prediction,1,CCD,N/A,N/A,N/A,Computer Vision,N/A
Cervical Nucleus Detection,1,Cervix93 Cytology Dataset,N/A,N/A,N/A,N/A,N/A
Attribute,1,CityFlow,N/A,N/A,N/A,N/A,N/A
Cross-Lingual Information Retrieval,1,CLIRMatrix,N/A,N/A,N/A,N/A,N/A
Foot keypoint detection,1,COCO-WholeBody,N/A,N/A,N/A,N/A,N/A
multi-word expression sememe prediction,1,COS960,N/A,N/A,N/A,Natural Language Processing,Predict sememes for unannotated multi-word expressions.
Visual Crowd Analysis,1,CrowdFlow,N/A,N/A,N/A,N/A,N/A
Stereotypical Bias Analysis,1,CrowS-Pairs,CrowS-Pairs,Religion; Overall; Disability; Gender; Nationality; Sexual Orientation; Age; Race/Color; Socioeconomic status; Physical Appearance,N/A,Natural Language Processing,N/A
Student Engagement Level Detection (Four Class Video Classification),1,DAiSEE,N/A,N/A,N/A,N/A,N/A
Clothes Landmark Detection,1,DeepFashion2,N/A,N/A,N/A,N/A,N/A
Video Question Answering (Level 3),1,DramaQA,N/A,N/A,N/A,N/A,N/A
Video Question Answering (Level 4),1,DramaQA,N/A,N/A,N/A,N/A,N/A
Learning Semantic Representations,1,Edge-Map-345C,N/A,N/A,N/A,N/A,N/A
Event-Based Video Reconstruction,1,Event-Camera Dataset,N/A,N/A,N/A,N/A,N/A
intensity image denoising,1,FMD,N/A,N/A,N/A,N/A,N/A
B-Rep face segmentation,1,Fusion 360 Gallery,N/A,N/A,N/A,N/A,N/A
Interest Point Detection,1,Hollywood 3D dataset,N/A,N/A,Homography Estimation,Computer Vision,N/A
Model Selection,1,Image Caption Quality Dataset,N/A,N/A,N/A,Methodology,"Given a set of candidate models, the goal of **Model Selection** is to select the model that best approximates the observed data and captures its underlying regularities. Model Selection criteria are ..."
Cross-Lingual Semantic Textual Similarity,1,Interpretable STS,N/A,N/A,N/A,N/A,N/A
Gesture Synchronization,1,LRS3-TED,N/A,N/A,N/A,N/A,N/A
Long-tailed Object Detection,1,LVIS,N/A,N/A,N/A,N/A,N/A
Novel Object Detection,1,LVIS,N/A,N/A,N/A,N/A,N/A
Zero-Shot Instance Segmentation,1,LVIS,N/A,N/A,N/A,N/A,N/A
Entity Extraction using GAN,1,MedMentions,N/A,N/A,N/A,Natural Language Processing,N/A
Predict Future Video Frames,1,Moving Symbols,N/A,N/A,N/A,N/A,N/A
Business Taxonomy Construction,1,NEEQ Annual Reports,N/A,N/A,N/A,Miscellaneous,N/A
Node Property Prediction,1,OGB,ogbn-products; ogbn-papers100M; ogbn-proteins; ogbn-arxiv; ogbn-mag,Test ROC-AUC; Number of params; Validation ROC-AUC; Ext. data; Validation Accuracy; Test Accuracy,Research Performance Prediction; Position regression,Graphs,N/A
Link Property Prediction,1,OGB,ogbl-citation2; ogbl-biokg; ogbl-ppa; ogbl-wikikg2; ogbl-ddi; ogbl-collab,Test MRR; Number of params; Validation MRR; Validation Hits@20; Validation Hits@50; Ext. data; Test Hits@20; Test Hits@50; Test Hits@100; Validation Hits@100,N/A,Graphs,N/A
Abstract generation,1,OGB,N/A,N/A,N/A,N/A,N/A
Webcam (RGB) image classification,1,Photi-LakeIce,N/A,N/A,N/A,N/A,N/A
Optic Cup Detection,1,REFUGE Challenge,REFUGE Challenge,IoU,N/A,Medical,Region proposal for optic cup
Motion Compensation,1,RGB-DAVIS Dataset,N/A,N/A,N/A,N/A,N/A
Rice Grain Disease Detection,1,RICE,N/A,N/A,N/A,N/A,N/A
Instance Shadow Detection,1,SOBA,SOBA,Bounding Box SOAP; Asso. AP_segm; Instance AP_segm; Asso. AP_bbox; mask SOAP; Instance AP_bbox,Shadow Detection And Removal,Computer Vision,N/A
Camera shot segmentation,1,SoccerNet-v2,N/A,N/A,N/A,N/A,N/A
"Indoor Localization (3-DoF Pose: X, Y, Yaw)",1,Structured3D,N/A,N/A,N/A,N/A,N/A
Indoor Localization (6-DoF Pose),1,Structured3D,N/A,N/A,N/A,N/A,N/A
Indoor Localization (6D),1,Structured3D,N/A,N/A,N/A,N/A,N/A
2D Indoor Localization (Position + Orientation),1,Structured3D,N/A,N/A,N/A,N/A,N/A
6D Indoor Localization,1,Structured3D,N/A,N/A,N/A,N/A,N/A
Indoor Localization (6 DoF Pose),1,Structured3D,N/A,N/A,N/A,N/A,N/A
Text Effects Transfer,1,TE141K,N/A,N/A,N/A,Natural Language Processing,"Text effects transfer refers to the task of transferring typography styles (e.g., color, texture) to an input image of a text element."
Counterfactual Detection,1,TimeTravel,N/A,N/A,N/A,N/A,N/A
Fashion Synthesis,1,TPIC17,N/A,N/A,N/A,N/A,N/A
Video-to-Sound Generation,1,VGG-Sound,N/A,N/A,N/A,N/A,N/A
Predicting Patient Outcomes,1,Ward2ICU,N/A,N/A,N/A,N/A,N/A
Online Video Anomaly Detection,1,Detection of Traffic Anomaly,N/A,N/A,N/A,N/A,N/A
Hardware Aware Neural Architecture Search,1,Visual Wake Words,N/A,N/A,N/A,N/A,N/A
Initial Structure to Relaxed Energy (IS2RE),1,OC20,OC20,Energy MAE,N/A,Graphs,N/A
"Initial Structure to Relaxed Energy (IS2RE), Direct",1,OC20,N/A,N/A,N/A,N/A,N/A
Image Paragraph Captioning,1,Image Paragraph Captioning,Image Paragraph Captioning,BLEU-4; METEOR; CIDEr,N/A,Reasoning; Graphs,"Image paragraph captioning involves generating a detailed, multi-sentence description of the content of an image."
Zero-Shot Image Paragraph Captioning,1,Image Paragraph Captioning,N/A,N/A,N/A,N/A,N/A
Breast Cancer Histology Image Classification (20% labels),1,BreakHis,N/A,N/A,N/A,N/A,N/A
Multi-Instance Retrieval,1,EPIC-KITCHENS-100,N/A,N/A,N/A,N/A,N/A
Sparse Information Retrieval,1,CoNLL 2003,N/A,N/A,N/A,N/A,N/A
Video Deinterlacing,1,MSU Deinterlacer Benchmark,N/A,N/A,N/A,N/A,N/A
Unsupervised nucleus segmentation,1,MoNuSeg,N/A,N/A,N/A,N/A,N/A
Malware Type Detection,1,MalNet,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Video Classification,1,UBI-Fights,N/A,N/A,N/A,Computer Vision,N/A
Workflow Discovery,1,ABCD,ABCD,In-domain CE; In-domain EM; Cross-domain  CE; Cross-domain EM,N/A,Natural Language Processing,Workflow Discovery (WD) was introduced by [Workflow Discovery from Dialogues in the Low Data Regime](https://openreview.net/forum?id=L9othQvPks). WD aims to extract work-flows that have either implici...
Speech-to-Gesture Translation,1,PATS,N/A,N/A,N/A,Speech,N/A
Named Entity Recognition In Vietnamese,1,PhoNER COVID19,N/A,N/A,N/A,N/A,N/A
Stance Detection (US Election 2020 - Biden),1,Twitter Stance Election 2020,N/A,N/A,N/A,N/A,N/A
Stance Detection (US Election 2020 - Trump),1,Twitter Stance Election 2020,N/A,N/A,N/A,N/A,N/A
MULTI-VIEW LEARNING,1,CarFusion,N/A,N/A,Incomplete multi-view clustering,Computer Vision,"**Multi-View Learning** is a machine learning framework where data are represented by multiple distinct feature groups, and each feature group is referred to as a particular view.   <span class=""descr..."
Hope Speech Detection for English,1,HopeEDI,N/A,N/A,N/A,N/A,N/A
Hope Speech Detection for Tamil,1,HopeEDI,N/A,N/A,N/A,N/A,N/A
Hope Speech Detection for Malayalam,1,HopeEDI,N/A,N/A,N/A,N/A,N/A
Protein Fold Quality Estimation,1,CASP13 MQA,N/A,N/A,N/A,N/A,N/A
Duplicate-Question Retrieval,1,BEIR,N/A,N/A,N/A,N/A,N/A
Infinite Image Generation,1,LHQ,N/A,N/A,N/A,N/A,N/A
Perpetual View Generation,1,LHQ,LHQ,FID (full 100 steps); KID (first 20 steps); FID (first 20 steps); KID (full 100 steps); IS (full 100 steps); IS (first 20 steps),N/A,Computer Vision,**Perpetual View Generation** is the task of generating long-range novel views by flying into a given image.
Image Augmentation,1,Intel Image Classification,Intel Image Classification,Balanced Accuracy,N/A,Computer Vision,**Image Augmentation** is a data augmentation method that generates more training data from the existing training samples. Image Augmentation is especially useful in domains where training data is lim...
Human fMRI response prediction,1,Algonauts 2021,N/A,N/A,N/A,Computer Vision,"The task is: Given a) the set of videos of everyday events and b) the corresponding brain responses recorded while human participants viewed those videos, use computational models to predict brain res..."
Organ Detection,1,Hyper-Kvasir Dataset,N/A,N/A,N/A,N/A,N/A
3D Scene Graph Alignment,1,3DSSG,3DSSG,F1; MRR; Hits@1,N/A,Computer Vision,N/A
3d scene graph generation,1,3DSSG,N/A,N/A,N/A,N/A,N/A
Text to Audio/Video Retrieval,1,AudioCaps,N/A,N/A,N/A,N/A,N/A
Audio/Video to Text Retrieval,1,AudioCaps,N/A,N/A,N/A,N/A,N/A
Retrieval-augmented Few-shot In-context Audio Captioning,1,AudioCaps,N/A,N/A,N/A,N/A,N/A
Evidence Selection,1,QASPER,N/A,N/A,N/A,N/A,N/A
Multi-Grained Named Entity Recognition,1,Few-NERD,N/A,N/A,N/A,Natural Language Processing,"Multi-Grained Named Entity Recognition aims to detect and recognize entities on multiple granularities, without explicitly assuming non-overlapping or totally nested structures."
Human-Object Interaction Anticipation,1,VidHOI,VidHOI,Person-wise Top5: t=3(mAP@0.5); Person-wise Top5: t=5(mAP@0.5); Person-wise Top5: t=1(mAP@0.5),N/A,Computer Vision,"Human-Object Interaction (HOI) Anticipation is the task of predicting ""a set of interactions"" in a video that will happen in the future, which involves the i) localization of the subject (i.e., humans..."
Actionable Phrase Detection,1,Enron Emails,N/A,N/A,N/A,N/A,N/A
Earth Surface Forecasting,1,EarthNet2021,N/A,N/A,N/A,N/A,N/A
Semantic Segmentation Of Orthoimagery,1,LandCover.ai,N/A,N/A,N/A,N/A,N/A
Event-Driven Trading,1,EDT,N/A,N/A,N/A,N/A,N/A
Cloud Detection,1,Sentinel 2 manually extracted deep water spectra with high noise levels and sunglint,N/A,N/A,N/A,Computer Vision,N/A
Blood Cell Count,1,CBC,N/A,N/A,N/A,N/A,N/A
eXtreme-Video-Frame-Interpolation,1,X4K1000FPS,N/A,N/A,N/A,Computer Vision,Type of Video Frame Interpolation (VFI) that interpolates an intermediate frame on X4K1000FPS dataset containing 4K videos of 1000 fps with the extreme motion. The dataset has a wide variety of textur...
Attractiveness Estimation,1,CFD,N/A,N/A,N/A,N/A,N/A
Cold-Start Anomaly Detection,1,BANKING77-OOS,N/A,N/A,N/A,N/A,N/A
Indoor Scene Reconstruction,1,Rent3D++,N/A,N/A,Plan2Scene,Computer Vision,N/A
Plan2Scene,1,Rent3D++,N/A,N/A,N/A,N/A,N/A
Fingertip Detection,1,TI1K Dataset,N/A,N/A,N/A,Computer Vision,N/A
Shape from Texture,1,SurfaceGrid,N/A,N/A,N/A,N/A,N/A
Chemical Reaction Prediction,1,USPTO-50k,Mol-Instruction,Exact; Morgan FTS; METEOR; Validity,N/A,Medical,N/A
Single-step retrosynthesis,1,USPTO-50k,USPTO-50k,Top-1 accuracy; Top-5 accuracy; Top-10 accuracy; Top-50 accuracy; Top-20 accuracy; Top-3 accuracy,N/A,Medical,N/A
Zero-shot Cross-lingual Fact-checking,1,X-Fact,N/A,N/A,N/A,N/A,N/A
Safety Perception Recognition,1,Place Pulse 2.0,Google Street Images; Place Pulse 2.0,AUC; Accuracy,N/A,Computer Vision,City safety perception recognition
Alzheimer's Disease Detection,1,ADNI,N/A,N/A,N/A,N/A,N/A
Cubic splines Image Registration,1,ADNI,N/A,N/A,N/A,N/A,N/A
Epilepsy Prediction,1,Epilepsy seizure prediction,Epilepsy seizure prediction,1:1 Accuracy,N/A,Medical,N/A
Face Recognition (Closed-Set),1,TinyFace,N/A,N/A,N/A,N/A,N/A
Micro-gesture Recognition,1,iMiGUE,iMiGUE,Top 1 Accuracy; Top 5 Accuracy,N/A,Computer Vision,N/A
Crowdsourced Text Aggregation,1,CrowdSpeech,CrowdSpeech test-clean; CrowdSpeech test-other,Word Error Rate (WER),N/A,Natural Language Processing,"One of the most important parts of processing responses from crowd workers is **aggregation**: given several conflicting opinions, a method should extract the truth. This problem is also known as *tru..."
Counterfactual Explanation,1,ExBAN,N/A,N/A,N/A,Miscellaneous,"Returns a contrastive argument that permits to achieve the desired class, e.g.,  “to obtain this loan, you need XXX of annual  revenue instead of the current YYY”"
Key Detection,1,Giantsteps,N/A,N/A,N/A,N/A,N/A
Zero-shot Moment Retrieval,1,QVHighlights,N/A,N/A,N/A,N/A,N/A
Contour Detection,1,ICDAR 2021,N/A,N/A,N/A,Computer Vision,"Object **Contour Detection** extracts information about the object shape in images.   <span class=""description-source"">Source: [Object Contour and Edge Detection with RefineContourNet ](https://arxiv...."
Anxiety Detection,1,Well-being Dataset,Well-being Dataset,F1-score,N/A,Medical,Detect anxiety distress of human beings / animals
Kidney Function,1,HiRID,HiRID,MAE,N/A,Medical,Continuous prediction of urine production in the next 2h as an average rate in ml/kg/h. The task is predicted at irregular intervals.
Multimodal Forgery Detection,1,FakeAVCeleb,N/A,N/A,N/A,N/A,N/A
Predict clinical outcome,1,CirCor DigiScope,CirCor DigiScope,Clinical cost score (cross-val); Clinical cost score; Clinical cost score (validation data),N/A,Time Series,"A cost-based metric that considers the costs of algorithmic prescreening, expert screening, treatment, and diagnostic errors that result in late or missed treatments. This metric is further described ..."
Person-centric Visual Grounding,1,Who’s Waldo,N/A,N/A,N/A,N/A,N/A
Home Activity Monitoring,1,DAHLIA,N/A,N/A,N/A,Miscellaneous,N/A
Action Triplet Detection,1,CholecT50,N/A,N/A,N/A,N/A,N/A
Zero-Shot Out-of-Domain Detection,1,Pano3D,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Object Detection,1,COCO 10% labeled data,COCO 1% labeled data; COCO 10% labeled data; COCO 5% labeled data; COCO 100% labeled data; COCO 0.5% labeled data; COCO; COCO 2% labeled data,10%; detector; mAP,N/A,Computer Vision,Semi-supervised object detection uses both labeled data and unlabeled data for training. It not only reduces the annotation burden for training high-performance object detectors but also further impro...
Clone Detection,1,CodeXGLUE,N/A,N/A,N/A,N/A,N/A
Role-filler Entity Extraction,1,MUC-4,MUC-4,Avg. F1,N/A,Natural Language Processing,Role-filler entity extraction task on the MUC-4 dataset.
Attribute Extraction,1,SWDE,SWDE,Avg F1,legal outcome extraction,Natural Language Processing,N/A
Video Harmonization,1,HYouTube,N/A,N/A,N/A,N/A,N/A
Skin Cancer Classification,1,ISIC 2020 Challenge Dataset,N/A,N/A,N/A,N/A,N/A
3D Object Super-Resolution,1,CAMELS Multifield Dataset,N/A,N/A,Super-Resolution,Computer Vision,"3D object super-resolution is the task of up-sampling 3D objects.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Multi-View Silhouette and Depth Decomposition for High Resolution 3D Object..."
Flowchart Grounded Dialog Response Generation,1,FloDial,N/A,N/A,N/A,N/A,N/A
Zero-Shot Flowchart Grounded Dialog Response Generation,1,FloDial,N/A,N/A,N/A,N/A,N/A
2D Semantic Segmentation task 2 (17 classes),1,CaDIS,N/A,N/A,N/A,N/A,N/A
Video-to-Shop,1,MovingFashion,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Tamil,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Kannada,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Malayalam,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Telugu,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Assamese,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Bengali,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Bodo,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Hindi,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Manipuri,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Marathi,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
Speech Synthesis - Rajasthani,1,IndicTTS,N/A,N/A,N/A,N/A,N/A
3D Building Mesh Labeling,1,BuildingNet,N/A,N/A,N/A,N/A,N/A
Object State Change Classification,1,Ego4D,N/A,N/A,N/A,N/A,N/A
State Change Object Detection,1,Ego4D,Ego4D,AP75; AP50; AP,N/A,Computer Vision,N/A
Short-term Object Interaction Anticipation,1,Ego4D,Ego4D,Noun (Top5 mAP); Noun+Verb(Top5 mAP); Noun+TTC (Top5 mAP); Overall (Top5 mAP),N/A,Computer Vision,N/A
Future Hand Prediction,1,Ego4D,Ego4D,M.Disp(Right); M.Disp(Left); C.Disp(Left); C.Disp(Right); Disp(Total),N/A,Computer Vision,N/A
Long Term Action Anticipation,1,Ego4D,N/A,N/A,N/A,N/A,N/A
Depth Image Estimation,1,HUMAN4D,N/A,N/A,N/A,Computer Vision,N/A
Table Functional Analysis,1,PubTables-1M,N/A,N/A,N/A,N/A,N/A
Unsupervised Instance Segmentation,1,UVO,COCO val2017; UVO,AP75; AP50; AP,Unsupervised Zero-Shot Instance Segmentation,Computer Vision,N/A
Open-World Instance Segmentation,1,UVO,N/A,N/A,N/A,N/A,N/A
Webpage Object Detection,1,CoVA,N/A,N/A,N/A,N/A,N/A
Natural Language Landmark Navigation Instructions Generation,1,map2seq,N/A,N/A,N/A,N/A,N/A
Event Expansion,1,Scifi TV Shows,N/A,N/A,N/A,N/A,N/A
Face Transfer,1,VGGFace2 HQ,N/A,N/A,N/A,N/A,N/A
Colon Cancer Detection In Confocal Laser Microscopy Images,1,Chaoyang,N/A,N/A,N/A,N/A,N/A
Weakly Supervised Data Denoising,1,BCNB,N/A,N/A,N/A,N/A,N/A
Caustics Segmentation,1,SuperCaustics,N/A,N/A,N/A,N/A,N/A
Discourse Marker Prediction,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Global Facts,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Miscellaneous,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Abstract Algebra,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Mathematical Induction,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Medical Genetics,1,BIG-bench,BIG-bench,Accuracy,Genetic Risk Prediction,Miscellaneous; Medical,N/A
Figure Of Speech Detection,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Question Selection,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Electrical Engineering,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Ventricular fibrillation detection,1,MIT-BIH Malignant Ventricular Ectopy Database (VFDB),N/A,N/A,N/A,N/A,N/A
Localization In Video Forgery,1,ForgeryNet,N/A,N/A,N/A,N/A,N/A
Physical Video Anomaly Detection,1,PHANTOM,N/A,N/A,N/A,N/A,N/A
Connective Detection,1,DISRPT2021,N/A,N/A,N/A,N/A,N/A
text-to-3d-human,1,ASLLVD,N/A,N/A,N/A,N/A,N/A
Text-to-Face Generation,1,FFHQ-Text,N/A,N/A,N/A,Computer Vision,N/A
Seizure prediction,1,TUH EEG Seizure Corpus,Melbourne University Seizure Prediction,AUC,N/A,Medical,N/A
Zero-Shot Cross-Lingual Text-to-Image Retrieval,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Zero-Shot Cross-Lingual Image-to-Text Retrieval,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Zero-Shot Cross-Lingual Visual Natural Language Inference,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Zero-Shot Cross-Lingual Visual Question Answering,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Max-Shot Cross-Lingual Visual Natural Language Inference,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Max-Shot Cross-Lingual Visual Question Answering,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Max-Shot Cross-Lingual Text-to-Image Retrieval,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Max-Shot Cross-Lingual Image-to-Text Retrieval,1,IGLUE,N/A,N/A,N/A,N/A,N/A
Cross-Domain Iris Presentation Attack Detection,1,NDPSID - WACV 2019,N/A,N/A,N/A,N/A,N/A
Image Categorization,1,Yeast colony morphologies,N/A,N/A,Fine-Grained Visual Categorization,Computer Vision,N/A
3D Human Dynamics,1,InfiniteRep,N/A,N/A,Portrait Animation,Audio,Image: [Zhang et al](https://openaccess.thecvf.com/content_ICCV_2019/papers/Zhang_Predicting_3D_Human_Dynamics_From_Video_ICCV_2019_paper.pdf)
Video Based Workflow Recognition,1,PETRAW,PETRAW,Average AD-Accuracy,N/A,Computer Vision,N/A
Segmentation Based Workflow Recognition,1,PETRAW,PETRAW,Average AD-Accuracy,N/A,Computer Vision,N/A
Video & Kinematic Base Workflow Recognition,1,PETRAW,PETRAW,Average AD-Accuracy,N/A,Computer Vision,N/A
"Video, Kinematic & Segmentation Base Workflow Recognition",1,PETRAW,PETRAW,Average AD-Accuracy,N/A,Computer Vision,N/A
Visual Abductive Reasoning,1,SHERLOCK,N/A,N/A,N/A,N/A,N/A
Fact Selection,1,ArgSciChat,ArgSciChat,Fact-F1,N/A,Natural Language Processing,A task where an agent should select at most two sentences from the paper as argumentative facts.
Anchor link prediction,1,Weibo-Douban,N/A,N/A,N/A,N/A,N/A
Style change detection,1,MuLD,N/A,N/A,N/A,N/A,N/A
Panorama Pose Estimation (N-view),1,ZInd,N/A,N/A,N/A,N/A,N/A
Pose Contrastive Learning,1,Fitness-AQA,N/A,N/A,N/A,Computer Vision,N/A
Motion Disentanglement,1,Fitness-AQA,N/A,N/A,N/A,Computer Vision,Disentangling irregular (anomalous) motion from regular motion.
Atom3D benchmark,1,ATOM3D,N/A,N/A,N/A,N/A,N/A
Ancient Text Restoration,1,I.PHI,I.PHI,Top 1 (%); Region (Top 1 (%)); Date (Years); CER (%); Top 20 (%); Region (Top 3 (%)),N/A,Miscellaneous,Image credit: [Restoring and attributing ancient texts using deep neural networks  ](https://paperswithcode.com/paper/restoring-and-attributing-ancient-texts-using)
Depth Anomaly Detection and Segmentation,1,MVTEC 3D-AD,N/A,N/A,N/A,N/A,N/A
RGB+3D Anomaly Detection and Segmentation,1,MVTEC 3D-AD,N/A,N/A,N/A,N/A,N/A
Behavioral Malware Detection,1,BODMAS,N/A,N/A,N/A,N/A,N/A
English Conversational Speech Recognition,1,CANDOR Corpus,N/A,N/A,N/A,N/A,N/A
Math Information Retrieval,1,ARQMath,ARQMath,P@10; bpref; MAP; NDCG,N/A,Natural Language Processing,Information Retrieval on Math Contents
Argument Pair Extraction (APE),1,RR,N/A,N/A,N/A,N/A,N/A
3D Source-Free Domain Adaptation,1,SynLiDAR,N/A,N/A,N/A,N/A,N/A
3D Unsupervised Domain Adaptation,1,SynLiDAR,N/A,N/A,N/A,N/A,N/A
Claim-Evidence Pair Extraction (CEPE),1,IAM Dataset,N/A,N/A,N/A,N/A,N/A
Reference-based Video Super-Resolution,1,RealMCVSR,N/A,N/A,N/A,N/A,N/A
Gait Recognition in the Wild,1,Gait3D,N/A,N/A,N/A,N/A,N/A
ECG QRS Detection,1,CPSC2019,N/A,N/A,N/A,N/A,N/A
Question Quality Assessment,1,60k Stack Overflow Questions,N/A,N/A,N/A,N/A,N/A
Weakly Supervised 3D Detection,1,KITTI-360,N/A,N/A,N/A,N/A,N/A
Short-observation new product sales forecasting,1,VISUELLE2.0,VISUELLE2.0,1 step MAE; 10 steps MAE,N/A,Time Series,N/A
Gallbladder Cancer Detection,1,GBCU,N/A,N/A,N/A,N/A,N/A
Classification Of Breast Cancer Histology Images,1,BCI,N/A,N/A,N/A,N/A,N/A
Object Segmentation,1,GRIT,GRIT,Segmentation (test); Segmentation (ablation),Landslide segmentation; Text-Line Extraction; Camouflaged Object Segmentation,Computer Vision,N/A
Keypoint Estimation,1,GRIT,N/A,N/A,N/A,N/A,N/A
Contact Detection,1,BEHAVE,BEHAVE,Precision; Recall,N/A,Robots,Static-friction contact detection in legged locomotion
Rolling Shutter Correction,1,BS-RSC,BS-RSC,Average PSNR (dB),N/A,N/A,Rolling Shutter Correction
LV Segmentation,1,Echonet-Dynamic,N/A,N/A,N/A,N/A,N/A
Respiratory motion forecasting,1,ExtMarker,N/A,N/A,N/A,N/A,N/A
Personality Recognition in Conversation,1,CPED,CPED,Accuracy of Openness; Accuracy of Neurotism; Accuracy of Agreeableness; Accuracy of Conscientiousness; Macro-F1; Accuracy (%); Accuracy of Extraversion,N/A,Natural Language Processing,"Given a speaker's conversation with others, it is required to recognize the speaker's personality traits through the conversation record, which includes two scenarios, (1) $1-1$ conversations: the rob..."
Personalized and Emotional Conversation,1,CPED,N/A,N/A,N/A,N/A,N/A
image smoothing,1,Real world moire pattern classification,N/A,N/A,N/A,Computer Vision,N/A
Fast Vehicle Detection,1,Autorickshaw Image Dataset | Niche Vehicle Dataset,N/A,N/A,N/A,N/A,N/A
Referring Image Matting (Expression-based),1,RefMatte,N/A,N/A,N/A,N/A,N/A
Referring Image Matting (Keyword-based),1,RefMatte,N/A,N/A,N/A,N/A,N/A
Referring Image Matting (RefMatte-RW100),1,RefMatte,N/A,N/A,N/A,N/A,N/A
Referring Image Matting (Prompt-based),1,RefMatte,N/A,N/A,N/A,N/A,N/A
Aesthetic Image Captioning,1,RPCD,N/A,N/A,N/A,N/A,N/A
Context-specific Spam Detection,1,Traditional and Context-specific Spam Twitter,N/A,N/A,N/A,N/A,N/A
Traditional Spam Detection,1,Traditional and Context-specific Spam Twitter,N/A,N/A,N/A,N/A,N/A
feature selection,1,Hotel,N/A,N/A,N/A,N/A,N/A
Fire Detection,1,895 Fire Videos Data,NIST Report of Test FR 4016,MCC; F1-Score,N/A,Time Series,Detection of fire using multi-variate time series sensor data.
Handwritten Line Segmentation,1,BN-HTRd,N/A,N/A,N/A,N/A,N/A
Markerless Motion Capture,1,RICH,N/A,N/A,N/A,N/A,N/A
Bangla Text Detection,1,Bengali.AI Handwritten Graphemes,N/A,N/A,N/A,N/A,N/A
Table Type Detection,1,T2Dv2,N/A,N/A,N/A,N/A,N/A
Weakly Supervised 3D Point Cloud Segmentation,1,ScribbleKITTI,N/A,N/A,N/A,Computer Vision,N/A
Translation mri-eng,1,FLoRes-200,N/A,N/A,N/A,N/A,N/A
Image-text Classification,1,FewSOL,N/A,N/A,Multilingual Image-Text Classification,Miscellaneous,N/A
Inverse-Tone-Mapping,1,MSU HDR Video Reconstruction Benchmark,MSU HDR Video Reconstruction Benchmark,HDR-SSIM; HDR-VQM; HDR-PSNR,inverse tone mapping,Computer Vision,N/A
Histopathological Segmentation,1,CoCaHis,N/A,N/A,N/A,N/A,N/A
image-sentence alignment,1,VALSE,N/A,N/A,N/A,N/A,N/A
Handwritten Digit Image Synthesis,1,MatriVasha:,N/A,N/A,N/A,N/A,N/A
Morphological Inflection,1,UniMorph 4.0,N/A,N/A,N/A,Natural Language Processing,"**Morphological Inflection** is the task of generating a target (inflected form) word from a source word (base form), given a morphological attribute, e.g. number, tense, and person etc. It is useful ..."
Panoptic Scene Graph Generation,1,PSG Dataset,N/A,N/A,N/A,N/A,N/A
Air Pollution Prediction,1,DEAP City Dataset,N/A,N/A,N/A,Miscellaneous,N/A
Unconditional Video Generation,1,CelebV-HQ,N/A,N/A,N/A,N/A,N/A
Face Reenactment,1,AnimeCeleb,N/A,N/A,N/A,Computer Vision,**Face Reenactment** is an emerging conditional face synthesis task that aims at fulfilling two goals simultaneously: 1) transfer a source face shape to a target face; while 2) preserve the appearance...
Predictive Process Monitoring,1,Ultra-processed Food Dataset,N/A,N/A,Prognosis,Time Series,A branch of predictive analysis that attempts to predict some future state of a business process.
3D scene Editing,1,LLFF,LLFF,CLIP,N/A,Computer Vision,N/A
3D Object Editing,1,LLFF,N/A,N/A,N/A,N/A,N/A
Extracting COVID-19 Events from Twitter,1,MOS Dataset,N/A,N/A,N/A,N/A,N/A
Multi-Labeled Relation Extraction,1,TimeBankPT,N/A,N/A,N/A,N/A,N/A
Information Cascade Popularity Prediction,1,Weibo,N/A,N/A,N/A,N/A,N/A
Grayscale Video Denoising,1,Videezy4K,N/A,N/A,N/A,N/A,N/A
Negation and Speculation Cue Detection,1,The BioScope Corpus,*sem 2012 Shared Task: Sherlock Dataset; BioScope : Abstracts,F1,N/A,Natural Language Processing,N/A
Few-shot Object Counting and Detection,1,FSC147,N/A,N/A,N/A,N/A,N/A
Out-of-Sight Trajectory Prediction,1,Vi-Fi Multi-modal Dataset,N/A,N/A,N/A,N/A,N/A
Local Color Enhancement,1,University of Waterloo skin cancer database,N/A,N/A,N/A,N/A,N/A
Cultural Vocal Bursts Intensity Prediction,1,HUME-VB,HUME-VB,Concordance correlation coefficient (CCC),N/A,Speech,to predict the intensity of 40 culture-specific emotions (10 emotions from each culture)
Vocal Bursts Intensity Prediction,1,HUME-VB,N/A,N/A,N/A,N/A,N/A
Vocal Bursts Valence Prediction,1,HUME-VB,N/A,N/A,N/A,N/A,N/A
Vocal Bursts Type Prediction,1,HUME-VB,N/A,N/A,N/A,N/A,N/A
Emotion-Cause Pair Extraction,1,"Xia and Ding, 2019",N/A,N/A,N/A,N/A,N/A
Landmark Tracking,1,MBW - Zoo Dataset,N/A,N/A,Muscle Tendon Junction Identification,Computer Vision; Medical,N/A
Unsupervised Landmark Detection,1,MBW - Zoo Dataset,MAFL Unaligned,Mean NME,N/A,Computer Vision,"The discovery of object landmarks on a set of images depicting objects of the same category, directly from raw images without using any manual annotations."
Weakly-supervised Anomaly Detection,1,ShanghaiTech Campus,N/A,N/A,N/A,N/A,N/A
Uncertainty Visualization,1,MUAD,N/A,N/A,N/A,N/A,N/A
Subject Transfer,1,iDesigner,N/A,N/A,N/A,N/A,N/A
Zero-Shot Facial Expression Recognition,1,MAFW,N/A,N/A,N/A,N/A,N/A
Gunshot Detection,1,BGG dataset,N/A,N/A,N/A,N/A,N/A
Shooter Localization,1,BGG dataset,N/A,N/A,N/A,Audio,Shooter localization based on videos.
Predicting Drug-Induced Laboratory Test Effects,1,CORRONA CERTAIN,N/A,N/A,N/A,N/A,N/A
Laminar-Turbulent Flow Localisation,1,Wind Tunnel and Flight Test Experiments,Wind Tunnel and Flight Test Experiments,Category IoU,N/A,Computer Vision,It is a segmentation task on thermographic measurement images in order to separate laminar and turbulent flow regions on flight body parts.
EEG Artifact Removal,1,CWL EEG/fMRI Dataset,N/A,N/A,N/A,N/A,N/A
Text to Image Generation,1,DiffusionDB,DiffusionDB,N/A,Text to 3D,Computer Vision; Natural Language Processing,N/A
Cancer-no cancer per image classification,1,CBIS-DDSM,N/A,N/A,N/A,N/A,N/A
Bangla Spelling Error Correction,1,DPCSpell-Bangla-SEC-Corpus,N/A,N/A,N/A,N/A,N/A
Counterfactual Planning,1,CRIPP-VQA,N/A,N/A,N/A,N/A,N/A
Streaming Target Sound Extraction,1,FSDSoundScapes,N/A,N/A,N/A,N/A,N/A
Video Defect Classification,1,QV-Pipe,N/A,N/A,N/A,N/A,N/A
Temporal Defect Localization,1,CCTV-Pipe,N/A,N/A,N/A,N/A,N/A
Mobile Periocular Recognition,1,UFPR-Periocular,N/A,N/A,N/A,N/A,N/A
Visual Text Correction,1,Dataset for Post-OCR text correction in Sanskrit,N/A,N/A,N/A,N/A,N/A
Phishing Website Detection,1,Padding Ain't Enough: Assessing the Privacy Guarantees of Encrypted DNS – Subpage-Agnostic Domain Classification Firefox,N/A,N/A,N/A,N/A,N/A
Arabic Speech Recognition,1,modified_shemo,N/A,N/A,N/A,N/A,N/A
Hyper-Relational Extraction,1,HyperRED,N/A,N/A,N/A,N/A,N/A
Event-based N-ary Relaiton Extraction,1,HyperRED,N/A,N/A,N/A,N/A,N/A
Role-based N-ary Relaiton Extraction,1,HyperRED,N/A,N/A,N/A,N/A,N/A
Hypergraph-based N-ary Relaiton Extraction,1,HyperRED,N/A,N/A,N/A,N/A,N/A
Open Vocabulary Attribute Detection,1,OVAD benchmark,N/A,N/A,N/A,N/A,N/A
Multilingual Image-Text Classification,1,GLAMI-1M,N/A,N/A,N/A,N/A,N/A
Acute Stroke Lesion Segmentation,1,ATLAS v2.0,N/A,N/A,N/A,N/A,N/A
SemEval-2022 Task 4-1 (Binary PCL Detection),1,DPM,DPM,F1-score,N/A,Natural Language Processing,N/A
SemEval-2022 Task 4-2 (Multi-label PCL Detection),1,DPM,N/A,N/A,N/A,N/A,N/A
Binary Condescension Detection,1,DPM,DPM,F1-score,N/A,Natural Language Processing,N/A
Multi-label Condescension Detection,1,DPM,DPM,Macro-F1,N/A,Natural Language Processing,N/A
One-Shot Part Segmentation of Grasp Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Cut Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Scoop Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Contain Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Wrap-Grasp Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Pound Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Support Affordance - Inter Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Grasp Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Cut Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Scoop Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Contain Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Wrap-Grasp Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Pound Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
One-Shot Part Segmentation of Support Affordance - Intra Class,1,UMD-i Affrodance Dataset,N/A,N/A,N/A,N/A,N/A
ArzEn Speech Recognition,1,ArzEn,N/A,N/A,N/A,N/A,N/A
Partial Video Copy Detection,1,STVD-PVCD,STVD-PVCD,F1,N/A,Computer Vision,The PVCD goal is identifying and locating if one or more segments of a long testing video have been copied (transformed) from the reference videos dataset.
Hyperspectral Image-Based Fruit Ripeness Prediction,1,DeepHS Fruit v2,N/A,N/A,N/A,N/A,N/A
Grounded Situation Recognition,1,VASR,SWiG,Top-1 Verb; Top-5 Verbs; Top-1 Verb & Value; Top-5 Verbs & Value; Top-1 Verb & Grounded-Value; Top-5 Verbs & Grounded-Value,N/A,Computer Vision,"Grounded Situation Recognition aims to produce the structured image summary which describes the primary activity (verb), its relevant entities (nouns), and their bounding-box groundings."
Document Image Skew Estimation,1,DISE 2021 Dataset,DISE 2021 Dataset,Percentage correct,N/A,Computer Vision,N/A
Multi-step retrosynthesis,1,USPTO-190,USPTO-190,Success Rate (100 model calls); Success Rate (500 model calls),N/A,N/A,These leaderboards are used to track progress in Multi-step retrosynthesis.
Sleep apnea detection,1,Apnea-ECG,N/A,N/A,N/A,N/A,N/A
Line Items Extraction,1,DocILE,N/A,N/A,N/A,N/A,N/A
Sign Language Retrieval,1,CSL-Daily,N/A,N/A,N/A,N/A,N/A
Calving Front Delineation In Synthetic Aperture Radar Imagery,1,CaFFe,CaFFe,Mean Distance Error,Calving Front Delineation In Synthetic Aperture Radar Imagery With Fixed Training Amount,Computer Vision,"Delineating the calving front of a marine-terminating glacier in synthetic aperture radar (SAR) imagery. This can, for example, be done through Semantic Segmentation."
Calving Front Delineation In Synthetic Aperture Radar Imagery With Fixed Training Amount,1,CaFFe,N/A,N/A,N/A,N/A,N/A
Video Narrative Grounding,1,Video Localized Narratives,N/A,N/A,N/A,N/A,N/A
Protein Secondary Structure Prediction,1,PS4,Jpred4 blind set; TS115; 2017_test set; CASP12; CB513; CullPDB; PS4; 2019_test set,Q3; Q8; Accuracy,N/A,Medical,"Protein secondary structure prediction is a vital task in bioinformatics, aiming to determine the arrangement of amino acids in proteins, including α-helices, β-sheets, and coils. By analyzing amino a..."
Color Mismatch Correction,1,Real-World Stereo Color and Sharpness Mismatch Dataset,N/A,N/A,N/A,N/A,N/A
Self-supervised Scene Flow Estimation,1,Argoverse 2,N/A,N/A,N/A,N/A,N/A
Visual Commonsense Tests,1,WHOOPS!,N/A,N/A,N/A,N/A,N/A
Social Media Popularity Prediction,1,Ranking social media news feed,SMP Test Split,SRC; MAE,N/A,Miscellaneous; Time Series,"Social Media Popularity Prediction (SMPP) aims to predict the future popularity (e.g., clicks, views, likes, etc.) of online posts automatically via plenty of social media data from public platforms. ..."
Heterogeneous Treatment Effect Estimation,1,IHDP,N/A,N/A,N/A,N/A,N/A
Robust Camera Only 3D Object Detection,1,nuScenes-C,N/A,N/A,N/A,N/A,N/A
Automatic Cell Counting,1,MuCeD,N/A,N/A,N/A,Miscellaneous,N/A
Face Image Quality Assessment,1,PIQ23,N/A,N/A,N/A,N/A,N/A
Robust BEV Detection,1,RoboBEV,N/A,N/A,N/A,N/A,N/A
Robust BEV Map Segmentation,1,RoboBEV,N/A,N/A,N/A,N/A,N/A
Long-tail Video Object Segmentation,1,BURST,N/A,N/A,N/A,N/A,N/A
Open-World Video Segmentation,1,BURST,N/A,N/A,N/A,N/A,N/A
Multi-Person Pose forecasting,1,Expi,N/A,N/A,N/A,N/A,N/A
Catalog Extraction,1,ChCatExt,N/A,N/A,N/A,N/A,N/A
Personalized Segmentation,1,PerSeg,PerSeg,mIoU,N/A,Computer Vision,"Given a one-shot image with a reference mask, the models are required to segment the indicated target object in any other images."
Generative Visual Question Answering,1,PMC-VQA,PMC-VQA,BLEU-1,Video-based Generative Performance Benchmarking,Reasoning,Generating answers in free form to questions posed about images.
Image Deconvolution,1,Stained mice brain blood vessels. Confocal-LFM,N/A,N/A,N/A,Computer Vision,N/A
Generalized Referring Expression Segmentation,1,gRefCOCO,N/A,N/A,N/A,N/A,N/A
Open-set video tagging,1,ChinaOpen-1k,N/A,N/A,N/A,Computer Vision,"In open-set video tagging, a model is asked to tag a given video with an ad-hoc vocabulary that the model is not specifically tuned for."
Multi-View 3D Shape Retrieval,1,FaMoS,N/A,N/A,N/A,Adversarial,N/A
CT Reconstruction,1,2DeteCT,N/A,N/A,N/A,N/A,N/A
Visual Analogies,1,Im-Promptu Visual Analogy Suite,N/A,N/A,N/A,Computer Vision,N/A
3D Multi-Person Human Pose Estimation,1,UNIPD-BPE,N/A,N/A,N/A,N/A,N/A
Promoter Detection,1,GUE,GUE,MCC,N/A,Medical,N/A
Core Promoter Detection,1,GUE,GUE,MCC,N/A,Medical,N/A
Splice Site Prediction,1,GUE,GUE,MCC,N/A,Medical,N/A
Covid Variant Prediction,1,GUE,GUE,Avg F1,N/A,Medical,N/A
Epigenetic Marks Prediction,1,GUE,GUE,MCC,N/A,Medical,N/A
Transcription Factor Binding Site Prediction (Human),1,GUE,N/A,N/A,N/A,N/A,N/A
Transcription Factor Binding Site Prediction (Mouse),1,GUE,N/A,N/A,N/A,N/A,N/A
Printed Text Recognition,1,UTRSet-Real,N/A,N/A,N/A,N/A,N/A
Real-Time Visual Tracking,1,Drunkard's Dataset,N/A,N/A,N/A,N/A,N/A
Tone Mapping,1,NILUT,N/A,N/A,N/A,N/A,N/A
Structured Report Generation,1,Rad-ReStruct,N/A,N/A,N/A,N/A,N/A
Retinal OCT Layer Segmentation,1,OCTID,N/A,N/A,N/A,N/A,N/A
Video-based Generative Performance Benchmarking (Correctness of Information),1,VideoInstruct,VideoInstruct,gpt-score,N/A,N/A,"The benchmark evaluates a generative Video Conversational Model with respect to Correctness of Information.    We curate a test set based on the ActivityNet-200 dataset, featuring videos with rich, de..."
Video-based Generative Performance Benchmarking (Detail Orientation)),1,VideoInstruct,N/A,N/A,N/A,N/A,N/A
Video-based Generative Performance Benchmarking (Contextual Understanding),1,VideoInstruct,N/A,N/A,N/A,N/A,N/A
Video-based Generative Performance Benchmarking (Temporal Understanding),1,VideoInstruct,N/A,N/A,N/A,N/A,N/A
Video-based Generative Performance Benchmarking (Consistency),1,VideoInstruct,N/A,N/A,N/A,N/A,N/A
Toxic Spans Detection,1,CAD,N/A,N/A,N/A,N/A,N/A
Described Object Detection,1,Description Detection Dataset,N/A,N/A,N/A,N/A,N/A
Nuclei Classification,1,MoNuSAC,N/A,N/A,N/A,N/A,N/A
Ontology Matching,1,AISECKG,N/A,N/A,N/A,Knowledge Base,N/A
Weakly-Supervised Object Segmentation,1,CheXlocalize,N/A,N/A,N/A,N/A,N/A
Semi-supervised Change Detection,1,GVLM,N/A,N/A,N/A,N/A,N/A
Landslide segmentation,1,GVLM,N/A,N/A,N/A,N/A,N/A
Vessel Detection,1,Vessel detection Dateset,N/A,N/A,N/A,N/A,N/A
Few-Shot Camera-Adaptive Color Constancy,1,Nikon RAW Low Light,N/A,N/A,N/A,N/A,N/A
Single-shot HDR Reconstruction,1,SI-HDR,N/A,N/A,N/A,Computer Vision,"SVE-based HDR imaging, also known as single-shot HDR imaging, algorithms capture a scene with pixel-wise varying exposures in a single image and then computationally synthesize an HDR image, which ben..."
Real-time instance measurement,1,MEIS,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Human Pose Estimation,1,PoPArt,N/A,N/A,N/A,N/A,N/A
Story Visualization,1,StoryBench,N/A,N/A,N/A,N/A,N/A
Conversational Sentiment Quadruple Extraction,1,DiaASQ,N/A,N/A,N/A,N/A,N/A
Composed Video Retrieval (CoVR),1,WebVid-CoVR,N/A,N/A,N/A,N/A,N/A
Global 3D Human Pose Estimation,1,EMDB,N/A,N/A,N/A,N/A,N/A
Unsupervised Image Decomposition,1,CongNaMul,N/A,N/A,N/A,Computer Vision,N/A
Depth And Camera Motion,1,CamlessVideosFromTheWild,N/A,N/A,Face Anti-Spoofing,Computer Vision,N/A
Fracture detection,1,GRAZPEDWRI-DX,N/A,N/A,N/A,N/A,N/A
Language-Based Temporal Localization,1,VidChapters-7M,VidChapters-7M,R1@.9; R@10s,Corpus Video Moment Retrieval,Computer Vision,N/A
Video Chaptering,1,VidChapters-7M,VidChapters-7M,R@3s; R@0.7; P@3s; R@0.5; P@0.5; P@5s; CIDEr; SODA; P@0.7; R@5s,N/A,Computer Vision,Partitioning a long video timeline into semantic units and generating corresponding chapter titles.
Outcome Prediction In Multimodal Mri,1,ABIDE,N/A,N/A,N/A,N/A,N/A
Multi-Subject Fmri Data Alignment,1,ABIDE,N/A,N/A,N/A,N/A,N/A
Motion Correction In Multishot Mri,1,ABIDE,N/A,N/A,N/A,Medical,N/A
Brain Lesion Segmentation From Mri,1,ABIDE,N/A,N/A,N/A,N/A,N/A
Android Malware Detection,1,Appdroid,N/A,N/A,N/A,N/A,N/A
Image-text matching,1,CommercialAdsDataset,N/A,N/A,N/A,N/A,N/A
Propaganda detection,1,Analysing state-backed propaganda websites: a new dataset and linguistic study,N/A,N/A,Propaganda technique identification; Propaganda span identification,Natural Language Processing,N/A
Odor Descriptor Prediction,1,Multi-Labelled SMILES Odors dataset,N/A,N/A,N/A,N/A,N/A
Automatic Modulation Recognition,1,AugMod,N/A,N/A,N/A,Time Series,Automatic modulation recognition/classification identifies the modulation pattern of communication signals received from wireless or wired networks.
"Suspicous (BIRADS 4,5)-no suspicous (BIRADS 1,2,3) per image classification",1,InBreast,N/A,N/A,N/A,N/A,N/A
Analog Video Restoration,1,TAPE,N/A,N/A,N/A,N/A,N/A
Scholarly Named Entity Recognition,1,GSAP-NER,N/A,N/A,N/A,N/A,N/A
Audio-Visual Question Answering (AVQA),1,AVQA,N/A,N/A,N/A,N/A,N/A
Coronary Artery Segmentation,1,ARCADE,N/A,N/A,N/A,N/A,N/A
Stenosis Segmentation,1,ARCADE,N/A,N/A,N/A,N/A,N/A
Caller Detection,1,InfantMarmosetsVox,N/A,N/A,N/A,N/A,N/A
Image-based Recommendation Explainability,1,Tripadvisor Restaurant Reviews,N/A,N/A,N/A,N/A,N/A
Pupil Tracking,1,INI-30,N/A,N/A,N/A,N/A,N/A
Pupil Detection,1,INI-30,N/A,N/A,N/A,N/A,N/A
Intraoperative Tracking,1,CholecTrack20,N/A,N/A,N/A,N/A,N/A
Intracorporeal Tracking,1,CholecTrack20,N/A,N/A,N/A,N/A,N/A
Visibility Tracking,1,CholecTrack20,N/A,N/A,N/A,N/A,N/A
3D Point Cloud Reinforcement Learning,1,Synthetic OD Data,N/A,N/A,N/A,N/A,N/A
video narration captioning,1,Shot2Story20K,Shot2Story20K,ROUGE; BLEU-4; METEOR; CIDEr,N/A,Computer Vision,Human narration is another critical factor to understand a multi-shot video. It often provides information of the background knowledge and commentator’s view on visual events. We conduct experiments t...
Brain Visual Reconstruction,1,GOD,N/A,N/A,Brain Visual Reconstruction from fMRI,Computer Vision,"Reconstruct viewed or imagined visual stimuli, including but not limited to images and videos from brain activities patterns recorded with neuoimaging technoligies."
Brain Visual Reconstruction from fMRI,1,GOD,N/A,N/A,N/A,N/A,N/A
inverse tone mapping,1,VDS dataset: Multi exposure stack-based inverse tone mapping,VDS dataset: Multi exposure stack-based inverse tone mapping,Kim and Kautz TMO-PSNR; HDR-VDP-2; PU21-PSNR; HDR-VDP-3; Reinhard'TMO-PSNR; PU21-SSIM,Inverse-Tone-Mapping,Computer Vision,For stack based inverse tone mapping
Specular Segmentation,1,PSD Dataset,N/A,N/A,N/A,Computer Vision,Segmentation of Specular Highlights in images
Geometrical View,1,MOSAD,N/A,N/A,N/A,Computer Vision,N/A
Clothing Attribute Recognition,1,Clothing Attributes Dataset,Clothing Attributes Dataset,Accuracy,N/A,Computer Vision,Clothing attribute recognition is the task of automatically identifying and categorizing various characteristics and attributes associated with clothing items in images
Historical Color Image Dating,1,Historical Color Image Dataset,HCI,accuracy; MAE,N/A,Computer Vision,N/A
Crop Type Mapping,1,SICKLE,N/A,N/A,N/A,N/A,N/A
Sowing Date Prediction,1,SICKLE,N/A,N/A,N/A,N/A,N/A
Transplanting Date Prediction,1,SICKLE,N/A,N/A,N/A,N/A,N/A
Harvesting Date Prediction,1,SICKLE,N/A,N/A,N/A,N/A,N/A
MLLM Evaluation: Aesthetics,1,EAPD,N/A,N/A,N/A,Computer Vision,N/A
Cell Tracking,1,ALFI,N/A,N/A,N/A,N/A,N/A
Factual Inconsistency Detection in Chart Captioning,1,CHOCOLATE,CHOCOLATE-LLM; CHOCOLATE-LVLM; CHOCOLATE; CHOCOLATE-FT,Kendall's Tau-c,N/A,N/A,Detect factual inconsistency between charts and captions.
Amodal Tracking,1,TAO-Amodal,N/A,N/A,N/A,N/A,N/A
Only Connect Walls Dataset Task 1 (Grouping),1,OCW,N/A,N/A,N/A,N/A,N/A
Hierarchical Text Segmentation,1,HierText,HierText,"F-score (stroke); F-score (word); F-score (para., layout); F-score (average); F-score (text-line)",N/A,Computer Vision,"Segment strokes, words, text-lines, paragraphs (layout analysis) in images within a unified framework"
Audio Quality Assessment,1,ODAQ: Open Dataset of Audio Quality,ODAQ: Open Dataset of Audio Quality,Pearson correlation coefficient (PCC),N/A,Audio,Computational audio quality assessment aims to predict the quality of audio signals as perceived by expert listeners.
Music Quality Assessment,1,ODAQ: Open Dataset of Audio Quality,N/A,N/A,N/A,Audio,Evaluating the quality of music given noise and filtering conditions
Conversational Web Navigation,1,WebLINX,WebLINX,Element (IoU); Text (F1); Overall score; Intent Match,N/A,Natural Language Processing,The problem of conversational web navigation is described as follow: a digital agent controls a web browser and follows user instructions to solve real-world tasks in a multi-turn dialogue fashion. It...
IUPAC Name Prediction,1,IUPAC Standards Online,N/A,N/A,N/A,N/A,N/A
Medical Image Deblurring,1,Human Protein Atlas,ChexPert ; Human Protein Atlas Image; COVID-19 CT Scan; Brain MRI segmentation,Average PSNR,N/A,Computer Vision,Medical image deblurring aims to remove blurs from medical images
AUDIO-VISUAL QUESTION ANSWERING (MUSIC-AVQA-v2.0),1,MUSIC-AVQA v2.0,N/A,N/A,N/A,N/A,N/A
Artifact Detection,1,HistoArtifacts,N/A,N/A,N/A,N/A,N/A
All-day Semantic Segmentation,1,All-day CityScapes,N/A,N/A,N/A,N/A,N/A
Aspect Category Sentiment Analysis,1,FABSA,N/A,N/A,N/A,N/A,N/A
Hidden Aspect Detection,1,FABSA,N/A,N/A,N/A,N/A,N/A
Latent Aspect Detection,1,FABSA,N/A,N/A,N/A,N/A,N/A
Chemical Entity Recognition,1,Chem-FINESE,N/A,N/A,N/A,Medical,Chemical Entity Recognition (CER) is a fundamental task in biomedical text mining and Natural Language Processing (NLP). It involves the identification and classification of chemical entities in textu...
Human Detection of Deepfakes,1,GOTCHA,N/A,N/A,N/A,N/A,N/A
Aerial Scene Classification,1,GDIT,N/A,N/A,N/A,N/A,N/A
Micro-Action Recognition,1,MA-52,N/A,N/A,N/A,N/A,N/A
Python Code Synthesis,1,MMCode,N/A,N/A,N/A,N/A,N/A
Game State Reconstruction,1,SoccerNet-GSR,N/A,N/A,N/A,N/A,N/A
Clinical Section Identification,1,MedSecId,N/A,N/A,N/A,N/A,N/A
Stereo-LiDAR Fusion,1,VBR,N/A,N/A,N/A,N/A,N/A
Novel LiDAR View Synthesis,1,VBR,N/A,N/A,N/A,N/A,N/A
Visual Prompt Tuning,1,VTAB,FGVC; VTAB-1k(Natural<7>); VTAB-1k(Specialized<4>); VTAB-1k(Structured<8>),Mean Accuracy,N/A,Computer Vision,Visual Prompt Tuning(VPT) only introduces a small amount of task-specific learnable parameters into the input space while freezing the entire pre-trained Transformer backbone during downstream trainin...
Face Image Retrieval,1,BTS3.1,N/A,N/A,N/A,N/A,N/A
motion retargeting,1,MultiSenseBadminton,N/A,N/A,N/A,Computer Vision,N/A
Retrosynthesis,1,PaRoutes,Mol-Instruction,Exact; Morgan FTS; METEOR; Validity,N/A,Medical,"Retrosynthetic analysis is a pivotal synthetic methodology in organic chemistry that employs a reverse-engineering approach, initiating from the target compound and retroactively tracing potential syn..."
Mono3DVG,1,Mono3DRefer,N/A,N/A,N/A,N/A,N/A
Bathymetry prediction,1,MagicBathyNet,N/A,N/A,N/A,N/A,N/A
Procedure Step Recognition,1,IndustReal,IndustReal,POS; F1; Delay (seconds),N/A,Computer Vision,"Procedure Step Recognition (PSR) focuses on recognizing the correct completion and order of procedural steps. Unlike traditional action recognition, which lacks a measure of success for actions, PSR a..."
3D Object Detection (RoI),1,View-of-Delft,N/A,N/A,N/A,N/A,N/A
Video Captioning on MSR-VTT,1,MSRVTT-CTN,N/A,N/A,N/A,N/A,N/A
Video Captioning on MS,1,MSVD-CTN,N/A,N/A,N/A,N/A,N/A
Linear Probing Object-Level 3D Awareness,1,ImageNet3D,N/A,N/A,N/A,Computer Vision,N/A
Anomaly Instance Segmentation,1,OoDIS,N/A,N/A,N/A,N/A,N/A
Segmented Multimodal Named Entity Recognition,1,Twitter-SMNER,Twitter-SMNER,F1,N/A,N/A,N/A
Oriented Object Detctio,1,EVD4UAV,N/A,N/A,N/A,N/A,N/A
Personalized Image Generation,1,DreamBooth,DreamBooth,Overall (CP * PF); Prompt Following (PF); Concept Preservation (CP),N/A,Computer Vision,"Utilizes single or multiple images that contain the same subject or style, along with text prompt, to generate images that contain that subject as well as match the textual description. Includes finet..."
Action Classification (zero-shot),1,WiGesture,N/A,N/A,N/A,N/A,N/A
Detection of potentially void clauses,1,AGB-DE,N/A,N/A,N/A,N/A,N/A
X-ray Visual Question Answering,1,MedPromptX-VQA,"MIMIC-CXR, MIMIC-IV",F1-score,N/A,Medical,N/A
Video Style Transfer,1,StyleGallery,N/A,N/A,N/A,Computer Vision,N/A
Video Temporal Consistency,1,Temporal Logic Video (TLV) Dataset,N/A,N/A,N/A,N/A,N/A
Hand-Object Interaction Detection,1,HOI-Synth,N/A,N/A,N/A,N/A,N/A
Uncertainty-Aware Panoptic Segmentation,1,MUSES: MUlti-SEnsor Semantic perception dataset,N/A,N/A,N/A,N/A,N/A
Sequential Place Recognition,1,Wild-Places,N/A,N/A,N/A,N/A,N/A
Few-shot Instance Segmentation,1,CAMO-FS,N/A,N/A,N/A,N/A,N/A
Multi-Objective Multi-Agent Reinforcement Learning,1,MOMAland,N/A,N/A,N/A,N/A,N/A
Joint Entity and Relation Extraction on Scientific Data,1,SemOpenAlex,N/A,N/A,N/A,Natural Language Processing,N/A
Vehicle Color Recognition,1,UFPR-VCR Dataset,N/A,N/A,N/A,N/A,N/A
Fake Song Detection,1,SONICS,N/A,N/A,N/A,Music,N/A
Synthetic Song Detection,1,SONICS,N/A,N/A,N/A,Audio,N/A
Fine-Grained Image Inpainting,1,InpaintCOCO,N/A,N/A,N/A,N/A,N/A
Lifelike 3D Human Generation,1,THuman2.0 Dataset,THuman2.0 Dataset,PSNR; SSIM; LPIPS; CLIP Similarity,N/A,Computer Vision,"Generating realistic and lifelike 3D Humans from a Single RGB image input, while preserving face identity, delivering realistic texture, accurate geometry, and maintaining a valid pose of the generate..."
ENF (Electric Network Frequency) Extraction,1,WHU - Audio ENF,N/A,N/A,ENF (Electric Network Frequency) Extraction from Video,Computer Vision; Time Series,"Extraction of Electric Network Frequency, that can be done from power grid raw voltage signal or audio and video files."
ENF (Electric Network Frequency) Detection,1,WHU - Audio ENF,N/A,N/A,N/A,N/A,"Detect if into a source (for example audio, video or raw valtages) it is contained the ENF signal."
ENF (Electric Network Frequency) Extraction from Video,1,ENF moving video,N/A,N/A,N/A,N/A,N/A
Information Extraction,1,SemTabNet,SemTabNet,average Tree Similarity Score,Extractive Summarization; Document-level Event Extraction; Event Extraction; Participant Intervention Comparison Outcome Extraction; Drug–drug Interaction Extraction,Natural Language Processing; Medical,Information extraction is the task of automatically extracting structured information from unstructured and / or semi-structured machine-readable documents and other electronically represented sources...
Singing Voice Synthesis,1,GTSinger,N/A,N/A,N/A,Music; Speech,"(Verse 1)  Sa bawat hakbang, sa bawat daan  May pangarap kang naghihintay  Westbridge ang gabay, sa iyong paglalakbay  Tungo sa kinabukasan, ng ating bayan      (Chorus)  Lagi kang kasama, sa aking pu..."
Image Stylization,1,CodeSCAN,N/A,N/A,One-Shot Face Stylization,Computer Vision,"**Image stylization** is a task that involves transforming an input image into a new image that has a different style, while preserving the content of the original image. The goal of image stylization..."
Novelty Detection,1,ADE-OoD,N/A,N/A,N/A,Natural Language Processing,Scientific Novelty Detection
Models Alignment,1,ListUltraFeedback,N/A,N/A,N/A,Knowledge Base,**Models Alignment** is the process of ensuring that multiple models used in a machine learning system are consistent with each other and aligned with the goals of the system. This involves defining c...
Political Salient Issue Orientation Detection,1,TwinViews-13k,N/A,N/A,N/A,N/A,N/A
Motion Interpolation,1,I2-2000FPS,N/A,N/A,N/A,N/A,3D human motion sequences interpolation and completion
Political Influence Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Scientific Data Usage Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Statistical Data Usage Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Data Sources Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Impacted Location Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Detecting Climate/Environmental Topics,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
News Target Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Authority Involvement Detection,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Voice pathology detection,1,Saarbruecken Voice Database,Saarbruecken Voice Database (males); Saarbruecken Voice Database (females),UAR,N/A,Medical,N/A
Beam Prediction,1,MVX,N/A,N/A,N/A,N/A,N/A
Optimize the trajectory of UAV which plays a BS in communication system,1,MVX,N/A,N/A,N/A,Adversarial,N/A
Few-shot Video Question Answering,1,CausalChaos!,N/A,N/A,N/A,N/A,N/A
Grounded Video Question Answering,1,CausalChaos!,N/A,N/A,N/A,N/A,N/A
Violence and Weaponized Violence Detection,1,ThreatGram 101 - Extreme Telegram Data,N/A,N/A,N/A,N/A,N/A
3D Open-Vocabulary Object Detection,1,IndraEye,N/A,N/A,N/A,N/A,N/A
Anomaly Localization,1,IDE,N/A,N/A,N/A,N/A,N/A
De novo molecule generation from MS/MS spectrum,1,MassSpecGym,MassSpecGym,Top-1 Tanimoto; Top-1 MCES; Top-1 Accuracy; Top-10 Tanimoto; Top-10 MCES; Top-10 Accuracy,N/A,Miscellaneous,N/A
Molecule retrieval from MS/MS spectrum,1,MassSpecGym,MassSpecGym,Hit rate @ 5; MCES @ 1; Hit rate @ 1; Hit rate @ 20,N/A,Miscellaneous,N/A
MS/MS spectrum simulation,1,MassSpecGym,MassSpecGym,Hit Rate @ 5; Hit Rate @ 1; Jensen-Shannon Similarity; Cosine Similarity; Hit Rate @ 20,N/A,Miscellaneous,N/A
De novo molecule generation from MS/MS spectrum (bonus chemical formulae),1,MassSpecGym,MassSpecGym,Top-1 Tanimoto; Top-1 MCES; Top-1 Accuracy; Top-10 Tanimoto; Top-10 MCES; Top-10 Accuracy,N/A,Miscellaneous,N/A
MS/MS spectrum simulation (bonus chemical formulae),1,MassSpecGym,MassSpecGym,Hit Rate @ 5; Hit Rate @ 1; Hit Rate @ 20,N/A,Miscellaneous,N/A
Molecule retrieval from MS/MS spectrum (bonus chemical formulae),1,MassSpecGym,MassSpecGym,Hit rate @ 5; MCES @ 1; Hit rate @ 1; Hit rate @ 20,N/A,Miscellaneous,N/A
CSV dialect detection,1,TUD,N/A,N/A,N/A,N/A,N/A
Sports Activity Recognition,1,IM-SportingBehaviors,N/A,N/A,N/A,N/A,N/A
Production Forecasting,1,SCG,N/A,N/A,N/A,N/A,N/A
Product Relation Classification,1,SCG,N/A,N/A,N/A,N/A,N/A
Product Relation Detection,1,SCG,N/A,N/A,N/A,N/A,N/A
Open Vocabulary Image Classification,1,OVIC Datasets,N/A,N/A,N/A,N/A,N/A
Fruit-type + Maturity-state Prediction (Multi-label Classification),1,RawRipe Dataset,N/A,N/A,N/A,N/A,N/A
Railway Track Image Classification,1,Railway Track Misalignment Detection Image Dataset,N/A,N/A,N/A,N/A,N/A
ECG Denoising,1,QT-NSTDB,N/A,N/A,N/A,N/A,N/A
Synthetic Image Attribution,1,SuSy Dataset,N/A,N/A,N/A,Computer Vision,"Determine the source or origin of a generated image, such as identifying the model or tool used to create it. This information can be useful for detecting copyright infringement or for investigating d..."
Omnnidirectional Stereo Depth Estimation,1,Helvipad,N/A,N/A,N/A,N/A,N/A
Video Synopsis,1,SynoClip,N/A,N/A,N/A,N/A,N/A
Blood Detection,1,EGC-FPHFS,N/A,N/A,N/A,N/A,N/A
Few-Shot Instance Classification,1,MVTec-FS,N/A,N/A,N/A,N/A,N/A
Hyperspectral Unmixing,1,Hyper Drive,N/A,N/A,N/A,N/A,N/A
Classification Of Hyperspectral Images,1,Hyper Drive,N/A,N/A,N/A,N/A,N/A
Inductive knowledge graph completion,1,Financial Dynamic Knowledge Graph,Wikidata5m-ind; FB15k-237-ind; WN18RR-ind,Hits@10; Hits@1; Hit@1; Hit@10; MRR; Hits@3,Large Language Model,Natural Language Processing,N/A
Persuasive Writing Strategy Detection Level-1,1,Persuasive Writing Strategy,N/A,N/A,N/A,N/A,N/A
Persuasive Writing Strategy Detection Level-2,1,Persuasive Writing Strategy,N/A,N/A,N/A,N/A,N/A
Persuasive Writing Strategy Detection Level-3,1,Persuasive Writing Strategy,N/A,N/A,N/A,N/A,N/A
Persuasive Writing Strategy Detection Level-4,1,Persuasive Writing Strategy,N/A,N/A,N/A,N/A,N/A
Cross-view Referring Multi-Object Tracking,1,2024 AI City Challenge,N/A,N/A,N/A,N/A,N/A
Single-View 3D Reconstruction on ShapeNet,1,SynthEVox3D-Tiny,N/A,N/A,N/A,N/A,N/A
Music Compression,1,Cadenza Woodwind,N/A,N/A,N/A,Audio,N/A
Human-Human Interaction Recognition,1,Waldo and Wenda,N/A,N/A,N/A,N/A,N/A
Malaria Vivax Detection,1,MP-IDB,N/A,N/A,N/A,N/A,N/A
Malaria Malariae Detection,1,MP-IDB,N/A,N/A,N/A,N/A,N/A
Malaria Ovale Detection,1,MP-IDB,N/A,N/A,N/A,N/A,N/A
Handwritten Document Recognition,1,U-DIADS-Bib,N/A,N/A,N/A,N/A,N/A
ADP Prediction,1,ADP Dataset,N/A,N/A,N/A,N/A,N/A
BEV Segmentation,1,SimBEV,SimBEV,rider; car; bus; truck; pedestrian; bicycle; road; mIoU; motorcycle,N/A,Computer Vision,N/A
3D Semantic Occupancy Prediction,1,SimBEV,N/A,N/A,N/A,Computer Vision,Uses sparse LiDAR semantic labels for training and testing
Computer Vision Transduction,1,CytoImage Net Dataset,N/A,N/A,N/A,Computer Vision,Transductive learning in computer vision tasks
Image-Variation,1,Horse Racing Photo Dataset,N/A,N/A,N/A,Computer Vision,"Given an image, generate variations of the image"
Event Relation Extraction,1,MAVEN-Arg,N/A,N/A,N/A,N/A,N/A
Relation Prediction,1,MAVEN-Arg,N/A,N/A,N/A,N/A,N/A
answerability prediction,1,PeerQA,PeerQA,Macro F1,N/A,Natural Language Processing,N/A
Cross-modal place recognition,1,Street360Loc,N/A,N/A,N/A,N/A,N/A
Raw reconstruction,1,RawNIND,N/A,N/A,N/A,Computer Vision,Reconstruct RAW camera sensor readings from the corresponding sRGB images
Object Rearrangement,1,Open6DOR V2,Open6DOR V2,pos-level0; rot-level2; pos-level1; rot-level0; 6-DoF; rot-level1,N/A,Robots,N/A
WaterDrop2D,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Sand2D,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Goop2D,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Elasticity3D,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
MultiMaterial2D,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Generating 3D Point Clouds,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Event Causality Identification,1,COLD: Causal Reasoning in Closed Daily Activities,N/A,N/A,N/A,N/A,N/A
Audio-Visual Video Captioning,1,LongVALE,N/A,N/A,N/A,N/A,N/A
Video Boundary Captioning,1,LongVALE,N/A,N/A,N/A,N/A,N/A
Surface Normals Estimation from Point Clouds,1,LiSu,N/A,N/A,N/A,Computer Vision,Parent task: 3d Point Clouds Analysis
3DGS,1,THVD,N/A,N/A,N/A,N/A,N/A
Image-to-Image Regression,1,2007 BP Anisotropic Velocity Benchmark,N/A,N/A,N/A,N/A,N/A
Safety Alignment,1,SUDO Dataset,N/A,N/A,N/A,Natural Language Processing,N/A
cross-modal alignment,1,Gaze-CIFAR-10,N/A,N/A,N/A,Computer Vision,N/A
Thermal Image Denoising,1,IRBFD,N/A,N/A,N/A,Computer Vision,N/A
Aerial Video Semantic Segmentation,1,WTA/TLA,N/A,N/A,N/A,N/A,N/A
Cyber Attack Detection,1,CAShift,N/A,N/A,N/A,Miscellaneous; Methodology,Cybersecurity attacks prediction using deep learning
Contact-rich Manipulation,1,Reactive Diffusion Policy-Dataset,N/A,N/A,N/A,N/A,N/A
Text-based Person Retrieval,1,TVPReid,N/A,N/A,N/A,Computer Vision,N/A
Disease Prediction,1,PreRAID,N/A,N/A,Retinal OCT Disease Classification; Disease Trajectory Forecasting,Computer Vision; Medical,N/A
confidence interval for traffic prediction,1,Traffic demand in NYC and CHI,N/A,N/A,N/A,Time Series,Providing confidence interval for traffic prediction
Silent Speech Recognition,1,Watch Your Mouth: Point Clouds based Speech Recognition Dataset,N/A,N/A,N/A,Speech,Interpret speech without acoustic signals
Hierarchical Text-Image Matching,1,HierarCaps,N/A,N/A,N/A,N/A,N/A
Dynamic Reconstruction,1,iPhone (Monocular Dynamic View Synthesis),iPhone (Monocular Dynamic View Synthesis),LPIPS,N/A,N/A,N/A
Vision-Language-Action,1,AutomotiveUI-Bench-4K,N/A,N/A,N/A,N/A,N/A
Dense contact estimation,1,MOW,N/A,N/A,N/A,N/A,N/A
Bone Suppression From Dual Energy Chest X-Rays,1,JSRT (negative formats),N/A,N/A,N/A,N/A,N/A
Anomaly Severity Classification (Anomaly vs. Defect),1,MVTec-AC,N/A,N/A,N/A,N/A,N/A
Video Action Detection,1,BAH,N/A,N/A,N/A,N/A,N/A
Human-Domain Subject-to-Video,1,OpenS2V-Eval,N/A,N/A,N/A,N/A,N/A
Single-Domain Subject-to-Video,1,OpenS2V-Eval,N/A,N/A,N/A,N/A,N/A
Temporal Action Segmentation,1,SICS-155,N/A,N/A,N/A,Computer Vision,Segment activities/actions temporally
Monocular 3D Object Localization,1,DTTD-Mobile,N/A,N/A,N/A,N/A,N/A
GUI Element Detection,1,CAGUI,N/A,N/A,N/A,N/A,N/A
WiFi CSI-based Image Reconstruction,1,TDMA CSI 16,N/A,N/A,N/A,N/A,N/A
Human-Object Interaction Generation,1,LLMafia,N/A,N/A,N/A,N/A,N/A
fake voice detection,1,ArVoice,ASVspoof 2019 - LA,Accuracy (%),N/A,Audio,N/A
Emotion Interpretation,1,EIBench,EIBench (complex); EIBench,Recall,N/A,Reasoning,N/A
Cross-Modal Information Retrieval,1,TED VCR,N/A,N/A,Cross-Modal Retrieval,Miscellaneous,"**Cross-Modal Information Retrieval** (CMIR) is the task of finding relevant items across different modalities. For example, given an image, find a text or vice versa. The main challenge in CMIR is kn..."
3D Architecture,1,[[contact~anytime]]How to Contact Expedia Customer Service,N/A,N/A,Denoising,Graphs; Adversarial,N/A
MRI classification,1,BRISC,N/A,N/A,N/A,N/A,N/A
Malaria Risk Exposure Prediction,0,N/A,N/A,N/A,N/A,Medical,N/A
Single-cell modeling,0,N/A,N/A,N/A,N/A,Medical,Single Cell RNA sequencing (scRNAseq) revolutionized our understanding of the fundamental of life sciences. The technology enables an unprecedented resolution to study heterogeneity in cell population...
X-Ray,0,N/A,N/A,N/A,Medical X-Ray Image Segmentation; Finding Pulmonary Nodules In Large-Scale Ct Images; Joint Vertebrae Identification And Localization In Spinal Ct Images; Low-Dose X-Ray Ct Reconstruction; Cbct Artifact Reduction,Medical,N/A
Facial Recognition and Modelling,0,N/A,N/A,N/A,Face Anti-Spoofing; Face Hallucination; Robust Face Alignment; Face Swapping; Robust Face Recognition,Miscellaneous; Computer Vision; Music,Facial tasks in machine learning operate based on images or video frames (or other datasets) focussed on human faces.
Sketch,0,N/A,N/A,N/A,Face Sketch Synthesis; Photo-To-Caricature Translation; Sketch Recognition; Drawing Pictures,Computer Vision,N/A
Link Quality Estimation,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Image Quality Estimation,0,N/A,N/A,N/A,N/A,Computer Vision,Same as [Image Quality Assessment](https://paperswithcode.com/task/image-quality-assessment).
Image and Video Forgery Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Image Text Removal,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Document To Image Conversion,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Transform A Video Into A Comics,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Detection Of Instrumentals Musical Tracks,0,N/A,N/A,N/A,N/A,Music,N/A
Video,0,N/A,N/A,N/A,Video Deinterlacing; Temporal Action Localization; Motion Compensation; Video Retrieval; Video Background Subtraction,Computer Vision,N/A
Pornography Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Typeface Completion,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Text-To-Image,0,N/A,N/A,N/A,Story Visualization; VGSI; Complex Scene Breaking and Synthesis,Computer Vision,N/A
Crowds,0,N/A,N/A,N/A,Crowd Counting; Group Detection In Crowds; Visual Crowd Analysis,Computer Vision,N/A
Visual Sentiment Prediction,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Sleep Quality,0,N/A,100 sleep nights of 8 caregivers,Accuracy,Spindle Detection; Multimodal Sleep Stage Detection; Sleep Quality Prediction; Sleep Stage Detection; Sleep Micro-event detection,Medical,"<span style=""color:grey; opacity: 0.6"">( Image credit: [DeepSleep](https://github.com/GuanLab/DeepSleep) )</span>"
Text-Independent Speaker Recognition,0,N/A,N/A,N/A,N/A,Speech,N/A
Skin,0,N/A,N/A,N/A,Skin Lesion Segmentation; Skin Lesion Classification; Skin Lesion Identification,Medical,N/A
Clickbait Detection,0,N/A,N/A,N/A,N/A,Playing Games; Natural Language Processing,"Clickbait detection is the task of identifying clickbait, a form of false advertisement, that uses hyperlink text or a thumbnail link that is designed to attract attention and to entice users to follo..."
Frame Duplication Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Automatic Machine Learning Model Selection,0,N/A,N/A,N/A,Smart Grid Prediction,Miscellaneous; Methodology,N/A
Flooded Building Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Manner Of Articulation Detection,0,N/A,N/A,N/A,N/A,Speech,N/A
Detecting Shadows,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Motion Detection In Non-Stationary Scenes,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Multi-modal image segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Constrained Diffeomorphic Image Registration,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Audio Signal Recognition,0,N/A,N/A,N/A,Gunshot Detection,Audio,N/A
Marine Robot Navigation,0,N/A,N/A,N/A,N/A,Robots,N/A
Trademark Retrieval,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Speaking Style Synthesis,0,N/A,N/A,N/A,N/A,Speech,N/A
Yield Mapping In Apple Orchards,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Population Mapping,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Air Quality Inference,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Modeling Local Geometric Structure,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Muscular Movement Recognition,0,N/A,N/A,N/A,N/A,Medical,N/A
Dynamic Texture Recognition,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Overlapping Mention Recognition,0,N/A,N/A,N/A,N/A,Natural Language Processing,Overlapping mention recognition is the task of correctly identifying all mentions of an entity in the presence of overlapping entity mentions.
Sar Image Despeckling,0,N/A,N/A,N/A,N/A,Computer Vision,Despeckling is the task of suppressing speckle from Synthetic Aperture Radar (SAR) acquisitions.    Image credits: GRD Sentinel-1 SAR image despeckled with [SAR2SAR-GRD](https://arxiv.org/abs/2102.006...
Image Declipping,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Image Steganography,0,N/A,N/A,N/A,N/A,Computer Vision,"**Image Steganography** is the main content of information hiding. The sender conceal a secret message into a cover image, then get the container image called stego, and finish the secret message’s tr..."
Multiple Sequence Alignment,0,N/A,OXBench; BAliBASE v3; SABMark v1.65,Total Column Score,N/A,Medical,N/A
White Matter Fiber Tractography,0,N/A,N/A,N/A,N/A,Medical,N/A
Abstract Argumentation,0,N/A,N/A,N/A,N/A,Reasoning; Natural Language Processing,Identifying argumentative statements from natural language dialogs.
Optimal Motion Planning,0,N/A,N/A,N/A,N/A,Robots,N/A
Hyperspectral,0,N/A,N/A,N/A,Hyperspectral Unmixing; Classification Of Hyperspectral Images; Hyperspectral Image Classification; Hyperspectral Image Segmentation,Computer Vision,N/A
Photometric Redshift Estimation,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Vector Quantization (k-means problem),0,N/A,N/A,N/A,N/A,Miscellaneous,Given a data set $X$ of d-dimensional numeric vectors and a number $k$ find a codebook $C$ of $k$ d-dimensional vectors such that the sum of square distances of each $x \in X$ to the respective neares...
Spectral Graph Clustering,0,N/A,N/A,N/A,N/A,Graphs,N/A
Spectrum Cartography,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Hypergraph Matching,0,N/A,N/A,N/A,N/A,Graphs,N/A
Video Games,0,N/A,N/A,N/A,Real-Time Strategy Games; FPS Games; Starcraft II; SNES Games; Dota 2,Playing Games,N/A
Image Instance Retrieval,0,N/A,N/A,N/A,Amodal Instance Segmentation,Computer Vision,"**Image Instance Retrieval** is the problem of retrieving images from a database representing the same object or scene as the one depicted in a query image.   <span class=""description-source"">Source: ..."
Spectral Estimation,0,N/A,N/A,N/A,Spectral Estimation From A Single Rgb Image,Computer Vision,N/A
Metal Artifact Reduction,0,N/A,N/A,N/A,N/A,Medical,Metal artifact reduction aims to remove the artifacts introduced by metallic implants in CT images.
GAN image forensics,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Counterspeech Detection,0,N/A,Youtube counterspeech dataset,F1 score,N/A,Natural Language Processing,"Counter-speech detection is the task of detecting counter-speech, i.e., a crowd-sourced response that argues, disagrees, or presents an opposing view to extremism or hateful content on social media pl..."
Video Correspondence Flow,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Ultrasound,0,N/A,Brain Anatomy US,Dice,Brain Ventricle Localization And Segmentation In 3D Ultrasound Images,Medical,N/A
Spoof Detection,0,N/A,ASVspoof 2019 - LA; ASVspoof 2019 - PA,t-DCF; EER,Face Presentation Attack Detection; Detecting Image Manipulation; Cross-Domain Iris Presentation Attack Detection; Finger Dorsal Image Spoof Detection,Computer Vision,N/A
3D,0,N/A,N/A,N/A,Pose Estimation; Text to 3D; 3D Volumetric Reconstruction; Generating 3D Point Clouds; 3D Geometry Perception,Music; Time Series; Knowledge Base; Computer Vision; Playing Games; Miscellaneous; Methodology,N/A
Visual Recognition,0,N/A,N/A,N/A,Fine-Grained Visual Recognition,Computer Vision,N/A
Image Recognition,0,N/A,N/A,N/A,License Plate Recognition; Material Recognition; Fine-Grained Image Recognition,Computer Vision,N/A
Shape Representation Of 3D Point Clouds,0,N/A,N/A,N/A,3D Point Cloud Reconstruction,Computer Vision,N/A
Kiss Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Natural Language Transduction,0,N/A,N/A,N/A,Lipreading,Computer Vision; Natural Language Processing,Converting one sequence into another
Time Series Denoising,0,N/A,N/A,N/A,N/A,Time Series,N/A
Smart Grid Prediction,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Crowd Flows Prediction,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Medical Super-Resolution,0,N/A,N/A,N/A,N/A,Medical,N/A
Cancer Metastasis Detection,0,N/A,N/A,N/A,N/A,Medical,N/A
Mitosis Detection,0,N/A,N/A,N/A,N/A,Medical,N/A
Pulsar Prediction,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Weakly-supervised panoptic segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Unbalanced Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Vision-based navigation with language-based assistance,0,N/A,N/A,N/A,N/A,Robots,A grounded vision-language task where an agent with visual perception is guided via language to find objects in photorealistic indoor environments. The task emulates a real-world scenario in that (a) ...
Phrase Vector Embedding,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Just like the generation of word (1-gram) vector embedding, this task is for phrase (n-gram) vector embedding."
Image Imputation,0,N/A,N/A,N/A,N/A,Computer Vision,"Image imputation is the task of creating plausible images from low-resolution images or images with missing data.    <span style=""color:grey; opacity: 0.6"">( Image credit: [NASA](https://www.jpl.nasa...."
Normalising Flows,0,N/A,N/A,N/A,N/A,Methodology,N/A
Knee Osteoarthritis Prediction,0,N/A,N/A,N/A,N/A,Medical,N/A
Readmission Prediction,0,N/A,N/A,N/A,N/A,Medical,N/A
Gravitational Wave Detection,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
4D Spatio Temporal Semantic Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,Image: [Choy et al](https://paperswithcode.com/paper/4d-spatio-temporal-convnets-minkowski)
incongruity detection,0,N/A,N/A,N/A,N/A,Natural Language Processing,Incongruity detection is the task of identifying statements in a text that are inconsistent with each other.
Text Attribute Transfer,0,N/A,N/A,N/A,N/A,Natural Language Processing,"The goal of the **Text Attribute Transfer** task is to change an input text such that the value of a particular linguistic attribute of interest (e.g. language = English, sentiment = Positive) is tran..."
Relation Mention Extraction,0,N/A,N/A,N/A,N/A,Natural Language Processing,Extracting phrases representative for a specific relation.
Image-To-Gps Verification,0,N/A,N/A,N/A,N/A,Computer Vision,"The image-to-GPS verification task asks whether a given image is taken at a claimed GPS location.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Image-to-GPS Verification Through A Bottom-..."
X-Ray Diffraction (XRD),0,N/A,N/A,N/A,N/A,Miscellaneous,"Diffraction of X-ray patterns and images, with common applications for materials and images."
Pulmonary Embolism Detection,0,N/A,PE-CAD FPRED,AUC,N/A,Medical,N/A
JPEG Artifact Removal,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Speculation Detection,0,N/A,N/A,N/A,Speculation Scope Resolution,Natural Language Processing,Identifying information in text that is speculative as opposed to factual information.
Acoustic Novelty Detection,0,N/A,A3Lab PASCAL CHiME,F1,N/A,Audio,"Detect novel events given acoustic signals, either in domestic or outdoor environments."
Image Morphing,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Value prediction,0,N/A,Py150,MRR,Body Mass Index (BMI) Prediction,Computer Vision; Computer Code,N/A
Triad Prediction,0,N/A,N/A,N/A,N/A,Graphs,N/A
Thai Word Segmentation,0,N/A,BEST-2010; WS160,F1-Score; F1-score,N/A,Natural Language Processing,Thai word segmentation
Unsupervised Speech Recognition,0,N/A,N/A,N/A,N/A,Speech,N/A
Diabetic Foot Ulcer Detection,0,N/A,N/A,N/A,N/A,Medical,N/A
Camouflage Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Manufacturing Quality Control,0,N/A,N/A,N/A,N/A,Computer Vision,AI for Quality control in manufacturing processes.
Sequential Distribution Function Estimation,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Multi Diseases Detection,0,N/A,N/A,N/A,N/A,Medical,N/A
Image popularity prediction,0,N/A,N/A,N/A,N/A,N/A,N/A
Low-rank compression,0,N/A,N/A,N/A,N/A,Computer Code,N/A
Pre-election ratings estimation,0,N/A,N/A,N/A,N/A,Reasoning,N/A
Quantum Circuit Mapping,0,N/A,N/A,N/A,N/A,Methodology,Mapping quantum circuits to quantum devices
Prototype Selection,0,N/A,N/A,N/A,N/A,N/A,N/A
Matrix Factorization / Decomposition,0,N/A,N/A,N/A,N/A,N/A,N/A
Template Matching,0,N/A,N/A,N/A,N/A,Computer Vision,Template matching is a technique that is used to find a subimage or a patch (called the template) within a larger image. The basic idea behind template matching is to slide the template image over the...
Detection of Dependencies,0,N/A,N/A,N/A,Detection of Higher Order Dependencies,Methodology,N/A
Text Compression,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Extractive Tags Summarization,0,N/A,N/A,N/A,N/A,Natural Language Processing,"The goal of Extractive Tags Summarization (ETS) task is to shorten the list of tags corresponding to a digital image while keeping the representativity; i.e., is to extract important tags from the con..."
Face Selection,0,N/A,N/A,N/A,N/A,Natural Language Processing,A task where an agent should select at most two sentences from the paper as argumentative facts.
Variable Selection,0,N/A,N/A,N/A,N/A,Methodology,N/A
3D Rotation Estimation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Seismic source localization,0,N/A,N/A,N/A,N/A,Time Series,Locating a seismic source using seismometer recordings
Structual Feature Correlation,0,N/A,N/A,N/A,N/A,Graphs,Expressive Power of GNN to predict structural feature's correlation mutually.
Automated Pulmonary Nodule Detection And Classification,0,N/A,N/A,N/A,Pulmonary Nodules Classification,Medical,N/A
Photoplethysmogram simulation,0,N/A,N/A,N/A,N/A,Medical,Simulating photoplethysmogram (PPG) signals
Chinese Spelling Error Correction,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Vietnamese Word Segmentation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Partially View-aligned Multi-view Learning,0,N/A,Scene-15; Caltech101; n-MNIST; Reuters En-Fr,NMI,N/A,Computer Vision,"In multi-view learning, Partially View-aligned Problem (PVP) refers to the case when only a portion of data is aligned, thus leading to data inconsistency."
Prostate Zones Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Cognate Prediction,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Bladder Segmentation,0,N/A,N/A,N/A,N/A,Medical,N/A
Bokeh Effect Rendering,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Design Synthesis,0,N/A,N/A,N/A,N/A,Adversarial,N/A
Touch detection,0,N/A,N/A,N/A,N/A,Robots,N/A
Job Prediction,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
road scene understanding,0,N/A,N/A,N/A,Monocular Cross-View Road Scene Parsing(Road); Monocular Cross-View Road Scene Parsing(Vehicle),Computer Vision,N/A
Control with Prametrised Actions,0,N/A,Half Field Offence; Robot Soccer Goal; Platform,Return; Goal Probability,N/A,Playing Games,"Most reinforcement learning research papers focus on environments where the agent’s actions are either discrete or continuous. However, when training an agent to play a video game, it is common to enc..."
3D Character Animation From A Single Photo,0,N/A,N/A,N/A,Scene Recognition,Computer Vision,Image: [Weng et al](https://arxiv.org/pdf/1812.02246v1.pdf)
Image Deblocking,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Action Analysis,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Table Extraction,0,N/A,N/A,N/A,Table Functional Analysis,Miscellaneous,Table extraction involves detecting and recognizing a table's logical structure and content from its unstructured presentation within a document
Protein-Ligand Affinity Prediction,0,N/A,CSAR-HiQ; PDBbind,RMSE,MHC presentation prediction,Medical,N/A
Dynamic Community Detection,0,N/A,N/A,N/A,N/A,Graphs,community detection in dynamic networks
Code Reuse Detection,0,N/A,N/A,N/A,N/A,N/A,N/A
2D Classification,0,N/A,N/A,N/A,Voice Conversion; 2D Pose Estimation; Multi-label Image Recognition with Partial Labels; Neural Network Compression; Neural Rendering,Music; Computer Vision; Computer Code; Playing Games; Audio; Adversarial; Medical; Methodology; Graphs,N/A
3D Bin Packing,0,N/A,N/A,N/A,N/A,Miscellaneous,"As a classic NP-hard problem, the bin packing problem (1D-BPP) seeks for an assignment of a collection of items with various weights to bins. The optimal assignment houses all the items with the fewes..."
3D Surface Generation,0,N/A,N/A,N/A,Visibility Estimation from Point Cloud,Computer Vision,Image: [AtlasNet](https://arxiv.org/pdf/1802.05384v3.pdf)
Programming Error Detection,0,N/A,N/A,N/A,N/A,Computer Code,N/A
SET TO GRAPH PREDICTION,0,N/A,N/A,N/A,N/A,Graphs,N/A
Set-to-Graph Prediction,0,N/A,N/A,N/A,N/A,Graphs,N/A
Hyperedge Prediction,0,N/A,N/A,N/A,N/A,Graphs,"Hyperlink/hyperedge prediction, targets to find missing hyperedges in a hypergraph."
Obfuscation Detection,0,N/A,N/A,N/A,N/A,N/A,N/A
Audio inpainting,0,N/A,N/A,N/A,N/A,Audio,Filling in holes in audio data
Linear Mode Connectivity,0,N/A,N/A,N/A,N/A,Knowledge Base,"**Linear Mode Connectivity** refers to the relationship between input and output variables in a linear regression model. In a linear regression model, input variables are combined with weights to pred..."
Open Set Video Captioning,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Vietnamese Aspect-Based Sentiment Analysis,0,N/A,N/A,N/A,Sentiment Dependency Learning,Natural Language Processing,"UIT-ViSFD: A Vietnamese Smartphone Feedback Dataset for Aspect-Based Sentiment Analysis       In this paper, we present a process of building a social listening system based on aspect-based sentiment ..."
Manufacturing simulation,0,N/A,N/A,N/A,N/A,Knowledge Base,Simulation of manufacturing system for applying AI methods and big data analysis
3D Prostate Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Video Propagation,0,N/A,N/A,N/A,N/A,Computer Vision,Propagating information in processed frames to unprocessed frames
Computer Architecture and Systems,0,N/A,N/A,N/A,N/A,N/A,N/A
Video Individual Counting,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Earthquake prediction,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
"Deep-Sea Treasure, Image version",0,N/A,N/A,N/A,N/A,Playing Games,"Image state version of the multi-objective reinforcement learning toy environment originally introduced in ""Empirical evaluation methods for multiobjective reinforcement learning algorithms"" by P. Vam..."
Spoken Command Recognition,0,N/A,Speech Command v2,Accuracy,N/A,Speech,N/A
Graph Outlier Detection,0,N/A,N/A,N/A,N/A,Graphs,N/A
3D Canonicalization,0,N/A,N/A,N/A,N/A,Computer Vision,3D Canonicalization is the process of estimating a transformation-invariant  feature for classification and part segmentation tasks.
Unsupervised 3D Point Cloud Linear Evaluation,0,N/A,N/A,N/A,N/A,Computer Vision,Training a linear classifier(e.g. SVM) on the representations learned in an unsupervised manner on the pretrained(e.g. ShapeNet) dataset.
Hate Intensity Prediction,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Referring Image Matting,0,N/A,N/A,N/A,Referring Image Matting (Keyword-based); Referring Image Matting (RefMatte-RW100); Referring Image Matting (Prompt-based); Referring Image Matting (Expression-based),Computer Vision,"Extracting the meticulous alpha matte of the specific object from the image that can best match the given natural language description, e.g., a keyword or a expression."
Inductive Bias,0,N/A,N/A,N/A,N/A,N/A,N/A
Medical waveform analysis,0,N/A,N/A,N/A,Electrocardiography (ECG); Electromyography (EMG),Methodology; Medical,"Information extraction from medical waveforms such as the electrocardiogram (ECG), arterial blood pressure (ABP) central venous pressure (CVP), photoplethysmogram (PPG, Pleth)."
Reverse Dictionary,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Image Retouching,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Image Fusion,0,N/A,N/A,N/A,Pansharpening; Multi Focus Image Fusion,Computer Vision,"Image fusion is a process in computational imaging where information from multiple images, often from different imaging modalities or spectral bands, is combined into a single image. The aim is to enh..."
Situation Recognition,0,N/A,imSitu,Top-1 Verb & Value; Top-5 Verbs & Value; Top-5 Verbs; Top-1 Verb,Grounded Situation Recognition,Computer Vision,"Situation Recognition aims to produce the structured image summary which describes the primary activity (verb), and its relevant entities (nouns)."
speed predict,0,N/A,N/A,N/A,N/A,N/A,N/A
Image Retargeting,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Continual Named Entity Recognition,0,N/A,N/A,N/A,FG-1-PG-1,Natural Language Processing,Continual learning for named entity recogntion
RGB-D Reconstruction,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Monocular Reconstruction,0,N/A,N/A,N/A,N/A,N/A,N/A
Transductive Learning,0,N/A,N/A,N/A,N/A,N/A,"In this setting, both a labeled training sample and an (unlabeled) test sample are provided at training time. The goal is to predict only the labels of the given test instances as accurately as possib..."
Concave shapes,0,N/A,N/A,N/A,N/A,N/A,N/A
Friction,0,N/A,N/A,N/A,N/A,N/A,N/A
Contact mechanics,0,N/A,N/A,N/A,N/A,N/A,N/A
HYPERVIEW Challenge,0,N/A,N/A,N/A,N/A,Computer Vision,The objective of this challenge is to advance the state of the art for soil parameter retrieval from hyperspectral data in view of the upcoming Intuition-1 mission. A campaign took place in March 2021...
Aggregate xView3 Metric,0,N/A,N/A,N/A,N/A,Computer Vision,"The aggregate xView3 metric is the combination of five metrics: object detection F1 score, close-to-shore object detection F1 score, vessel/not vessel classification F1 score, fishing/not fishing clas..."
Image Deep Networks,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Solar Flare Prediction,0,N/A,N/A,N/A,N/A,Time Series,Solar flare prediction in heliophysics
Detect Ground Reflections,0,N/A,N/A,N/A,N/A,Miscellaneous,This task helps in detecting the significant ground reflections at mm-wave bands. The harvested ground reflections can help in overcoming transient blockages at mm-wave bands
3D Inpainting,0,N/A,N/A,N/A,N/A,Computer Vision,"**3D Inpainting** is the removal of unwanted objects  from a 3D scene, such that the replaced region is visually  plausible and consistent with its context."
Conformal Prediction,0,N/A,N/A,N/A,N/A,Computer Vision,Conformal Prediction is a machine learning framework that provides valid measures of confidence for individual predictions. It offers a principled approach to quantify uncertainty in predictions witho...
Pcl Detection,0,N/A,N/A,N/A,SemEval-2022 Task 4-1 (Binary PCL Detection); SemEval-2022 Task 4-2 (Multi-label PCL Detection),Miscellaneous; Music; Natural Language Processing,N/A
Spectral Efficiency Analysis of Uplink-Downlink Decoupled Access in C-V2X Networks,0,N/A,N/A,N/A,N/A,Computer Code,Code for Spectral Efficiency Analysis of Uplink-Downlink Decoupled Access in C-V2X Networks
Finger Vein Recognition,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Metadata quality,0,N/A,N/A,N/A,N/A,Miscellaneous,This task consists of the automatic assessment of the quality of the metadata of an information system.
single-image-generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
human-scene contact detection,0,N/A,N/A,N/A,N/A,Computer Vision,detecting contact between human bodies and scenes
Symmetric face inpainting,0,N/A,N/A,N/A,N/A,N/A,N/A
Generative Semantic Nursing,0,N/A,N/A,N/A,N/A,Methodology,**Generative Semantic Nursing** is a task of intervening the generative process on the fly during inference time to improve the faithfulness of the generated images. It works by carefully manipulating...
Calving Front Delineation From Synthetic Aperture Radar Imagery,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Multi-Objective Reinforcement Learning,0,N/A,N/A,N/A,N/A,N/A,N/A
Twinwidth Contraction Sequence,0,N/A,N/A,N/A,N/A,Graphs,"**Twinwidth Contraction Sequence** is a concept in graph theory and computer science that refers to a sequence of graph contractions that transform a graph into a smaller, more manageable graph. The g..."
Instrument Playing Technique Detection,0,N/A,N/A,N/A,N/A,Music,N/A
Zero-shot Text-to-Video Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Class-Incremental Semantic Segmentation,0,N/A,N/A,N/A,Disjoint 10-1; Overlapped 100-10; Overlapped 5-3; Overlapped 14-1; Disjoint 15-5,Computer Vision,Semantic segmentation with continous increments of classes.
Robust Traffic Prediction,0,N/A,N/A,N/A,N/A,Time Series,N/A
Motion Magnification,0,N/A,N/A,N/A,N/A,Computer Vision,"Motion magnification is a technique that acts like a microscope for visual motion. It can amplify subtle motions in a video sequence, allowing for visualization of deformations that would otherwise be..."
Video Relationship,0,N/A,N/A,N/A,N/A,N/A,N/A
Spatio-temporal Scene Graphs,0,N/A,N/A,N/A,N/A,N/A,N/A
Adversarial Attack Detection,0,N/A,N/A,N/A,N/A,Knowledge Base,The detection of adversarial attacks.
continual anomaly detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Muscle-Computer Interfaces (MCIs),0,N/A,N/A,N/A,Low-latency processing,Robots,An interaction methodology that directly senses and decodes human muscular activity rather than relying on physical device actuation or user actions that are externally visible or audible.
Emotion Detection and Trigger Summarization,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
3D Molecule Generation,0,N/A,N/A,N/A,N/A,Medical,N/A
Multilingual Text-to-Image Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Crosslingual Text-to-Image Generation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Multi-lingual Text-to-Image Generation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Cross-lingual Text-to-Image Generation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Dielectric Constant,0,N/A,N/A,N/A,N/A,Miscellaneous,"Materials with high-dielectric constant easily polarize under external electric fields, allowing them to perform essential functions in many modern electronic devices."
Wearable Activity Recognition,0,N/A,N/A,N/A,N/A,Time Series,N/A
Forward reaction prediction,0,N/A,Mol-Instruction,Exact; Morgan FTS; METEOR; Validity,N/A,Medical,"Forward reaction prediction pertains to the anticipatory determination of the probable product(s) of a chemical reaction, given specific reactants and reagents.   This facilitates the optimization of ..."
Reagent Prediction,0,N/A,Mol-Instruction,Exact; Morgan FTS; METEOR; Validity,N/A,Medical,"Reagent prediction endeavors to ascertain the suitable catalysts, solvents, or ancillary substances required for a specific chemical reaction.  This endeavor facilitates chemists in uncovering novel r..."
Property Prediction,0,N/A,N/A,N/A,N/A,Medical,Property prediction involves forecasting or estimating a molecule's inherent physical and chemical properties based on information derived from its structural characteristics.   It facilitates high-th...
Catalytic activity prediction,0,N/A,N/A,N/A,N/A,Medical,"The EC number, a numerical classification system for enzymes hinging on the chemical reactions they catalyze, is substituted with the corresponding reaction. This substitution aims to leverage the tac..."
Domain/Motif Prediction,0,N/A,N/A,N/A,N/A,Medical,"The domain prediction task which tasks language models with the identification of the domain type within a given protein sequence, which is defined as a compact folded three-dimensional structure."
Functional Description Generation,0,N/A,N/A,N/A,N/A,Medical,"The functional description generation task, which not only evaluates the reasoning capability of the language model in determining the function of a protein sequence but also assesses the efficacy of ..."
Chemical-Disease Interaction Extraction,0,N/A,N/A,N/A,N/A,Medical,"The goal of this task is to discern the relationships between chemicals and diseases from given medical literature, a concept known as chemical-induced disease (CID) relations. These CID relations are..."
Chemical-Protein Interaction Extraction,0,N/A,N/A,N/A,N/A,Medical,The models are presented with excerpts from scientific literature and are required to not only identify distinct chemicals within the text but also to discern the specific nature of the interactions b...
Transcription Factor Binding Site Prediction,0,N/A,690 ChIP-seq,AUC-ROC,Transcription Factor Binding Site Prediction (Mouse); Transcription Factor Binding Site Prediction (Human),Medical,N/A
Visually Guided Sound Source Separation,0,N/A,N/A,N/A,N/A,Audio,The task of visually guided sound source separation (also referred as audio-visual sound separation or visual sound separation) aims to recover sound components from a mixture audio with the aid of vi...
Computing Characateristic Functions,0,N/A,N/A,N/A,N/A,Time Series,N/A
Label Error Detection,0,N/A,TREC-6,Accuracy,N/A,Miscellaneous,Identify labeling errors in data
Point- of-no-return (PNR) temporal localization,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Motion Expressions Guided Video Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,"Given a video and an expression describing the motion clues of the target object(s), MeViS requires to segment and track the target object(s) accurately."
Reconstruction Attack,0,N/A,N/A,N/A,N/A,Adversarial,"Facial reconstruction attack of facial manipulation models such as: Face swapping models, anonymization models, etc."
Zero-Shot Visual Question Answring,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Adversarial Attack on Video Classification,0,N/A,N/A,N/A,N/A,Adversarial,Use optimizer to add disturbe on video frame to fool the video classification systems. The key issue exist in tremendos calculation and the selection of key frame and key area.
Train Ego-Path Detection,0,N/A,N/A,N/A,N/A,Computer Vision,Refer to https://arxiv.org/abs/2403.13094 for the task description.
RNA 3D STRUCTURE PREDICTION,0,N/A,N/A,N/A,N/A,Medical,N/A
Japanese Word Segmentation,0,N/A,BCCWJ,F1-score (Word),N/A,Natural Language Processing,N/A
Task and Motion Planning,0,N/A,N/A,N/A,N/A,Robots,N/A
gaze redirection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Image Operation Chain Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Specular Reflection Mitigation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Model Predictive Control,0,N/A,N/A,N/A,N/A,Time Series,N/A
Personality Trait Recognition by Face,0,N/A,First Impressions v2,CCC; mAcc,N/A,Computer Vision,N/A
Only Connect Walls Dataset Task 2 (Connections),0,N/A,N/A,N/A,N/A,Natural Language Processing,Finding the connection between 4 clues
Unsupervised Zero-Shot Panoptic Segmentation,0,N/A,COCO val2017,SQ; PQ; RQ,N/A,Computer Vision,N/A
Micro-video recommendations,0,N/A,N/A,N/A,N/A,N/A,N/A
MLLM Aesthetic Evaluation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Derendering,0,N/A,N/A,N/A,N/A,Computer Vision,Convert offline handwriting (images) to online handwriting (digital ink).
Climate Projection,0,N/A,N/A,N/A,N/A,Time Series,Simulation of Earth's climate for future decades.
Mutational/Variant Effect Prediction,0,N/A,N/A,N/A,N/A,Medical,Using sequence or any other variant-specific information for organisms to determine the phenotypic change compared to wild-type variant.
Computer Vision Techniques Adopted in 3D Cryogenic Electron Microscopy,0,N/A,N/A,N/A,Cryogenic Electron Tomography; Single Particle Analysis,Computer Vision,Computer Vision Techniques Adopted in 3D Cryogenic Electron Microscopy
Scene Text Editing,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Brain Computer Interface,0,N/A,N/A,N/A,ERP; channel selection; Motor Imagery; SSVEP,Time Series; Medical,"A Brain-Computer Interface (BCI), also known as a Brain-Machine Interface (BMI), is a technology that enables direct communication between the brain and an external device, such as a computer or a mac..."
Motor Imagery,0,N/A,N/A,N/A,Within-Session Motor Imagery,Medical,"Classification of examples recorded under the Motor Imagery paradigm, as part of Brain-Computer Interfaces (BCI).    A number of motor imagery datasets can be downloaded using the MOABB library: [moto..."
Within-Session Motor Imagery,0,N/A,N/A,N/A,Within-Session Motor Imagery (left hand vs. right hand); Within-Session Motor Imagery (all classes); Within-Session Motor Imagery (right hand vs. feet),Medical,MOABB's Within-Session evaluation for the Motor Imagery paradigm.    Evaluation details: [http://moabb.neurotechx.com/docs/generated/moabb.evaluations.WithinSessionEvaluation.html](http://moabb.neurot...
Social Media Mental Health Detection,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Vietnamese Fact Checking,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
ENF (Electric Network Frequency) Classification,0,N/A,N/A,N/A,N/A,N/A,Electric Network Grid classification of ENF signal
Vietnamese Scene Text,0,N/A,N/A,N/A,N/A,Computer Vision; Natural Language Processing,N/A
Grounded Multimodal Named Entity Recognition,0,N/A,Twitter-GMNER,F1,N/A,Computer Vision,N/A
spatial-aware image editing,0,N/A,N/A,N/A,N/A,Computer Vision,Spatial-aware image editing is an image editing technique that recognizes and utilizes the spatial relationships within various regions of an image to achieve more precise and natural editing effects.
Multi Class Classification (Four-level Video Classification),0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Semantic Compression,0,N/A,N/A,N/A,N/A,N/A,N/A
Anatomical Landmark Detection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Speech Interruption Detection,0,N/A,N/A,N/A,N/A,Speech,"""Overlapping speech is a natural and frequently occurring phenomenon in human-human conversations with an underlying  purpose. Speech overlap events may be categorized as competitive and non-competiti..."
Red Wine Quality Prediction,0,N/A,N/A,N/A,N/A,N/A,N/A
Skeleton Rig Prediction,0,N/A,N/A,N/A,N/A,Graphs,Automated methods for producing animation rigs from input 3d models.
Weakly Supervised Referring Expression Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,RES with less percentage of ground truth annotations
Sound Source Localization,0,N/A,^(#$!@#$)(()))******,0..5sec,N/A,Audio,N/A
Vietnamese Hate Speech Detection,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Onset Detection,0,N/A,N/A,N/A,N/A,Music,N/A
Video/Text-to-Audio Generation,0,N/A,N/A,N/A,N/A,Audio,N/A
Structural Health Monitoring,0,N/A,N/A,N/A,N/A,N/A,N/A
3D Shape Reconstruction from Videos,0,N/A,N/A,N/A,DeepFake Detection,Computer Vision; Music; Robots; Medical,N/A
Exposure Correction,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Equilibrium Reaction Energy (ev/atom),0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
3D Video Frame Interpolation,0,N/A,N/A,N/A,Video Style Transfer,Playing Games; Time Series,N/A
Vietnamese Speech Recognition,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Multi-Dialect Vietnamese,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
entity_extraction,0,N/A,N/A,N/A,Pharmacovigilance,Medical,N/A
Image Editing Dection,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Numerical Weather Prediction,0,N/A,N/A,N/A,N/A,Time Series,N/A
Multi-Loss Function Penalizing,0,N/A,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Image Regression,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Action Quality Assessment Report Generation,0,N/A,N/A,N/A,N/A,Computer Vision,"Generate full length action quality assessment reports containing evidence for trustworthy, comprehensive, explainable and unbiased action quality assessment."
Active 3D Reconstruction,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Structured Output Generation,0,N/A,N/A,N/A,N/A,Miscellaneous,Generating Outputs adhering to a given structure.
Battery cycle life prediction,0,N/A,N/A,N/A,N/A,Time Series,"Battery degradation remains a critical challenge in the pursuit of green technologies and sustainable energy solutions. Despite significant research efforts, predicting battery capacity loss accuratel..."
Emotion Detection and Classification,0,N/A,N/A,N/A,N/A,N/A,N/A
Knowledge Base Construction,0,N/A,N/A,N/A,N/A,Knowledge Base,N/A
Relevance Detection,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Temperature Prediction Using Specklegrams,0,N/A,FSS Dataset,Average MAE,N/A,Computer Vision,N/A
Zero-shot skeleton-based action recognition,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Subject-driven Video Generation,0,N/A,N/A,N/A,Human Animation; Audio-Driven Body Animation,Computer Vision,N/A
Reasoning Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Reasoning Video Object Segmentation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
High-Level Synthesis,0,N/A,N/A,N/A,N/A,N/A,N/A
Drawing Pictures,0,N/A,N/A,N/A,Style Transfer,Computer Vision,N/A
Blind Image Deblurring,0,N/A,N/A,N/A,Deblurring,Computer Vision; Computer Code,"**Blind Image Deblurring** is a classical problem in image processing and computer vision, which aims to recover a latent image from a blurred input.      <span class=""description-source"">Source: [Lea..."
Inductive Learning,0,N/A,N/A,N/A,N/A,Methodology,N/A
How To Refund A Wrong Transaction In Phonepe ,0,N/A,N/A,N/A,How to refund a wrong transaction in PhonePe; Community Question Answering,Methodology; Knowledge Base,N/A
Referring Multi-Object Tracking,0,N/A,N/A,N/A,Cross-view Referring Multi-Object Tracking,Computer Vision,N/A
Citation Visualization,0,N/A,N/A,N/A,N/A,Knowledge Base,"The visualization, and possibly further analysis, of scholarly citations."
multimodal interaction,0,N/A,N/A,N/A,N/A,Robots,N/A
Single Image Deblurring,0,N/A,N/A,N/A,N/A,N/A,N/A
Binding Site Prediction,0,N/A,N/A,N/A,Antibody-antigen binding prediction,Medical,N/A
Continued fraction,0,N/A,N/A,N/A,N/A,Miscellaneous; Methodology,N/A
Writer Retrieval,0,N/A,N/A,N/A,N/A,N/A,N/A
Video Forecasting,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Philosophical Reflection,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Philosophical Reflection refers to the ability of AI systems to generate, evolve, or respond to deep conceptual and existential questions, often beyond factual reasoning. This includes reflections on ..."
multidimensional harmonic retrieval,0,N/A,N/A,N/A,N/A,N/A,N/A
Zero-day intrusion detection,0,N/A,N/A,N/A,N/A,Miscellaneous,Zero-day or new attacks detection
Full reference image quality assessment,0,N/A,KADID10K; TID2008; ESPL; DRIQ,SRCC; PLCC,N/A,Computer Vision,The goal is to calculate an objective quality score for a given (potentially) distorted image given its reference (potentially) pristine quality image is available. Training-free metrics are listed.
Drift Detection,0,N/A,N/A,N/A,N/A,N/A,N/A
Nested Term Recognition,0,N/A,N/A,N/A,Nested Term Recognition from Flat Supervision,Natural Language Processing,N/A
Quantitative MRI,0,N/A,N/A,N/A,Diffusion  MRI,Medical,N/A
Protein Stability Prediction,0,N/A,N/A,N/A,N/A,Medical,Predicting relative stabilities typically between mutants of the same protein.
Retrieval-augmented Generation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Forgery Image Detection,0,N/A,N/A,N/A,N/A,Computer Vision,Detecting Forgery Images Generated By LLMs
ObjectGoal Navigation,0,N/A,N/A,N/A,Heuristic Search,Computer Code,ObjectGoal Navigation
3D Mesh Denoising,0,N/A,N/A,N/A,3D Noise Generation,Computer Vision,"3D Mesh Denoising involves removing distortions—referred to as noise—introduced during 3D scanning and reconstruction processes, which cause spatial displacement of mesh vertices. The goal is to elimi..."
ECG signal quality,0,N/A,N/A,N/A,N/A,N/A,N/A
Query focused video summarization,0,N/A,N/A,N/A,N/A,Computer Vision,"Model takes a long video and a query in the following forms(image, textual, video) and based on query model generates video summary relevant to given query."
Referring Audio-Visual Segmentation,0,N/A,N/A,N/A,N/A,Computer Code,N/A
Streaming video understanding,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Pointwise large-scale scene completion,0,N/A,N/A,N/A,N/A,Computer Vision,Obtaining dense point cloud representation of a large-scale scene from a sparse point cloud input.
Spatio-temporal Action Recognition,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Video Focal Modulation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
