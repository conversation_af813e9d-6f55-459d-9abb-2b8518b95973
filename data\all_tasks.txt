""
"1 Image, 2*2 Stitchi"
"1 Image, 2*2 Stitching"
"10-shot image generation"
"16k"
"2-task Classification"
"2D Cyclist Detection"
"2D Human Pose Estimation"
"2D Indoor Localization (Position + Orientation)"
"2D Object Detection"
"2D Panoptic Segmentation"
"2D Pose Estimation"
"2D Semantic Segmentation task 1 (8 classes)"
"2D Semantic Segmentation task 2 (17 classes)"
"2D Semantic Segmentation task 3 (25 classes)"
"2D Semantic Segmentation"
"2D Tiny Object Detection"
"2k"
"3D Absolute Human Pose Estimation"
"3D Action Recognition"
"3D Anomaly Detection and Segmentation"
"3D Anomaly Detection"
"3D Architecture"
"3D Assembly"
"3D Building Mesh Labeling"
"3D Canonical Hand Pose Estimation"
"3D Car Instance Understanding"
"3D Classification"
"3D dense captioning"
"3D Dense Shape Correspondence"
"3D Depth Estimation"
"3D Face Alignment"
"3D Face Animation"
"3D Face Modelling"
"3D Face Reconstruction"
"3D Facial Expression Recognition"
"3D Facial Landmark Localization"
"3D Feature Matching"
"3D Generation"
"3D Geometry Perception"
"3D Geometry Prediction"
"3D geometry"
"3D Hand Pose Estimation"
"3D Human Dynamics"
"3D human pose and shape estimation"
"3D Human Pose Estimation in Limited Data"
"3D Human Pose Estimation"
"3D Human Pose Tracking"
"3D Human Reconstruction"
"3D Human Shape Estimation"
"3D Instance Segmentation"
"3D Interacting Hand Pose Estimation"
"3D Lane Detection"
"3D Medical Imaging Segmentation"
"3D Multi-Object Tracking"
"3D Multi-Person Human Pose Estimation"
"3D Multi-Person Mesh Recovery"
"3D Multi-Person Pose Estimation (absolute)"
"3D Multi-Person Pose Estimation (root-relative)"
"3D Multi-Person Pose Estimation"
"3D Object Captioning"
"3D Object Classification"
"3D Object Detection (RoI)"
"3D Object Detection From Monocular Images"
"3D Object Detection From Stereo Images"
"3D Object Detection"
"3D Object Editing"
"3D Object Recognition"
"3D Object Reconstruction From A Single Image"
"3D Object Reconstruction"
"3D Object Retrieval"
"3D Object Super-Resolution"
"3D Object Tracking"
"3D Open-Vocabulary Instance Segmentation"
"3D Open-Vocabulary Object Detection"
"3D Panoptic Segmentation"
"3D Parameter-Efficient Fine-Tuning for Classification"
"3D Part Segmentation"
"3D Place Recognition"
"3D Point Cloud Classification"
"3D Point Cloud Data Augmentation"
"3D Point Cloud Interpolation"
"3D Point Cloud Linear Classification"
"3D Point Cloud Matching"
"3D Point Cloud Reconstruction"
"3D Point Cloud Reinforcement Learning"
"3D Pose Estimation"
"3D Question Answering (3D-QA)"
"3D Reconstruction"
"3D Room Layouts From A Single RGB Panorama"
"3D scene Editing"
"3D Scene Graph Alignment"
"3d scene graph generation"
"3D Scene Reconstruction"
"3D Semantic Instance Segmentation"
"3D Semantic Occupancy Prediction"
"3D Semantic Scene Completion from a single RGB image"
"3D Semantic Scene Completion"
"3D Semantic Segmentation"
"3D Shape Classification"
"3D Shape Generation"
"3D Shape Modeling"
"3D Shape Recognition"
"3D Shape Reconstruction From A Single 2D Image"
"3D Shape Reconstruction"
"3D Shape Representation"
"3D Shape Retrieval"
"3D Single Object Tracking"
"3D Source-Free Domain Adaptation"
"3D Unsupervised Domain Adaptation"
"3D visual grounding"
"3D Volumetric Reconstruction"
"3D-Aware Image Synthesis"
"3DGS"
"4-ary Relation Extraction"
"4D Panoptic Segmentation"
"4k"
"6D Indoor Localization"
"6D Pose Estimation using RGB"
"6D Pose Estimation using RGBD"
"6D Pose Estimation"
"A-VB Culture"
"A-VB High"
"A-VB Two"
"AbbreviationDetection"
"Abnormal Event Detection In Video"
"Abstract Algebra"
"Abstract generation"
"Abstractive Dialogue Summarization"
"Abstractive Text Summarization"
"Abuse Detection"
"Abusive Language"
"Accented Speech Recognition"
"Accident Anticipation"
"Acne Severity Grading"
"Acoustic echo cancellation"
"Acoustic Modelling"
"Acoustic Scene Classification"
"Acrobot"
"Action Anticipation"
"Action Assessment"
"Action Classification (1-shot)"
"Action Classification (zero-shot)"
"Action Classification"
"Action Detection"
"Action Generation"
"Action Localization"
"Action Parsing"
"Action Quality Assessment"
"Action Recognition In Videos"
"Action Recognition"
"Action Segmentation"
"Action Spotting"
"Action Triplet Detection"
"Action Triplet Recognition"
"Action Understanding"
"Action Unit Detection"
"Actionable Phrase Detection"
"Active Learning"
"Active Object Detection"
"Active Speaker Detection"
"Active Speaker Localization"
"Activeness Detection"
"Activity Detection"
"Activity Prediction"
"Activity Recognition In Videos"
"Activity Recognition"
"Acute Stroke Lesion Segmentation"
"Ad-Hoc Information Retrieval"
"Ad-hoc video search"
"Add - PO"
"Add - PQ"
"ADP Prediction"
"Adroid door-cloned"
"Adroid door-human"
"Adroid hammer-cloned"
"Adroid hammer-human"
"Adroid pen-cloned"
"Adroid pen-human"
"Adroid relocate-cloned"
"Adroid relocate-human"
"Adversarial Attack"
"Adversarial Defense against FGSM Attack"
"Adversarial Defense"
"Adversarial Robustness"
"Adversarial Text"
"Aerial Scene Classification"
"Aerial Video Semantic Segmentation"
"Aesthetic Image Captioning"
"Aesthetics Quality Assessment"
"Affordance Detection"
"Affordance Recognition"
"Age And Gender Classification"
"Age and Gender Estimation"
"Age Classification"
"Age Estimation"
"Age-Invariant Face Recognition"
"Age/Bias-conflicting"
"Age/Unbiased"
"Aggression Identification"
"AI Agent"
"AI and Safety"
"Air Pollution Prediction"
"Algorithmic Trading"
"All-day Semantic Segmentation"
"All"
"AllNLI Triplet"
"Alzheimer's Disease Detection"
"Amodal Instance Segmentation"
"Amodal Panoptic Segmentation"
"Amodal Tracking"
"AMR Graph Similarity"
"AMR Parsing"
"AMR-to-Text Generation"
"Analog Video Restoration"
"Analogical Similarity"
"Analytic Entailment"
"Anatomy"
"Ancestor-descendant prediction"
"Anchor link prediction"
"Ancient Text Restoration"
"Android Malware Detection"
"Animal Action Recognition"
"Animal Pose Estimation"
"Annotated Code Search"
"Anomaly Classification"
"Anomaly Detection in Edge Streams"
"Anomaly Detection In Surveillance Videos"
"Anomaly Detection"
"Anomaly Forecasting"
"Anomaly Instance Segmentation"
"Anomaly Localization"
"Anomaly Segmentation"
"Anomaly Severity Classification (Anomaly vs. Defect)"
"Answer Generation"
"Answer Selection"
"answerability prediction"
"Antibody-antigen binding prediction"
"Anxiety Detection"
"Arabic Sentiment Analysis"
"Arabic Speech Recognition"
"Arabic Text Diacritization"
"Argument Mining"
"Argument Pair Extraction (APE)"
"Argument Retrieval"
"Arithmetic Reasoning"
"Arousal Estimation"
"Arrhythmia Detection"
"Art Analysis"
"Artery/Veins Retinal Vessel Segmentation"
"Articulated Object modelling"
"Artifact Detection"
"Artist classification"
"ArzEn Code-switched Translation to ara"
"ArzEn Code-switched Translation to eng"
"ArzEn Speech Recognition"
"Aspect Category Detection"
"Aspect Category Polarity"
"Aspect Category Sentiment Analysis"
"Aspect Extraction"
"Aspect Sentiment Triplet Extraction"
"Aspect Term Extraction and Sentiment Classification"
"Aspect-Based Sentiment Analysis (ABSA)"
"Aspect-Based Sentiment Analysis"
"Aspect-Category-Opinion-Sentiment Quadruple Extraction"
"Aspect-oriented  Opinion Extraction"
"Asthmatic Lung Sound Classification"
"Astronomy"
"Asynchronous Group Communication"
"Atari Games 100k"
"Atari Games"
"Atom3D benchmark"
"Atomic action recognition"
"Atomic Forces"
"Atomic number classification"
"Atrial Fibrillation Detection"
"Attention Score Prediction"
"Attractiveness Estimation"
"Attribute Extraction"
"Attribute Mining"
"Attribute Value Extraction"
"Attribute"
"Audio captioning"
"Audio Classification"
"Audio Deepfake Detection"
"Audio Denoising"
"Audio Effects Modeling"
"Audio Emotion Recognition"
"Audio Fingerprint"
"Audio Generation"
"Audio Multiple Target Classification"
"Audio Quality Assessment"
"Audio Question Answering"
"Audio Signal Processing"
"Audio Source Separation"
"Audio Super-Resolution"
"Audio Synthesis"
"Audio Tagging"
"Audio to Text Retrieval"
"Audio-Video Question Answering (AVQA)"
"Audio-Visual Active Speaker Detection"
"audio-visual event localization"
"audio-visual learning"
"Audio-Visual Question Answering (AVQA)"
"AUDIO-VISUAL QUESTION ANSWERING (MUSIC-AVQA-v2.0)"
"Audio-visual Question Answering"
"Audio-Visual Speech Recognition"
"Audio-Visual Synchronization"
"Audio-Visual Video Captioning"
"Audio/Video to Text Retrieval"
"Author Attribution"
"Author Profiling"
"Authority Involvement Detection"
"Authorship Attribution"
"Authorship Verification"
"Auto Debugging"
"Automated Essay Scoring"
"Automated Theorem Proving"
"Automatic Cell Counting"
"Automatic Lyrics Transcription"
"Automatic Modulation Recognition"
"Automatic Phoneme Recognition"
"Automatic Post-Editing"
"automatic short answer grading"
"Automatic Sleep Stage Classification"
"Automatic Speech Recognition (ASR)"
"Automatic Speech Recognition"
"automatic-speech-translation"
"AutoML"
"Autonomous Driving"
"Autonomous Navigation"
"Autonomous Vehicles"
"Avg. sequence length"
"B-Rep face segmentation"
"backdoor defense"
"Band Gap"
"Bandwidth Extension"
"Bangla Spelling Error Correction"
"Bangla Text Detection"
"Bathymetry prediction"
"Bayesian Inference"
"Bayesian Optimization"
"BBBC021 NSC Accuracy"
"Beam Prediction"
"Beat Tracking"
"Behavioral Malware Classification"
"Behavioral Malware Detection"
"Behavioural cloning"
"Benchmarking"
"BEV Segmentation"
"Bias Detection"
"Bidirectional Relationship Classification"
"BIG-bench Machine Learning"
"Binarization"
"Binary Classification"
"Binary Condescension Detection"
"Binary Quantification"
"Binary Relation Extraction"
"Binary text classification"
"Biomedical Information Retrieval"
"Bird Audio Detection"
"Bird Species Classification With Audio-Visual Data"
"Bird's-Eye View Semantic Segmentation"
"Birds Eye View Object Detection"
"Blackout Poetry Generation"
"Blended-target Domain Adaptation"
"Blind Face Restoration"
"Blind Image Quality Assessment"
"Blind Super-Resolution"
"Blocking"
"Blood Cell Count"
"Blood Cell Detection"
"Blood Detection"
"Blood pressure estimation"
"Board Games"
"Body Detection"
"Bone Suppression From Dual Energy Chest X-Rays"
"Boundary Captioning"
"Boundary Detection"
"Boundary Grounding"
"Box-supervised Instance Segmentation"
"Brain Decoding"
"Brain Image Segmentation"
"Brain Lesion Segmentation From Mri"
"Brain Segmentation"
"Brain Tumor Classification"
"Brain Tumor Segmentation"
"Brain Visual Reconstruction from fMRI"
"Brain Visual Reconstruction"
"Breast Cancer Detection"
"Breast Cancer Histology Image Classification (20% labels)"
"Breast Cancer Histology Image Classification"
"Breast Tumour Classification"
"Bridging Anaphora Resolution"
"Bug fixing"
"Building change detection for remote sensing images"
"Building Damage Assessment"
"Building Flood Damage Assessment"
"Burst Image Reconstruction"
"Burst Image Super-Resolution"
"Business Ethics"
"Business Taxonomy Construction"
"CAD Reconstruction"
"Cadenza 1 - Task 1 - Headphone"
"Cadenza 1 - Task 2 - In Car"
"Caller Detection"
"Calving Front Delineation In Synthetic Aperture Radar Imagery With Fixed Training Amount"
"Calving Front Delineation In Synthetic Aperture Radar Imagery"
"Camera Auto-Calibration"
"Camera Calibration"
"Camera Localization"
"Camera Pose Estimation"
"Camera Relocalization"
"Camera shot boundary detection"
"Camera shot segmentation"
"Camouflaged Object Segmentation with a Single Task-generic Prompt"
"Camouflaged Object Segmentation"
"Cancer Classification"
"Cancer type classification"
"Cancer-no cancer per breast classification"
"Cancer-no cancer per image classification"
"candy animation generation"
"Capacity Estimation"
"Caption Generation"
"Car Pose Estimation"
"Card Games"
"Cardiac Segmentation"
"Caricature"
"CARLA Leaderboard 2.0"
"CARLA longest6"
"CARLA MAP Leaderboard"
"Catalog Extraction"
"Causal Discovery in Video Reasoning"
"Causal Discovery"
"Causal Emotion Entailment"
"Causal Identification"
"Causal Inference"
"Causal Judgment"
"Caustics Segmentation"
"CCG Supertagging"
"Cell Detection"
"Cell Entity Annotation"
"Cell Segmentation"
"Cell Tracking"
"Cervical Nucleus Detection"
"Change detection for remote sensing images"
"Change Detection"
"Change Point Detection"
"Chart Question Answering"
"Chart Understanding"
"Chat-based Image Retrieval"
"Chatbot"
"Chemical Entity Recognition"
"Chemical Indexing"
"Chemical Process"
"Chemical Reaction Prediction"
"Chinese Document Classification"
"Chinese Landscape Painting Generation"
"Chinese Named Entity Recognition"
"Chinese Reading Comprehension"
"Chinese Semantic Role Labeling"
"Chinese Sentence Pair Classification"
"Chinese Sentiment Analysis"
"Chinese Word Segmentation"
"Chord Recognition"
"Chunking"
"Circulatory Failure"
"Citation Intent Classification"
"Citation Prediction"
"Citation Recommendation"
"Citation worthiness"
"Claim Extraction with Stance Classification (CESC)"
"Claim Verification"
"Claim-Evidence Pair Extraction (CEPE)"
"Class Incremental Learning"
"Class-agnostic Object Detection"
"class-incremental learning"
"Classification Of Breast Cancer Histology Images"
"Classification Of Hyperspectral Images"
"Classification of toxic, engaging, fact-claiming comments"
"Classification with Binary Neural Network"
"Classification with Binary Weight Network"
"Classification"
"Classifier calibration"
"Classify 3D Point Clouds"
"Classify murmurs"
"Clean-label Backdoor Attack (0.024%)"
"Clean-label Backdoor Attack (0.05%)"
"Click-Through Rate Prediction"
"Clinical Assertion Status Detection"
"Clinical Concept Extraction"
"Clinical Knowledge"
"Clinical Note Phenotyping"
"Clinical Section Identification"
"Clique Prediction"
"Clone Detection"
"Clothes Landmark Detection"
"Clothing Attribute Recognition"
"Cloud Detection"
"Cloud Removal"
"Cloze (multi-choices) (Few-Shot)"
"Cloze (multi-choices) (One-Shot)"
"Cloze (multi-choices) (Zero-Shot)"
"Cloze Test"
"Clustering Algorithms Evaluation"
"Clustering Ensemble"
"Clustering"
"Co-Salient Object Detection"
"Code Classification"
"Code Comment Generation"
"Code Completion"
"Code Documentation Generation"
"Code Generation"
"Code Repair"
"Code Search"
"Code Summarization"
"Code Translation"
"CodeSearchNet - Java"
"Coherence Evaluation"
"Col BERTTriplet"
"Cold-Start Anomaly Detection"
"Collaborative Filtering"
"College Biology"
"College Chemistry"
"College Computer Science"
"College Mathematics"
"College Medicine"
"College Physics"
"Collision Avoidance"
"Colon Cancer Detection In Confocal Laser Microscopy Images"
"Color Constancy"
"Color Image Denoising"
"Color Mismatch Correction"
"Colorectal Gland Segmentation:"
"Colorectal Polyps Characterization"
"Colorization"
"Column Type Annotation"
"Columns Property Annotation"
"Combinatorial Optimization"
"Common Sense Reasoning (Few-Shot)"
"Common Sense Reasoning (One-Shot)"
"Common Sense Reasoning (Zero-Shot)"
"Common Sense Reasoning"
"Commonsense Causal Reasoning"
"Commonsense Knowledge Base Construction"
"Community Detection"
"Community Question Answering"
"Complex Query Answering"
"Complex Word Identification"
"Complimentary Image Retrieval"
"Component Classification"
"Composed Image Retrieval (CoIR)"
"Composed Video Retrieval (CoVR)"
"Composite action recognition"
"Compositional Generalization (AVG)"
"Compositional Zero-Shot Learning"
"Compressive Sensing"
"Computational Phenotyping"
"Computed Tomography (CT)"
"Computer Security"
"Computer Vision Transduction"
"Concept-based Classification"
"Concept-To-Text Generation"
"Conceptual Physics"
"Conditional Image Generation"
"Conditional Text Generation"
"Conditional Text-to-Image Synthesis"
"confidence interval for traffic prediction"
"Connective Detection"
"Connectivity Estimation"
"Constituency Grammar Induction"
"Constituency Parsing"
"Contact Detection"
"Contact-rich Manipulation"
"Content-Based Image Retrieval"
"Context Query Reformulation"
"Context-specific Spam Detection"
"ContextNER"
"Contextual Embedding for Source Code"
"Continual Learning"
"Continual Pretraining"
"Continual Semantic Segmentation"
"Continuous Affect Estimation"
"Continuous Control (100k environment steps)"
"Continuous Control (500k environment steps)"
"Continuous Control"
"Continuously Indexed Domain Adaptation"
"Contour Detection"
"Contrastive Learning"
"Controllable Grasp Generation"
"Conversation Disentanglement"
"Conversational Question Answering"
"Conversational Recommendation"
"Conversational Response Generation"
"Conversational Response Selection"
"Conversational Search"
"Conversational Sentiment Quadruple Extraction"
"Conversational Web Navigation"
"Copy Detection"
"Core Promoter Detection"
"Core Psychological Reasoning"
"Core set discovery"
"Coreference Resolution"
"coreference-resolution"
"Coronary Artery Segmentation"
"Correlated Time Series Forecasting"
"Counterfactual Detection"
"Counterfactual Explanation"
"Counterfactual Inference"
"Counterfactual Planning"
"Counterfactual Reasoning"
"Covid Variant Prediction"
"COVID-19 Diagnosis"
"COVID-19 Modelling"
"COVID-19 Tracking"
"Crack Segmentation"
"Crash Blossom"
"Crashworthiness Design"
"Crass AI"
"Credit score"
"Crime Prediction"
"Croatian Text Diacritization"
"Crop Classification"
"Crop Type Mapping"
"Crop Yield Prediction"
"Cross Document Coreference Resolution"
"Cross-device ASR"
"Cross-Document Language Modeling"
"Cross-domain 3D Human Pose Estimation"
"Cross-Domain Document Classification"
"cross-domain few-shot learning"
"Cross-Domain Few-Shot Object Detection"
"Cross-Domain Few-Shot"
"Cross-Domain Iris Presentation Attack Detection"
"Cross-Domain Named Entity Recognition"
"Cross-environment ASR"
"Cross-Language Text Summarization"
"Cross-Lingual Abstractive Summarization"
"Cross-Lingual ASR"
"Cross-Lingual Bitext Mining"
"Cross-Lingual Document Classification"
"Cross-Lingual Entity Linking"
"Cross-Lingual Information Retrieval"
"Cross-Lingual Natural Language Inference"
"Cross-Lingual NER"
"Cross-Lingual Paraphrase Identification"
"Cross-Lingual POS Tagging"
"Cross-Lingual Question Answering"
"Cross-Lingual Semantic Textual Similarity"
"Cross-Lingual Sentiment Classification"
"Cross-Lingual Transfer"
"Cross-lingual zero-shot dependency parsing"
"Cross-Modal  Person Re-Identification"
"cross-modal alignment"
"Cross-Modal Information Retrieval"
"Cross-Modal Person Re-Identification"
"Cross-modal place recognition"
"Cross-modal retrieval with noisy correspondence"
"Cross-Modal Retrieval"
"Cross-Modality Person Re-identification"
"Cross-Part Crowd Counting"
"Cross-View Geo-Localisation"
"Cross-View Image-to-Image Translation"
"Cross-view Referring Multi-Object Tracking"
"Crowd Counting"
"Crowdsourced Text Aggregation"
"Crystal system classification"
"CSV dialect detection"
"CT Reconstruction"
"Cubic splines Image Registration"
"Cultural Vocal Bursts Intensity Prediction"
"Curved Text Detection"
"Cyber Attack Detection"
"CYCLoPs Accuracy"
"Czech Text Diacritization"
"D4RL"
"Damaged Building Detection"
"Dark Humor Detection"
"Data Augmentation"
"Data Free Quantization"
"Data Integration"
"Data Sources Detection"
"Data Summarization"
"Data Visualization"
"Data-free Knowledge Distillation"
"Data-to-Text Generation"
"Dataset Distillation - 1IPC"
"Dataset Size Recovery"
"De novo molecule generation from MS/MS spectrum (bonus chemical formulae)"
"De novo molecule generation from MS/MS spectrum"
"de-en"
"De-identification"
"Deblurring"
"Deception Detection"
"Decipherment"
"Decision Making Under Uncertainty"
"Decision Making"
"Deep Attention"
"Deep Clustering"
"Deep Learning"
"DeepFake Detection"
"Defect Detection"
"Definition Extraction"
"Definition Modelling"
"Defocus Blur Detection"
"Defocus Estimation"
"Deformable Object Manipulation"
"Demand Forecasting"
"Demosaicking"
"Denoising"
"Dense Captioning"
"Dense contact estimation"
"Dense Object Detection"
"Dense Pixel Correspondence Estimation"
"Dense Video Captioning"
"Density Estimation"
"Dependency Parsing"
"Depression Detection"
"Depth Aleatoric Uncertainty Estimation"
"Depth And Camera Motion"
"Depth Anomaly Detection and Segmentation"
"Depth Completion"
"Depth Estimation"
"Depth Image Estimation"
"Depth Map Super-Resolution"
"Depth Prediction"
"Depth-aware Video Panoptic Segmentation"
"Described Object Detection"
"Description-guided molecule generation"
"Descriptive"
"Detecting Climate/Environmental Topics"
"Detecting Image Manipulation"
"Detection of potentially void clauses"
"Diabetes Prediction"
"Diabetic Retinopathy Detection"
"Diabetic Retinopathy Grading"
"Diagnostic"
"Dial Meter Reading"
"Dialect Identification"
"Dialog Act Classification"
"Dialog Relation Extraction"
"Dialogue Act Classification"
"Dialogue Evaluation"
"Dialogue Generation"
"Dialogue Management"
"Dialogue Rewriting"
"Dialogue Safety Prediction"
"Dialogue State Tracking"
"dialogue summary"
"Dialogue Understanding"
"Dichotomous Image Segmentation"
"Dictionary Learning"
"Diffeomorphic Medical Image Registration"
"Diffusion Personalization Tuning Free"
"Dimensionality Reduction"
"Direction of Arrival Estimation"
"Directional Hearing"
"Disaster Response"
"Discourse Marker Prediction"
"Discourse Parsing"
"Discourse Segmentation"
"Disease Prediction"
"Disentanglement"
"Disguised Face Verification"
"Disparity Estimation"
"Distance regression"
"Distant Speech Recognition"
"Distractor Generation"
"Distributed Computing"
"Document AI"
"Document Classification"
"Document Dating"
"Document Embedding"
"Document Enhancement"
"Document Image Classification"
"Document Image Skew Estimation"
"Document Layout Analysis"
"Document Level Machine Translation"
"Document Ranking"
"Document Shadow Removal"
"Document Summarization"
"Document Text Classification"
"Document Translation"
"document understanding"
"Document-level Closed Information Extraction"
"Document-level Event Extraction"
"Document-level RE with incomplete labeling"
"Document-level Relation Extraction"
"Domain 1-1"
"Domain 11-1"
"Domain 11-5"
"Domain Adaptation"
"Domain Adaptive Person Re-Identification"
"domain classification"
"Domain Generalization"
"Domain-IL Continual Learning"
"Dominance Estimation"
"Downbeat Tracking"
"Drivable Area Detection"
"Driver Attention Monitoring"
"Driver Identification"
"Drone navigation"
"Drone Pose Estimation"
"drone-based object tracking"
"Drone-view target localization"
"Drug ATC Classification"
"Drug Discovery"
"Drug Response Prediction"
"Drug–drug Interaction Extraction"
"DrugProt"
"Drum Transcription in Music (DTM)"
"Drum Transcription"
"Duplicate-Question Retrieval"
"Dynamic Facial Expression Recognition"
"Dynamic Link Prediction"
"Dynamic Point Removal"
"Dynamic Reconstruction"
"Early  Classification"
"Early Action Prediction"
"Earth Surface Forecasting"
"ECG Classification"
"ECG Denoising"
"ECG Digitization"
"ECG Patient Identification (gallery-probe)"
"ECG Patient Identification"
"ECG QRS Detection"
"ECG Wave Delineation"
"Econometrics"
"Edge Classification"
"Edge Detection"
"EditCompletion"
"EEG 4 classes"
"EEG Artifact Removal"
"Eeg Decoding"
"EEG Emotion Recognition"
"EEG Left/Right hand"
"EEG Signal Classification"
"EEG"
"Efficient Exploration"
"Egocentric Activity Recognition"
"Egocentric Pose Estimation"
"Elasticity3D"
"Electrical Engineering"
"Electrocardiography (ECG)"
"Electroencephalogram (EEG)"
"Electromyography (EMG)"
"Electron Microscopy Image Segmentation"
"Elementary Mathematics"
"EM showers clusterization"
"Email Thread Summarization"
"Embeddings Evaluation"
"Embodied Question Answering"
"EMG Gesture Recognition"
"Emotion Cause Extraction"
"Emotion Classification"
"Emotion Interpretation"
"Emotion Recognition in Context"
"Emotion Recognition in Conversation"
"Emotion Recognition"
"Emotion-Cause Pair Extraction"
"Emotional Dialogue Acts"
"Emotional Intelligence"
"Emotional Speech Synthesis"
"Empathetic Response Generation"
"Empirical Judgments"
"End-To-End Dialogue Modelling"
"End-to-End RST Parsing"
"ENF (Electric Network Frequency) Detection"
"ENF (Electric Network Frequency) Extraction from Video"
"ENF (Electric Network Frequency) Extraction"
"English Conversational Speech Recognition"
"English Proverbs"
"English-Ukrainian Translation"
"Ensemble Learning"
"Entailed Polarity"
"Entity Alignment"
"Entity Cross-Document Coreference Resolution"
"Entity Disambiguation"
"Entity Embeddings"
"Entity Extraction using GAN"
"Entity Linking"
"Entity Resolution"
"Entity Retrieval"
"Entity Typing"
"Enumerative Search"
"Environment Sound Classification"
"Environmental Sound Classification"
"Epidemiology"
"Epigenetic Marks Prediction"
"Epilepsy Prediction"
"Epistemic Reasoning"
"Equilibrium traffic assignment"
"Error Understanding"
"erson Re-Identification"
"es-en"
"Ethics"
"Evaluating Information Essentiality"
"Event Argument Extraction"
"Event Causality Identification"
"Event Coreference Resolution"
"Event Cross-Document Coreference Resolution"
"Event data classification"
"Event Detection"
"Event Expansion"
"Event Extraction"
"Event Relation Extraction"
"Event Segmentation"
"Event-based Motion Estimation"
"Event-based N-ary Relaiton Extraction"
"Event-based Object Segmentation"
"Event-based Optical Flow"
"Event-Based Video Reconstruction"
"Event-based vision"
"Event-Driven Trading"
"Evidence Selection"
"Exemplar-Free Counting"
"Explainable Artificial Intelligence (XAI)"
"Explainable artificial intelligence"
"Explainable Models"
"Explainable Recommendation"
"Explanation Fidelity Evaluation"
"Explanation Generation"
"Explanatory Visual Question Answering"
"Extended Summarization"
"Extracting Buildings In Remote Sensing Images"
"Extracting COVID-19 Events from Twitter"
"Extractive Document Summarization"
"Extractive Question-Answering"
"Extractive Summarization"
"Extractive Text Summarization"
"Extreme Multi-Label Classification"
"Extreme Summarization"
"eXtreme-Video-Frame-Interpolation"
"Eyeblink detection"
"Face Age Editing"
"Face Alignment"
"Face Anonymization"
"Face Anti-Spoofing"
"Face Clustering"
"Face Detection"
"Face Generation"
"Face Hallucination"
"Face Identification"
"Face Image Quality Assessment"
"Face Image Retrieval"
"Face Model"
"Face Morphing Attack Detection"
"Face Parsing"
"Face Presentation Attack Detection"
"Face Quality Assessement"
"Face Recognition (Closed-Set)"
"Face Recognition"
"Face Reconstruction"
"Face Reenactment"
"Face Sketch Synthesis"
"Face Swapping"
"Face Transfer"
"Face Verification"
"Facial Action Unit Detection"
"Facial Attribute Classification"
"Facial Emotion Recognition"
"Facial expression generation"
"Facial Expression Recognition (FER)"
"Facial Expression Recognition"
"Facial Expression Translation"
"Facial Inpainting"
"Facial Landmark Detection"
"Facial Makeup Transfer"
"Fact Checking"
"Fact Selection"
"Fact Verification"
"Fact-based Text Editing"
"Factual Inconsistency Detection in Chart Captioning"
"Factual probe"
"Factual Visual Question Answering"
"Fairness"
"Fake Image Attribution"
"Fake Image Detection"
"Fake News Detection"
"Fake Song Detection"
"fake voice detection"
"Fantasy Reasoning"
"Fashion Compatibility Learning"
"Fashion Synthesis"
"Fashion Understanding"
"Fast Vehicle Detection"
"Fault Detection"
"Fault Diagnosis"
"Fault localization"
"Feature Engineering"
"Feature Importance"
"feature selection"
"Feature Upsampling"
"Federated Learning (Video Super-Resolution)"
"Federated Learning"
"Feedback Vertex Set (FVS)"
"FEVER (2-way)"
"FEVER (3-way)"
"Few Shot Action Recognition"
"Few Shot Open Set Object Detection"
"Few Shot Temporal Action Localization"
"Few-Shot 3D Point Cloud Classification"
"Few-shot 3D Point Cloud Semantic Segmentation"
"Few-shot Age Estimation"
"Few-Shot Audio Classification"
"Few-Shot Camera-Adaptive Color Constancy"
"Few-Shot Class-Incremental Learning"
"Few-Shot Image Classification"
"Few-Shot Instance Classification"
"Few-shot Instance Segmentation"
"Few-Shot Learning - 4 shots"
"Few-Shot Learning"
"Few-shot NER"
"Few-Shot NLI"
"Few-shot Object Counting and Detection"
"Few-Shot Object Detection"
"Few-Shot Point Cloud Classification"
"Few-Shot Relation Classification"
"Few-Shot Semantic Segmentation"
"Few-Shot Skeleton-Based Action Recognition"
"Few-Shot Text Classification"
"Few-Shot Transfer Learning for Saliency Prediction"
"Few-shot Video Question Answering"
"FG-1-PG-1"
"Field Boundary Delineation"
"Figure Of Speech Detection"
"Fill Mask"
"Film Removal"
"Film Simulation"
"Financial Relation Extraction"
"Fine-Grained Action Detection"
"Fine-grained Action Recognition"
"Fine-Grained Facial Editing"
"Fine-Grained Image Classification"
"Fine-Grained Image Inpainting"
"Fine-Grained Image Recognition"
"Fine-Grained Opinion Analysis"
"Fine-Grained Urban Flow Inference"
"Fine-Grained Vehicle Classification"
"Fine-Grained Visual Categorization"
"Fine-Grained Visual Recognition"
"Fingertip Detection"
"Fire Detection"
"Fish Detection"
"Fixed Few Shot Prompting Danger Assessment"
"Fixed Few Shot Prompting"
"Flare Removal"
"Flood extent forecasting"
"Flood Inundation Mapping"
"Flowchart Grounded Dialog Response Generation"
"FLUE"
"FocusNews (test)"
"Foggy Scene Segmentation"
"Font Generation"
"Font Recognition"
"Font Style Transfer"
"Food Recognition"
"Food recommendation"
"Foot keypoint detection"
"Foreground Segmentation"
"Form"
"Formal Logic"
"Formality Style Transfer"
"Formation Energy"
"Fovea Detection"
"fr-en"
"Fracture detection"
"Fraud Detection"
"French Text Diacritization"
"Fruit-type + Maturity-state Prediction (Multi-label Classification)"
"FS-MEVQA"
"Future Hand Prediction"
"Future prediction"
"Gait Identification"
"Gait Recognition in the Wild"
"Gait Recognition"
"Galaxy emergent property recreation"
"Gallbladder Cancer Detection"
"Game of Doom"
"Game of Football"
"Game of Go"
"Game of Hanabi"
"Game of Mafia"
"Game of Poker"
"Game of Sudoku"
"Game State Reconstruction"
"Garment Reconstruction"
"Gaussian Processes"
"Gaze Estimation"
"Gaze Prediction"
"Gaze Target Estimation"
"Gender Bias Detection"
"Gender Classification"
"Gender Prediction"
"Gene Interaction Prediction"
"General Action Video Anomaly Detection"
"General Classification"
"General Knowledge"
"General Reinforcement Learning"
"Generalizable Novel View Synthesis"
"Generalizable Person Re-identification"
"Generalized Few-Shot Classification"
"Generalized Few-Shot Learning"
"Generalized Referring Expression Comprehension"
"Generalized Referring Expression Segmentation"
"Generalized Zero Shot skeletal action recognition"
"Generalized Zero-Shot Learning"
"Generalized Zero-Shot Object Detection"
"Generating 3D Point Clouds"
"Generative 3D Object Classification"
"Generative Question Answering"
"Generative Visual Question Answering"
"Generic Event Boundary Detection"
"Genome Understanding"
"Genre classification"
"geo-localization"
"Geometric Matching"
"Geometrical View"
"Geophysics"
"GermEval2024 Shared Task 1 Subtask 1"
"GermEval2024 Shared Task 1 Subtask 2"
"Gesture Generation"
"Gesture Recognition"
"Gesture Synchronization"
"GLinear"
"Global 3D Human Pose Estimation"
"Global Facts"
"Gloss-free Sign Language Translation"
"Goal-Oriented Dialog"
"Goal-Oriented Dialogue Systems"
"Goop2D"
"GPR"
"Grammatical Error Correction"
"Grammatical Error Detection"
"Graph Anomaly Detection"
"Graph Attention"
"Graph Classification"
"Graph Clustering"
"graph construction"
"Graph Embedding"
"Graph Generation"
"Graph Learning"
"Graph Matching"
"Graph Mining"
"graph partitioning"
"Graph Property Prediction"
"Graph Question Answering"
"Graph Ranking"
"Graph Reconstruction"
"Graph Regression"
"Graph Representation Learning"
"Graph Sampling"
"Graph Similarity"
"Graph structure learning"
"Graph-to-Sequence"
"Graphon Estimation"
"Grasp Contact Prediction"
"Grasp Generation"
"Grayscale Image Denoising"
"Grayscale Video Denoising"
"GRE Reading Comprehension"
"Grounded language learning"
"Grounded Situation Recognition"
"Grounded Video Question Answering"
"Group Activity Recognition"
"Group Anomaly Detection"
"GSM8K"
"GUI Element Detection"
"Gunshot Detection"
"Gym halfcheetah-expert"
"Gym halfcheetah-full-replay"
"Gym halfcheetah-medium-expert"
"Gym halfcheetah-medium-replay"
"Gym halfcheetah-medium"
"Gym halfcheetah-random"
"GZSL Video Classification"
"HairColor/Bias-conflicting"
"HairColor/Unbiased"
"Hallucination Evaluation"
"Hallucination"
"Hand Detection"
"Hand Gesture Recognition"
"Hand Joint Reconstruction"
"Hand Pose Estimation"
"Hand Segmentation"
"Hand-Gesture Recognition"
"Hand-Object Interaction Detection"
"hand-object pose"
"Handwriting generation"
"Handwriting Recognition"
"Handwriting Verification"
"Handwritten Chinese Text Recognition"
"Handwritten Digit Image Synthesis"
"Handwritten Digit Recognition"
"Handwritten Document Recognition"
"Handwritten Line Segmentation"
"Handwritten Mathmatical Expression Recognition"
"Handwritten Text Recognition"
"Handwritten Word Generation"
"Handwritten Word Segmentation"
"Hard-label Attack"
"Hardware Aware Neural Architecture Search"
"Harvesting Date Prediction"
"Hate Span Identification"
"Hate Speech Detection"
"Hateful Meme Classification"
"HD semantic map learning"
"HDR Reconstruction"
"Head Detection"
"Head Pose Estimation"
"Headline Generation"
"Heart rate estimation"
"Heart Rate Variability"
"Heart Segmentation"
"Heartbeat Classification"
"HeavyMakeup/Bias-conflicting"
"HeavyMakeup/Unbiased"
"Heterogeneous Face Recognition"
"Heterogeneous Node Classification"
"Heterogeneous Treatment Effect Estimation"
"Hidden Aspect Detection"
"Hierarchical Multi-label Classification"
"Hierarchical Reinforcement Learning"
"Hierarchical Text Segmentation"
"Hierarchical Text-Image Matching"
"High School Biology"
"High School Chemistry"
"High School Computer Science"
"High School European History"
"High School Geography"
"High School Government and Politics"
"High School Macroeconomics"
"High School Mathematics"
"High School Microeconomics"
"High School Physics"
"High School Psychology"
"High School Statistics"
"High School US History"
"High School World History"
"Highlight Detection"
"Hint Generation"
"Histopathological Image Classification"
"Histopathological Segmentation"
"Historical Color Image Dating"
"Holdout Set"
"Home Activity Monitoring"
"Homography Estimation"
"Hope Speech Detection for English"
"Hope Speech Detection for Malayalam"
"Hope Speech Detection for Tamil"
"Hope Speech Detection"
"Horizon Line Estimation"
"HTR"
"Human action generation"
"Human Activity Recognition"
"Human Aging"
"Human Behavior Forecasting"
"Human Body Volume Estimation"
"Human Detection of Deepfakes"
"Human Detection"
"Human Dynamics"
"Human fMRI response prediction"
"Human Instance Segmentation"
"Human Interaction Recognition"
"Human Judgment Correlation"
"Human Mesh Recovery"
"Human motion prediction"
"Human Organs Senses Multiple Choice"
"Human Parsing"
"Human Part Segmentation"
"Human Pose Forecasting"
"Human Sexuality"
"Human-Domain Subject-to-Video"
"Human-Human Interaction Recognition"
"Human-Object Interaction Anticipation"
"Human-Object Interaction Concept Discovery"
"Human-Object Interaction Detection"
"Human-Object Interaction Generation"
"Human-Object-interaction motion tracking"
"HumanEval"
"Humanitarian"
"Humor Detection"
"Hungarian Text Diacritization"
"Hurricane Forecasting"
"Hurtful Sentence Completion"
"Hybrid Machine Learning"
"Hyper-Relational Extraction"
"Hypergraph Contrastive Learning"
"Hypergraph-based N-ary Relaiton Extraction"
"Hypernym Discovery"
"Hyperparameter Optimization"
"Hyperspectral image analysis"
"Hyperspectral Image Classification"
"Hyperspectral Image Denoising"
"Hyperspectral Image Inpainting"
"Hyperspectral Image Segmentation"
"Hyperspectral Image Super-Resolution"
"Hyperspectral Image-Based Fruit Ripeness Prediction"
"Hyperspectral Semantic Segmentation"
"Hyperspectral Unmixing"
"ICU Mortality"
"Identify Odd Metapor"
"IFC Entity Classification"
"Iloko Speech Recognition"
"Image Attribution"
"Image Augmentation"
"Image Captioning"
"Image Categorization"
"Image Classification with Differential Privacy"
"Image Classification with Human Noise"
"Image Classification with Label Noise"
"Image Classification"
"Image Clustering"
"Image Colorization"
"Image Comprehension"
"Image Compressed Sensing"
"Image Compression"
"Image Cropping"
"Image Deblurring"
"Image Deconvolution"
"Image Defocus Deblurring"
"Image Dehazing"
"Image Denoising"
"Image Editing"
"Image Enhancement"
"Image Forensics"
"Image Forgery Detection"
"Image Generation from Scene Graphs"
"Image Generation"
"Image Harmonization"
"Image Inpainting"
"Image Manipulation Detection"
"Image Manipulation Localization"
"Image Manipulation"
"Image Matching"
"Image Matting"
"Image Outpainting"
"Image Paragraph Captioning"
"Image Quality Assessment"
"Image Reconstruction"
"Image Registration"
"Image Relighting"
"Image Rescaling"
"Image Restoration"
"Image Retrieval with Multi-Modal Query"
"Image Retrieval"
"Image Segmentation"
"Image Shadow Removal"
"Image Similarity Detection"
"Image Similarity Search"
"image smoothing"
"Image Stitching"
"Image Stylization"
"Image Super-Resolution"
"Image to 3D"
"Image to Point Cloud Registration"
"Image to sketch recognition"
"Image to text"
"Image to Video Generation"
"Image-based Automatic Meter Reading"
"Image-Based Localization"
"Image-based Recommendation Explainability"
"Image-guided Story Ending Generation"
"Image-level Supervised Instance Segmentation"
"image-sentence alignment"
"Image-text Classification"
"Image-text matching"
"Image-text Retrieval"
"Image-to-Image Regression"
"Image-to-Image Translation"
"Image-to-Text Retrieval"
"Image-Variation"
"Image/Document Clustering"
"imbalanced classification"
"Imitation Learning"
"Impacted Location Detection"
"Implicatures"
"Implicit Discourse Relation Classification"
"Implicit Relations"
"Imputation"
"Incremental Constrained Clustering"
"Incremental Learning"
"Indoor Localization (3-DoF Pose: X, Y, Yaw)"
"Indoor Localization (6 DoF Pose)"
"Indoor Localization (6-DoF Pose)"
"Indoor Localization (6D)"
"Indoor Localization"
"Indoor Monocular Depth Estimation"
"Indoor Scene Reconstruction"
"Indoor Scene Synthesis"
"Inductive knowledge graph completion"
"Inductive Link Prediction"
"Inductive logic programming"
"Industrial Robots"
"Inference Attack"
"Infinite Image Generation"
"Informal-to-formal Style Transfer"
"Information Cascade Popularity Prediction"
"Information Extraction"
"Information Retrieval"
"Information Threading"
"Informativeness"
"Infrared And Visible Image Fusion"
"Infrared image super-resolution"
"Initial Structure to Relaxed Energy (IS2RE), Direct"
"Initial Structure to Relaxed Energy (IS2RE)"
"Instance Search"
"Instance Segmentation"
"Instance Shadow Detection"
"Instruction Following"
"Instrument Recognition"
"Intelligent Communication"
"intensity image denoising"
"Intent Classification and Slot Filling"
"Intent Classification"
"Intent Detection"
"Intent Discovery"
"Intent Recognition"
"intent-classification"
"Interactive 3D Instance Segmentation -Trained on Scannet40 - Evaluated on Scannet40"
"Interactive 3D Instance Segmentation"
"Interactive Segmentation"
"Interactive Video Object Segmentation"
"Interest Point Detection"
"International Law"
"Interpretability Techniques for Deep Learning"
"Interpretable Machine Learning"
"Intracorporeal Tracking"
"Intraoperative Tracking"
"Intrinsic Image Decomposition"
"Intrusion Detection"
"Inverse Rendering"
"inverse tone mapping"
"Inverse-Tone-Mapping"
"Iris Recognition"
"Iris Segmentation"
"Irish Text Diacritization"
"Irony Identification"
"Irregular Text Recognition"
"Irregular Time Series"
"IUPAC Name Prediction"
"Jet Tagging"
"Job classification"
"Joint Demosaicing and Denoising"
"Joint Entity and Relation Extraction on Scientific Data"
"Joint Entity and Relation Extraction"
"Joint Event and Temporal Relation Extraction"
"Joint Radar-Communication"
"Joint Vertebrae Identification And Localization In Spinal Ct Images"
"JPEG Artifact Correction"
"Jpeg Compression Artifact Reduction"
"JPEG Decompression"
"Jurisprudence"
"K-complex detection"
"KB-to-Language Generation"
"Key Detection"
"Key Information Extraction"
"Key Point Matching"
"Key-Frame-based Video Super-Resolution (K = 15)"
"Key-value Pair Extraction"
"Keyphrase Extraction"
"Keyphrase Generation"
"Keypoint detection and image matching"
"Keypoint Detection"
"Keypoint Estimation"
"Keyword Extraction"
"Keyword Spotting CSS"
"Keyword Spotting"
"KG-to-Text Generation"
"Kidney Function"
"Kinematic Based Workflow Recognition"
"Kinship Verification"
"Knowledge Base Completion"
"Knowledge Base Population"
"Knowledge Base Question Answering"
"Knowledge Distillation"
"knowledge editing"
"Knowledge Graph Completion"
"Knowledge Graph Embedding"
"Knowledge Graph Embeddings"
"Knowledge Graphs"
"Knowledge Probing"
"Knowledge Tracing"
"Knowledge-Aware Recommendation"
"Label shift of blended-target domain adaptation"
"LABELED_DEPENDENCIES"
"Lake Ice Monitoring"
"LAMBADA"
"Laminar-Turbulent Flow Localisation"
"Land Cover Classification"
"Landmark Recognition"
"Landmark Tracking"
"Landmark-based Lipreading"
"Landslide segmentation"
"Lane Detection"
"Language Acquisition"
"Language Identification"
"Language Modelling"
"Language-Based Temporal Localization"
"Large Language Model"
"Large-Scale Person Re-Identification"
"Latent Aspect Detection"
"Latvian Text Diacritization"
"Lay Summarization"
"Layout Design"
"Layout-to-Image Generation"
"Learning Semantic Representations"
"Learning with coarse labels"
"Learning with noisy labels"
"Learning-To-Rank"
"Left Ventricle Segmentation"
"Legal Document Summarization"
"legal outcome extraction"
"LEMMA"
"Lemmatization"
"Length-of-Stay prediction"
"Lesion Classification"
"Lesion Detection"
"Lesion Segmentation"
"Lexical Analysis"
"Lexical Entailment"
"Lexical Normalization"
"Lexical Simplification"
"Li-ion State of Health Estimation"
"Library-Oriented Code Generation"
"License Plate Detection"
"License Plate Recognition"
"lidar absolute pose regression"
"Lidar Scene Completion"
"LIDAR Semantic Segmentation"
"Lifelike 3D Human Generation"
"Lightweight Face Recognition"
"Line Detection"
"Line Items Extraction"
"Line Segment Detection"
"Linear evaluation"
"Linear Probing Object-Level 3D Awareness"
"Linear-Probe Classification"
"Linguistic Acceptability"
"Link Prediction"
"Link Property Prediction"
"Link Sign Prediction"
"Lip password classification"
"Lip Reading"
"Lip to Speech Synthesis"
"Lipreading"
"Liquid Simulation"
"ListOps"
"Live Video Captioning"
"Liver Segmentation"
"LLM Jailbreak"
"LLM real-life tasks"
"LLM-generated Text Detection"
"Load Forecasting"
"Load Virtual Sensing (Fx)"
"Load Virtual Sensing (Fy)"
"Load Virtual Sensing"
"Local Color Enhancement"
"Local Distortion"
"Localization In Video Forgery"
"Log Solubility"
"Logical Args"
"Logical Fallacies"
"Logical Reasoning Question Answering"
"Logical Reasoning Reading Comprehension"
"Logical Reasoning"
"Logo Recognition"
"Long Form Question Answering"
"Long Term Action Anticipation"
"Long Term Anticipation"
"Long Video Retrieval (Background Removed)"
"Long-Context Understanding"
"Long-Form Narrative Summarization"
"Long-range modeling"
"Long-tail Learning on CIFAR-10-LT (ρ=100)"
"Long-tail learning with class descriptors"
"Long-tail Learning"
"Long-tail Video Object Segmentation"
"Long-tailed Object Detection"
"Long-video Activity Recognition"
"Loop Closure Detection"
"Low Resource Named Entity Recognition"
"Low-Dose X-Ray Ct Reconstruction"
"Low-latency processing"
"Low-light Image Deblurring and Enhancement"
"Low-Light Image Enhancement"
"Low-light Pedestrian Detection"
"Low-Resource Neural Machine Translation"
"Lung Cancer Diagnosis"
"Lung Disease Classification"
"Lung Nodule 3D Classification"
"Lung Nodule 3D Detection"
"Lung Nodule Classification"
"Lung Nodule Detection"
"Lung Nodule Segmentation"
"Lung Sound Classification"
"LV Segmentation"
"LWR Classification"
"Machine Reading Comprehension"
"Machine Translation"
"mage-to-Text Retrieval"
"Malaria Falciparum Detection"
"Malaria Malariae Detection"
"Malaria Ovale Detection"
"Malaria Vivax Detection"
"Malicious Detection"
"Malware Analysis"
"Malware Classification"
"Malware Clustering"
"Malware Detection"
"Malware Family Detection"
"Malware Type Detection"
"Management"
"Marine Animal Segmentation"
"Markerless Motion Capture"
"Marketing"
"Masked Language Modeling"
"Matching Disparate Images"
"Material Classification"
"Material Recognition"
"Materials Screening"
"Math Information Retrieval"
"Math Word Problem Solving"
"Math Word Problem SolvingΩ"
"Math"
"Mathematical Induction"
"Mathematical Problem-Solving"
"Mathematical Proofs"
"Mathematical Question Answering"
"Mathematical Reasoning"
"Matrix Completion"
"Max-Shot Cross-Lingual Image-to-Text Retrieval"
"Max-Shot Cross-Lingual Text-to-Image Retrieval"
"Max-Shot Cross-Lingual Visual Natural Language Inference"
"Max-Shot Cross-Lingual Visual Question Answering"
"Max-Shot Cross-Lingual Visual Reasoning"
"Medical Code Prediction"
"Medical Concept Normalization"
"Medical Diagnosis"
"Medical Genetics"
"Medical Image Analysis"
"Medical Image Classification"
"Medical Image Deblurring"
"Medical Image Denoising"
"medical image detection"
"Medical Image Enhancement"
"Medical Image Generation"
"Medical Image Registration"
"Medical Image Retrieval"
"Medical Image Segmentation"
"Medical Named Entity Recognition"
"Medical Object Detection"
"Medical Procedure"
"Medical Relation Extraction"
"Medical Report Generation"
"Medical Visual Question Answering"
"Medical X-Ray Image Segmentation"
"Meeting Summarization"
"Melody Extraction"
"Meme Classification"
"Memex Question Answering"
"Memorization"
"Mental Workload Estimation"
"Meta Reinforcement Learning"
"Meta-Learning"
"Metaheuristic Optimization"
"Metaphor Boolean"
"Meter Reading"
"Method name prediction"
"Metric Learning"
"Micro Expression Recognition"
"Micro-Action Recognition"
"Micro-expression Generation"
"Micro-Expression Recognition"
"Micro-Expression Spotting"
"Micro-gesture Recognition"
"Mirror Detection"
"Miscellaneous"
"Misconceptions"
"Misinformation"
"Missing Elements"
"Mistake Detection"
"Mitigating Contextual Bias"
"Mixed Reality"
"MLLM Evaluation: Aesthetics"
"MMLU"
"MMR total"
"MMSQL performance"
"Mobile Periocular Recognition"
"Mobile Security"
"Modality completion"
"Model Compression"
"Model Editing"
"Model extraction"
"Model Poisoning"
"Model Selection"
"Model-based Reinforcement Learning"
"Models Alignment"
"Molecular Graph Generation"
"Molecular Property Prediction (1-shot))"
"Molecular Property Prediction"
"Molecule Captioning"
"Molecule retrieval from MS/MS spectrum (bonus chemical formulae)"
"Molecule retrieval from MS/MS spectrum"
"Moment Queries"
"Moment Retrieval"
"Mono3DVG"
"Monocular 3D Human Pose Estimation"
"Monocular 3D Object Detection"
"Monocular 3D Object Localization"
"Monocular Cross-View Road Scene Parsing(Road)"
"Monocular Cross-View Road Scene Parsing(Vehicle)"
"Monocular Depth Estimation"
"Monocular Visual Odometry"
"Montezuma's Revenge"
"Moral Disputes"
"Moral Permissibility"
"Moral Scenarios"
"MORPH"
"Morpheme Segmentaiton"
"Morphological Analysis"
"Morphological Disambiguation"
"Morphological Inflection"
"Morphological Tagging"
"Mortality Prediction"
"Motion Captioning"
"Motion Compensation"
"Motion Correction In Multishot Mri"
"Motion Detection"
"Motion Disentanglement"
"Motion Estimation"
"Motion Forecasting"
"Motion Generation"
"Motion Interpolation"
"Motion Planning"
"motion prediction"
"motion retargeting"
"Motion Segmentation"
"Motion Synthesis"
"Motor Imagery Decoding (left-hand vs right-hand)"
"Movie Dialog Same Or Different"
"Movie Recommendation"
"Moving Object Detection"
"Moving Point Cloud Processing"
"MRI classification"
"MRI Reconstruction"
"MRI segmentation"
"MS-SSIM"
"MS/MS spectrum simulation (bonus chemical formulae)"
"MS/MS spectrum simulation"
"MTEB Benchmark"
"MuJoCo Games"
"MuJoCo"
"Multi Class Text Classification"
"Multi Future Trajectory Prediction"
"Multi Label Text Classification"
"Multi-agent Integration"
"Multi-Agent Path Finding"
"Multi-agent Reinforcement Learning"
"Multi-Animal Tracking with identification"
"Multi-Armed Bandits"
"Multi-Choice MRC"
"Multi-class Anomaly Detection"
"Multi-class Classification"
"Multi-Document Summarization"
"Multi-domain Dialogue State Tracking"
"Multi-Domain Recommender Systems"
"Multi-Domain Sentiment Classification"
"Multi-Frame Super-Resolution"
"Multi-future Trajectory Prediction"
"Multi-Goal Reinforcement Learning"
"Multi-Grained Named Entity Recognition"
"Multi-hop Question Answering"
"Multi-Hop Reading Comprehension"
"Multi-Human Parsing"
"Multi-Hypotheses 3D Human Pose Estimation"
"Multi-Instance Retrieval"
"Multi-instrument Music Transcription"
"Multi-Label Classification Of Biomedical Texts"
"Multi-Label Classification"
"Multi-label Condescension Detection"
"Multi-Label Image Classification"
"Multi-label Image Recognition with Partial Labels"
"Multi-Label Learning"
"Multi-Label Text Classification"
"Multi-label zero-shot learning"
"Multi-Labeled Relation Extraction"
"Multi-Media Recommendation"
"Multi-modal Classification"
"Multi-modal Dialogue Generation"
"Multi-Modal Document Classification"
"Multi-modal Entity Alignment"
"Multi-modal Knowledge Graph"
"Multi-modal Recommendation"
"Multi-object colocalization"
"Multi-object discovery"
"Multi-Object Tracking and Segmentation"
"Multi-Object Tracking"
"Multi-Objective Multi-Agent Reinforcement Learning"
"Multi-Oriented Scene Text Detection"
"Multi-Person Pose Estimation and Tracking"
"Multi-Person Pose Estimation"
"Multi-Person Pose forecasting"
"Multi-Source Unsupervised Domain Adaptation"
"Multi-step retrosynthesis"
"Multi-Subject Fmri Data Alignment"
"Multi-target Domain Adaptation"
"Multi-target regression"
"Multi-task Audio Source Seperation"
"Multi-task Language Understanding"
"Multi-Task Learning"
"Multi-tissue Nucleus Segmentation"
"Multi-view 3D Human Pose Estimation"
"Multi-View 3D Reconstruction"
"Multi-View 3D Shape Retrieval"
"MULTI-VIEW LEARNING"
"Multi-view Subspace Clustering"
"multi-word expression embedding"
"multi-word expression sememe prediction"
"Multibehavior Recommendation"
"Multiclass Quantification"
"Multilabel Text Classification"
"Multilingual Image-Text Classification"
"Multilingual Machine Comprehension in English Hindi"
"Multilingual Named Entity Recognition"
"Multilingual NLP"
"Multilingual text classification"
"MultiMaterial2D"
"Multimodal Abstractive Text Summarization"
"Multimodal Activity Recognition"
"Multimodal Association"
"Multimodal Deep Learning"
"Multimodal Emotion Recognition"
"Multimodal Forgery Detection"
"multimodal generation"
"Multimodal GIF Dialog"
"Multimodal Intent Recognition"
"Multimodal Large Language Model"
"Multimodal Lexical Translation"
"Multimodal Machine Translation"
"Multimodal Reasoning"
"Multimodal Recommendation"
"Multimodal Sentiment Analysis"
"Multimodal Sleep Stage Detection"
"Multimodal Text and Image Classification"
"Multimodal Text Prediction"
"Multimodal Unsupervised Image-To-Image Translation"
"Multiobjective Optimization"
"Multiple Choice Question Answering (MCQA)"
"Multiple Instance Learning"
"Multiple Object Track and Segmentation"
"Multiple Object Tracking with Transformer"
"Multiple Object Tracking"
"Multiple People Tracking"
"Multiple-choice"
"Multispectral Image Super-resolution"
"Multispectral Object Detection"
"Multivariate Time Series Forecasting"
"Multivariate Time Series Forecastingm"
"Multivariate Time Series Imputation"
"Multiview Clustering"
"Multiview Contextual Commonsense Inference"
"Multiview Detection"
"Multiview Gait Recognition"
"Multiview Learning"
"Multlingual Neural Machine Translation"
"Muscle Tendon Junction Identification"
"Music Auto-Tagging"
"Music Captioning"
"Music Classification"
"Music Compression"
"Music Emotion Recognition"
"Music Generation"
"Music Genre Classification"
"Music Genre Recognition"
"Music Genre Transfer"
"Music Information Retrieval"
"Music Modeling"
"Music Performance Rendering"
"Music Quality Assessment"
"Music Question Answering"
"Music Recommendation"
"Music Source Separation"
"Music Style Transfer"
"Music Tagging"
"Music Transcription"
"Myocardial infarction detection"
"N-Queens Problem - All Possible Solutions"
"Named Entity Recognition (NER)"
"Named Entity Recognition In Vietnamese"
"Named Entity Recognition"
"named-entity-recognition"
"Native Language Identification"
"Natural Language Inference (Few-Shot)"
"Natural Language Inference (One-Shot)"
"Natural Language Inference (Zero-Shot)"
"Natural Language Inference"
"Natural Language Landmark Navigation Instructions Generation"
"Natural Language Moment Retrieval"
"Natural Language Queries"
"Natural Language Understanding"
"Natural Language Visual Grounding"
"Natural Questions"
"Nature-Inspired Optimization Algorithm"
"Navigate"
"ND regression"
"Negation and Speculation Cue Detection"
"Negation and Speculation Scope resolution"
"Negation Detection"
"Negation Scope Resolution"
"Negation"
"NER"
"Nested Mention Recognition"
"Nested Named Entity Recognition"
"Nested Term Extraction"
"Nested Term Recognition from Flat Supervision"
"NetHack Score"
"Network Community Partition"
"Network Embedding"
"Network Intrusion Detection"
"Network Pruning"
"Neural Architecture Search"
"Neural Network Compression"
"Neural Network Security"
"Neural Network simulation"
"Neural Rendering"
"Neutron PDF regression"
"New Product Sales Forecasting"
"News Annotation"
"News Authenticity Identification"
"News Classification"
"News Generation"
"News Recommendation"
"News Retrieval"
"News Summarization"
"News Target Detection"
"NLP based Person Retrival"
"NMR J-coupling"
"NMT"
"No real Data Binarization"
"No-Reference Image Quality Assessment"
"Node Classification on Non-Homophilic (Heterophilic) Graphs"
"Node Classification"
"Node Clustering"
"Node Property Prediction"
"Node Regression"
"Noise Estimation"
"Noise Level Prediction"
"Noisy Semantic Image Synthesis"
"Non-exemplar-based Class Incremental Learning"
"Non-Intrusive Load Monitoring"
"Nonhomogeneous Image Dehazing"
"Nonsense Words Grammar"
"Noun Phrase Canonicalization"
"Novel Class Discovery"
"Novel LiDAR View Synthesis"
"Novel Object Detection"
"Novel View Synthesis"
"Novelty Detection"
"NR-IQA"
"Nuclear Segmentation"
"Nuclei Classification"
"Nutrition"
"Object Categorization"
"Object Counting"
"Object Detection In Aerial Images"
"Object Detection In Indoor Scenes"
"Object Detection"
"Object Discovery"
"Object Localization"
"Object Proposal Generation"
"Object Rearrangement"
"Object Recognition"
"Object Reconstruction"
"Object Segmentation"
"Object Skeleton Detection"
"Object SLAM"
"Object State Change Classification"
"Object Tracking"
"object-detection"
"Occluded 3D Object Symmetry Detection"
"Occluded Face Detection"
"Occlusion Estimation"
"Occlusion Handling"
"Odd One Out"
"Odor Descriptor Prediction"
"Offline Handwritten Chinese Character Recognition"
"Offline RL"
"Offline surgical phase recognition"
"Omniverse Isaac Gym"
"Omnnidirectional Stereo Depth Estimation"
"One-class classifier"
"One-Shot 3D Action Recognition"
"One-Shot Instance Segmentation"
"One-Shot Learning"
"One-Shot Object Detection"
"One-Shot Part Segmentation of Contain Affordance - Inter Class"
"One-Shot Part Segmentation of Contain Affordance - Intra Class"
"One-Shot Part Segmentation of Cut Affordance - Inter Class"
"One-Shot Part Segmentation of Cut Affordance - Intra Class"
"One-Shot Part Segmentation of Grasp Affordance - Inter Class"
"One-Shot Part Segmentation of Grasp Affordance - Intra Class"
"One-Shot Part Segmentation of Pound Affordance - Inter Class"
"One-Shot Part Segmentation of Pound Affordance - Intra Class"
"One-Shot Part Segmentation of Scoop Affordance - Inter Class"
"One-Shot Part Segmentation of Scoop Affordance - Intra Class"
"One-Shot Part Segmentation of Support Affordance - Inter Class"
"One-Shot Part Segmentation of Support Affordance - Intra Class"
"One-Shot Part Segmentation of Wrap-Grasp Affordance - Inter Class"
"One-Shot Part Segmentation of Wrap-Grasp Affordance - Intra Class"
"One-Shot Segmentation"
"One-shot Unsupervised Domain Adaptation"
"One-shot visual object segmentation"
"One-stage Anchor-free Oriented Object Detection"
"Online Action Detection"
"Online Beat Tracking"
"Online Clustering"
"Online Downbeat Tracking"
"Online Multi-Object Tracking"
"Online surgical phase recognition"
"Online Vectorized HD Map Construction"
"Online Video Anomaly Detection"
"Only Connect Walls Dataset Task 1 (Grouping)"
"Ontology Matching"
"Open Information Extraction"
"Open Intent Detection"
"Open Intent Discovery"
"Open Knowledge Graph Canonicalization"
"Open Knowledge Graph Embedding"
"Open Relation Modeling"
"Open Set Action Recognition"
"Open Set Learning"
"Open Vocabulary Action Detection"
"Open Vocabulary Action Recognition"
"Open Vocabulary Attribute Detection"
"Open Vocabulary Image Classification"
"Open Vocabulary Object Detection"
"Open Vocabulary Panoptic Segmentation"
"Open Vocabulary Semantic Segmentation"
"Open World Object Detection"
"Open-Domain Dialog"
"Open-Domain Question Answering"
"Open-Domain Subject-to-Video"
"Open-Ended Question Answering"
"open-set classification"
"Open-Set Multi-Target Domain Adaptation"
"Open-set video tagging"
"Open-Vocabulary Semantic Segmentation"
"Open-World Instance Segmentation"
"Open-World Semi-Supervised Learning"
"Open-World Video Segmentation"
"OpenAI Gym"
"OpenAPI code completion"
"Operator learning"
"Opinion Mining"
"Optic Cup Detection"
"Optic Cup Segmentation"
"Optic Disc Detection"
"Optic Disc Segmentation"
"Optical Character Recognition (OCR)"
"Optical Charater Recogntion"
"Optical Flow Estimation"
"Optimize the trajectory of UAV which plays a BS in communication system"
"OrangeSum"
"Organ Detection"
"Organ Segmentation"
"Oriented Object Detctio"
"Oriented Object Detection"
"Out of Distribution (OOD) Detection"
"Out-of-Distribution Detection"
"Out-of-Distribution Generalization"
"Out-of-Sight Trajectory Prediction"
"Outcome Prediction In Multimodal Mri"
"Outdoor Light Source Estimation"
"Outdoor Localization"
"Outlier Detection"
"Overall - Test"
"Overlapped 10-1"
"Overlapped 100-10"
"Overlapped 100-5"
"Overlapped 100-50"
"Overlapped 14-1"
"Overlapped 25-25"
"Overlapped 50-50"
"PAIR TRADING"
"Pancreas Segmentation"
"Panoptic Scene Graph Generation"
"Panoptic Segmentation (PanopticNDT instances)"
"Panoptic Segmentation"
"Panorama Pose Estimation (N-view)"
"Pansharpening"
"Paper generation (abstract-to-conclusion)"
"Paper generation (Conclusion-to-title)"
"Paper generation (Title-to-abstract)"
"Paper generation"
"Parallel Corpus Mining"
"Parameter Prediction"
"parameter-efficient fine-tuning"
"Paraphrase Generation"
"Paraphrase Identification within Bi-Encoder"
"Paraphrase Identification"
"Parking Space Occupancy"
"Part-aware Panoptic Segmentation"
"Part-Of-Speech Tagging"
"Partial Domain Adaptation"
"Partial Label Learning"
"Partial Point Cloud Matching"
"Partial Video Copy Detection"
"Partially Observable Reinforcement Learning"
"Partially Relevant Video Retrieval"
"Participant Intervention Comparison Outcome Extraction"
"Passage Ranking"
"Passage Re-Ranking"
"Passage Retrieval"
"Patch Matching"
"Patent classification"
"Patent Figure Description Generation"
"Patient Phenotyping"
"PD-L1 Tumor Proportion Score Prediction"
"PDE Surrogate Modeling"
"Pedestrian Attribute Recognition"
"Pedestrian Detection"
"Pedestrian Image Caption"
"Pedestrian Trajectory Prediction"
"Perceptual Distance"
"Period Estimation"
"Perpetual View Generation"
"Persian Sentiment Analysis"
"Person Identification (1-shot)"
"Person Identification (zero-shot)"
"Person Identification"
"Person Re-Identification"
"Person Recognition"
"Person Retrieval"
"Person Search"
"Person-centric Visual Grounding"
"Persona Dialogue in Story"
"Personality Recognition in Conversation"
"Personality Trait Recognition"
"Personalized and Emotional Conversation"
"Personalized Federated Learning"
"Personalized Image Generation"
"Personalized Segmentation"
"Persuasion Strategies"
"Persuasive Writing Strategy Detection Level-1"
"Persuasive Writing Strategy Detection Level-2"
"Persuasive Writing Strategy Detection Level-3"
"Persuasive Writing Strategy Detection Level-4"
"Philosophy"
"Phishing Website Detection"
"Phone-level pronunciation scoring"
"Phoneme Recognition"
"Photo geolocation estimation"
"Photo Retouching"
"Photo to Rest Generalization"
"Photoplethysmography (PPG) beat detection"
"Photoplethysmography (PPG) heart rate estimation"
"Photoplethysmography (PPG)"
"Phrase Grounding"
"Phrase Ranking"
"Phrase Relatedness"
"Phrase Tagging"
"Physical Attribute Prediction"
"Physical Commonsense Reasoning"
"Physical Intuition"
"Physical Simulations"
"Physical Video Anomaly Detection"
"Physics MC"
"Physics-informed machine learning"
"Physiological Computing"
"Piano Music Modeling"
"Pill Classification (Both Sides)"
"Pitch Classification"
"Plan2Scene"
"Plane Instance Segmentation"
"Plant Phenotyping"
"Plasticine3D"
"Playing the Game of 2048"
"Pneumonia Detection"
"Poem meters classification"
"Point cloud classification dataset"
"Point Cloud Classification"
"Point Cloud Completion"
"Point Cloud Generation"
"Point Cloud Quality Assessment"
"Point Cloud Registration"
"Point Cloud Segmentation"
"Point Cloud Super Resolution"
"point cloud upsampling"
"Point Clouds"
"point of interests"
"Point Processes"
"Point Tracking"
"Point-interactive Image Colorization"
"Point-Supervised Instance Segmentation"
"PointGoal Navigation"
"Poker Hand Classification"
"Policy Gradient Methods"
"Polish Text Diacritization"
"Political Influence Detection"
"Political Salient Issue Orientation Detection"
"Poll Generation"
"Polyp Segmentation"
"Polyphone disambiguation"
"Popularity Forecasting"
"Populist attitude"
"Portrait Segmentation"
"POS Tagging"
"POS"
"Pose Contrastive Learning"
"Pose Estimation"
"Pose Prediction"
"Pose Retrieval"
"Pose Tracking"
"Pose Transfer"
"Pose-Based Human Instance Segmentation"
"Position regression"
"Precipitation Forecasting"
"Predicate Classification"
"Predicate Detection"
"Predict clinical outcome"
"Predict Future Video Frames"
"Predicting Drug-Induced Laboratory Test Effects"
"Predicting Patient Outcomes"
"Prediction Intervals"
"Prediction Of Occupancy Grid Maps"
"Prediction"
"Predictive Process Monitoring"
"Preference Mapping"
"Prehistory"
"Presuppositions As NLI"
"Pretrained Multilingual Language Models"
"Printed Text Recognition"
"Privacy Preserving Deep Learning"
"Privacy Preserving"
"Probabilistic Deep Learning"
"Probabilistic Time Series Forecasting"
"Probing Language Models"
"Problem-Solving Deliberation"
"Procedural Text Understanding"
"Procedure Learning"
"Procedure Step Recognition"
"Product Categorization"
"Product Recommendation"
"Product Relation Classification"
"Product Relation Detection"
"Production Forecasting"
"Professional Accounting"
"Professional Law"
"Professional Medicine"
"Professional Psychology"
"Profile Generation"
"Program induction"
"Program Repair"
"Program Synthesis"
"Promoter Detection"
"Prompt Engineering"
"Propaganda detection"
"Propaganda technique identification"
"Proper Noun"
"Prosody Prediction"
"Protein Annotation"
"Protein Design"
"Protein Fold Quality Estimation"
"Protein Folding"
"Protein Function Prediction"
"Protein Language Model"
"Protein Secondary Structure Prediction"
"Protein Structure Prediction"
"Provable Adversarial Defense"
"Public Relations"
"Pulmonary Artery–Vein Classification"
"Pulmorary Vessel Segmentation"
"Punctuation Restoration"
"Pupil Detection"
"Pupil Tracking"
"Python Code Synthesis"
"Q-Learning"
"QQP"
"QRS Complex Detection"
"Quant Trading"
"Quantization"
"Quantum Machine Learning"
"Query-Based Extractive Summarization"
"Query-focused Summarization"
"Question Answering"
"Question Generation"
"Question Quality Assessment"
"Question Rewriting"
"Question Selection"
"Question Similarity"
"Question to Declarative Sentence"
"Question-Answer categorization"
"Question-Answer-Generation"
"RACE-h"
"RACE-m"
"Race/Bias-conflicting"
"Race/Unbiased"
"Radar Object Detection"
"Radar odometry"
"RAG"
"Railway Track Image Classification"
"Rain Removal"
"Raindrop Removal"
"Raspberry Pi 3"
"Raspberry Pi 4"
"Raspberry Pi 5"
"Raw reconstruction"
"Raw vs Ripe (Generic)"
"Re-Ranking"
"Reading Comprehension (Few-Shot)"
"Reading Comprehension (One-Shot)"
"Reading Comprehension (Zero-Shot)"
"Reading Comprehension"
"Reading Order Detection"
"Real-Time 3D Semantic Segmentation"
"Real-time Directional Hearing"
"Real-time instance measurement"
"Real-time Instance Segmentation"
"Real-Time Multi-Object Tracking"
"Real-Time Object Detection"
"Real-Time Semantic Segmentation"
"Real-Time Strategy Games"
"Real-Time Visual Tracking"
"Real-World Adversarial Attack"
"Reasoning Chain Explanations"
"Recipe Generation"
"Recognizing And Localizing Human Actions"
"Recognizing Emotion Cause in Conversations"
"Recommendation Systems (Item cold-start)"
"Recommendation Systems"
"Reconstruction"
"Record linking"
"Red Teaming"
"Reference-based Video Super-Resolution"
"Referring Expression Comprehension"
"Referring expression generation"
"Referring Expression Segmentation"
"Referring Expression"
"Referring Image Matting (Expression-based)"
"Referring Image Matting (Keyword-based)"
"Referring Image Matting (Prompt-based)"
"Referring Image Matting (RefMatte-RW100)"
"Referring Video Object Segmentation"
"Reflection Removal"
"Region Proposal"
"regression"
"Reinforcement Learning (Atari Games)"
"Reinforcement Learning (RL)"
"Reinforcement Learning"
"reinforcement-learning"
"Relation Classification"
"Relation Extraction"
"Relation Linking"
"Relation Prediction"
"Relational Pattern Learning"
"Relational Reasoning"
"Relationship Detection"
"Relationship Extraction (Distant Supervised)"
"Remaining Length of Stay"
"Remaining Useful Lifetime Estimation"
"Remote Sensing Image Classification"
"Remove - PO"
"Remove - PQ"
"Repetitive Action Counting"
"Replace - PO"
"Replace - PQ"
"Replay Grounding"
"Representation Learning"
"Reranking"
"Research Performance Prediction"
"Respiratory Failure"
"Respiratory motion forecasting"
"Response Generation"
"Resynthesis"
"Retinal OCT Disease Classification"
"Retinal OCT Layer Segmentation"
"Retinal Vessel Segmentation"
"Retrieval-augmented Few-shot In-context Audio Captioning"
"Retrieval"
"Retrosynthesis"
"Review Generation"
"RGB Salient Object Detection"
"RGB-D Instance Segmentation"
"RGB-D Salient Object Detection"
"Rgb-T Tracking"
"RGB+3D Anomaly Detection and Segmentation"
"Rhythm"
"Rice Grain Disease Detection"
"Riddle Sense"
"Road Damage Detection"
"Road Segmentation"
"Robot Manipulation Generalization"
"Robot Manipulation"
"Robot Navigation"
"Robot Pose Estimation"
"Robot Task Planning"
"Robotic Grasping"
"Robust 3D Object Detection"
"Robust 3D Semantic Segmentation"
"Robust BEV Detection"
"Robust BEV Map Segmentation"
"Robust Camera Only 3D Object Detection"
"Robust classification"
"Robust Face Recognition"
"Robust Object Detection"
"Robust Semi-Supervised RGBD Semantic Segmentation"
"Robust Speech Recognition"
"Role-based N-ary Relaiton Extraction"
"Role-filler Entity Extraction"
"Rolling Shutter Correction"
"ROLSSL-Consistent"
"ROLSSL-Reversed"
"ROLSSL-Uniform"
"Romanian Text Diacritization"
"Room Layout Estimation"
"RoomEnv-v0"
"RoomEnv-v1"
"RoomEnv-v2"
"Root Joint Localization"
"Rotated MNIST"
"Row Annotation"
"RTE"
"Rumour Detection"
"Safe Reinforcement Learning"
"Safety Alignment"
"Safety Perception Recognition"
"Saliency Detection"
"Saliency Prediction"
"Salient Object Detection"
"Salt-And-Pepper Noise Removal"
"Sand"
"Sand2D"
"Sand3D_Long"
"SANS regression"
"Sarcasm Detection"
"Satellite Image Classification"
"satellite image super-resolution"
"Satire Detection"
"SAXS regression"
"Scale Generalisation"
"Scanpath prediction"
"Scene Change Detection"
"Scene Classification (unified classes)"
"Scene Classification"
"Scene Flow Estimation"
"Scene Generation"
"Scene Graph Classification"
"Scene Graph Detection"
"Scene Graph Generation"
"Scene Labeling"
"Scene Parsing"
"Scene Recognition"
"Scene Segmentation"
"Scene Text Detection"
"Scene Text Recognition"
"Scene Understanding"
"Scene-Aware Dialogue"
"Scheduling"
"Scholarly Named Entity Recognition"
"Science Question Answering"
"Scientific Concept Extraction"
"Scientific Data Usage Detection"
"Scientific Document Summarization"
"Scientific Results Extraction"
"Security Studies"
"Seeing Beyond the Visible"
"Segmentation Based Workflow Recognition"
"Segmentation Of Remote Sensing Imagery"
"Segmentation"
"Segmented Multimodal Named Entity Recognition"
"Seismic Detection"
"Seizure Detection"
"Seizure prediction"
"Selection bias"
"Self-Driving Cars"
"Self-Learning"
"Self-Supervised Action Recognition Linear"
"Self-Supervised Action Recognition"
"Self-Supervised Anomaly Detection"
"Self-Supervised Audio Classification"
"Self-Supervised Human Action Recognition"
"Self-Supervised Image Classification"
"Self-Supervised Learning"
"Self-Supervised Person Re-Identification"
"Self-supervised Scene Flow Estimation"
"self-supervised scene text recognition"
"Self-supervised Skeleton-based Action Recognition"
"Self-supervised Video Retrieval"
"Semantic Communication"
"Semantic Composition"
"Semantic Contour Prediction"
"Semantic correspondence"
"Semantic entity labeling"
"Semantic Frame Parsing"
"Semantic Image Similarity"
"Semantic Image-Text Similarity"
"Semantic Object Interaction Classification"
"Semantic Parsing"
"Semantic Part Detection"
"Semantic Retrieval"
"Semantic Role Labeling (predicted predicates)"
"Semantic Role Labeling"
"Semantic Segmentation Of Orthoimagery"
"Semantic Segmentation"
"Semantic Similarity"
"Semantic SLAM"
"Semantic Text Matching"
"Semantic Textual Similarity within Bi-Encoder"
"Semantic Textual Similarity"
"Semanticity prediction"
"SemEval-2022 Task 4-1 (Binary PCL Detection)"
"SemEval-2022 Task 4-2 (Multi-label PCL Detection)"
"Semi Supervised Learning for Image Captioning"
"Semi-Supervised Action Detection"
"Semi-supervised Anomaly Detection"
"Semi-supervised Change Detection"
"Semi-supervised Domain Adaptation"
"Semi-Supervised Human Pose Estimation"
"Semi-Supervised Image Classification (Cold Start)"
"Semi-Supervised Image Classification"
"Semi-Supervised Instance Segmentation"
"Semi-supervised Medical Image Classification"
"Semi-supervised Medical Image Segmentation"
"Semi-Supervised Object Detection"
"Semi-Supervised Person Re-Identification"
"Semi-Supervised RGBD Semantic Segmentation"
"Semi-Supervised Semantic Segmentation"
"Semi-Supervised Text Classification"
"Semi-supervised time series classification"
"Semi-Supervised Video Action Detection"
"Semi-Supervised Video Classification"
"Semi-Supervised Video Object Segmentation"
"Sensor Fusion"
"Sensor Modeling"
"Sentence Ambiguity"
"Sentence Classification"
"Sentence Completion"
"Sentence Compression"
"Sentence Embedding"
"Sentence Embeddings For Biomedical Texts"
"Sentence Embeddings"
"Sentence Fusion"
"Sentence Ordering"
"Sentence Retrieval"
"Sentence ReWriting"
"Sentence segmentation"
"Sentence Similarity"
"Sentence-Embedding"
"Sentence-Pair Classification"
"Sentiment Analysis (Product + User)"
"Sentiment Analysis"
"Sentiment Classification"
"SENTS"
"Sequence-to-sequence Language Modeling"
"Sequential Decision Making"
"Sequential Image Classification"
"Sequential Pattern Mining"
"Sequential Place Recognition"
"Sequential Recommendation"
"Sequential sentence segmentation"
"Sequential skip prediction"
"Session-Based Recommendations"
"severity prediction"
"Shadow Detection"
"Shadow Removal"
"Shape from Texture"
"Shooter Localization"
"Short Text Clustering"
"Short-observation new product sales forecasting"
"Short-term Object Interaction Anticipation"
"Short-Text Conversation"
"Sign Language Production"
"Sign Language Recognition"
"Sign Language Retrieval"
"Sign Language Translation"
"Silent Speech Recognition"
"Similarities Abstraction"
"Similarity Explanation"
"Simultaneous Localization and Mapping"
"Singer Identification"
"Singing Voice Synthesis"
"single catogory classification"
"Single Choice Question"
"Single Image Dehazing"
"Single Image Deraining"
"Single Image Desnowing"
"Single Image Haze Removal"
"Single-Domain Subject-to-Video"
"Single-Image Portrait Relighting"
"Single-Image-Based Hdr Reconstruction"
"Single-object colocalization"
"Single-object discovery"
"Single-shot HDR Reconstruction"
"Single-Source Domain Generalization"
"Single-step retrosynthesis"
"Single-View 3D Reconstruction on ShapeNet"
"Single-View 3D Reconstruction"
"Skeleton Based Action Recognition"
"Sketch Recognition"
"Sketch-Based Image Retrieval"
"Sketch-to-Image Translation"
"Skill Generalization"
"Skill Mastery"
"Skills Assessment"
"Skills Evaluation"
"Skin Cancer Classification"
"Skin Cancer Segmentation"
"Skin Lesion Classification"
"Skin Lesion Segmentation"
"Sleep apnea detection"
"Sleep Arousal Detection"
"Sleep spindles detection"
"Sleep Stage Detection"
"Sleep Staging"
"Slot Filling"
"slot-filling"
"Slovak Text Diacritization"
"SMAC"
"SMAC+"
"Small Data Image Classification"
"Small Object Detection"
"Small-Footprint Keyword Spotting"
"Smile Recognition"
"SNES Games"
"Snow Removal"
"Social Media Popularity Prediction"
"Social Navigation"
"Sociology"
"software testing"
"Solar Irradiance Forecasting"
"Sound Classification"
"Sound Event Detection"
"Sound Event Localization and Detection"
"Sound Prompted Semantic Segmentation"
"Soundscape evaluation"
"Source Code Summarization"
"Source Free Object Detection"
"Source-Free Domain Adaptation"
"Sowing Date Prediction"
"Space group classification"
"Space-time Video Super-resolution"
"Spam detection"
"Span-Extraction MRC"
"Spanish Text Diacritization"
"Sparse Information Retrieval"
"Sparse Learning and binarization"
"Sparse Learning"
"Sparse Representation-based Classification"
"Spatial Reasoning"
"Spatial Relation Recognition"
"Spatio-Temporal Action Localization"
"Spatio-Temporal Forecasting"
"Spatio-Temporal Video Grounding"
"Speaker Attribution in German Parliamentary Debates (GermEval 2023, subtask 1)"
"Speaker Attribution in German Parliamentary Debates (GermEval 2023, subtask 2)"
"Speaker Diarization"
"Speaker Identification"
"Speaker Profiling"
"Speaker Recognition"
"Speaker Separation"
"Speaker Verification"
"Speaker-Specific Lip to Speech Synthesis"
"Specificity"
"Spectral Reconstruction"
"Specular Segmentation"
"Speculation Scope Resolution"
"Speech Denoising"
"Speech Dereverberation"
"Speech Emotion Recognition"
"Speech Enhancement"
"Speech Extraction"
"Speech Intent Classification"
"Speech Prompted Semantic Segmentation"
"Speech Recognition"
"Speech Separation"
"Speech Synthesis - Assamese"
"Speech Synthesis - Bengali"
"Speech Synthesis - Bodo"
"Speech Synthesis - Gujarati"
"Speech Synthesis - Hindi"
"Speech Synthesis - Kannada"
"Speech Synthesis - Malayalam"
"Speech Synthesis - Manipuri"
"Speech Synthesis - Marathi"
"Speech Synthesis - Rajasthani"
"Speech Synthesis - Tamil"
"Speech Synthesis - Telugu"
"Speech Synthesis"
"speech-recognition"
"Speech-to-Gesture Translation"
"Speech-to-Speech Translation"
"Speech-to-Text Translation"
"Speech-to-Text"
"Spelling Correction"
"Spindle Detection"
"Splice Site Prediction"
"Split and Rephrase"
"SpO2 estimation"
"Spoken Dialogue Systems"
"Spoken language identification"
"Spoken Language Understanding"
"Sports Activity Recognition"
"Sports Analytics"
"Sports Ball Detection and Tracking"
"Sports Understanding"
"Sql Chatbots"
"SQL Parsing"
"SQL-to-Text"
"SRF (test)"
"SSIM"
"SSTOD"
"Stable MCI vs Progressive MCI"
"Stance Classification"
"Stance Detection (US Election 2020 - Biden)"
"Stance Detection (US Election 2020 - Trump)"
"Stance Detection"
"Starcraft II"
"Starcraft"
"State Change Object Detection"
"Statistical Data Usage Detection"
"Steering Control"
"Steiner Tree Problem"
"Stenosis Segmentation"
"Stereo Depth Estimation"
"Stereo Disparity Estimation"
"Stereo Image Super-Resolution"
"Stereo Matching Hand"
"Stereo Matching"
"Stereo-LiDAR Fusion"
"Stereotypical Bias Analysis"
"Stochastic Optimization"
"Stock Market Prediction"
"Stock Prediction"
"Stock Price Prediction"
"Stock Trend Prediction"
"Story Completion"
"Story Continuation"
"Story Generation"
"Story Visualization"
"Streaming Target Sound Extraction"
"Stroke Classification"
"Structured Prediction"
"Structured Report Generation"
"STS Benchmark"
"STS"
"Student Engagement Level Detection (Four Class Video Classification)"
"Style change detection"
"Style Transfer"
"Sub-diagnostic"
"Subgraph Counting - 2 star"
"Subgraph Counting - 3 Star"
"Subgraph Counting - C4"
"Subgraph Counting - C5"
"Subgraph Counting - C6"
"Subgraph Counting - Chordal C4"
"Subgraph Counting - K4"
"Subgraph Counting - Tailed Triangle"
"Subgraph Counting - Triangle"
"Subgraph Counting"
"Subject Transfer"
"Subjectivity Analysis"
"Success Rate (5 task-horizon)"
"Summarization Consistency Evaluation"
"Summarization"
"Super-diagnostic"
"Super-Resolution"
"Superclass classification"
"Superpixel Image Classification"
"Supervised Anomaly Detection"
"Supervised Defect Detection"
"Supervised Image Retrieval"
"Supervised Only 3D Point Cloud Classification"
"Supervised Text Retrieval"
"Supervised Video Summarization"
"Surface Normal Estimation"
"Surface Normals Estimation from Point Clouds"
"Surface Normals Estimation"
"Surface Reconstruction"
"Surgical Gesture Recognition"
"Surgical phase recognition"
"Surgical Skills Evaluation"
"Surgical tool detection"
"Surveillance-to-Booking"
"Surveillance-to-Single"
"Surveillance-to-Surveillance"
"Survival Analysis"
"Survival Prediction"
"Suspicous (BIRADS 4,5)-no suspicous (BIRADS 1,2,3) per image classification"
"SVBRDF Estimation"
"Symbolic Regression"
"Symmetry Detection"
"Syntax Representation"
"Synthetic Data Evaluation"
"Synthetic Data Generation"
"Synthetic Face Recognition"
"Synthetic Image Attribution"
"Synthetic Image Detection"
"Synthetic Song Detection"
"Synthetic Speech Detection"
"Synthetic-to-Real Translation"
"Systematic Generalization"
"Table annotation"
"Table Detection"
"Table Functional Analysis"
"Table Recognition"
"Table Retrieval"
"Table Search"
"Table Type Detection"
"Table-based Fact Verification"
"Table-to-Text Generation"
"Tabular Data Generation"
"tabular-classification"
"tabular-regression"
"TAG"
"Talking Face Generation"
"Talking Head Generation"
"TAR"
"Target Sound Extraction"
"Task 2"
"Task Planning"
"Task-Oriented Dialogue Systems"
"TDC ADMET Benchmarking Group"
"Temporal Action Localization"
"Temporal Action Proposal Generation"
"Temporal Action Segmentation"
"Temporal Complex Logical Reasoning"
"Temporal Defect Localization"
"Temporal Forgery Localization"
"Temporal Information Extraction"
"Temporal Localization"
"Temporal Metadata Manipulation Detection"
"Temporal Relation Classification"
"Temporal Relation Extraction"
"Temporal Sentence Grounding"
"Temporal Sequences"
"Temporal Tagging"
"Temporal View Synthesis"
"Temporal/Casual QA"
"Term Extraction"
"Test Agnostic Long-Tailed Learning"
"Test-time Adaptation"
"text annotation"
"Text based Person Retrieval"
"Text based Person Search"
"Text Categorization"
"Text Classification"
"Text Clustering"
"Text Complexity Assessment (GermEval 2022)"
"Text Detection"
"Text Effects Transfer"
"Text Generation"
"Text Infilling"
"Text Matching"
"Text Pair Classification"
"text political leaning classification"
"Text Reranking"
"Text Retrieval"
"Text Segmentation"
"text similarity"
"Text Simplification"
"Text Spotting"
"Text Style Transfer"
"Text Summarization"
"Text to 3D"
"Text to Audio Retrieval"
"Text to Audio/Video Retrieval"
"Text to Image Generation"
"Text to Speech"
"Text to Video Retrieval"
"Text within image generation"
"Text-based de novo Molecule Generation"
"text-based games"
"Text-based Image Editing"
"Text-based Person Retrieval with Noisy Correspondence"
"Text-based Person Retrieval"
"Text-Based Stock Prediction"
"text-classification"
"Text-Line Extraction"
"Text-to-3D-Human Generation"
"text-to-3d-human"
"Text-to-Code Generation"
"text-to-Cypher"
"Text-to-Face Generation"
"Text-to-GQL"
"Text-to-Image Generation"
"Text-to-Music Generation"
"Text-To-Speech Synthesis"
"text-to-speech"
"Text-To-SQL"
"Text-to-Video Editing"
"Text-to-Video Generation"
"Text-to-video search"
"Text-Variation"
"Text2Sparql"
"Text2text Generation"
"text2text-generation"
"Texture Classification"
"Texture Image Retrieval"
"Texture Synthesis"
"TFLM sequence generation"
"TGIF-Action"
"TGIF-Frame"
"TGIF-Transition"
"The Semantic Segmentation Of Remote Sensing Imagery"
"Theory of Mind Modeling"
"Thermal Image Denoising"
"Thermal Image Segmentation"
"Thermal Infrared Object Tracking"
"Thermal Infrared Pedestrian Detection"
"Thoracic Disease Classification"
"Time Offset Calibration"
"Time Series Alignment"
"Time Series Analysis"
"Time Series Anomaly Detection"
"Time Series Averaging"
"Time Series Classification"
"Time Series Clustering"
"Time Series Forecasting"
"Time Series Generation"
"Time Series Prediction"
"Time Series Regression"
"Time Series"
"Time-interval Prediction"
"Time-Series Few-Shot Learning with Heterogeneous Channels"
"Time-to-Event Prediction"
"Timedial"
"Timex normalization"
"TinyQA Benchmark++"
"TiROD"
"Token Classification"
"Tomographic Reconstructions"
"Tone Mapping"
"Topic Classification"
"Topic coverage"
"Topic Models"
"Topological Data Analysis"
"Toponym Resolution"
"Total Magnetization"
"Toxic Comment Classification"
"Toxic Spans Detection"
"Traditional Spam Detection"
"Traffic Accident Detection"
"Traffic Classification"
"Traffic Data Imputation"
"Traffic Object Detection"
"Traffic Prediction"
"Traffic Sign Detection"
"Traffic Sign Recognition"
"Training-free 3D Part Segmentation"
"Training-free 3D Point Cloud Classification"
"Training-free Object Counting"
"Trajectory Clustering"
"Trajectory Forecasting"
"Trajectory Modeling"
"Trajectory Planning"
"Trajectory Prediction"
"Transcription Factor Binding Site Prediction (Human)"
"Transcription Factor Binding Site Prediction (Mouse)"
"Transductive Zero-Shot Classification"
"Transfer Learning"
"Transfer Reinforcement Learning"
"Transferability"
"Translation acm-deu"
"Translation acm-eng"
"Translation acm-fra"
"Translation acm-por"
"Translation acm-spa"
"Translation afr-deu"
"Translation afr-eng"
"Translation afr-fra"
"Translation afr-nld"
"Translation afr-por"
"Translation afr-spa"
"Translation amh-eng"
"Translation amh-fra"
"Translation apc-deu"
"Translation apc-eng"
"Translation apc-fra"
"Translation apc-spa"
"Translation ara-deu"
"Translation ara-eng"
"Translation ara-fra"
"Translation ara-por"
"Translation ara-spa"
"Translation arz-deu"
"Translation arz-eng"
"Translation arz-fra"
"Translation arz-por"
"Translation arz-spa"
"Translation ast-deu"
"Translation ast-eng"
"Translation ast-por"
"Translation ast-spa"
"Translation awa-eng"
"Translation awa-fra"
"Translation awa-spa"
"Translation bel-deu"
"Translation bel-fra"
"Translation bel-por"
"Translation bel-spa"
"Translation ben-deu"
"Translation ben-eng"
"Translation ben-fra"
"Translation ben-por"
"Translation ben-spa"
"Translation bho-eng"
"Translation bho-fra"
"Translation bho-por"
"Translation bos_Latn-eng"
"Translation bul-deu"
"Translation bul-eng"
"Translation bul-fra"
"Translation bul-por"
"Translation bul-spa"
"Translation cat-deu"
"Translation cat-eng"
"Translation cat-fra"
"Translation cat-por"
"Translation cat-spa"
"Translation ceb-deu"
"Translation ceb-eng"
"Translation ceb-fra"
"Translation ceb-por"
"Translation ceb-spa"
"Translation ces-deu"
"Translation ces-eng"
"Translation ces-fra"
"Translation ces-por"
"Translation ces-spa"
"Translation ckb-eng"
"Translation ckb-fra"
"Translation cmn_Hans-eng"
"Translation cmn_Hans-fra"
"Translation cmn_Hans-por"
"Translation cmn_Hans-spa"
"Translation cmn_Hant-eng"
"Translation cmn_Hant-fra"
"Translation cmn_Hant-por"
"Translation cmn_Hant-spa"
"Translation crh-deu"
"Translation crh-eng"
"Translation crh-fra"
"Translation crh-por"
"Translation crh-spa"
"Translation cym-deu"
"Translation cym-eng"
"Translation cym-fra"
"Translation cym-por"
"Translation cym-spa"
"Translation dan-deu"
"Translation dan-eng"
"Translation dan-fra"
"Translation dan-por"
"Translation dan-spa"
"Translation deu-afr"
"Translation deu-deu"
"Translation deu-eng"
"Translation deu-fra"
"Translation deu-nld"
"Translation deu-por"
"Translation deu-spa"
"Translation dsb-deu"
"Translation ell-deu"
"Translation ell-eng"
"Translation ell-fra"
"Translation ell-por"
"Translation ell-spa"
"Translation eng-afr"
"Translation eng-cat"
"Translation eng-deu"
"Translation eng-ell"
"Translation eng-eng"
"Translation eng-fra"
"Translation eng-nld"
"Translation eng-oci"
"Translation eng-por"
"Translation eng-spa"
"Translation eng-tur"
"Translation epo-deu"
"Translation epo-eng"
"Translation epo-fra"
"Translation epo-por"
"Translation epo-spa"
"Translation est-deu"
"Translation est-eng"
"Translation est-fra"
"Translation est-por"
"Translation est-spa"
"Translation eus-deu"
"Translation eus-eng"
"Translation eus-fra"
"Translation eus-por"
"Translation eus-spa"
"Translation fao-deu"
"Translation fao-eng"
"Translation fao-fra"
"Translation fao-por"
"Translation fao-spa"
"Translation fas-deu"
"Translation fas-fra"
"Translation fas-por"
"Translation fas-spa"
"Translation fij-eng"
"Translation fin-eng"
"Translation fra-deu"
"Translation fra-eng"
"Translation fra-por"
"Translation fra-spa"
"Translation fry-eng"
"Translation fry-nld"
"Translation fur-fra"
"Translation fur-por"
"Translation fur-spa"
"Translation gla-eng"
"Translation gle-deu"
"Translation gle-eng"
"Translation gle-fra"
"Translation gle-por"
"Translation gle-spa"
"Translation glg-deu"
"Translation glg-eng"
"Translation glg-fra"
"Translation glg-por"
"Translation glg-spa"
"Translation gos-deu"
"Translation gos-eng"
"Translation grn-eng"
"Translation grn-fra"
"Translation grn-por"
"Translation guj-eng"
"Translation guj-fra"
"Translation guj-por"
"Translation guj-spa"
"Translation hat-deu"
"Translation hat-eng"
"Translation hat-fra"
"Translation hat-por"
"Translation hat-spa"
"Translation hau-eng"
"Translation hau-fra"
"Translation hau-por"
"Translation hau-spa"
"Translation hbs-deu"
"Translation hbs-eng"
"Translation hbs-fra"
"Translation hbs-spa"
"Translation heb-eng"
"Translation heb-fra"
"Translation heb-por"
"Translation heb-spa"
"Translation hin-deu"
"Translation hin-eng"
"Translation hin-fra"
"Translation hin-por"
"Translation hin-spa"
"Translation hne-deu"
"Translation hne-eng"
"Translation hne-fra"
"Translation hne-por"
"Translation hne-spa"
"Translation hrv-deu"
"Translation hrv-eng"
"Translation hrv-fra"
"Translation hrv-por"
"Translation hrv-spa"
"Translation hrx-deu"
"Translation hrx-eng"
"Translation hsb-deu"
"Translation hun-deu"
"Translation hun-eng"
"Translation hun-fra"
"Translation hun-por"
"Translation hun-spa"
"Translation hye-deu"
"Translation hye-eng"
"Translation hye-fra"
"Translation hye-por"
"Translation hye-spa"
"Translation ibo-eng"
"Translation ido_Latn-eng"
"Translation ilo-deu"
"Translation ilo-eng"
"Translation ilo-fra"
"Translation ilo-por"
"Translation ilo-spa"
"Translation ind-deu"
"Translation ind-eng"
"Translation ind-fra"
"Translation ind-por"
"Translation ind-spa"
"Translation isl-deu"
"Translation isl-eng"
"Translation isl-fra"
"Translation isl-spa"
"Translation ita-deu"
"Translation ita-eng"
"Translation ita-fra"
"Translation ita-por"
"Translation ita-spa"
"Translation jav-deu"
"Translation jav-eng"
"Translation jav-fra"
"Translation jav-por"
"Translation jav-spa"
"Translation jpn-eng"
"Translation jpn-fra"
"Translation jpn-por"
"Translation jpn-spa"
"Translation kat-eng"
"Translation kat-fra"
"Translation kat-por"
"Translation kat-spa"
"Translation kaz_Cyrl-eng"
"Translation kaz-deu"
"Translation kaz-eng"
"Translation kaz-por"
"Translation kaz-spa"
"Translation kea-deu"
"Translation kea-eng"
"Translation kea-fra"
"Translation kea-por"
"Translation kea-spa"
"Translation kon-eng"
"Translation kor-eng"
"Translation lav-eng"
"Translation lfn_Latn-deu"
"Translation lfn_Latn-eng"
"Translation lfn_Latn-por"
"Translation lij-eng"
"Translation lij-fra"
"Translation lij-por"
"Translation lij-spa"
"Translation lim-eng"
"Translation lim-fra"
"Translation lim-por"
"Translation lim-spa"
"Translation lin-eng"
"Translation lin-por"
"Translation lin-spa"
"Translation ltz-deu"
"Translation ltz-eng"
"Translation ltz-nld"
"Translation lug-eng"
"Translation mag-fra"
"Translation mal-eng"
"Translation mal-fra"
"Translation mar-deu"
"Translation mar-eng"
"Translation mar-fra"
"Translation mar-por"
"Translation mar-spa"
"Translation mkd-deu"
"Translation mkd-eng"
"Translation mkd-fra"
"Translation mkd-por"
"Translation mkd-spa"
"Translation mlt-deu"
"Translation mlt-eng"
"Translation mlt-fra"
"Translation mlt-por"
"Translation mlt-spa"
"Translation mri-eng"
"Translation msa-deu"
"Translation msa-eng"
"Translation msa-fra"
"Translation msa-por"
"Translation nds-deu"
"Translation nds-eng"
"Translation nds-nld"
"Translation nld-afr"
"Translation nld-deu"
"Translation nld-eng"
"Translation nld-fry"
"Translation nld-nds"
"Translation nno-eng"
"Translation nno-fra"
"Translation nob-eng"
"Translation nob-por"
"Translation nob-spa"
"Translation nor-deu"
"Translation npi-deu"
"Translation npi-eng"
"Translation nso-eng"
"Translation nya-eng"
"Translation oci-deu"
"Translation oci-eng"
"Translation oci-fra"
"Translation oci-spa"
"Translation pag-fra"
"Translation pag-por"
"Translation pag-spa"
"Translation pan-deu"
"Translation pan-eng"
"Translation pan-por"
"Translation pan-spa"
"Translation pap-deu"
"Translation pap-eng"
"Translation pap-fra"
"Translation pap-por"
"Translation pap-spa"
"Translation pes-deu"
"Translation pes-eng"
"Translation pes-fra"
"Translation pes-por"
"Translation pes-spa"
"Translation plt-eng"
"Translation plt-fra"
"Translation plt-por"
"Translation plt-spa"
"Translation pol-deu"
"Translation pol-eng"
"Translation pol-fra"
"Translation pol-por"
"Translation pol-spa"
"Translation por-deu"
"Translation por-eng"
"Translation por-fra"
"Translation por-por"
"Translation por-spa"
"Translation prs-deu"
"Translation prs-eng"
"Translation prs-fra"
"Translation prs-por"
"Translation prs-spa"
"Translation pus-eng"
"Translation pus-fra"
"Translation pus-por"
"Translation pus-spa"
"Translation ron-deu"
"Translation ron-eng"
"Translation ron-fra"
"Translation ron-por"
"Translation ron-spa"
"Translation run-eng"
"Translation run-fra"
"Translation run-por"
"Translation run-spa"
"Translation rus-deu"
"Translation rus-eng"
"Translation rus-fra"
"Translation rus-por"
"Translation rus-spa"
"Translation slv-deu"
"Translation slv-eng"
"Translation slv-fra"
"Translation slv-por"
"Translation spa-eng"
"Translation tur-eng"
"Translation zho-eng"
"Translation"
"Transliteration"
"Transparent Object Depth Estimation"
"Transparent Object Detection"
"Transparent objects"
"Transplanting Date Prediction"
"Travel Time Estimation"
"Traveling Salesman Problem"
"TREC 2019 Passage Ranking"
"Triple Classification"
"TriviaQA"
"TruthfulQA"
"Tumor Segmentation"
"Tumour Classification"
"Türkçe Görüntü Altyazılama"
"Turkish Text Diacritization"
"Tweet Retrieval"
"Tweet-Reply Sentiment Analysis"
"Twitter Bot Detection"
"Twitter Event Detection"
"Twitter Sentiment Analysis"
"Two-sample testing"
"Type prediction"
"UCCA Parsing"
"UIE"
"Unbiased Scene Graph Generation"
"Uncertainty Quantification"
"Uncertainty Visualization"
"Uncertainty-Aware Panoptic Segmentation"
"Unconditional Crystal Generation"
"Unconditional Image Generation"
"Unconditional Molecule Generation"
"Unconditional Video Generation"
"Unconstrained Lip-synchronization"
"Uncropping"
"Understanding Fables"
"Underwater Image Restoration"
"UNET Segmentation"
"Unfairness Detection"
"Unified Image Restoration"
"Univariate Time Series Forecasting"
"Universal Domain Adaptation"
"UNLABELED_DEPENDENCIES"
"Unseen Object Instance Segmentation"
"Unsupervised 3D Human Pose Estimation"
"Unsupervised 3D Multi-Person Pose Estimation"
"Unsupervised 3D Semantic Segmentation"
"Unsupervised Action Segmentation"
"Unsupervised Anomaly Detection with Specified Settings -- 0.1% anomaly"
"Unsupervised Anomaly Detection with Specified Settings -- 1% anomaly"
"Unsupervised Anomaly Detection with Specified Settings -- 10% anomaly"
"Unsupervised Anomaly Detection with Specified Settings -- 20% anomaly"
"Unsupervised Anomaly Detection with Specified Settings -- 30% anomaly"
"Unsupervised Anomaly Detection"
"Unsupervised Continual Domain Shift Learning"
"Unsupervised Dependency Parsing"
"Unsupervised Domain Adaptation"
"Unsupervised Domain Adaptationn"
"Unsupervised Domain Expansion"
"Unsupervised Extractive Summarization"
"Unsupervised face recognition"
"Unsupervised Facial Landmark Detection"
"Unsupervised Few-Shot Image Classification"
"Unsupervised Human Pose Estimation"
"Unsupervised Image Classification"
"Unsupervised Image Decomposition"
"Unsupervised Image Segmentation"
"Unsupervised Image-To-Image Translation"
"Unsupervised Instance Segmentation"
"Unsupervised Keypoint Estimation"
"Unsupervised KG-to-Text Generation"
"Unsupervised Landmark Detection"
"Unsupervised Machine Translation"
"Unsupervised MNIST"
"Unsupervised Monocular Depth Estimation"
"Unsupervised nucleus segmentation"
"Unsupervised Object Detection"
"Unsupervised Object Localization"
"Unsupervised Object Segmentation"
"Unsupervised Opinion Summarization"
"Unsupervised Panoptic Segmentation"
"Unsupervised Person Re-Identification"
"Unsupervised Pre-training"
"Unsupervised Reinforcement Learning"
"Unsupervised Saliency Detection"
"Unsupervised semantic parsing"
"Unsupervised Semantic Segmentation with Language-image Pre-training"
"Unsupervised Semantic Segmentation"
"Unsupervised Skeleton Based Action Recognition"
"Unsupervised Spatial Clustering"
"Unsupervised Text Classification"
"Unsupervised Text Style Transfer"
"Unsupervised Text Summarization"
"Unsupervised Vehicle Re-Identification"
"Unsupervised Video Object Segmentation"
"Unsupervised Video Summarization"
"US Foreign Policy"
"User Simulation"
"Utterance-level pronounciation scoring"
"Valence Estimation"
"ValNov"
"Variable Detection"
"Variable Disambiguation"
"Variational Inference"
"VCGBench-Diverse"
"Vector Graphics Animation"
"Vector Graphics"
"Vehicle Color Recognition"
"Vehicle Key-Point and Orientation Estimation"
"Vehicle Pose Estimation"
"Vehicle Re-Identification"
"Vehicle Speed Estimation"
"Ventricular fibrillation detection"
"Veracity Classification"
"Vessel Detection"
"VGSI"
"Video & Kinematic Base Workflow Recognition"
"Video Action Detection"
"Video Alignment"
"Video Anomaly Detection"
"Video Based Workflow Recognition"
"Video Boundary Captioning"
"Video Captioning on MS"
"Video Captioning on MSR-VTT"
"Video Captioning"
"Video Chaptering"
"Video Classification"
"Video Compression"
"Video Corpus Moment Retrieval"
"Video Deblurring"
"Video Defect Classification"
"Video Deinterlacing"
"Video Denoising"
"Video deraining"
"Video Description"
"Video Domain Adapation"
"Video Editing"
"Video Emotion Detection"
"Video Emotion Recognition"
"Video Enhancement"
"Video Forensics"
"Video Frame Interpolation"
"Video Generation"
"Video Grounding"
"Video Harmonization"
"Video Inpainting"
"Video Instance Segmentation"
"Video Matting"
"video narration captioning"
"Video Narrative Grounding"
"Video Object Detection"
"Video Object Segmentation"
"Video Object Tracking"
"Video Panoptic Segmentation"
"Video Polyp Segmentation"
"Video Prediction"
"Video Quality Assessment"
"Video Question Answering (Level 3)"
"Video Question Answering (Level 4)"
"Video Question Answering"
"Video Recognition"
"Video Reconstruction"
"Video Restoration"
"Video Retrieval"
"Video Saliency Detection"
"Video Saliency Prediction"
"Video Salient Object Detection"
"Video scene graph generation"
"Video Segmentation"
"Video Semantic Segmentation"
"Video Story QA"
"Video Style Transfer"
"Video Summarization"
"Video Super-Resolution"
"Video Synchronization"
"Video Synopsis"
"Video Temporal Consistency"
"Video to Text Retrieval"
"Video Understanding"
"Video Visual Relation Detection"
"Video Visual Relation Tagging"
"Video-Adverb Retrieval (Unseen Compositions)"
"Video-Adverb Retrieval"
"Video-based Generative Performance Benchmarking (Consistency)"
"Video-based Generative Performance Benchmarking (Contextual Understanding)"
"Video-based Generative Performance Benchmarking (Correctness of Information)"
"Video-based Generative Performance Benchmarking (Detail Orientation))"
"Video-based Generative Performance Benchmarking (Temporal Understanding)"
"Video-based Generative Performance Benchmarking"
"Video-Based Person Re-Identification"
"Video-Text Retrieval"
"Video-to-image Affordance Grounding"
"Video-to-Shop"
"Video-to-Sound Generation"
"Video-to-Video Synthesis"
"Video, Kinematic & Segmentation Base Workflow Recognition"
"Vietnamese Datasets"
"Vietnamese Machine Reading Comprehension"
"Vietnamese Natural Language Inference"
"Vietnamese Text Diacritization"
"Viewpoint Estimation"
"Vignetting Removal"
"Violence and Weaponized Violence Detection"
"Virology"
"Virtual Try-Off"
"Virtual Try-on (Model2Street)"
"Virtual Try-on (Shop2Street)"
"Virtual Try-on (Street2Street)"
"Virtual Try-on"
"Visibility Tracking"
"Vision and Language Navigation"
"Vision-Language Navigation"
"Vision-Language-Action"
"Visual Abductive Reasoning"
"Visual Analogies"
"Visual Commonsense Reasoning"
"Visual Commonsense Tests"
"Visual Crowd Analysis"
"Visual Dialog"
"Visual Entailment"
"Visual Grounding"
"visual instruction following"
"Visual Keyword Spotting"
"Visual Localization"
"Visual Navigation"
"Visual Object Tracking"
"Visual Odometry"
"Visual Place Recognition"
"Visual Prompt Tuning"
"Visual Question Answering (VQA) Split A"
"Visual Question Answering (VQA) Split B"
"Visual Question Answering (VQA)"
"Visual Question Answering"
"Visual Reasoning"
"Visual Relationship Detection"
"Visual Social Relationship Recognition"
"Visual Speech Recognition"
"Visual Storytelling"
"Visual Text Correction"
"Visual Tracking"
"Vocal Bursts Intensity Prediction"
"Vocal Bursts Type Prediction"
"Vocal Bursts Valence Prediction"
"Vocal ensemble separation"
"Vocal technique classification"
"Voice Anti-spoofing"
"Voice Cloning"
"Voice Conversion"
"Voice pathology detection"
"Voice Query Recognition"
"Volumetric Medical Image Segmentation"
"Vulnerability Detection"
"W-R-L-D Sleep Staging"
"W-R-N Sleep Staging"
"Water3D_Long"
"WaterDrop2D"
"Weakly Supervised 3D Detection"
"Weakly Supervised 3D Point Cloud Segmentation"
"Weakly Supervised Action Localization"
"Weakly Supervised Action Segmentation (Action Set))"
"Weakly Supervised Action Segmentation (Transcript)"
"Weakly Supervised Classification"
"Weakly Supervised Data Denoising"
"Weakly Supervised Defect Detection"
"Weakly Supervised Object Detection"
"Weakly supervised segmentation"
"Weakly supervised Semantic Segmentation"
"Weakly-supervised 3D Human Pose Estimation"
"Weakly-supervised Anomaly Detection"
"Weakly-supervised instance segmentation"
"Weakly-Supervised Named Entity Recognition"
"Weakly-Supervised Object Localization"
"Weakly-Supervised Object Segmentation"
"Weakly-Supervised Semantic Segmentation"
"Weakly-supervised Temporal Action Localization"
"Weakly-supervised Video Anomaly Detection"
"Weather Forecasting"
"Webcam (RGB) image classification"
"Webpage Object Detection"
"Website Fingerprinting Attacks"
"Website Fingerprinting Defense"
"whole slide images"
"WiFi CSI-based Image Reconstruction"
"Wildly Unsupervised Domain Adaptation"
"Winogrande"
"Within-Session ERP"
"Within-Session Motor Imagery (all classes)"
"Within-Session Motor Imagery (left hand vs. right hand)"
"Within-Session Motor Imagery (right hand vs. feet)"
"Within-Session SSVEP"
"Word Alignment"
"Word Embeddings"
"Word Sense Disambiguation"
"Word Sense Induction"
"Word Similarity"
"Word Spotting In Handwritten Documents"
"Word Translation"
"Word-level pronunciation scoring"
"Workflow Discovery"
"World Knowledge"
"World Religions"
"X-ray PDF regression"
"X-ray Visual Question Answering"
"XLM-R"
"XRD regression"
"Zero Shot on BEIR (Inference Free Model)"
"Zero Shot Prompting Danger Assessment"
"Zero Shot Segmentation"
"Zero Shot Skeletal Action Recognition"
"Zero-shot 3D classification"
"Zero-shot 3D Point Cloud Classification"
"Zero-shot 3D Point Cloud Classificationclassification"
"Zero-Shot Action Detection"
"Zero-Shot Action Recognition"
"zero-shot anomaly detection"
"Zero-shot Audio Captioning"
"Zero-shot Audio Classification"
"Zero-Shot Audio Retrieval"
"Zero-shot Classification (unified classes)"
"Zero-Shot Composed Image Retrieval (ZS-CIR)"
"Zero-shot Composed Person Retrieval"
"Zero-Shot Counting"
"Zero-shot Cross-lingual Fact-checking"
"Zero-Shot Cross-Lingual Image-to-Text Retrieval"
"Zero-Shot Cross-Lingual Text-to-Image Retrieval"
"Zero-Shot Cross-Lingual Transfer"
"Zero-Shot Cross-Lingual Visual Natural Language Inference"
"Zero-Shot Cross-Lingual Visual Question Answering"
"Zero-Shot Cross-Lingual Visual Reasoning"
"Zero-Shot Cross-Modal Retrieval"
"Zero-shot dense video captioning"
"Zero-Shot Environment Sound Classification"
"Zero-Shot Facial Expression Recognition"
"Zero-Shot Flowchart Grounded Dialog Response Generation"
"Zero-shot Generalization"
"Zero-Shot Human-Object Interaction Detection"
"Zero-Shot Image Classification"
"Zero-Shot Image Paragraph Captioning"
"Zero-shot Image Retrieval"
"Zero-Shot Instance Segmentation"
"Zero-Shot Intent Classification and Slot Filling"
"Zero-Shot Intent Classification"
"Zero-Shot Learning + Domain Generalization"
"Zero-Shot Learning"
"zero-shot long video question answering"
"Zero-Shot Machine Translation"
"Zero-shot Moment Retrieval"
"Zero-Shot Multi-Speaker TTS"
"Zero-shot Named Entity Recognition (NER)"
"Zero-Shot Object Detection"
"Zero-Shot Out-of-Domain Detection"
"Zero-Shot Region Description"
"Zero-shot Relation Classification"
"Zero-shot Relation Triplet Extraction"
"Zero-shot Scene Classification (unified classes)"
"Zero-Shot Semantic Segmentation"
"Zero-shot Sentiment Classification"
"Zero-Shot Single Object Tracking"
"Zero-shot Slot Filling"
"Zero-Shot Text Classification"
"Zero-shot Text Retrieval"
"Zero-shot Text Search"
"Zero-shot Text to Audio Retrieval"
"Zero-shot Text-to-Image Retrieval"
"Zero-Shot Transfer 3D Point Cloud Classification"
"Zero-Shot Transfer Image Classification (CN)"
"Zero-Shot Transfer Image Classification"
"Zero-Shot Video Question Answer"
"Zero-Shot Video Retrieval"
"Zero-Shot Video-Audio Retrieval"
"Zeroshot Video Question Answer"
"ZSL Video Classification"
