Task Name,Dataset Count,Common Datasets (Top 10),Benchmarks,SOTA Metrics,Subtasks,Categories,Description (First 200 chars)
Person Re-Identification,65,MARS; PRCC; iLIDS-VID; VeRi-Wild; DukeMTMC-attribute; SenseReID; Market1501-Attributes; Dataset: Privacy-Preserving Gaze Data Streaming in Immersive Interactive Virtual Reality: Robustness and User Experience.; Occluded-DukeMTMC; OpeReid,MARS; PRCC; iLIDS-VID; DukeTracklet; CUHK03 (detected); SenseReID; Occluded-DukeMTMC; eSports Sensors Dataset; CCVID; CUHK-SYSU, Rank-1;  mAP (All Search); Rank-1 (Indoor Search);  mINP; mAP; Rank-1; mINP (Indoor Search); Rank-1 (All Search); Rank-20; ROC AUC,Clothes Changing Person Re-Identification; Video-Based Person Re-Identification; Semi-Supervised Person Re-Identification; Large-Scale Person Re-Identification; Image-To-Video Person Re-Identification,Computer Vision,**Person Re-Identification** is a computer vision task in which the goal is to match a person's identity across different cameras or locations in a video or image sequence. It involves detecting and t...
Data Augmentation,63,RPC; InfoTabS; CCPD; GAP Coreference Dataset; UFPR-AMR; Ricordi; ImageNet-Sketch; Evidence Inference; HELP; Tilt-RGBD,GA1457; CIFAR-10; ImageNet,Accuracy (%); Percentage error; Classification Accuracy,Text Augmentation; Image Augmentation,Methodology; Computer Vision; Natural Language Processing,"Data augmentation involves techniques used for increasing the amount of data, based on different modifications, to expand the amount of examples in the original dataset. Data augmentation not only hel..."
Multi-Task Learning,59,JuICe; VidSet; MTL-AQA; SkillSpan; CAL500; CVGL; Photographic Defect Severity; TartanAir; CH-SIMS; FIW-MM,ChestX-ray14; NYUv2; CelebA; UTKFace; QM9; wireframe dataset; Cityscapes test; OMNIGLOT,delta_m; ∆m%; sAP10; Average Accuracy; sAP15; RMSE; Error; mIoU; Mean IoU; FH,Multi-task Language Understanding; Task Arithmetic,Methodology,"Multi-task learning aims to learn multiple different tasks simultaneously while maximizing  performance on one or all of the tasks.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Cross-sti..."
Self-Supervised Learning,47,ImageNet-100 (TEMI Split); MotionSense; ApartmenTour; MAX-60K; Open Radar Datasets; diaforge-utc-r-0725; Wild-Time; Extended Agriculture-Vision; STL-10; CREMA-D,cifar10; ImageNet-100 (TEMI Split); Tiny ImageNet; cifar100; CIFAR-100; TinyImageNet; DABS; STL-10; CREMA-D; CIFAR-10,Natural Images; Text; Accuracy; Images & Text; Top-1 Accuracy; average top-1 classification accuracy; Speech; Med. Imaging; Sensors,Unsupervised Video Clustering; Point Cloud Pre-training,Computer Vision,"**Self-Supervised Learning** is proposed for utilizing unlabeled data with the success of supervised learning. Producing a dataset with good labels is expensive, while unlabeled data is being generate..."
Time Series Forecasting,46,USNA-Cn2 (short-duration); ETT; Hotel; Beam-Level (5G) Time-Series Dataset; ExtMarker; Regional AQ Datasets; Weather2K; PJM(AEP); BOOM; AgeGroup Transactions MTPP,Weather2K1786 (336); USNA-Cn2 (short-duration); Weather2K114 (720); ETTh2 (24) Multivariate; ETTh2 (48) Univariate; ETTh2 (96) Univariate; ETTh1 (48) Multivariate; Exchange (720); Weather2K114 (336); ETTh1 (336) Multivariate,9 steps MAE; MAPE; Accuracy; MAE; RMSE; MSE; MSE ,Probabilistic Time Series Forecasting; New Product Sales Forecasting; Correlated Time Series Forecasting; Univariate Time Series Forecasting; COVID-19 Tracking,Time Series,"**Time Series Forecasting** is the task of fitting a model to historical, time-stamped data in order to predict future values. Traditional approaches include moving average, exponential smoothing, and..."
Entity Linking,44,EC-FUNSD; CareerCoach 2022; MEIR; IBM Debater Mention Detection Benchmark; FreebaseQA; Rare Diseases Mentions in MIMIC-III; Twitter-MEL; KILT; STEM-ECR; BC7 NLM-Chem,EC-FUNSD; OKE-2016; KILT: WNED-CWEB; KILT: WNED-WIKI; Rare Diseases Mentions in MIMIC-III; BC7 NLM-Chem; REBEL; KILT: AIDA-YAGO2; CoNLL-Aida; N3-RSS-500,Task 1 Accuracy: general purpose; KILT-AC; Micro-F1; Task 1 Accuracy: domain specific; Macro F1; R-Prec; Unnormalized Accuracy; Task 2 Accuracy: general purpose; Task 2 Accuracy: all; Accuracy,N/A,Natural Language Processing,"Assigning a unique identity to entities (such as famous individuals, locations, or companies) mentioned in text (Source: Wikipedia)."
Misinformation,44,CoAID; RAWFC; Hateful Memes; MEIR; UPFD-POL; CoVaxFrames; Hateful Memes Challenge; Some Like it Hoax; UPFD-GOS; Analysing state-backed propaganda websites: a new dataset and linguistic study,NLP4IF-2021--Fighting the COVID-19 Infodemic ,Average F1,N/A,Miscellaneous,N/A
Decision Making,40,ROAD; CoAID; BLVD; Car_Price_Prediction; D4RL; Evidence Inference; LogiQA; ShARC; Negotiation Dialogues Dataset; NASA C-MAPSS,./01/01/1967; NASA C-MAPSS,0..5sec; Average Remaining Cycles,Imitation Learning,Methodology; Reasoning,N/A
Continual Learning,35,TiROD; ROAD; mini-Imagenet; Visual Domain Decathlon; CRL-Person; TemporalWiki; Wild-Time; F-CelebA (10 tasks); DSC (10 tasks); Oxford 102 Flower,Stanford Cars (Fine-grained 6 Tasks); ImageNet (Fine-grained 6 Tasks); Cifar100 (10 tasks); Split MNIST (5 tasks); CUB-200-2011 (20 tasks) - 1 epoch; Rotated MNIST; F-CelebA (10 tasks); DSC (10 tasks); AIDS; Wikiart (Fine-grained 6 Tasks),MLP Hidden Layers-width; Accuracy; Top 1 Accuracy %; Average Accuracy; Avg. Accuracy; F1 - macro; Pretrained/Transfer Learning; Acc; 1:3 Accuracy; decathlon discipline (Score),Continual Panoptic Segmentation; TiROD; Continual Named Entity Recognition; Class Incremental Learning; unsupervised class-incremental learning,Methodology; Computer Vision; Natural Language Processing,"**Continual Learning** (also known as **Incremental Learning**, **Life-long Learning**) is a concept to learn a model for a large number of tasks sequentially without forgetting knowledge obtained fro..."
Metric Learning,33,TopLogo-10; VeRi-Wild; Structured3D; WildDeepfake; In-Shop; MovieGraphs; N-Digit MNIST; OpeReid; IMEMNET; DyML-Animal,DyML-Vehicle; DyML-Animal; DyML-Product; CARS196;  CUB-200-2011; In-Shop; CUB-200-2011; Stanford Online Products,Average-mAP; R@1,N/A,Methodology; Computer Vision,The goal of **Metric Learning** is to learn a representation function that maps objects into an embedded space. The distance in the embedded space should preserve the objects’ similarity — similar obj...
regression,31,MineralImage5k; Car_Price_Prediction; Meta Omnium; MAX-60K; Regional AQ Datasets; Brightfield vs Fluorescent Staining Dataset; Satlas; LimeSoDa; DRIFT; Medical Cost Personal Dataset,ADORE; Car_Price_Prediction; Medical Cost Personal Dataset; California Housing Prices; Concrete Compressive Strength; Synthetic: y = x * sin x,R2 Score; chemical macro-average RMSE; R Squared; lambda; micro-averaged RMSE,Travel Time Estimation; quantile regression,Miscellaneous; Methodology; Computer Vision; Time Series,N/A
Drug Discovery,26,LIT-PCBA(KAT2A); LIT-PCBA(MAPK1); ESOL (Estimated SOLubility); HIV (Human Immunodeficiency Virus); LIT-PCBA(ALDH1); SIDER; DrugBank; MUV; BBBP (Blood-Brain Barrier Penetration); DAVIS-DTA,LIT-PCBA(KAT2A); LIT-PCBA(MAPK1); ESOL (Estimated SOLubility); BBBP; LIT-PCBA(ALDH1); KIBA; SIDER; MUV; BBBP (Blood-Brain Barrier Penetration); DAVIS-DTA,CI; AUC; Pearson Correlation; Error ratio; MSE; RMSE; Success; Diversity,Drug Response Prediction; Therapeutics Data Commons; Text-based de novo Molecule Generation,Medical,"Drug discovery is the task of applying machine learning to discover new candidate drugs.    <span style=""color:grey; opacity: 0.6"">( Image credit: [A Turing Test for Molecular Generators](https://pubs..."
Time Series Analysis,25,FedTADBench; MOSAD; Hotel; Speech Commands; eSports Sensors Dataset; SMD; ESA-AD; Sen4AgriNet; A Simulated 4-DOF Ship Motion Dataset for System Identification under Environmental Disturbances; EigenWorms,Ventilator Pressure Prediction; PhysioNet Challenge 2012; Speech Commands,F1; % Test Accuracy (Raw Data); % Test Accuracy; MAE,Semi-supervised time series classification; Model Optimization; Time Series Averaging; Time Series Clustering; Time Series Anomaly Detection,Time Series,"**Time Series Analysis** is a statistical technique used to analyze and model time-based data. It is used in various fields such as finance, economics, and engineering to analyze patterns and trends i..."
Fairness,23,DiveFace; HELP; ec-darkpattern; CI-MNIST; RFW; MIAP; TwinViews-13k; ICLR Database; FairFace; fNIRS2MW,BAF – Variant V; BAF – Variant II; UTKFace; DiveFace; BAF – Variant I; BAF – Variant III; BAF – Variant IV; MORPH; BAF – Base,Predictive Equality (age); Degree of Bias (DoB),Exposure Fairness,Miscellaneous; Computer Vision; Adversarial,N/A
Multimodal Deep Learning,22,AU Dataset for Visuo-Haptic Object Recognition for Robots; VIDIMU: Multimodal video and IMU kinematic dataset on daily life activities using affordable devices; Multimodal PISA; MIMIC Meme Dataset; Mudestreda; LUMA; Boombox; ScienceQA; CUB-200-2011; OLIVES Dataset,CUB-200-2011,Accuracy,Multimodal Text and Image Classification,Methodology; Natural Language Processing,"**Multimodal deep learning** is a type of deep learning that combines information from multiple modalities, such as text, image, audio, and video, to make more accurate and comprehensive predictions. ..."
Robotic Grasping,22,Grasp MultiObject; Cornell; A Billion Ways to Grasp; Multiview Manipulation Data; HouseCat6D; NBMOD; EGAD; CONG; GraspClutter6D; The RobotriX,NBMOD; Cornell Grasp Dataset; GraspNet-1Billion;  Jacquard dataset,AP_similar; AP_seen; Acc; mAP; Accuracy (%); 5 fold cross validation; AP_novel,Grasp Contact Prediction,Miscellaneous; Robots,This task is composed of using Deep Learning to identify how best to grasp objects using robotic arms in different scenarios. This is a very complex task as it might involve dynamic environments and o...
Graph Regression,21,ESOL (Estimated SOLubility); GlassTemp; OCB; dockstring; GVLQA; PEM Fuel Cell Dataset; DrivAerNet; MD22; Cylinder in Crossflow; ZINC,PGR ; ZINC-full; QM9: UATOM; GlassTemp; PCQM4M-LSC; QM9: del e; ESOL; KIT; F2; Tox21 ,Validation MAE; Inference Time (ms); MAE; Test MAE; RMSE; R2; RMSE@80%Train; AUC@80%Train; RMSE ,N/A,Graphs,The regression task is similar to graph classification but using different loss function and performance metric.
Imitation Learning,21,Simitate; CARLA; AirSim; Reactive Diffusion Policy-Dataset; CoDraw; INTERACTION Dataset; DeformPAM-Dataset; StarData; MineRL; AI2-THOR,N/A,N/A,Behavioural cloning,Methodology,"**Imitation Learning** is a framework for learning a behavior policy from demonstrations. Usually, demonstrations are presented in the form of state-action trajectories, with each pair indicating the ..."
Age Estimation,20,Facial  Skeletal angles; VGGFace2 HQ; PhysioNet Challenge 2021; AgeDB; mebeblurf; Bone Age; IMDB-WIKI; USF; MORPH; LAGENDA,MORPH Album2 (SE); MORPH Album2; ChaLearn 2016; ChaLearn 2015; PhysioNet Challenge 2021; UTKFace; FGNET; AgeDB; KANFace; mebeblurf,Mean absolute error; Accuracy; Mean Absolute Error (cross-val); MAE; e-error; Mean Squared Error (cross-val); CS; Mean Squared Error; Average mean absolute error,Few-shot Age Estimation,Computer Vision,"Age Estimation is the task of estimating the age of a person from an image some other kind of data.    <span style=""color:grey; opacity: 0.6"">( Image credit: [BridgeNet](https://arxiv.org/pdf/1904.033..."
Reinforcement Learning (RL),20,PRM800K; RoomEnv-v2; lilGym; PushWorld; MIKASA-Robo Dataset; Gun Detection Dataset; opin-pref; FinRL-Meta; EMMOE-100; Avalon,.; ProcGen,0..5sec; Mean Normalized Performance,3D Point Cloud Reinforcement Learning; RoomEnv-v0; Off-policy evaluation; RoomEnv-v2; Multi-Objective Reinforcement Learning,Methodology; Computer Vision; Computer Code; Knowledge Base,**Reinforcement Learning (RL)** involves training an agent to take actions in an environment to maximize a cumulative reward signal. The agent interacts with the environment and learns by receiving fe...
Graph Clustering,19,Netzschleuder; 97 synthetic datasets; Biase et al; Deng et al; Orkut; TrackML challenge Throughput phase dataset; Perfume Co-Preference Network; Pollen et al; Friendster; SLNET,Citeseer; Goolam et al; Bozec et al; Pollen et al; Biase et al; Deng et al; Treutlein et al; Cora; Pubmed; Yan et al,F1; F score; ARI; Adjusted Rand Index; NMI; ACC; Precision,Clustering Ensemble,Graphs,"**Graph Clustering** is the process of grouping the nodes of the graph into clusters, taking into account the edge structure of the graph in such a way that there are several edges within each cluster..."
Quantization,18,Kannada-MNIST; Tiny Images; REDDIT-12K; LFW; ImageNet; Bach Doodle; Groove; Wiki-40B; FAS100K; Twitter100k,AgeDB-30; Wiki-40B; ImageNet; LFW; Knowledge-based:; CFP-FP; CIFAR-10; IJB-C; COCO (Common Objects in Context); IJB-B,Accuracy; Perplexity; MAP; Activation bits; Top-1 Accuracy (%); TAR @ FAR=1e-4; Weight bits; All,Data Free Quantization; UNET Quantization,Methodology; Computer Vision,"**Quantization** is a promising technique to reduce the computation cost of neural network training, which can replace high-cost floating-point numbers (e.g., float32) with low-cost fixed-point number..."
Multivariate Time Series Forecasting,18,MIMIC-III; PhysioNet Challenge 2012; PeMS07; Weather; EarthNet2021; ETT; PeMSD7; MuJoCo; Electricity; PeMS04,MIMIC-III; PhysioNet Challenge 2012; ETTh1 (336) Multivariate; Weather; ETTh1 (48) Multivariate; MuJoCo; ETTh1 (720) Multivariate; ETTh1 (96) Multivariate; Electricity; ExtMarker,"Accuracy; 12 steps RMSE; normalized RMSE; MAE; RMSE; NegLL; MSE; Maximum error; Jitter; MSE (10^-2, 50% missing)",GLinear,Time Series,N/A
Time Series,18,Detecting Security Patches Via Behavioral Data; Monopedal Gaits; edeniss2020; HASCD; Weather; BorealTC; MSL; Sales; QM9; stocknet,N/A,N/A,N/A,N/A,N/A
Binarization,17,DIBCO 2011; FDST; H-DIBCO 2012; ImageNet; DIBCO 2013; H-DIBCO 2016; H-DIBCO 2014; LRDE DBD; DIBCO 2009; CIFAR-100,N/A,N/A,N/A,N/A,N/A
Topic Models,17,AG News; Reddit Ideology Database; MIE Articles Dataset (1996-2024); COVID-19 Twitter Chatter Dataset; Fashion 144K; PostNauka; 20 Newsgroups; 20NewsGroups; RuWiki-Good; OpoSum,AG News; NYT; 20 Newsgroups; 20NewsGroups; Arxiv HEP-TH citation graph; AgNews,NPMI; Topic coherence@5; Test perplexity; C_v; MACC; Topic Coherence@50,Dynamic Topic Modeling; Topic coverage,Natural Language Processing,"A topic model is a type of statistical model for discovering the abstract ""topics"" that occur in a collection of documents. Topic modeling is a frequently used text-mining tool for the discovery of hi..."
Meta-Learning,17,ExtremeWeather; Meta-World Benchmark; CODEBRIM; Chinese Literature NER RE; L-Bird; Meta-Dataset; MEx; MQ2007; BlendedMVS; Kuzushiji-49,"MT50; ML10; ML45; OMNIGLOT - 1-Shot, 20-way",Average Success Rate; Meta-train success rate; Meta-test success rate; % Test Accuracy; Meta-test success rate (zero-shot),universal meta-learning; Few-Shot Learning; Sample Probing,Methodology; Computer Vision,"Meta-learning is a methodology considered with ""learning to learn"" machine learning algorithms.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Model-Agnostic Meta-Learning for Fast Adaptat..."
SMAC+,17,Off_Complicated_sequential; Def_Outnumbered_sequential; Off_Near_parallel; Off_Near_sequential; Off_Superhard_parallel; Off_Superhard_sequential; Def_Infantry_sequential; Off_Hard_sequential; Off_Complicated_parallel; Off_Distant_parallel,N/A,N/A,N/A,N/A,N/A
Long-tail Learning,16,ImageNet-LT; VOC-MLT; Kinetics; CelebA; Imbalanced-MiniKinetics200; COCO-MLT; Places-LT; Lot-insts; NIH-CXR-LT; MIMIC-CXR-LT,CIFAR-10-LT (ρ=200); CIFAR-100-LT (ρ=10); iNaturalist 2018; ImageNet-GLT; mini-ImageNet-LT; Lot-insts; CIFAR-10-LT (ρ=100); COCO-MLT; ImageNet-LT; NIH-CXR-LT,Accuracy; Top-1 Accuracy; Average Precision; Error Rate; Average mAP; Average Recall; Balanced Accuracy; Top 1 Accuracy;  Macro-F1,Long-tail learning with class descriptors,Methodology,"Long-tailed learning, one of the most challenging problems in visual recognition, aims to train well-performing models from a large number of images that follow a long-tailed class distribution."
Prompt Engineering,16,EuroSAT; Food-101; OmniBenchmark; ImageNet; UCF101; Oxford-IIIT Pets; ImageNet-A; SUN397; Oxford-IIIT Pet Dataset; DTD,EuroSAT; Food-101; ImageNet; ImageNet V2; UCF101; ImageNet-R; DTD; ImageNet-21k; Oxford-IIIT Pet Dataset; SUN397,Top-1 accuracy %; Harmonic mean; Accuracy,Visual Prompting,Computer Vision; Natural Language Processing,"**Prompt engineering** is the process of designing and refining the prompts used to generate text from language models, such as GPT-3 or similar models. The goal of prompt engineering is to improve th..."
Contrastive Learning,16,"FSD50K; INRIA Aerial Image Labeling; STL-10; ImageNet; CommitBART; BankNote-Net; 10,000 People - Human Pose Recognition Data; BIOSCAN-5M; US-4; OLIVES Dataset","imagenet-1k; STL-10; CIFAR-10; 10,000 People - Human Pose Recognition Data",Accuracy (Top-1); ImageNet Top-1 Accuracy; 0..5sec,N/A,Methodology; Computer Vision,**Contrastive Learning** is a deep learning technique for unsupervised representation learning. The goal is to learn a representation of data such that similar instances are close together in the repr...
Learning with noisy labels,16,Food-101; VoxCeleb1; Red MiniImageNet 40% label noise; Galaxy Zoo DECaLS; CIFAR-10N; COCO-WAN (Medium noise); WebVision; Red MiniImageNet 80% label noise; CIFAR-100; CIFAR-100N,N/A,N/A,N/A,N/A,N/A
Self-Driving Cars,16,Lost and Found; SOD; Argoverse 2 Motion Forecasting; OC; HPD; BDD-X; SESIV; TUT Sound Events 2018; FRSign; Talk2Car,N/A,N/A,N/A,N/A,N/A
Weather Forecasting,16,ExtremeWeather; CloudCast; Extreme Events > Natural Disasters > Hurricane; Tornet; GFF; nuScenes; Shifts; WeatherBench; NOAA Atmospheric Temperature Dataset; Narvik Road Dataset,NOAA Atmospheric Temperature Dataset; Shifts; LA; SD; SEVIR,MSE (t+6); mCSI; MAE (t+1); MSE; R-AUC MSE; MAE (t+10); MSE (t+1),Solar Irradiance Forecasting,Miscellaneous; Time Series,"**Weather Forecasting** is the prediction of future weather conditions such as precipitation, temperature, pressure and wind.   <span class=""description-source"">Source: [MetNet: A Neural Weather Model..."
Node Clustering,15,WebKB; Amazon Photo; Citeseer; Brazil Air-Traffic; DBLP; arXivCS; Wiki-CS; Facebook Pages; Wiki; Cornell,N/A,N/A,N/A,N/A,N/A
Physical Simulations,15,ClimART; A Simulated 4-DOF Ship Motion Dataset for System Identification under Environmental Disturbances; Dataset and Model Weights for Plasma Sheet Model Graph Network Simulator; Expressive Gaussian mixture models for high-dimensional statistical modelling: simulated data and neural network model files; ABC Dataset; CAMELS Multifield Dataset; 2D_NACA_RANS; DrivAerNet; PlasticineLab; 4D-DRESS,Sphere Simple; Deforming Plate; Deformable Plate; 4D-DRESS; Impact Plate,Stretching Energy; Rollout RMSE-all [1e3] Stress; Chamfer (cm); Rollout RMSE-all [1e3] Position,Liquid Simulation; Optical Tweezers Simulations; Neural Network simulation,Miscellaneous; Computer Code,N/A
Within-Session ERP,15,BrainInvaders2013a MOABB; BrainInvaders2012 MOABB; BNCI2014-009 MOABB; BNCI2014-008 MOABB; BrainInvaders2014b MOABB; Sosulski2019 MOABB; BrainInvaders2015a MOABB; Huebner2018 MOABB; EPFLP300 MOABB; BrainInvaders2014a MOABB,N/A,N/A,N/A,N/A,N/A
Density Estimation,13,UCI Machine Learning Repository; Silhouettes; BSD; JHU-CROWD++; UCF-CC-50; PEM Fuel Cell Dataset; APRICOT; CelebA-HQ; COWC; Caltech-101,Freyfaces; UCI GAS; UCI POWER; UCI HEPMASS; ImageNet 32x32; OMNIGLOT; ImageNet 64x64; UCI MINIBOONE; Caltech-101; BSDS300,MMD-CD; COV-L2; Log-likelihood; MMD-L2; Log-likelihood (nats); EMD; MMD-EMD; NLL; Negative ELBO; NLL (bits/dim),Arbitrary Conditional Density Estimation,Methodology,"The goal of **Density Estimation** is to give an accurate description of the underlying probabilistic density distribution of an observable data set with unknown density.   <span class=""description-so..."
Clustering Algorithms Evaluation,13,97 synthetic datasets; JAFFE; Satimage; seeds; Failure-Dataset-OpenStack; Online retail dataset; iris; pathbased; Olivetti face; MNIST,97 synthetic datasets; MNIST; JAFFE; seeds; pathbased; iris; Olivetti face; ionosphere; Fashion-MNIST; pixraw10P,HIT-THE-BEST; ARI; Purity; Rank difference; NMI; F1-score,Novel Class Discovery,Methodology,N/A
Adversarial Robustness,13,ImageNet-Patch; ImageNet; Stylized ImageNet; AdvSuffixes; OSN-transmission_mini_CelebA; ImageNet-A; CIFAR-100; AdvGLUE; SHADR; Speech Robust Bench,ImageNet; Stylized ImageNet; ImageNet-C; ImageNet-A; CIFAR-100; AdvGLUE; CIFAR-10,Attack: AutoAttack; AutoAttacked Accuracy; Accuracy; mean Corruption Error (mCE); Robust Accuracy; Clean Accuracy,N/A,Adversarial,Adversarial Robustness evaluates the vulnerabilities of machine learning models under various types of adversarial attacks.
UIE,13,WikiANN; SUIM-E; ACE 2004; CoNLL 2003; WNUT 2017; FindVehicle; BC5CDR; SciERC; New York Times Annotated Corpus; BC2GM,N/A,N/A,N/A,N/A,N/A
Entity Disambiguation,13,DocRED-IE; Mewsli-9; Hansel; CoNLL; ShadowLink; ACE 2004; AQUAINT; Wikidata-Disamb; TAC 2010; AIDA CoNLL-YAGO,AIDA-CoNLL; DocRED-IE; Mewsli-9; AQUAINT; ShadowLink-Shadow; ACE2004; TAC2010; WNED-WIKI; MSNBC; ShadowLink-Top,Avg F1; Micro Precision; In-KB Accuracy; Micro-F1,N/A,Natural Language Processing,"**Entity Disambiguation** is the task of linking mentions of ambiguous entities to their referent entities in a knowledge base such as Wikipedia.   <span class=""description-source"">Source: [Leveraging..."
Open-Domain Dialog,13,DuLeMon; ProsocialDialog; MultiDoc2Dial; ClovaCall; CPsyCounD; MMDialog; MMChat; CPsyCounE; KILT; Reddit Conversation Corpus,KILT: Wizard of Wikipedia,F1; ROUGE-L; Recall@5; KILT-F1; R-Prec; KILT-RL,Dialogue Evaluation,Natural Language Processing,N/A
Disentanglement,13,KITTI-Masks; dSprites; Causal3DIdent; Natural Sprites; Coil100-Augmented; 3DIdent; Fitness-AQA; 3D Shapes Dataset; XYSquares; Sprites,N/A,N/A,N/A,N/A,N/A
Graph Embedding,12,Synthetic Dynamic Networks; ChEMBL; IS-A; OLPBENCH; AIDS Antiviral Screen; ERA; KG20C; CARD-660; BioGRID; LastFM Asia,Barabasi-Albert,Entropy Difference,Structural Node Embedding; Knowledge Graph Embedding; Dynamic graph embedding; Knowledge Graph Embeddings; Role Embedding,Graphs,"Graph embeddings learn a mapping from a network to a vector space, while preserving relevant network properties.    <span style=""color:grey; opacity: 0.6"">( Image credit: [GAT](https://github.com/Peta..."
Imputation,12,WebKB; PhysioNet Challenge 2012; FDF; MotionSense; Adult; AIDS Antiviral Screen; PulseImpute; Ecoli; METR-LA Point Missing; PEMS-BAY Point Missing,Adult; HMNIST; PhysioNet Challenge 2012; Sprites,AUROC; NLL; Test error; MSE,Multivariate Time Series Imputation,Computer Vision; Time Series,Substituting missing data with values according to some criteria.
Entity Typing,12,DocRED-IE; Open Entity; CoNLL; AIDA CoNLL-YAGO; Few-NERD; WikiSRS; FIGER; HTDM; GUM; Figment,AIDA-CoNLL; DocRED-IE; Open Entity; Freebase FIGER; OntoNotes; FIGER; Ontonotes v5 (English);  Open Entity,F1; Avg F1; BEP; Accuracy; Micro-F1; Micro F1; Recall; Macro F1; Precision; P@1,Entity Typing on DH-KGs,Natural Language Processing,"**Entity Typing** is an important task in text analysis. Assigning types (e.g., person, location, organization) to mentions of entities in documents enables effective structured analysis of unstructur..."
COVID-19 Diagnosis,12,COVIDGR; BrixIA; CoIR; Synthetic COVID-19 CXR Dataset; COVIDx CXR-3; COVID-19-CT-CXR; BIMCV COVID-19; COVID-CT; Novel COVID-19 Chestxray Repository; Large COVID-19 CT scan slice dataset,COVIDGR; ; Novel COVID-19 Chestxray Repository; COVIDx; COVID-19 CXR Dataset; Large COVID-19 CT scan slice dataset; COVIDx CXR-3; Covid-19 Cough Cambridge,10 fold Cross validation; Macro Recall; Accuracy; Macro Precision; AUC-ROC; Average Precision; AUC; Average F1; Per-Class Accuracy; Specificity,N/A,Medical,Covid-19 Diagnosis is the task of diagnosing the presence of COVID-19 in an individual with machine learning.
Referring Expression Comprehension,12,ColonINST-v1 (Seen); ColonINST-v1 (Unseen); A Game Of Sorts; Google Refexp; Talk2Car; Description Detection Dataset; FineCops-Ref; ColonINST-v1; VQDv1; RefCOCO,N/A,N/A,N/A,N/A,N/A
Entity Resolution,12,PIZZA; Binette's 2022 Inventors Benchmark; Abt-Buy; Weibo-Douban; MovieGraphBenchmark; CEREC; Amazon-Google; DBLP Temporal; WDC LSPM; MusicBrainz20K,WDC Products-50%cc-unseen-medium; WDC Computers-xlarge; WDC Watches-xlarge; Abt-Buy; WDC Products-80%cc-seen-medium; Amazon-Google; MusicBrainz20K; WDC Products-80%cc-seen-medium-multi; WDC Products; WDC Computers-small,F1 Micro; F1; F1 (%),Blocking,Natural Language Processing,"**Entity resolution** (also known as entity matching, record linkage, or duplicate detection) is the task of finding records that refer to the same real-world entity across different data sources (e.g..."
Robot Manipulation,12,FurnitureBench DiffIK sim demos; pick_screw; SimplerEnv-Google Robot; RLBench; robosuite Benchmark; SimplerEnv-Widow X; CALVIN; MIKASA-Robo Dataset; MotIF-1K; YCB-Slide,RLBench; SimplerEnv-Widow X; CALVIN; SimplerEnv-Google Robot; MimicGen,"Average; Variant Aggregation-Open/Close Drawer; Put Eggplant  in Yellow Basket; Succ. Rate (12 tasks, 100 demo/task); Succ. Rate (10 tasks, 100 demos/task); Variant Aggregation-Pick Coke Can; Visual Matching-Open/Close Drawer; Training Time (A100 x hour); Stack Green Block on Yellow Block; Put Spoon on Towel",Robot Manipulation Generalization; Contact-rich Manipulation; Deformable Object Manipulation,Robots,N/A
Multiple-choice,12,OllaBench v.0.2; MATH-V; WorldCuisines; Belebele; MMInstruct-GPT4V; TUMTraffic-VideoQA; Probability words NLI; Tasksource; Neptune; MathVista,N/A,N/A,N/A,N/A,N/A
Stochastic Optimization,11,CoLA; GLUE; Amazon Product Data; CIFAR-100; OpeReid; AG News; Penn Treebank; MNIST-8M; MNIST; MSRA Hand,CoLA; ImageNet ResNet-50 - 60 Epochs; CIFAR-100 WRN-28-10 - 200 Epochs; ImageNet ResNet-50 - 50 Epochs; CIFAR-10 ResNet-18 - 200 Epochs; CIFAR-100; Penn Treebank (Character Level) 3x1000 LSTM - 500 Epochs; ImageNet ResNet-50 - 90 Epochs; AG News; MNIST,Accuracy (mean); Accuracy; Top 5 Accuracy; Bit per Character (BPC); Accuracy (max); NLL; Top 1 Accuracy,Evolutionary Algorithms; Distributed Optimization,Miscellaneous; Methodology,**Stochastic Optimization** is the task of optimizing certain objective functional by generating and using stochastic random variables. Usually the Stochastic Optimization is an iterative process of g...
Unsupervised Person Re-Identification,11,MARS; PRCC; iLIDS-VID; DukeMTMC-VideoReID; ClonedPerson; LTCC; VC-Clothes; Market-1501; PRID2011; DukeMTMC-reID,N/A,N/A,N/A,N/A,N/A
Adversarial Attack,11,AdvSuffixes; REAP; WSJ0-2mix; Cifar10Mnist; ImageNet-P; NAS-Bench-1Shot1; CIFAR-100; PointDenoisingBenchmark; comma 2k19; TCAB,CIFAR-100; WSJ0-2mix; CIFAR-10,Attack: DeepFool; Attack: AutoAttack; Robust Accuracy; Attack: PGD20; SDR,Adversarial Attack Detection; Backdoor Attack; Real-World Adversarial Attack; Adversarial Text,Computer Vision; Adversarial,An **Adversarial Attack** is a technique to find a perturbation that changes the prediction of a machine learning model. The perturbation can be very small and imperceptible to human eyes.   <span cla...
Virtual Try-on,11,VITON-HD; Deep Fashion3D; Dress Code; STRAT; MoVi; VITON; Fashion IQ; DeepFashion; StreetTryOn; MPV,VITON-HD; Microsoft COCO dataset; UBC Fashion Videos; VITON; FashionIQ; StreetTryOn; MPV; Deep-Fashion; Dress Code,IS; LPIPS; 10 fold Cross validation; FID; FVD; KID; SSIM; PSNR; SWD,N/A,Computer Vision,Virtual try-on of clothing or other items such as glasses and makeup. Most recent techniques use Generative Adversarial Networks.
Federated Learning,11,UniMiB SHAR; FedTADBench; Street Dataset; adVFed; LEAF Benchmark; SMM4H; Customer Support on Twitter; Speech Commands; SAMSum; CC-19,"CIFAR-100 (alpha=0, 10 clients per round); CIFAR-100 (alpha=0.5, 5 clients per round); CIFAR-100 (alpha=1000, 10 clients per round); CIFAR-100 (alpha=0, 20 clients per round); CIFAR-100 (alpha=1000, 5 clients per round); Landmarks-User-160k; CIFAR-100 (alpha=1000, 20 clients per round); CIFAR-100 (alpha=0.5, 10 clients per round); CIFAR100 (alpha=0.3, 10 clients per round); Cityscapes heterogeneous",mIoU; Acc@1-1262Clients; ACC@1-100Clients; Average Top-1 Accuracy,Contribution Assessment; Collaborative Fairness; Model Posioning; Vertical Federated Learning; Personalized Federated Learning,Methodology; Adversarial,**Federated Learning** is a machine learning approach that allows multiple devices or entities to collaboratively train a shared model without exchanging their data with each other. Instead of sending...
Continuous Control,10,MoCapAct; DeepMind Control Suite; D4RL; Omniverse Isaac Gym; MO-Gymnasium; OpenAI Gym; RLLab Framework; RLU; PyBullet; Lani,Inverted Pendulum (noisy observations); finger.turn_hard; PyBullet Walker2D; finger.turn_easy; 2D Walker; Mountain Car; acrobot.swingup; PyBullet Hopper; walker.run; DeepMind Cheetah Run (Images),Score; Return,Drone Controller; Car Racing; Steering Control,Computer Vision; Robots; Playing Games,"Continuous control in the context of playing games, especially within artificial intelligence (AI) and machine learning (ML), refers to the ability to make a series of smooth, ongoing adjustments or a..."
Graph Learning,10,GVLQA; Lowest Common Ancestor Generations (LCAG) Phasespace Particle Decay Reconstruction Dataset; ETHZ-Shape; AsEP; SAGC-A68; NBA player performance prediction dataset; BotNet; Mindboggle; OCB; MIMIC-SPARQL,CAMELS,absolute relative error; R^2,Task Graph Learning; Graph Sampling,Graphs,"Graph learning is a branch of machine learning that focuses on the analysis and interpretation of data represented in graph form. In this context, a graph is a collection of nodes (or vertices) and ed..."
Automatic Post-Editing,10,WMT 2018 News; WMT 2015 News; eSCAPE; MLQE-PE; SubEdits; DivEMT; WMT 2016 Biomedical; WMT 2016 IT; WMT 2016 News; APE,N/A,N/A,N/A,Computer Vision,Automatic post-editing (APE) is used to correct errors in the translation made by the machine translation systems.
Person Identification,10,iQIYI-VID; WiGesture; WiFall; MotionID: IMU all motions part1; MotionID: IMU all motions part2; NSVA; MotionID: IMU all motions part3; EEG Motor Movement/Imagery Dataset; 11k Hands; MotionID: IMU specific motion,EEG Motor Movement/Imagery Dataset; BioEye; WiGesture,R1; Accuracy; Accuracy (% ),N/A,Computer Vision,N/A
Benchmarking,10,Apron Dataset; PsOCR; Wiki-40B; Europarl-ASR; FluidLab; COCO-N Medium; CSTS; GenoTEX; CropAndWeed; SemanticSugarBeets,CloudEval-YAML; Wiki-40B,ACC; Perplexity,N/A,Miscellaneous; Robots,N/A
Table annotation,10,Tough Tables; T2Dv2; GitTables; WikipediaGS; TNCR Dataset; WDC SOTAB V2; WDC SOTAB; WikiTables-TURL; VizNet-Sato; Information Extraction from Tables,N/A,N/A,Cell Entity Annotation; Column Type Annotation; Row Annotation; Table Type Detection; Columns Property Annotation,Natural Language Processing; Knowledge Base,**Table annotation** is the task of annotating a table with terms/concepts from knowledge graph or database schema. Table annotation is typically broken down into the following five subtasks:     1. C...
Column Type Annotation,10,Tough Tables; T2Dv2; GitTables; WikipediaGS; BiodivTab; WDC SOTAB V2; WDC SOTAB; WikiTables-TURL; GitTables-SemTab; VizNet-Sato,N/A,N/A,N/A,N/A,N/A
Time Series Regression,10,Energy Consumption Curves of 499 Customers from Spain; USNA-Cn2 (short-duration); PPG  Dalia; USNA-Cn2 (long-term); Appliances Energy; SKF-BLS Dataset; FinSen; WildPPG; MLO-Cn2; HEMEW^S-3D,FinSen; MLO-Cn2; USNA-Cn2 (short-duration); USNA-Cn2 (long-term),Mean MSE; RMSE,COVID-19 Modelling,Time Series,Predicting one or more scalars for an entire time series example.
Homography Estimation,9,CARWC; NYU-VP; PDS-COCO; POT-210; S-COCO; Boson-nighttime; COCO (Common Objects in Context); VidSet; YUD+,N/A,N/A,N/A,N/A,N/A
Reinforcement Learning,9,Basketball; ATLANTIS; MuJoCo; Omniverse Isaac Gym; HAMMER; Tennis; iris; TruthGen; Soccer,Go To Red Blue Ball; Open Red Door; Stick Pull; Tennis; HAMMER; Button Press Topdown Wall; Go To; Sweep Into; Q*Bert; Demon Attack,"10 Images, 4*4 Stitching, Exact Accuracy",Deep Reinforcement Learning,Methodology; Natural Language Processing,N/A
AutoML,9,Chalearn-AutoML-1; NAS-Bench-101; Wine; NAS-Bench-1Shot1; CSS10; MedMNIST; GenoTEX; PMLB; OrdinalDataset,Chalearn-AutoML-1; Breast Cancer Coimbra Data Set; OrdinalDataset; Wine,Rank (AutoML5); Set3 (AUC); Accuracy; Set1 (F1); Set2 (PAC); 1:1 Accuracy; Set4 (ABS); Duration; accuracy; Set5 (BAC),Hyperparameter Optimization; Neural Architecture Search; Automated Feature Engineering,Methodology,"Automated Machine Learning (**AutoML**) is a general concept which covers diverse techniques for automated model learning including automatic data preprocessing, architecture search, and model selecti..."
Shadow Removal,9,SD7K; WSRD+; ISTD+; Jung; INS Dataset; SRD; Doc3DShade; ISTD; Kligler,^(#$!@#$)(()))******; WSRD+; ISTD+; SRD; INS Dataset; ISTD,LPIPS; Average PSNR (dB); MAE; RMSE; SSIM; PSNR; 0S,Document Shadow Removal,Computer Vision,Image Shadow Removal
Program Repair,9,CodRep; ManySStuBs4J; GitHub-Python; Defects4J; xCodeEval; ETH Py150 Open; HumanEvalPack; DeepFix; TFix's Code Patches Data,DeepFix; GitHub-Python; TFix's Code Patches Data; HumanEvalPack,Exact Match; Pass@1; Average Success Rate; Error Removal; Accuracy (%),Swapped operands; Variable misuse; Wrong binary operator; Function-docstring mismatch; Fault localization,Reasoning; Computer Code,Task of teaching ML models to modify an existing program to fix a bug in a given code.
Learning-To-Rank,9,ReQA; MQ2007; MSLR-WEB10K; Flickr Cropping Dataset; MQ2008; MultiFC; Learning to Rank Challenge; REFreSD; ART Dataset,N/A,N/A,N/A,Miscellaneous; Graphs,"Learning to rank is the application of machine learning to build ranking models. Some common use cases for ranking models are information retrieval (e.g., web search) and news feeds application (think..."
Multi-agent Reinforcement Learning,9,RoomEnv-v0; pursuitMW; ColosseumRL; OG-MARL; Hanabi Learning Environment; SMAC-Exp; CityFlow; CivRealm; StarCraft II Learning Environment,SMAC-Exp; ParticleEnvs Cooperative Communication; UAV Logistics,final agent reward; Average Reward; Median Win Rate,SMAC,Methodology; Playing Games,"The target of **Multi-agent Reinforcement Learning** is to solve complex problems by integrating multiple agents that focus on different sub-tasks. In general, there are two types of multi-agent syste..."
Representation Learning,9,xView3-SAR; OmniBenchmark; SYNTH-PEDES; Causal Triplet; TYC Dataset; Sports10; EXTREME CLASSIFICATION; Animals-10; SciDocs,Circle Data; CIFAR10; Sports10; Animals-10; SciDocs,Accuracy; Avg.; 1:1 Accuracy; Silhouette Score; Accuracy (%),Network Embedding; Learning Word Embeddings; Feature Upsampling; Part-based Representation Learning; Learning Representation Of Multi-View Data,Methodology; Computer Vision; Natural Language Processing,**Representation Learning**  is a process in machine learning where algorithms extract meaningful patterns from raw data to create representations that are easier to understand and process. These repr...
Code Repair,9,Performance Improving Code Edits (PIE); FixEval; CriticBench; migration-bench-java-full; migration-bench-java-utg; PyTorrent; migration-bench-java-selected; DISL; CodeXGLUE,CodeXGLUE - Bugs2Fix,BLEU (small); Accuracy (small); Accuracy (medium); CodeBLEU (small); BLEU (medium); CodeBLEU (medium),N/A,Computer Code; Natural Language Processing,N/A
Multi-Label Learning,8,Multi-Label Classification Dataset Repository; BirdSong; Corn Seeds Dataset; ExpW; EXTREME CLASSIFICATION; Animal Kingdom; COCO (Common Objects in Context); MIMIC Meme Dataset,COCO 2014,OP; CP; OR; CF1; mAP; CR; OF1,Missing Labels,Methodology; Reasoning,Multi-label learning (MLL) is a generalization of the binary and multi-category classification problems and deals with tagging a data instance with several possible class labels simultaneously [1]. Ea...
Lipreading,8,CAS-VSR-W1k (LRW-1000); MIRACL-VC1; GRID Dataset; LRS2; LRS3-TED; GLips; CAS-VSR-S101; LRW,LRW-1000; CAS-VSR-W1k (LRW-1000); Lip Reading in the Wild; LRS2; CMLR; GRID corpus (mixed-speech); LRS3-TED; CAS-VSR-S101,Top-1 Accuracy; Word Error Rate (WER); CER,Landmark-based Lipreading,Computer Vision,Lipreading is a process of extracting speech by watching lip movements of a speaker in the absence of sound. Humans lipread all the time without even noticing. It is a big part in communication albeit...
Incremental Learning,8,TopLogo-10; MLT17; Incremental Dialog Dataset; Concept-1K; CIFAR-100; Permuted MNIST; CORe50; HOWS,ImageNet-100 - 50 classes + 50 steps of 1 class; ImageNet - 500 classes + 10 steps of 50 classes; CIFAR100B050S(2ClassesPerStep); CIFAR-100 - 40 classes + 60 steps of 1 class (Exemplar-free); ImageNet100 - 20 steps; CIFAR-100 - 50 classes + 10 steps of 5 classes; ImageNet - 500 classes + 5 steps of 100 classes; ImageNet-100 - 50 classes + 5 steps of 10 classes; CIFAR-100 - 50 classes + 2 steps of 25 classes; ImageNet - 10 steps,Average Incremental Accuracy; Acc; # M Params; Average Incremental Accuracy Top-5; Final Accuracy; Final Accuracy Top-5,N/A,Methodology,Incremental learning aims to develop artificially intelligent systems that can continuously learn to address new tasks from new data while preserving knowledge learned from previously learned tasks.
Multivariate Time Series Imputation,8,PhysioNet Challenge 2012; UCI Machine Learning Repository; Beijing Multi-Site Air-Quality Dataset; MuJoCo; Electricity; METR-LA Point Missing; METR-LA; PEMS-BAY Point Missing,N/A,N/A,N/A,N/A,N/A
Correlated Time Series Forecasting,8,PeMS07; PeMSD7; Electricity; PeMS04; PeMSD8; METR-LA; PEMS-BAY; Solar-Power,N/A,N/A,N/A,N/A,N/A
Disaster Response,8,RoadTracer; xView; fMoW; MSAW; MEDIC; xBD; C2A: Human Detection in Disaster Scenarios; CrisisMMD,N/A,N/A,N/A,N/A,N/A
Automated Theorem Proving,8,HolStep; MiniF2F; Kinship; GamePad Environment; ProofNet; HOList; MED; Geometry3K,HolStep (Unconditional); CoqGym; miniF2F-test; miniF2F-curriculum; miniF2F-valid; Metamath set.mm; HOList benchmark; HolStep (Conditional); CompCert,Percentage correct; Pass@1; Pass@64; ITP; pass@8192; Pass@32; Pass@8; Classification Accuracy; Pass@100; pass@1024,N/A,Miscellaneous,"The goal of **Automated Theorem Proving** is to automatically generate a proof, given a conjecture (the target theorem) and a knowledge base of known facts, all expressed in a formal language. Automat..."
Starcraft,8,VizDoom; SC2ReSet: StarCraft II Esport Replaypack Set; StarData; MSC; Mario AI; SC2EGSet: StarCraft II Esport Game State Dataset; StarCraft II Learning Environment; Lani,N/A,N/A,N/A,N/A,N/A
Heart rate estimation,8,UBFC-rPPG; VIPL-HR; MMPD; MTHS; MMSE-HR; BUAA-MIHR dataset; WildPPG; V4V,N/A,N/A,N/A,N/A,N/A
Explainable artificial intelligence,8,ExBAN; BDD-X; AnthroProtect; XAI-Bench; EUCA dataset; e-SNLI-VE; e-ViL; OpenXAI,N/A,N/A,FAD Curve Analysis; Explanation Fidelity Evaluation; Explainable Models,Methodology; Computer Vision,"XAI refers to methods and techniques in the application of artificial intelligence (AI) such that the results of the solution can be understood by humans. It contrasts with the concept of the ""black b..."
AI Agent,8,Plancraft; MMTB; DevAI; BeNYfits; Spider2-V; CAGUI; diaforge-utc-r-0725; LLMafia,N/A,N/A,N-Queens Problem - All Possible Solutions; Agent-based model inverse problem,Methodology; Robots,"Fundamentação teórica    A educação contemporânea busca desenvolver habilidades e competências em diferentes áreas do conhecimento. Nesse contexto, os jogos interdisciplinares surgem como uma ferramen..."
GLinear,7,ETTh1 (192); Weather; ETT; Electricity; ETTh1 (96); Traffic; Electricity Consuming Load,N/A,N/A,N/A,N/A,N/A
Argument Mining,7,DebateSum; OpenDebateEvidence; x-stance; UKP; FinArg; Demosthenes; ARCT,TACO -- Twitter Arguments from COnversations,macro F1,Claim-Evidence Pair Extraction (CEPE); Claim Extraction with Stance Classification (CESC); ValNov; Component Classification; Argument Pair Extraction (APE),Natural Language Processing,"**Argument Mining** is a field of corpus-based discourse analysis that involves the automatic identification of argumentative structures in text.   <span class=""description-source"">Source: [AMPERSAND:..."
Driver Attention Monitoring,7,BDD-A; HPD; OC; DR(eye)VE; MAAD; SEED-VIG; GD,N/A,N/A,N/A,N/A,N/A
Point Processes,7,AgeGroup Transactions MTPP; StackOverflow MTPP; RETWEET; MemeTracker; BillSum; Amazon MTPP; Retweet MTPP,AgeGroup Transactions MTPP; StackOverflow MTPP; RETWEET; MemeTracker; Stackoverflow; Amazon MTPP; MIMIC-II; Retweet MTPP,Accuracy; T-mAP; MAE; RMSE; OTD; Accuracy (%),N/A,Methodology,N/A
Offline RL,7,NeoRL; RL Unplugged; Visuomotor affordance learning (VAL) robot interaction dataset; NeoRL-2; D4RL; RoboNet; RLU,Walker2d; D4RL,Average Reward; D4RL Normalized Score,DQN Replay Dataset,Miscellaneous; Playing Games,N/A
Rain Removal,7,Nightrain; Real Rain Dataset; RainDS; HRI; LasVR; Raindrop; VRDS,Nightrain; DID-MDN,PSNR,Single Image Deraining,Computer Vision,N/A
Unsupervised Pre-training,7,m2caiSeg; Icon645; SECO; OADAT; TYC Dataset; CARLANE Benchmark; GBUSV,Measles; UCI measles,Sensitivity (VEB); Accuracy (%); Sensitivity,N/A,Methodology,Pre-training a neural network using unsupervised (self-supervised) auxiliary tasks on unlabeled data.
Robot Task Planning,7,Taskography; Fields2Benchmark dataset; PackIt; EMMOE-100; HRI Simple Tasks; Synthetic Object Preference Adaptation Data; SheetCopilot,PackIt; SheetCopilot,Average Reward; Pass@1,Task Planning,Reasoning; Robots,N/A
Data Integration,7,Abt-Buy; WDC Block; Amazon-Google; WDC SOTAB V2; WDC SOTAB; WikiTables-TURL; WDC Products,N/A,N/A,Entity Alignment; Entity Resolution; Table annotation,Natural Language Processing; Knowledge Base,**Data integration** (also called information integration) is the process of consolidating data from a set of heterogeneous data sources into a single uniform data set (materialized integration) or vi...
Within-Session SSVEP,7,Nakanishi2015 MOABB; Lee2019-SSVEP MOABB; MAMEM3 MOABB; MAMEM1 MOABB; Wang2016 MOABB; Kalunga2016 MOABB; MAMEM2 MOABB,N/A,N/A,N/A,N/A,N/A
RAG,7,CompMix-IR; PeerQA; PubMedQA corpus with metadata; CRSB; RiskData; Frames (part); TIME,N/A,N/A,N/A,N/A,N/A
Personalized Federated Learning,6,Tiny ImageNet; FEMNIST; CIFAR-100; Customer Support on Twitter; MNIST; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Phrase Grounding,6,Flickr30k; VD-Ref; Flickr30K Entities; G-VUE; Visual Genome; MS-CXR,Flickr30k; Flickr30k Entities Dev; Flickr30k Entities Test; ReferIt; Visual Genome,R@10; Accuracy; R@1; Pointing Game Accuracy; R@5,Grounded Open Vocabulary Acquisition,Natural Language Processing,"Given an image and a corresponding caption, the **Phrase Grounding** task aims to ground each entity mentioned by a noun phrase in the caption to a region in the image.   <span class=""description-sour..."
Lip Reading,6,CAS-VSR-W1k (LRW-1000); TIMIT; GRID Dataset; GLips; CAS-VSR-S101; LRW,LRW; TCD-TIMIT corpus (mixed-speech); GRID corpus (mixed-speech),WER,Lip password classification,Time Series,"**Lip Reading** is a task to infer the speech content in a video by using only the visual information, especially the lip movements. It has many crucial applications in practice, such as assisting aud..."
Explanation Fidelity Evaluation,6,SST; SST-2; SST-5; BA-2motifs; BBBP (Blood-Brain Barrier Penetration); MUTAG,N/A,N/A,N/A,N/A,N/A
Collaborative Filtering,6,Gowalla; MovieLens; Genre2Movies; Yelp; Amazon-Book; Yelp2018,Gowalla; MovieLens 1M; Amazon-Book; Yelp2018,Recall@20; NDCG@20,N/A,Miscellaneous,N/A
Graph Mining,6,Netzschleuder; Yelp-Fraud; IMCPT-SparseGM-100; Amazon-Fraud; NBA player performance prediction dataset; Yelp,N/A,N/A,N/A,Graphs,N/A
Band Gap,6,OQM9HK; OQMD v1.2; JARVIS-DFT; WBM; Materials Project; Matbench,N/A,N/A,N/A,N/A,N/A
Feature Importance,6,Wine; Hotel; Diabetes; iris; Digits; simply-CLEVR,Wine; Diabetes; Breastcancer; iris; Digits; boston,Pearson Correlation,N/A,Methodology,N/A
Graph Representation Learning,6,REDDIT-BINARY; IMDB-BINARY; COMA; Reddit; WikiGraphs; Myket Android Application Install,COMA,Error (mm),Knowledge Graph Embedding,Graphs,The goal of **Graph Representation Learning** is to construct a set of features (‘embeddings’) representing the structure of the graph and the data thereon. We can distinguish among Node-wise embeddin...
Univariate Time Series Forecasting,6,Extreme Events > Natural Disasters > Hurricane; ETT; Electricity; ExtMarker; Solar-Power; Multivariate-Mobility-Paris,N/A,N/A,N/A,N/A,N/A
Bandwidth Extension,6,VibraVox (soft in-ear microphone); VibraVox (throat microphone); VibraVox (temple vibration pickup); VibraVox (forehead accelerometer); VibraVox (rigid in-ear microphone); VCTK,N/A,N/A,N/A,N/A,N/A
Atari Games,6,AtariARI; DQN Replay Dataset; RLU; Atari-HEAD; Atari Grand Challenge; Arcade Learning Environment,Atari 2600 Private Eye; Atari 2600 Breakout; Atari 2600 Journey Escape; Atari 2600 James Bond; Atari 2600 Centipede; Atari 2600 Double Dunk; Atari 2600 Krull; Atari-57; Atari 2600 Frostbite; Atari 2600 Yars Revenge,Total Reward; Medium Human-Normalized Score; Human World Record Breakthrough; Return; Best Score; Mean Human Normalized Score; Score,Montezuma's Revenge,Playing Games,"The Atari 2600 Games task (and dataset) involves training an agent to achieve high game scores.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Playing Atari with Deep Reinforcement Learnin..."
Starcraft II,6,SC2ReSet: StarCraft II Esport Replaypack Set; Lani; MSC; SC2EGSet: StarCraft II Esport Game State Dataset; SMAC-Exp; StarCraft II Learning Environment,N/A,N/A,N/A,N/A,N/A
Goal-Oriented Dialog,6,DSTC7 Task 1; Permuted bAbI dialog task; ClovaCall; MutualFriends; Doc2Dial; HINT3,Kvret,BLEU; Embedding Average; Vector Extrema; Greedy Matching,User Simulation,Natural Language Processing,Achieving a pre-defined goal through a dialog.
Probabilistic Deep Learning,6,CBC; Hotel; ADVIO; VQA-HAT; DENSE; Data Storage System Performance,N/A,N/A,N/A,Computer Vision; Knowledge Base,N/A
16k,6,21 Ways to Contact: How Can I Speak to Someone at Expedia; ConceptNet; ####How do i ask a question at Expedia?; [[request~refund]]How do I request a refund from Expedia?; [FaQ's--Help]How do I speak to someone on Expedia?; [QUESTioN~Agent~Calling]How do I complain to Expedia?,ConceptNet,"1'""",Image Deblurring; Highlight Detection; Image Super-Resolution; Object Detection; Scene Generation,Computer Vision; Playing Games; Medical; Miscellaneous; Methodology,N/A
Open Set Learning,6,NINCO; MIMII; ToyADMOS; DAD; ImageNet-1k vs NINCO; UCCS,N/A,N/A,N/A,Miscellaneous,"Traditional supervised learning aims to train a classifier in the closed-set world, where training and test samples share the same label space. Open set learning (OSL) is a more challenging and realis..."
Explainable Artificial Intelligence (XAI),6,XImageNet-12; ADNI; SpanEX; 3U-VQA; B-XAIC; InVar-100,ADNI,AD-Related Brain Areas Identified,Slice Discovery,Computer Vision; Knowledge Base,Explainable Artificial Intelligence
Cross-Modal Person Re-Identification,6,SYSU-MM01-C; TVPReid; RegDB-C; Correlated Corrupted Dataset; RSTPReid; Uncorrelated Corrupted Dataset,N/A,N/A,N/A,N/A,N/A
Protein Design,6,"Protein structures Ingraham; FLIP; FLIP -- AAV, Designed vs mutant; CATH 4.2; CATH 4.3; CAMEO",CATH 4.3; CATH 4.2,Sequence Recovery %(All); Perplexity,N/A,Medical,"Formally, given the design requirements of users, models are required to generate protein amino acid sequences that align with those requirements."
Math,6,ValiMath; DART-Math-Hard; GSM-Plus; DART-Math-Uniform; MathBench; MM-Eval,N/A,N/A,N/A,N/A,N/A
Core set discovery,5,UCI Machine Learning Repository; Letter; Electricity; Abalone; MNIST,Kr-vs-kp; Letter; micro-mass; Mozilla4; JM1; UCI GAS; Glass identification; Electricity; Abalone; Soybean,F1(10-fold),N/A,Methodology,A core set in machine learning is defined as the minimal set of training samples that allows a supervised algorithm to deliver a result as good as the one obtained when the whole set is used.
Adversarial Defense,5,ImageNet; CIFAR-100; Icons-50; MNIST; CIFAR-10,"ImageNet; CAAD 2018; ImageNet (non-targeted PGD, max perturbation=4); TrojAI Round 0; CIFAR-100; TrojAI Round 1; miniImageNet; ImageNet (targeted PGD, max perturbation=16); MNIST; CIFAR-10",Attack: AutoAttack; Inference speed; Accuracy; autoattack; Detection Accuracy; Robust Accuracy; Accuracy ,Adversarial Purification; Provable Adversarial Defense,Adversarial,Competitions with currently unpublished results:    - [TrojAI](https://pages.nist.gov/trojai/)
Network Pruning,5,Netzschleuder; ImageNet; CIFAR-100; MNIST; CIFAR-10,ImageNet; CIFAR-100; ImageNet - ResNet 50 - 90% sparsity; MNIST; CIFAR-10,GFLOPs; Accuracy; Top-1 Accuracy; Inference Time (ms); Avg #Steps; MParams,N/A,Methodology,"**Network Pruning** is a popular approach to reduce a heavy network to obtain a light-weight form by removing redundancy in the heavy network. In this approach, a complex over-parameterized network is..."
Linguistic Acceptability,5,CoLA; GLUE; ItaCoLA; RuCoLA; DaLAJ,CoLA; ItaCoLA; CoLA Dev; RuCoLA; DaLAJ,MCC; Accuracy,N/A,Natural Language Processing,Linguistic Acceptability is the task of determining whether a sentence is grammatical or ungrammatical.    Image Source: [Warstadt et al](https://arxiv.org/pdf/1901.03438v4.pdf)
Chunking,5,CoNLL-2000; CoNLL; CoNLL 2003; HindEnCorp; Penn Treebank,CoNLL 2003 (German); CoNLL 2000; CoNLL 2003 (English); CoNLL 2003; Penn Treebank,F1; Accuracy; AUC; Recall; Precision; Exact Span F1; F1 score,N/A,Natural Language Processing,"Chunking, also known as shallow parsing, identifies continuous spans of tokens that form syntactic units such as noun phrases or verb phrases.    Example:    | Vinken | , | 61 | years | old |  | --- |..."
Point Cloud Completion,5,Completion3D; ShapeNet-ViPC; MVP; ShapeNet; VBR,ShapeNet; Completion3D; ShapeNet-ViPC,F-Score@1%; Chamfer Distance; Chamfer Distance L2; Earth Mover's Distance; Frechet Point cloud Distance,Point Cloud Semantic Completion,Computer Vision,N/A
Region Proposal,5,ISBDA; MobilityAids; ABC Dataset; COCO (Common Objects in Context); RefCOCO,N/A,N/A,N/A,N/A,N/A
Clustering,5,Furit360; Daily and Sports Activities; MapReader Data; TIMIT; VocSim,N/A,N/A,Constrained Clustering; Categorical data clustering,Methodology,Clustering is the task of grouping unlabeled data point into disjoint subsets. Each data point is labeled with a single class. The number of classes is not known a priori. The grouping criteria is typ...
Complex Query Answering,5,FB15k; FB15k-237; NELL; NELL-995; Genre2Movies,N/A,N/A,N/A,N/A,N/A
Arousal Estimation,5,EMOTIC; AffectNet; AVCAffe; MSP-IMPROV; MEEG,N/A,N/A,N/A,N/A,N/A
Valence Estimation,5,EMOTIC; AffectNet; AVCAffe; MSP-IMPROV; MEEG,N/A,N/A,N/A,N/A,N/A
Handwriting Verification,5,AND Dataset; MatriVasha:; CEDAR Signature; DigiLeTs; BRUSH,AND Dataset; CEDAR Signature,Average F1; FAR,Bangla Spelling Error Correction,Computer Vision; Natural Language Processing,The goal of handwriting verification is to find a measure of confidence whether the given handwritten samples are written by the same or different writer.
Gaussian Processes,5,WHOI-Plankton; UCI Machine Learning Repository; MPI FAUST Dataset; Photoswitch; Poser,UCI POWER,Root mean square error (RMSE),GPR,Miscellaneous; Methodology,"**Gaussian Processes** is a powerful framework for several machine learning tasks such as regression, classification and inference. Given a finite set of input output training data that is generated o..."
Network Embedding,5,Netzschleuder; IS-A; PART-OF; BIRDeep; arXiv Astro-Ph,N/A,N/A,N/A,N/A,N/A
Real-Time Strategy Games,5,StarData; MSC; Mario AI; eSports Sensors Dataset; LLMafia,N/A,N/A,Starcraft; Starcraft II,Playing Games,"Real-Time Strategy (RTS) tasks involve training an agent to play video games with continuous gameplay and high-level macro-strategic goals such as map control, economic superiority and more.    <span ..."
Color Constancy,5,Rendered WB dataset; LSMI; VegFru; INTEL-TAU; Cube++,INTEL-TUT2,Best 25%,Few-Shot Camera-Adaptive Color Constancy,Computer Vision,**Color Constancy** is the ability of the human vision system to perceive the colors of the objects in the scene largely invariant to the color of the light source. The task of computational Color Con...
Feature Engineering,5,SMHD; KIT Motion-Language; EMBER; OneStopEnglish; NCBI Datasets,2019_test set,14 gestures accuracy,Imputation,Miscellaneous; Methodology,"Feature engineering is the process of taking a dataset and constructing explanatory variables — features — that can be used to train a machine learning model for a prediction problem. Often, data is s..."
Time Series Clustering,5,edeniss2020; Drosophila Immunity Time-Course Data; Bosch CNC Machining Dataset; CSTS; UCR Time Series Classification Archive,eICU Collaborative Research Database,NMI (physiology_6_hours); NMI (physiology_24_hours); NMI (physiology_12_hours),N/A,Time Series,**Time Series Clustering** is an unsupervised data mining technique for organizing data points into groups based on their similarity. The objective is to maximize data similarity within clusters and m...
Causal Discovery,5,BCOPA-CE; CausalBench; Ultra-processed Food Dataset; TimeGraph; CausalChaos!,N/A,N/A,N/A,Knowledge Base,"<span style=""color:grey; opacity: 0.6"">( Image credit: [TCDF](https://github.com/M-Nauta/TCDF) )</span>"
Physics-informed machine learning,5,PINO-darcy-pentagram; BubbleML; Expressive Gaussian mixture models for high-dimensional statistical modelling: simulated data and neural network model files; DrivAerNet; CAMELS Multifield Dataset,N/A,N/A,Soil moisture estimation,Computer Vision; Graphs,Machine learning used to represent physics-based and/or engineering models
SSTOD,5,SSD_PHONE; SSD_PLATE; SSD_NAME; SSD; SSD_ID,N/A,N/A,N/A,N/A,N/A
Natural Questions,5,BIG-bench; TheoremQA; Belebele; QAMPARI; QASports,N/A,N/A,N/A,N/A,N/A
College Mathematics,5,BIG-bench; DART-Math-Hard; DART-Math-Uniform; ASyMOB; MathBench,N/A,N/A,N/A,N/A,N/A
Elementary Mathematics,5,NCTE Transcripts; BIG-bench; DART-Math-Hard; DART-Math-Uniform; MathBench,N/A,N/A,N/A,N/A,N/A
Uncertainty Quantification,5,LUMA; MUAD; JAMBO; Uncertainty Quantification for Underwater Object Segmentation; Data Storage System Performance,N/A,N/A,N/A,Miscellaneous,N/A
Intelligent Communication,5,SPAVE-28G; MVX; iV2V and iV2I+; dichasus-cf0x; Berlin V2X,N/A,N/A,Semantic Communication; Beam Prediction,Miscellaneous; Time Series,Intelligently decide (i) the content of data  to be shared/communicated and (ii) the direction in which the chosen  data is transmitted.
Occlusion Handling,5,3D-POP; Separated COCO; OCFR-LFW; Occluded COCO; IITKGP_Fence Dataset,N/A,N/A,N/A,Computer Vision,N/A
PDE Surrogate Modeling,5,FSI; Representative PDE Benchmarks; DrivAerNet; Vibrating Plates; HEMEW^S-3D,N/A,N/A,N/A,Miscellaneous,N/A
AI and Safety,5,SALAD-Bench; SGXSTest; CLEAR-Bias; SUDO Dataset; HiXSTest,N/A,N/A,N/A,Natural Language Processing,N/A
POS,4,Kannada Treebank; MNIST; Universal Dependencies; CoNLL 2003,N/A,N/A,N/A,N/A,N/A
One-Shot Learning,4,MatSim; TACO; MNIST; TopLogo-10,MNIST,Accuracy,N/A,Methodology; Computer Vision,"One-shot learning is the task of learning information about object categories from a single training example.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Siamese Neural Networks for One..."
Fill Mask,4,LSMDC; MNIST; MAX-60K; HALvest,N/A,N/A,N/A,N/A,N/A
Dense Pixel Correspondence Estimation,4,ETH3D; HPatches; TSS; KITTI,TSS; ETH3D; HPatches; KITTI 2015; KITTI 2012,PCK-3px; Viewpoint I AEPE; PCK-1px; Average PCK@0.05; Viewpoint IV AEPE; AEPE (rate=5); PCK-5px; Viewpoint V AEPE; Viewpoint II AEPE; Average End-Point Error,N/A,Computer Vision,N/A
Interpretable Machine Learning,4,Morph Call; CUB-200-2011; SkinCon; Controversial News Topic Datasets,CUB-200-2011,Top 1 Accuracy,Abstention Prediction; Data Mining,Methodology,The goal of **Interpretable Machine Learning** is to allow oversight and understanding of machine-learned decisions. Much of the work in Interpretable Machine Learning has come in the form of devising...
Long-tail learning with class descriptors,4,ImageNet-LT; CUB-200-2011; AwA; SUN,N/A,N/A,N/A,N/A,N/A
FG-1-PG-1,4,2010 i2b2/VA; OntoNotes 5.0; CoNLL 2003; CoNLL,N/A,N/A,N/A,N/A,N/A
Partial Label Learning,4,SD-198; M-VAD Names; ISIC 2019; CIFAR-10,CIFAR-10 (partial ratio 0.1); Caltech-UCSD Birds 200 (partial ratio 0.05); M-VAD Names; MPII Movie Description; CIFAR-100 (partial ratio 0.01); CIFAR-10 (partial ratio 0.3); Autoimmune Dataset; ISIC 2019; CIFAR-100 (partial ratio 0.1); CIFAR-100 (partial ratio 0.05),Balanced Multi-Class Accuracy; F1-Score; F1 score; Accuracy,N/A,Methodology,N/A
Open-World Semi-Supervised Learning,4,CIFAR-100; ImageNet-100 (TEMI Split); BIOSCAN-5M; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Learning with coarse labels,4,ImageNet-32; CIFAR-100; Stanford Cars; Stanford Online Products,N/A,N/A,N/A,N/A,N/A
Graph-to-Sequence,4,ENT-DESC; EventNarrative; WikiOFGraph; WebNLG,WebNLG; LDC2015E86:,BLEU,N/A,Natural Language Processing,Mapping an input graph to a sequence of vectors.
SSIM,4,DRealSR; DocUNet; VIDIT; I-HAZE,DocUNet,SSIM,N/A,N/A,N/A
Compressive Sensing,4,BSD; Set11; Set5; Urban100,BSD68 CS=50%; Set11 cs=50%; Set5; BSDS100 - 2x upscaling; Urban100 - 2x upscaling,PSNR; Average PSNR,N/A,Computer Vision,"**Compressive Sensing** is a new signal processing framework for efficiently acquiring and reconstructing a signal that have a sparse representation in a fixed linear basis.   <span class=""description..."
Matrix Completion,4,Netflix Prize; CAL500; SweetRS; SAVOIAS,N/A,N/A,Low-Rank Matrix Completion,Methodology,**Matrix Completion** is a method for recovering lost information. It originates from machine learning and usually deals with highly sparse matrices. Missing or unknown data is estimated using the low...
Room Layout Estimation,4,ZInd; Rent3D; Structured3D; SUN RGB-D,SUN RGB-D,IoU; Camera Roll; Camera Pitch,Multi-view Floor Layout Reconstruction (N-view),Computer Vision,N/A
Cloze (multi-choices) (One-Shot),4,CMRC 2017; CMRC 2019; CMRC; ChID,N/A,N/A,N/A,N/A,N/A
Disparity Estimation,4,Middlebury 2014; IRS; FlyingThings3D; Dynamic Replica,Sintel 4D LFV - thebigfight2; Sintel 4D LFV - bamboo3; Sintel 4D LFV - ambushfight5; Sintel 4D LFV - shaman2,BadPix(0.03); BadPix(0.05); BadPix(0.01); BadPix(0.07); MSE*100,N/A,Computer Vision,The Disparity Estimation is the task of finding the pixels in the multiscopic views that correspond to the same 3D point in the scene.
STS,4,ASSIN; MTEB; ASSIN2; Translated SNLI Dataset in Marathi,N/A,N/A,N/A,N/A,N/A
Meter Reading,4,UFPR-ADMR-v1; Copel-AMR; UFPR-ADMR-v2; UFPR-AMR,UFPR-ADMR-v1; UFPR-AMR Dataset; Copel-AMR; UFPR-AMR,Rank-1 Recognition Rate,Image-based Automatic Meter Reading,Computer Vision,N/A
Decision Making Under Uncertainty,4,xView3-SAR; LUMA; MUAD; Risk-Aware Planning Dataset,N/A,N/A,Uncertainty Visualization,Methodology; Reasoning; Computer Vision,N/A
Survival Analysis,4,Survival Analysis of Heart Failure Patients; GBSG2; Replication Data for: Investigating the concentration of High Yield Investment Programs in the United Kingdom; PBC,N/A,N/A,N/A,Miscellaneous,"**Survival Analysis** is a branch of statistics focused on the study of time-to-event data, usually called survival times. This type of data appears in a wide range of applications such as failure tim..."
Remaining Useful Lifetime Estimation,4,IMS Bearing Dataset; NASA C-MAPSS; NASA C-MAPSS-2; PRONOSTIA Bearing Dataset,NASA C-MAPSS; NASA C-MAPSS-2,Score; RMSE,N/A,Time Series,Estimating the number of machine operation cycles until breakdown from the time series of previous cycles.
Long-range modeling,4,LRA; MuLD; SCROLLS; Pathfinder-X2,N/A,N/A,N/A,N/A,N/A
Astronomy,4,RGZ EMU: Semantic Taxonomy; BIG-bench; PLAsTiCC; MaNGA,BIG-bench,Accuracy,N/A,Miscellaneous,"Astronomy is the study of everything in the universe beyond Earth’s atmosphere. That includes objects we can see with our naked eyes, like the Sun, the Moon, the planets, and the stars. It also contai..."
Memorization,4,DS-1000; BIG-bench; PopQA; LM Email Address Leakage,BIG-bench (Hindu Knowledge),Accuracy,N/A,Natural Language Processing,N/A
Columns Property Annotation,4,WDC SOTAB; WikiTables-TURL; WDC SOTAB V2; T2Dv2,N/A,N/A,N/A,N/A,N/A
Persuasion Strategies,4,Werewolf Among Us; Among Them; Persuasion Strategies; MIPD,N/A,N/A,N/A,Computer Vision,Prediction of Persuasion Strategy in Advertisements
Human Behavior Forecasting,4,Large Car-following Dataset Based on Lyft level-5: Following Autonomous Vehicles vs. Human-driven Vehicles; DARai; SinD; FollowMe Vehicle Behaviour Prediction Dataset,N/A,N/A,Social Cue Forecasting,Time Series,N/A
Temporal Sequences,4,DBE-KT22; Panoramic Video Panoptic Segmentation Dataset; f5C Dataset; POPGym,N/A,N/A,N/A,N/A,N/A
Pansharpening,4,WorldView-2 PairMax; PanCollection; WorldView-3 PAirMax; GeoEye-1 PairMax,N/A,N/A,N/A,N/A,N/A
Model Poisoning,3,MNIST; Fashion-MNIST; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Sparse Learning and binarization,3,CIFAR-100; MNIST; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Sparse Learning,3,ImageNet-32; CINIC-10; ImageNet,ImageNet32; CINIC-10; ImageNet,Top-1 Accuracy; Sparsity,N/A,Methodology,N/A
OpenAI Gym,3,MO-Gymnasium; Industrial Benchmark; OpenAI Gym,Ant-v4; InvertedDoublePendulum-v2; Mountain Car; LunarLander-v2; Walker2d-v4; Humanoid-v2; Pendulum-v1; HalfCheetah-v4; MountainCarContinuous-v0; Hopper-v2,Average Decisions; Action Repetition; Average Return; Mean Reward,Acrobot,Playing Games,"An open-source toolkit from OpenAI that implements several Reinforcement Learning benchmarks including: classic control, Atari, Robotics and MuJoCo tasks.    (Description by [Evolutionary learning of ..."
Unconstrained Lip-synchronization,3,GLips; LRW; LRS2,N/A,N/A,N/A,N/A,N/A
Blood pressure estimation,3,MIMIC-III; HYPE; VitalDB,MIMIC-III; Multi-day Continuous BP Prediction; VitalDB,MAE for SBP [mmHg]; MAE for DBP [mmHg]; MAE; RMSE; Mean Squared Error,ECG Classification; Hypertension detection,Medical,N/A
Dataset Distillation - 1IPC,3,CIFAR-100; CUB-200-2011; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Timex normalization,3,GUM; TimeBank; PNT,N/A,N/A,N/A,N/A,N/A
Continual Pretraining,3,SciERC; ACL ARC; AG News,ACL-ARC; SciERC; AG News,F1 (macro); F1 - macro,N/A,Methodology,N/A
Novel Class Discovery,3,SVHN; CIFAR-100; CIFAR-10,SVHN; cifar100; cifar10,Clustering Accuracy,N/A,Methodology; Computer Vision,"The goal of Novel Class Discovery (NCD) is to identify new classes in unlabeled data, by exploiting prior knowledge from known classes. In this specific setup, the data is split in two sets. The first..."
ECG Patient Identification,3,PTB Diagnostic ECG Database; CODE-15%; PTB-XL,N/A,N/A,ECG Patient Identification (gallery-probe),Medical,Identifying patients using their electrocardiograms.
ECG Patient Identification (gallery-probe),3,PTB Diagnostic ECG Database; CODE-15%; PTB-XL,N/A,N/A,N/A,N/A,N/A
parameter-efficient fine-tuning,3,BoolQ; WinoGrande; HellaSwag,BoolQ; WinoGrande; HellaSwag,Accuracy (% ),N/A,Methodology,Parameter-Efficient Fine-Tuning (PEFT) is a technique used to adapt pre-trained models to new tasks with minimal changes to the model's parameters. This approach is particularly useful in scenarios wh...
Skills Assessment,3,AQA-7; Multimodal PISA; eSports Sensors Dataset,Multimodal PISA,Accuracy (%),N/A,Computer Vision,N/A
Skills Evaluation,3,AQA-7; Multimodal PISA; eSports Sensors Dataset,eSports Sensors Dataset,LogLoss; ROC AUC; Accuracy,N/A,Computer Vision,N/A
Cloud Removal,3,SEN12MS-CR; SEN12MS-CR-TS; RICE,N/A,N/A,N/A,N/A,N/A
Cross-Modal  Person Re-Identification,3,SYSU-MM01; RegDB; RegDB-C,N/A,N/A,N/A,N/A,N/A
Combinatorial Optimization,3,MemeTracker; Distributional MIPLIB; CVRPTW,N/A,N/A,N/A,Methodology,**Combinatorial Optimization** is a category of problems which requires optimizing a function over a combination of discrete objects and the solutions are constrained. Examples include finding shortes...
Graph Sampling,3,Friendster; ChEMBL; LastFM Asia,N/A,N/A,N/A,N/A,N/A
One-class classifier,3,COVID-19-CT-CXR; Simulated micro-Doppler Signatures; Riseholme-2021,N/A,N/A,N/A,Methodology,N/A
Art Analysis,3,ArtQuest; HappyDB; SemArt,N/A,N/A,N/A,Computer Vision,N/A
Mathematical Proofs,3,ConstructiveBench; HolStep; NaturalProofs,N/A,N/A,Automated Theorem Proving,Miscellaneous; Reasoning,N/A
Efficient Exploration,3,Replica; House3D Environment; QDSD,N/A,N/A,N/A,Methodology,**Efficient Exploration** is one of the main obstacles in scaling up modern deep reinforcement learning algorithms. The main challenge in Efficient Exploration is the balance between exploiting curren...
Relation Linking,3,DBLP-QuAD; LC-QuAD; XFUND,N/A,N/A,N/A,N/A,N/A
Large-Scale Person Re-Identification,3,LPW; ENTIRe-ID; Occluded-DukeMTMC,N/A,N/A,N/A,N/A,N/A
Human Mesh Recovery,3,MoVi; BEDLAM; FLAG3D,BEDLAM,PVE-All,N/A,Computer Vision,Estimate 3D body mesh from images
Hyperparameter Optimization,3,PMLB; NAS-Bench-201; NAS-Bench-101,N/A,N/A,N/A,N/A,N/A
Radar odometry,3,Oxford Radar RobotCar Dataset; ORU Diverse radar dataset; K-Radar,Oxford Radar RobotCar Dataset,translation error [%],N/A,Robots,"Radar odometry is the task of estimating the trajectory of the radar sensor, e.g. as presented in https://arxiv.org/abs/2105.01457.  A well established performance metric was presented by Geiger (2012..."
Privacy Preserving Deep Learning,3,ConcurrentQA Benchmark; MS-FIMU; PA-HMDB51,N/A,N/A,Membership Inference Attack; Homomorphic Encryption for Deep Learning,Miscellaneous; Methodology; Computer Vision,"The goal of privacy-preserving (deep) learning is to train a model while preserving privacy of the training dataset. Typically, it is understood that the trained model should be privacy-preserving (e...."
Multi-target regression,3,CAMELS Multifield Dataset; SIZER; Replication Data for: Investigating the concentration of High Yield Investment Programs in the United Kingdom,Google 5 qubit random Hamiltonian,Average mean absolute error,N/A,Miscellaneous,N/A
Ensemble Learning,3,SMS Spam Collection Data Set; Wild-Time; Replication Data for: Online Learning with Optimism and Delay,SMS Spam Collection Data Set,Accuracy,N/A,Methodology; Computer Vision,N/A
Heart Rate Variability,3,VIPL-HR; UBFC-rPPG; MMSE-HR,N/A,N/A,N/A,N/A,N/A
Drum Transcription,3,YourMT3 Dataset; E-GMD; ENST Drums,N/A,N/A,N/A,Music,N/A
Diabetic Retinopathy Grading,3,Retinal-Lesions; DRTiD; Kaggle EyePACS,Kaggle EyePACS,Specificity; AUC; Sensitivity,N/A,Medical,Grading the severity of diabetic retinopathy from (ophthalmic) fundus images
News Annotation,3,HLGD; GVFC; Reddit Ideological and Extreme Bias Dataset,N/A,N/A,N/A,Natural Language Processing,Assigning the appropriate labels to a news text based on a set of pre-defined labels.
Network Community Partition,3,Netzschleuder; A collection of LFR benchmark graphs; Perfume Co-Preference Network,N/A,N/A,N/A,N/A,N/A
Parking Space Occupancy,3,PKLot; SPKL; Action-Camera Parking,ACMPS; PKLot; SPKL; Action-Camera Parking; CNRPark+EXT,F1; Average-mAP; F1-score,N/A,Computer Vision,Image credit: [https://github.com/martin-marek/parking-space-occupancy](https://github.com/martin-marek/parking-space-occupancy)
Blocking,3,Abt-Buy; Amazon-Google; WDC Block,N/A,N/A,N/A,N/A,N/A
Riddle Sense,3,BIG-bench; RiddleSense; Situation Puzzle,N/A,N/A,N/A,N/A,N/A
High School Mathematics,3,BIG-bench; MathBench; DART-Math-Hard,N/A,N/A,N/A,N/A,N/A
Professional Psychology,3,BIG-bench; CPsyCounE; CPsyCounD,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Eeg Decoding,3,Selective Visual Attention Decoding Dataset KU Leuven; NMED-T; CWL EEG/fMRI Dataset,CWL EEG/fMRI Dataset,Pearson Correlation,EEG Signal Classification,Time Series; Medical,**EEG Decoding** - extracting useful information directly from EEG data.
Malware Clustering,3,BODMAS; MOTIF; Malimg,N/A,N/A,N/A,N/A,N/A
Malware Analysis,3,BODMAS; AutoRobust; Malimg,N/A,N/A,N/A,N/A,N/A
Referring Expression,3,SQA3D; A Game Of Sorts; GRIT,SQA3D,Acc@0.5m; Acc@1.0m; Acc@15°; Acc@30°,N/A,Computer Vision,Referring expressions places a bounding box around  the instance corresponding to the provided description and  image.
Symbolic Regression,3,SRSD-Feynman (Medium set); SRSD-Feynman (Easy set); SRSD-Feynman (Hard set),N/A,N/A,Equation Discovery,Natural Language Processing; Knowledge Base,producing a mathematical expression (symbolic expression)  that fits a given tabular data.
Explainable Models,3,OpenXAI; B-XAIC; ScienceQA,N/A,N/A,N/A,N/A,N/A
Multi-Domain Recommender Systems,3,LEA-GCN-dataset; RL-ISN-dataset; X-Wines,N/A,N/A,N/A,N/A,N/A
Brain Decoding,3,Stanford ECoG library: ECoG to Finger Movements; GOD; BCI Competition IV: ECoG to Finger Movements,Stanford ECoG library: ECoG to Finger Movements; BCI Competition IV: ECoG to Finger Movements,Pearson Correlation,Brain Computer Interface,Miscellaneous; Medical,**Motor Brain Decoding** is fundamental task for building motor brain computer interfaces (BCI).    Progress in predicting finger movements based on brain activity allows us to restore motor functions...
Website Fingerprinting Attacks,3,Padding Ain't Enough: Assessing the Privacy Guarantees of Encrypted DNS – Subpage-Agnostic Domain Classification Firefox; Padding Ain't Enough: Assessing the Privacy Guarantees of Encrypted DNS – Subpage-Agnostic Domain Classification Tor Browser; Padding Ain't Enough: Assessing the Privacy Guarantees of Encrypted DNS – Web Scans,Website Traffic Data on Tor,Accuracy (%),N/A,Adversarial,N/A
Plant Phenotyping,3,CropAndWeed; SICKLE; SemanticSugarBeets,N/A,N/A,N/A,N/A,N/A
Feedback Vertex Set (FVS),3,PACE 2016 Feedback Vertex Set; PACE 2022 Exact; PACE 2022 Heuristic,N/A,N/A,N/A,Graphs,The **Feedback Vertex Set (FVS)** problem is a computational problem in computer science and graph theory that involves finding the smallest possible subset of vertices in an undirected graph such tha...
MuJoCo Games,3,MO-Gymnasium; IL-Datasets; Omniverse Isaac Gym,Swimmer; Humanoid-v3; Sweeper; InvertedDoublePendulum; Humanoid-v2; Point Maze; HalfCheetah; Hopper; Sawyer Pusher; Reacher,Return; Average Reward; Average Return; Mean,D4RL,Robots,N/A
Snow Removal,3,RVSD; SRRS; Snow100K,N/A,N/A,N/A,N/A,N/A
Game of Sudoku,3,rrn-sudoku; satnet-sudoku; many-solutions-sudoku,Sudoku 9x9,Accuracy,N/A,Playing Games,N/A
Hate Span Identification,3,TuPyE-Dataset; TUN-EL; ViHOS,N/A,N/A,N/A,Natural Language Processing,N/A
Hallucination Evaluation,3,PhD; HalluEditBench; UHGEvalDataset,N/A,N/A,N/A,Natural Language Processing,Evaluate the ability of LLM to generate non-hallucination text or assess the capability of LLM to recognize hallucinations.
Theory of Mind Modeling,3,DynToM; MMToM-QA; 2D-ATOMS,N/A,N/A,N/A,Reasoning,N/A
Anomaly Forecasting,3,SupplyGraph; edeniss2020; Hawk Annotation Dataset,N/A,N/A,N/A,Time Series,"Anomaly forecasting is a critical aspect of modern data analysis, where the goal is to predict unusual patterns or behaviors in data sets that deviate from the norm. This process is vital across vario..."
LLM Jailbreak,3,CLEAR-Bias; SALAD-Bench; SUDO Dataset,N/A,N/A,N/A,Adversarial,N/A
Deep Clustering,2,MNIST; USPS,Searchsnippets; USPS; Coil-20; Stackoverflow; MNIST,ARI; NMI; 1:1 Accuracy,Deep Nonparametric Clustering; Trajectory Clustering; NONPARAMETRIC DEEP CLUSTERING,Miscellaneous; Methodology; Natural Language Processing,N/A
Hard-label Attack,2,MNIST; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Nature-Inspired Optimization Algorithm,2,MNIST; CIFAR-10,MNIST; CIFAR-10,training time (s),N/A,Computer Code,N/A
TAG,2,MNIST; Universal Dependencies,N/A,N/A,N/A,N/A,N/A
Interpretability Techniques for Deep Learning,2,CelebA; CausalGym,CelebA; CausalGym,Insertion AUC score; Log odds-ratio (pythia-6.9b),N/A,Miscellaneous,N/A
QQP,2,Quora Question Pairs; GLUE,N/A,N/A,N/A,N/A,N/A
Feature Upsampling,2,GenSC-6G; ImageNet,N/A,N/A,N/A,N/A,N/A
Missing Elements,2,Numeric Fused-Head; Penn Treebank,N/A,N/A,N/A,N/A,N/A
Horizon Line Estimation,2,York Urban Line Segment Database; KITTI,Horizon Lines in the Wild; KITTI Horizon; Eurasian Cities Dataset; York Urban Dataset,AUC; MSE; AUC (horizon error); ATV,N/A,Computer Vision,N/A
Dense Captioning,2,Visual Genome; VisArgs,Visual Genome,mAP,Live Video Captioning,Computer Vision,N/A
Defocus Estimation,2,Motion Blurred and Defocused Dataset; CUHK03,CUHK - Blur Detection Dataset,MAE; Blur Segmentation Accuracy; F-measure,N/A,Computer Vision,N/A
FLUE,2,PAWS-X; XNLI,N/A,N/A,N/A,N/A,N/A
Steering Control,2,BDD100K; Udacity,N/A,N/A,N/A,N/A,N/A
Boundary Captioning,2,Kinetics-GEB+; Kinetics,N/A,N/A,N/A,N/A,N/A
Boundary Grounding,2,Kinetics-GEB+; Kinetics,N/A,N/A,N/A,N/A,N/A
Domain-IL Continual Learning,2,Permuted MNIST; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Provable Adversarial Defense,2,CIFAR-100; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Clean-label Backdoor Attack (0.05%),2,Tiny ImageNet; CIFAR-10,N/A,N/A,N/A,N/A,N/A
Test Agnostic Long-Tailed Learning,2,ImageNet-LT; iNaturalist,N/A,N/A,N/A,N/A,N/A
STS Benchmark,2,STS Benchmark; HumanEval,N/A,N/A,N/A,N/A,N/A
Total Magnetization,2,OQM9HK; OQMD v1.2,N/A,N/A,N/A,N/A,N/A
Unsupervised Continual Domain Shift Learning,2,PACS; DomainNet,N/A,N/A,N/A,N/A,N/A
MuJoCo,2,NeoRL; MuJoCo,N/A,N/A,N/A,N/A,N/A
Incremental Constrained Clustering,2,iris; Wine,N/A,N/A,N/A,N/A,N/A
Q-Learning,2,VizDoom; Yeast,N/A,N/A,N/A,Methodology,"The goal of Q-learning is to learn a policy, which tells an agent what action to take under what circumstances.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Playing Atari with Deep Reinf..."
Test-time Adaptation,2,DADE; ImageNet-C,N/A,N/A,N/A,N/A,N/A
Cross-Modality Person Re-identification,2,SYSU-MM01; RegDB,N/A,N/A,N/A,N/A,N/A
Point Cloud Super Resolution,2,VBR; SHREC,PU-GAN; SHREC15,Hausdorff Distance; F-measure (%); Chamfer Distance; Point-to-surface distance,N/A,Computer Vision,Point cloud super-resolution is a fundamental problem  for 3D reconstruction and 3D data understanding. It takes  a low-resolution (LR) point cloud as input and generates  a high-resolution (HR) point...
Information Threading,2,NewSHead; Multi-News,N/A,N/A,N/A,N/A,N/A
Deep Attention,2,METU-VIREF Dataset; Google Refexp,N/A,N/A,N/A,Computer Vision; Natural Language Processing,N/A
Authorship Verification,2,Blog Authorship Corpus; Enron Emails,N/A,N/A,N/A,Natural Language Processing,"Authorship verification (**AV**) is a research subject in the field of digital text forensics that concerns itself with the question, whether two documents have been written by the same person.     De..."
Accident Anticipation,2,CAP-DATA; CCD,N/A,N/A,N/A,N/A,N/A
Entity Embeddings,2,MovieGraphBenchmark; DAWT,N/A,N/A,N/A,Methodology,Entity Embeddings is a technique for applying deep learning to tabular data. It involves representing the categorical data of an information systems entity with multiple dimensions.
Dominance Estimation,2,MSP-IMPROV; EMOTIC,N/A,N/A,N/A,N/A,N/A
Hierarchical Reinforcement Learning,2,Gibson Environment; bipedal-skills,Ant + Maze,Return,N/A,Methodology; Playing Games,N/A
graph partitioning,2,A collection of LFR benchmark graphs; HAM,custom,All,N/A,Graphs,Graph Partitioning is generally the first step of distributed graph computing tasks. The targets are load-balance and minimizing the communication volume.
Policy Gradient Methods,2,Hanabi Learning Environment; RLLab Framework,N/A,N/A,N/A,Methodology,N/A
Meta Reinforcement Learning,2,Meta-World Benchmark; MIKASA-Robo Dataset,N/A,N/A,N/A,N/A,N/A
Board Games,2,Obstacle Tower; Cards Against Humanity,N/A,N/A,Game of Shogi; Game of Chess; Game of Go,Playing Games,N/A
Self-Supervised Person Re-Identification,2,ENTIRe-ID; SYSU-30k,N/A,N/A,N/A,N/A,N/A
Multi-Goal Reinforcement Learning,2,UniMiB SHAR; bipedal-skills,no extra data,Average Reward,N/A,Methodology,N/A
Clustering Ensemble,2,pathbased; ionosphere,N/A,N/A,N/A,N/A,N/A
Point Clouds,2,Tanks and Temples; DTU,Tanks and Temples; DTU,Overall; Mean F1 (Advanced); Mean F1 (Intermediate),point cloud video understanding; Point Cloud Rrepresentation Learning; Cross-modal place recognition,Computer Vision,N/A
Dial Meter Reading,2,UFPR-ADMR-v1; UFPR-ADMR-v2,N/A,N/A,N/A,N/A,N/A
No real Data Binarization,2,DIBCO 2019; DIBCO and H_DIBCO,N/A,N/A,N/A,N/A,N/A
Behavioural cloning,2,CoWeSe; BDD-X,N/A,N/A,N/A,N/A,N/A
Relational Pattern Learning,2,TexRel; Real world moire pattern classification,N/A,N/A,N/A,N/A,N/A
Human Dynamics,2,TikTok Dataset; LLMafia,N/A,N/A,3D Human Dynamics,Computer Vision,N/A
Author Attribution,2,Taiga Corpus; BN-AuthProf,N/A,N/A,N/A,Natural Language Processing,Authorship attribution is the task of determining the author of a text.
ICU Mortality,2,eICU-CRD; HiRID,N/A,N/A,N/A,N/A,N/A
Popularity Forecasting,2,SHIFT15M; VISUELLE2.0,N/A,N/A,N/A,N/A,N/A
Cloze Test,2,Completion norms for 3085 English sentence contexts; CodeXGLUE,CodeXGLUE - CT-all; CodeXGLUE - CT-maxmin,Go; Java; JS; Python; Ruby; PHP,N/A,Natural Language Processing,The cloze task refers to infilling individual words.
Moment Queries,2,Ego4D; ViLCo,N/A,N/A,N/A,N/A,N/A
Unsupervised Reinforcement Learning,2,URLB; bipedal-skills,N/A,N/A,N/A,N/A,N/A
Linear evaluation,2,Car_Price_Prediction; ESC50,N/A,N/A,N/A,N/A,N/A
Story Completion,2,XStoryCloze; Scifi TV Shows,N/A,N/A,N/A,N/A,N/A
Multi-Armed Bandits,2,Duolingo Bandit Notifications; KuaiRand,Mushroom,Cumulative regret,Thompson Sampling,Miscellaneous; Methodology,Multi-armed bandits refer to a task where a fixed amount of resources must be allocated between competing resources that maximizes expected gain. Typically these problems involve an exploration/exploi...
Molecule Captioning,2,ChEBI-20; L+M-24,ChEBI-20; L+M-24,ROUGE-1; ROUGE-L; METEOR; BLEU-2; Text2Mol; ROUGE-2; BLEU-4,N/A,Medical,"Molecular description generation entails the creation of a detailed textual depiction illuminating the structure, properties, biological activity, and applications of a molecule based on its molecular..."
Aggression Identification,2,The ComMA Dataset v0.2; WikiDetox,N/A,N/A,N/A,Natural Language Processing,"Develop a classifier that could make a 3-way classification in-between ‘Overtly Aggressive’, ‘Covertly Aggressive’ and ‘Non-aggressive’ text data. For this, TRAC-2 dataset of 5,000 aggression-annotate..."
Irony Identification,2,BIG-bench; ArSarcasm-v2,N/A,N/A,N/A,N/A,N/A
Moral Scenarios,2,BIG-bench; OllaBench v.0.2,N/A,N/A,N/A,N/A,N/A
Computer Security,2,BIG-bench; OllaBench v.0.2,BIG-bench,Accuracy ,File Type Identification,Miscellaneous,N/A
Security Studies,2,BIG-bench; VNAT,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Multi-Agent Path Finding,2,pursuitMW; CPP simulated evaluation,N/A,N/A,N/A,Playing Games,N/A
Record linking,2,Binette's 2022 Inventors Benchmark; Weibo-Douban,N/A,N/A,N/A,Natural Language Processing,The task of finding records in a data set that refer to the same entity across different data sources.     Record linking is also called *entity resolution* or *entity matching*. Further material abou...
Multi-agent Integration,2,BBAI Dataset; LLMafia,BBAI Dataset,P@1,N/A,Natural Language Processing,N/A
De-identification,2,Dataset: Privacy-Preserving Gaze Data Streaming in Immersive Interactive Virtual Reality: Robustness and User Experience.; i2b2 De-identification Dataset,N/A,N/A,Privacy Preserving Deep Learning; Full-body anonymization,Computer Vision; Natural Language Processing,"De-identification is the task of detecting privacy-related entities in text, such as person names, emails and contact data."
Unsupervised Domain Expansion,2,UDE-Office-Home; UDE-DomainNet,N/A,N/A,N/A,N/A,N/A
Automated Essay Scoring,2,ASAP-AES; The Write & Improve Corpus 2024,ASAP-AES,Quadratic Weighted Kappa,N/A,Natural Language Processing,"Essay scoring: **Automated Essay Scoring** is the task of assigning a score to an essay, usually in the context of assessing the language ability of a language learner. The quality of an essay is affe..."
Traffic Data Imputation,2,PEMS-BAY Point Missing; METR-LA Point Missing,N/A,N/A,N/A,N/A,N/A
Overall - Test,2,JEEBench; FeedbackQA,N/A,N/A,N/A,N/A,N/A
Variable Disambiguation,2,SV-Ident; SILD,N/A,N/A,N/A,N/A,N/A
Embeddings Evaluation,2,Names pairs dataset; CANNOT,N/A,N/A,N/A,N/A,N/A
Multimodal Association,2,taste-music-dataset; Vi-Fi Multi-modal Dataset,N/A,N/A,multimodal generation,Time Series; Natural Language Processing,"**Multimodal association** refers to the process of associating multiple modalities or types of data in time series analysis. In time series analysis, multiple modalities or types of data can be colle..."
Seeing Beyond the Visible,2,KITTI360-EX; HYPERVIEW,KITTI360-EX; HYPERVIEW,normalized MSE; Average PSNR,N/A,Computer Vision,"The objective of this challenge is to automate the process of estimating the soil parameters, specifically, potassium (KKK), phosphorus pentoxide (P2O5P_2O_5P2​O5​), magnesium (MgMgMg) and pHpHpH, thr..."
Flare Removal,2,Flare7K; FlareReal600,N/A,N/A,N/A,N/A,N/A
Website Fingerprinting Defense,2,Padding Ain't Enough: Assessing the Privacy Guarantees of Encrypted DNS – Subpage-Agnostic Domain Classification Tor Browser; Padding Ain't Enough: Assessing the Privacy Guarantees of Encrypted DNS – Web Scans,Website Traffic Data on Tor,Accuracy (%),N/A,Adversarial,N/A
ValNov,2,ValNov Subtask B; ValNov Subtask A,ValNov Subtask B; ValNov Subtask A,JOINT-F1; NOV-F1; VAL-F1,N/A,Natural Language Processing,"Given a textual premise and conclusion candidate, the Argument-Validity-and-Novelty-Prediction-Shared-Task ValNov consists in predicting two aspects of a conclusion: its validity and novelty.    Valid..."
Load Forecasting,2,PJM(AEP); ASHRAE energy prediction III,N/A,N/A,N/A,Miscellaneous; Computer Code,N/A
Spatio-Temporal Forecasting,2,Weather2K; Beijing Traffic,N/A,N/A,Human Behavior Forecasting,Time Series,N/A
Protein Annotation,2,Protein-Instructions-OOD; PS4,N/A,N/A,N/A,N/A,N/A
slot-filling,2,Persian-ATIS; Noise-SF,N/A,N/A,N/A,N/A,N/A
Ethics,2,SHADR; Ethics (per ethics),Ethics; Ethics (per ethics),Accuracy,Moral Permissibility; Moral Disputes; Business Ethics; Moral Scenarios,Miscellaneous,N/A
Dynamic Point Removal,2,semi-indoor; Argoverse 2,semi-indoor; Argoverse 2,associated accuracy; static accuracy; dynamic accuracy,N/A,Robots,"In the field of robotics, the point cloud has become an essential map representation. From the perspective of downstream tasks like localization and global path planning, points corresponding to dynam..."
PAIR TRADING,2,CSI 300 Pair Trading; S&P 500 Pair Trading,N/A,N/A,N/A,N/A,N/A
Irregular Time Series,2,Santa Clara Reservoir Levels; Extreme Events > Natural Disasters > Hurricane,N/A,N/A,N/A,Time Series,Irregular Time Series
Age and Gender Estimation,2,LAGENDA; BN-AuthProf,LAGENDA gender; LAGENDA age,MAE; Accuracy; CS@5,N/A,Computer Vision,Age and gender estimation is a dual-task of identifying the age via regression analysis and classification of gender of a person.
Mathematical Problem-Solving,2,TheoremQA; ASyMOB,N/A,N/A,N/A,N/A,N/A
Sensor Fusion,2,SICKLE; VIDIMU: Multimodal video and IMU kinematic dataset on daily life activities using affordable devices,N/A,N/A,N/A,Computer Vision,Sensor fusion is the process of combining sensor data or data derived from disparate sources such that the resulting information has less uncertainty than would be possible when these sources were use...
Negation,2,OVDEval; Thunder-NUBench,N/A,N/A,N/A,N/A,N/A
Human Judgment Correlation,2,AlpacaEval; MT-Bench,Flickr8k-CF; Flickr8k-Expert,Kendall's Tau-c; Kendall's Tau-b,N/A,Reasoning,A task where an algorithm should generate the judgment scores correlating with human judgments.
Kinship Verification,2,KinFaceW-I; KinFaceW-II,KinFaceW-I; KinFaceW-II,Mean Accuracy,N/A,Computer Vision,Kinship verification aims to find out whether there is a  kin relation for a given pair of facial images.
Position regression,2,CHILI-3K; CHILI-100K,N/A,N/A,N/A,N/A,N/A
SAXS regression,2,CHILI-3K; CHILI-100K,N/A,N/A,N/A,N/A,N/A
XRD regression,2,CHILI-3K; CHILI-100K,N/A,N/A,N/A,N/A,N/A
SANS regression,2,CHILI-3K; CHILI-100K,N/A,N/A,N/A,N/A,N/A
ND regression,2,CHILI-3K; CHILI-100K,N/A,N/A,N/A,N/A,N/A
Neutron PDF regression,2,CHILI-3K; CHILI-100K,N/A,N/A,N/A,N/A,N/A
NMT,2,PolyNewsParallel; Leipzig Corpora,N/A,N/A,Direct NMT,Computer Code; Natural Language Processing,"Neural machine translation is an approach to machine translation that uses an artificial neural network to predict the likelihood of a sequence of words, typically modeling entire sentences in a singl..."
Sports Analytics,2,Predictive Model for Assessing Knee Muscle Injury Risk in Athletes and Non-Athletes Using sEMG; MultiSenseBadminton,N/A,N/A,N/A,Computer Vision,N/A
RTE,2,RTE3-FR; GQNLI-FR,N/A,N/A,N/A,N/A,N/A
Lung Cancer Diagnosis,2,National Lung Screening Trial (NLST); Duke Lung Nodule Dataset 2024,N/A,N/A,N/A,N/A,N/A
Collision Avoidance,2,HARPER; A Ball Collision Dataset (ABCD),A Ball Collision Dataset (ABCD),Accuracy (L:R) - T1,N/A,Robots,N/A
Modality completion,2,Amazon Sports; Amazon Baby,N/A,N/A,N/A,N/A,N/A
Hybrid Machine Learning,2,GLARE; High-Quality Invoice Images for OCR,N/A,N/A,N/A,N/A,N/A
Raindrop Removal,2,Video Waterdrop Removal Dataset; VRDS,N/A,N/A,N/A,N/A,N/A
backdoor defense,2,ULP Dataset; Trojans Against Trojans (TAT),N/A,N/A,Backdoor Defense for Data-Free Distillation with Poisoned Teachers,Adversarial,N/A
User Simulation,2,LMSYS-USP; VMD,N/A,N/A,N/A,N/A,N/A
Fault Diagnosis,2,Discrete-Time Modeling of Interturn Short Circuits in Interior PMSMs - Data and Models; Digital twin-supported deep learning for fault diagnosis,Digital twin-supported deep learning for fault diagnosis,Accuray,N/A,Time Series,N/A
Unsupervised MNIST,1,MNIST,N/A,N/A,N/A,N/A,N/A
Rotated MNIST,1,MNIST,Rotated MNIST,Test error,N/A,Computer Vision,N/A
Adversarial Defense against FGSM Attack,1,MNIST,N/A,N/A,N/A,N/A,N/A
Türkçe Görüntü Altyazılama,1,MNIST,N/A,N/A,N/A,N/A,N/A
HairColor/Unbiased,1,CelebA,N/A,N/A,N/A,N/A,N/A
HeavyMakeup/Unbiased,1,CelebA,N/A,N/A,N/A,N/A,N/A
Lexical Simplification,1,WikiLarge,N/A,N/A,N/A,Natural Language Processing,"The goal of **Lexical Simplification** is to replace complex words (typically words that are used less often in language and are therefore less familiar to readers) with their simpler synonyms, withou..."
Gait Identification,1,CASIA-B,N/A,N/A,N/A,Computer Vision,N/A
Online Clustering,1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
Long-tail Learning on CIFAR-10-LT (ρ=100),1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
ROLSSL-Consistent,1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
ROLSSL-Reversed,1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
ROLSSL-Uniform,1,CIFAR-10,N/A,N/A,N/A,N/A,N/A
Class Incremental Learning,1,CIFAR-100,Cifar100-B0(10 tasks)-no-exemplars; CIFAR100-B0(50 tasks)-no-exemplars; cifar100; CIFAR-100 - 50 classes + 10 steps of 5 classes; CIFAR-100 - 50 classes + 5 steps of 10 classes; Cifar100-B0(20 tasks)-no-exemplars,10-stage average accuracy; Final Accuracy; Average Incremental Accuracy,Non-exemplar-based Class Incremental Learning; Class-Incremental Semantic Segmentation; Few-Shot Class-Incremental Learning,Methodology; Computer Vision,Incremental learning of a sequence of tasks when the task-ID is not available at test time.
Non-exemplar-based Class Incremental Learning,1,CIFAR-100,N/A,N/A,N/A,N/A,N/A
Data Free Quantization,1,CIFAR-100,N/A,N/A,N/A,N/A,N/A
Classifier calibration,1,CIFAR-100,N/A,N/A,N/A,N/A,N/A
class-incremental learning,1,CIFAR-100,cifar100,10-stage average accuracy,N/A,N/A,N/A
Overlapped 100-50,1,ADE20K,N/A,N/A,N/A,N/A,N/A
Overlapped 50-50,1,ADE20K,N/A,N/A,N/A,N/A,N/A
Overlapped 100-10,1,ADE20K,N/A,N/A,N/A,N/A,N/A
Overlapped 100-5,1,ADE20K,N/A,N/A,N/A,N/A,N/A
Overlapped 25-25,1,ADE20K,N/A,N/A,N/A,N/A,N/A
NMR J-coupling,1,QM9,N/A,N/A,N/A,N/A,N/A
Race/Unbiased,1,UTKFace,N/A,N/A,N/A,N/A,N/A
Age/Unbiased,1,UTKFace,N/A,N/A,N/A,N/A,N/A
Surgical Skills Evaluation,1,JIGSAWS,MISTIC-SIL; JIGSAWS,Edit Distance; Accuracy,N/A,Medical,The task is to classify surgical skills using data that is recorded during the surgical intervention.
Outdoor Light Source Estimation,1,SUN360,N/A,N/A,N/A,N/A,N/A
Materials Screening,1,OQMD v1.2,N/A,N/A,N/A,N/A,N/A
Cadenza 1 - Task 1 - Headphone,1,MUSDB18,N/A,N/A,N/A,N/A,N/A
Parallel Corpus Mining,1,ASLG-PC12,N/A,N/A,N/A,N/A,N/A
Story Continuation,1,VIST,FlintstonesSV; PororoSV; VIST,FID; F-Acc; Char-F1,N/A,Computer Vision,"The task involves providing an initial scene that can be obtained in real world use cases. By including this scene, a model can then copy and adapt elements from it as it generates subsequent images. ..."
NetHack Score,1,NetHack Learning Environment,N/A,N/A,N/A,N/A,N/A
MS-SSIM,1,DocUNet,N/A,N/A,N/A,N/A,N/A
Local Distortion,1,DocUNet,DocUNet,LD,N/A,N/A,N/A
SENTS,1,Universal Dependencies,N/A,N/A,N/A,N/A,N/A
MORPH,1,Universal Dependencies,N/A,N/A,N/A,N/A,N/A
UNLABELED_DEPENDENCIES,1,Universal Dependencies,N/A,N/A,N/A,N/A,N/A
LABELED_DEPENDENCIES,1,Universal Dependencies,N/A,N/A,N/A,N/A,N/A
LEMMA,1,Universal Dependencies,N/A,N/A,N/A,N/A,N/A
Informativeness,1,CrisisMMD,N/A,N/A,N/A,N/A,N/A
COVID-19 Modelling,1,WHO-COVID19 Dataset,N/A,N/A,N/A,N/A,N/A
Question Rewriting,1,CANARD,N/A,N/A,N/A,N/A,N/A
HTR,1,IAM,N/A,N/A,N/A,N/A,N/A
GPR,1,Poser,N/A,N/A,N/A,Computer Vision,Gaussian Process Regression
Montezuma's Revenge,1,Arcade Learning Environment,N/A,N/A,N/A,N/A,N/A
Cadenza 1 - Task 2 - In Car,1,FMA,N/A,N/A,N/A,N/A,N/A
Overlapped 10-1,1,Cityscapes,N/A,N/A,N/A,N/A,N/A
Domain 11-5,1,Cityscapes,N/A,N/A,N/A,N/A,N/A
Domain 11-1,1,Cityscapes,N/A,N/A,N/A,N/A,N/A
Domain 1-1,1,Cityscapes,N/A,N/A,N/A,N/A,N/A
Overlapped 14-1,1,Cityscapes,N/A,N/A,N/A,N/A,N/A
CARLA MAP Leaderboard,1,CARLA,N/A,N/A,N/A,N/A,N/A
CARLA longest6,1,CARLA,CARLA,Driving Score; Infraction Score; Route Completion,N/A,Robots,"longest6 is an evaluation benchmark for sensorimotor autonomous driving methods using the CARLA 0.9.10.1 simulator.  It consists of 36 long routes in the publicly available Town 01-06 which, are popul..."
CARLA Leaderboard 2.0,1,CARLA,N/A,N/A,N/A,N/A,N/A
Uncropping,1,Places,Places2 val,PD; FID; Fool rate,N/A,Computer Vision,N/A
Distributed Computing,1,MNIST-8M,N/A,N/A,Collaborative Inference; Distributed Voting,N/A,N/A
Game of Doom,1,VizDoom,N/A,N/A,N/A,N/A,N/A
Continuous Control (100k environment steps),1,DeepMind Control Suite,N/A,N/A,N/A,N/A,N/A
Continuous Control (500k environment steps),1,DeepMind Control Suite,N/A,N/A,N/A,N/A,N/A
SNES Games,1,Mario AI,N/A,N/A,N/A,N/A,N/A
D4RL,1,D4RL,N/A,N/A,N/A,N/A,N/A
Gym halfcheetah-random,1,D4RL,N/A,N/A,N/A,N/A,N/A
Gym halfcheetah-medium,1,D4RL,N/A,N/A,N/A,N/A,N/A
Gym halfcheetah-expert,1,D4RL,N/A,N/A,N/A,N/A,N/A
Gym halfcheetah-medium-expert,1,D4RL,N/A,N/A,N/A,N/A,N/A
Gym halfcheetah-medium-replay,1,D4RL,N/A,N/A,N/A,N/A,N/A
Gym halfcheetah-full-replay,1,D4RL,N/A,N/A,N/A,N/A,N/A
Adroid pen-human,1,D4RL,N/A,N/A,N/A,N/A,N/A
Adroid hammer-human,1,D4RL,N/A,N/A,N/A,N/A,N/A
Adroid door-human,1,D4RL,N/A,N/A,N/A,N/A,N/A
Adroid relocate-human,1,D4RL,N/A,N/A,N/A,N/A,N/A
Adroid pen-cloned,1,D4RL,N/A,N/A,N/A,N/A,N/A
Adroid hammer-cloned,1,D4RL,N/A,N/A,N/A,N/A,N/A
Adroid door-cloned,1,D4RL,N/A,N/A,N/A,N/A,N/A
Adroid relocate-cloned,1,D4RL,N/A,N/A,N/A,N/A,N/A
Decipherment,1,Chinese Gigaword,N/A,N/A,N/A,Natural Language Processing,N/A
TGIF-Transition,1,TGIF-QA,N/A,N/A,N/A,N/A,N/A
TGIF-Frame,1,TGIF-QA,N/A,N/A,N/A,N/A,N/A
ListOps,1,ListOps,N/A,N/A,N/A,N/A,N/A
Diffusion Personalization Tuning Free,1,AgeDB,N/A,N/A,N/A,N/A,N/A
Epidemiology,1,PHM2017,N/A,N/A,N/A,Medical,"**Epidemiology** is a scientific discipline that provides reliable knowledge for clinical medicine focusing on prevention, diagnosis and treatment of diseases. Research in Epidemiology aims at charact..."
Demosaicking,1,PixelShift200,N/A,N/A,N/A,N/A,N/A
Probabilistic Time Series Forecasting,1,Lorenz Dataset,N/A,N/A,N/A,N/A,N/A
Metaheuristic Optimization,1,OSTD,N/A,N/A,N/A,Methodology,"In computer science and mathematical optimization, a metaheuristic is a higher-level procedure or heuristic designed to find, generate, or select a heuristic (partial search algorithm) that may provid..."
Polyphone disambiguation,1,CPP,CPP,Accuracy,N/A,Natural Language Processing,A part of the TTS-front end framework which serves to predict the correct pronunciation for the input polyphone characters.
Sensor Modeling,1,eSports Sensors Dataset,N/A,N/A,N/A,N/A,N/A
Multivariate Time Series Forecastingm,1,ETT,N/A,N/A,N/A,N/A,N/A
Game of Hanabi,1,Hanabi Learning Environment,N/A,N/A,N/A,N/A,N/A
Two-sample testing,1,HIGGS Data Set,"CIFAR-10 vs CIFAR-10.1 (1000 samples); MNIST vs Fake MNIST; HDGM (d=10, N=4000); Blob (9 modes, 40 for each); HIGGS Data Set",Avg accuracy,N/A,Miscellaneous; Methodology,"In statistical hypothesis testing, a two-sample test is a test performed on the data of two random samples, each independently obtained from a different given population. The purpose of the test is to..."
Lexical Normalization,1,MultiSenti,LexNorm,Accuracy,Pronunciation Dictionary Creation,Natural Language Processing,Lexical normalization is the task of translating/transforming a non standard text to a standard register.  Example:  ``` new pix comming tomoroe new pictures coming tomorrow ```  Datasets usually cons...
Period Estimation,1,OmniArt,OmniArt,Mean absolute error,Art Period Estimation (544 Artists),Computer Vision,N/A
OrangeSum,1,OrangeSum,N/A,N/A,N/A,N/A,N/A
Lake Ice Monitoring,1,Photi-LakeIce,N/A,N/A,N/A,N/A,N/A
Clean-label Backdoor Attack (0.024%),1,PubFig,N/A,N/A,N/A,N/A,N/A
Micro-Expression Spotting,1,SAMM Long Videos,N/A,N/A,N/A,N/A,N/A
Game of Football,1,SoccerData,N/A,N/A,Injury Prediction; Pass Classification; Football Action Valuation,Playing Games,N/A
Replay Grounding,1,SoccerNet-v2,N/A,N/A,N/A,N/A,N/A
Caricature,1,WebCaricature Dataset,N/A,N/A,N/A,Computer Vision,**Caricature** is a pictorial representation or description that deliberately exaggerates a person’s distinctive features or peculiarities to create an easily identifiable visual likeness with a comic...
Split and Rephrase,1,WikiSplit,N/A,N/A,N/A,N/A,N/A
Winogrande,1,WinoGrande,WinoGrande,N/A,N/A,Natural Language Processing,N/A
Geophysics,1,xView,N/A,N/A,N/A,Miscellaneous,N/A
Precipitation Forecasting,1,SEVIR,SEVIR,CSI-pool4; CSI-pool16,N/A,Computer Vision,N/A
Noun Phrase Canonicalization,1,ReVerb45K,N/A,N/A,N/A,N/A,N/A
Hypernym Discovery,1,SemEval-2018 Task-9,Music domain; General; Medical domain,P@5; MRR; MAP,N/A,Natural Language Processing,"Given a corpus and a target term (hyponym), the task of hypernym discovery consists of extracting a set of its most appropriate hypernyms from the corpus. For example, for the input word “dog”, some v..."
Col BERTTriplet,1,CoNLL 2003,N/A,N/A,N/A,N/A,N/A
point of interests,1,Gowalla,N/A,N/A,N/A,N/A,N/A
Populist attitude,1,Us Vs. Them,N/A,N/A,N/A,N/A,N/A
Phone-level pronunciation scoring,1,speechocean762,N/A,N/A,N/A,N/A,N/A
Utterance-level pronounciation scoring,1,speechocean762,N/A,N/A,N/A,N/A,N/A
Non-Intrusive Load Monitoring,1,SynD,N/A,N/A,N/A,Miscellaneous; Time Series; Knowledge Base,N/A
Time Series Averaging,1,UCR Time Series Classification Archive,N/A,N/A,N/A,Time Series,N/A
Quantum Machine Learning,1,iris,iris; https://www.kaggle.com/datasets/saurabhshahane/classification-of-malwares,F1 score; Average F1,N/A,Medical,N/A
N-Queens Problem - All Possible Solutions,1,Gun Detection Dataset,N/A,N/A,N/A,N/A,N/A
Traveling Salesman Problem,1,TSP/HCP Benchmark set,N/A,N/A,N/A,N/A,N/A
Multi-Choice MRC,1,ExpMRC,N/A,N/A,N/A,N/A,N/A
Graphon Estimation,1,Netzschleuder,N/A,N/A,N/A,Graphs,N/A
Clinical Note Phenotyping,1,Rare Diseases Mentions in MIMIC-III,N/A,N/A,N/A,N/A,N/A
Computational Phenotyping,1,Rare Diseases Mentions in MIMIC-III,N/A,N/A,Patient Phenotyping,Medical,"**Computational Phenotyping** is the process of transforming the noisy, massive Electronic Health Record (EHR) data into meaningful medical concepts that can be used to predict the risk of disease for..."
Propaganda technique identification,1,Dataset of Propaganda Techniques of the State-Sponsored Information Operation of the People's Republic of China,N/A,N/A,N/A,N/A,N/A
Synthetic Data Evaluation,1,Titanic,N/A,N/A,N/A,N/A,N/A
Mobile Security,1,Dataset of Context information for Zero Interaction Security,N/A,N/A,N/A,Miscellaneous,N/A
Stable MCI vs Progressive MCI,1,ADNI,N/A,N/A,N/A,N/A,N/A
Human Body Volume Estimation,1,SURREALvols,N/A,N/A,N/A,N/A,N/A
Causal Identification,1,BCOPA-CE,N/A,N/A,N/A,Reasoning,N/A
HumanEval,1,HumanEval,N/A,N/A,N/A,N/A,N/A
MTEB Benchmark,1,HumanEval,N/A,N/A,N/A,N/A,N/A
AllNLI Triplet,1,HumanEval,N/A,N/A,N/A,N/A,N/A
Question-Answer categorization,1,QC-Science,QC-Science,R@10; R@15; R@20; R@5,N/A,Natural Language Processing,N/A
EditCompletion,1,C# EditCompletion,C# EditCompletion,Accuracy,N/A,Computer Code,"Given a code snippet that is partially edited, the goal is to predict a completion of the edit for the rest of the snippet."
Node Regression,1,Wiki Squirrel,N/A,N/A,N/A,N/A,N/A
Algorithmic Trading,1,S&P 500 Intraday Data,N/A,N/A,N/A,Time Series,An algorithmic trading system is a software that is used for trading in the stock market.
Patient Phenotyping,1,HiRID,HiRID,Balanced Accuracy,N/A,N/A,N/A
Circulatory Failure,1,HiRID,HiRID,Recall@50; AUPRC,N/A,Medical,"Continuous prediction of onset of circulatory failure in the next 12h, given the patient is not in failure now."
Respiratory Failure,1,HiRID,HiRID,Recall@50; AUPRC,N/A,Medical,Continuous prediction of onset of respiratory failure in the next 12h given the patient is not in failure now.
Remaining Length of Stay,1,HiRID,N/A,N/A,N/A,N/A,N/A
Task 2,1,SROIE,N/A,N/A,N/A,N/A,N/A
Problem-Solving Deliberation,1,DeliData,N/A,N/A,N/A,N/A,N/A
Classify murmurs,1,CirCor DigiScope,N/A,N/A,N/A,N/A,N/A
reinforcement-learning,1,NASA C-MAPSS,N/A,N/A,N/A,N/A,N/A
Capacity Estimation,1,PEM Fuel Cell Dataset,N/A,N/A,N/A,N/A,N/A
SMAC,1,SMAC,SMAC 6h_vs_8z; SMAC 3s5z_vs_3s6z; SMAC 3s5z_vs_4s6z; SMAC corridor_2z_vs_24zg; SMAC 26m_vs_30m; SMAC 27m_vs_30m; SMAC MMM2; SMAC MMM2_7m2M1M_vs_9m3M1M; SMAC 6h_vs_9z; SMAC corridor,Average Score; Median Win Rate,SMAC Plus; SMAC+,Playing Games,"The StarCraft Multi-Agent Challenge (SMAC) is a benchmark that provides elements of partial observability, challenging dynamics, and high-dimensional observation spaces. SMAC is built using the StarCr..."
Moving Point Cloud Processing,1,LiDAR-MOS,N/A,N/A,N/A,Time Series,N/A
Fine-Grained Facial Editing,1,CelebA-Dialog,N/A,N/A,N/A,N/A,N/A
VGSI,1,wikiHow-image,N/A,N/A,N/A,N/A,N/A
Safe Reinforcement Learning,1,safe-control-gym,N/A,N/A,N/A,N/A,N/A
GSM8K,1,GSM8K,GSM8K; gsm8k (5-shots),0-shot MRR; Accuracy,N/A,Natural Language Processing,N/A
DrugProt,1,DrugProt,N/A,N/A,N/A,N/A,N/A
Galaxy emergent property recreation,1,SDSS Galaxies,N/A,N/A,N/A,N/A,N/A
BBBC021 NSC Accuracy,1,CytoImageNet,N/A,N/A,N/A,N/A,N/A
CYCLoPs Accuracy,1,CytoImageNet,N/A,N/A,N/A,N/A,N/A
Multimodal GIF Dialog,1,GIF Reply Dataset,N/A,N/A,N/A,N/A,N/A
Success Rate (5 task-horizon),1,CALVIN,N/A,N/A,N/A,N/A,N/A
Avg. sequence length,1,CALVIN,N/A,N/A,N/A,N/A,N/A
BIG-bench Machine Learning,1,BIG-bench,38-Cloud; BIG-bench,account and password ; Accuracy,N/A,Methodology,This branch include most common machine learning fundamental algorithms.
Identify Odd Metapor,1,BIG-bench,BIG-bench,Accuracy,N/A,Reasoning,N/A
Odd One Out,1,BIG-bench,BIG-bench,Accuracy,N/A,Reasoning,This task tests to what extent a language model is able to identify the odd word.    Source: [BIG-bench](https://github.com/google/BIG-bench/tree/main/bigbench/benchmark_tasks/odd_one_out)
Crash Blossom,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Auto Debugging,1,BIG-bench,Big-bench Lite,Exact string match,N/A,Miscellaneous,N/A
Crass AI,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Empirical Judgments,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Timedial,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Business Ethics,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Moral Disputes,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Moral Permissibility,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
FEVER (2-way),1,BIG-bench,N/A,N/A,N/A,N/A,N/A
FEVER (3-way),1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Misconceptions,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
High School European History,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
High School US History,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
High School World History,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
International Law,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
Jurisprudence,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Management,1,BIG-bench,BIG-bench,Accuracy ,Asset Management,Miscellaneous,N/A
Marketing,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
Philosophy,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
Prehistory,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
Professional Law,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
World Religions,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
Entailed Polarity,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Evaluating Information Essentiality,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Metaphor Boolean,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Physical Intuition,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Presuppositions As NLI,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Professional Accounting,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Anatomy,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
College Medicine,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Human Aging,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Human Organs Senses Multiple Choice,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Nutrition,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Professional Medicine,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
Virology,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
English Proverbs,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Implicatures,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Implicit Relations,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
LAMBADA,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Movie Dialog Same Or Different,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Phrase Relatedness,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
RACE-h,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
RACE-m,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
College Biology,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
College Chemistry,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
College Computer Science,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
College Physics,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Conceptual Physics,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
High School Biology,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
High School Chemistry,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
High School Computer Science,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
High School Physics,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
High School Statistics,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Physics MC,1,BIG-bench,N/A,N/A,N/A,N/A,N/A
Econometrics,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
High School Geography,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
High School Government and Politics,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
High School Macroeconomics,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
High School Microeconomics,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
High School Psychology,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Human Sexuality,1,BIG-bench,BIG-bench,Accuracy,N/A,Miscellaneous,N/A
Public Relations,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Sociology,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
US Foreign Policy,1,BIG-bench,BIG-bench,Accuracy ,N/A,Miscellaneous,N/A
Sleep Staging,1,Montreal Archive of Sleep Studies,N/A,N/A,EEG based sleep staging; W-R-N Sleep Staging; W-R-L-D Sleep Staging; ECG based Sleep Staging,Time Series,Human Sleep Staging into W-R-N or W-R-L-D classes from multiple or single polysomnography signals
W-R-L-D Sleep Staging,1,Montreal Archive of Sleep Studies,N/A,N/A,N/A,N/A,N/A
W-R-N Sleep Staging,1,Montreal Archive of Sleep Studies,N/A,N/A,N/A,N/A,N/A
Atari Games 100k,1,Atari 100k,N/A,N/A,N/A,N/A,N/A
fr-en,1,CVSS,N/A,N/A,N/A,N/A,N/A
es-en,1,CVSS,N/A,N/A,N/A,N/A,N/A
de-en,1,CVSS,N/A,N/A,N/A,N/A,N/A
EEG 4 classes,1,EEG Motor Movement/Imagery Dataset,N/A,N/A,N/A,N/A,N/A
EEG Left/Right hand,1,EEG Motor Movement/Imagery Dataset,N/A,N/A,N/A,N/A,N/A
EM showers clusterization,1,Simulated EM showers data,N/A,N/A,N/A,N/A,N/A
Topic coverage,1,Topic modeling topic coverage dataset,N/A,N/A,N/A,N/A,N/A
Single Choice Question,1,MML,N/A,N/A,N/A,N/A,N/A
Toponym Resolution,1,TR-News,N/A,N/A,N/A,Natural Language Processing,The goal is to find a mapping from a toponym (a location mention) in the text to a spatial footprint.
RoomEnv-v0,1,RoomEnv-v0,N/A,N/A,N/A,N/A,N/A
Surveillance-to-Single,1,IJB-S,N/A,N/A,N/A,N/A,N/A
Surveillance-to-Booking,1,IJB-S,N/A,N/A,N/A,N/A,N/A
Surveillance-to-Surveillance,1,IJB-S,N/A,N/A,N/A,N/A,N/A
ECG Wave Delineation,1,LUDB,N/A,N/A,N/A,N/A,N/A
TDC ADMET Benchmarking Group,1,tdcommons,N/A,N/A,N/A,N/A,N/A
SpO2 estimation,1,MTHS,N/A,N/A,N/A,N/A,N/A
Time Offset Calibration,1,MTic,N/A,N/A,N/A,Miscellaneous,N/A
Mental Workload Estimation,1,STEW,N/A,N/A,N/A,Computer Vision,N/A
Industrial Robots,1,Synthetic Object Preference Adaptation Data,N/A,N/A,Trajectory Planning,Robots,"An industrial robot is a robot system used for manufacturing. Industrial robots are automated, programmable and capable of movement on three or more axes."
Singer Identification,1,VocalSet,N/A,N/A,N/A,N/A,N/A
Skill Mastery,1,RGB-Stacking,RGB-Stacking,Average; Group 3; Group 1; Group 4; Group 2; Group 5,N/A,Robots,N/A
Binary Quantification,1,genius,N/A,N/A,N/A,N/A,"Prediction of class prevalence in test samples that may exhibit prior probability shift from training data, in a binary setting."
Unsupervised Spatial Clustering,1,Replication Data for: Singapore Soundscape Site Selection Survey (S5),N/A,N/A,N/A,Time Series,N/A
Privacy Preserving,1,ConcurrentQA Benchmark,N/A,N/A,Graph Neural Network,Graphs,N/A
Log Solubility,1,ESOL (Estimated SOLubility),ESOL,RMSE,N/A,Miscellaneous,N/A
Row Annotation,1,T2Dv2,N/A,N/A,N/A,N/A,N/A
Morpheme Segmentaiton,1,UniMorph 4.0,UniMorph 4.0,f1 macro avg (subtask 2); macro avg (subtask 1); lev dist (subtask 2),N/A,Natural Language Processing,Succesful systems segment a given word or sentence into a sequence of morphemes.
Chemical Indexing,1,BC7 NLM-Chem,BC7 NLM-Chem,F1-score (strict),N/A,Natural Language Processing,Predict which chemicals should be indexed.
Virtual Try-Off,1,VITON-HD,N/A,N/A,N/A,N/A,N/A
Playing the Game of 2048,1,The Game of 2048,The Game of 2048,Average Score,N/A,Playing Games,N/A
Lexical Analysis,1,UzWordnet,N/A,N/A,Lexical Complexity Prediction,Natural Language Processing,Lexical analysis is the process of converting a sequence of characters into a sequence of tokens (strings with an assigned and thus identified meaning). (Source: Adapted from Wikipedia)
Negation Scope Resolution,1,The BioScope Corpus,N/A,N/A,N/A,N/A,N/A
Speculation Scope Resolution,1,The BioScope Corpus,N/A,N/A,N/A,N/A,N/A
Negation and Speculation Scope resolution,1,The BioScope Corpus,N/A,N/A,N/A,Natural Language Processing,N/A
Exemplar-Free Counting,1,FSC147,N/A,N/A,N/A,N/A,N/A
A-VB Two,1,HUME-VB,N/A,N/A,N/A,N/A,N/A
A-VB High,1,HUME-VB,N/A,N/A,N/A,N/A,N/A
A-VB Culture,1,HUME-VB,N/A,N/A,N/A,N/A,N/A
Open Relation Modeling,1,Open Relation Modeling,N/A,N/A,N/A,Natural Language Processing,N/A
Game of Go,1,PAGE,ELO Ratings,ELO Rating,N/A,Playing Games,"Go is an abstract strategy board game for two players, in which the aim is to surround more territory than the opponent. The task is to train an agent to play the game and be superior to other players..."
Holdout Set,1,xView3-SAR,xView3-SAR,Aggregate xView3 Score,N/A,Computer Vision,N/A
Humanitarian,1,HumSet,N/A,N/A,N/A,N/A,N/A
Descriptive,1,CRIPP-VQA,N/A,N/A,N/A,N/A,N/A
Remove - PQ,1,CRIPP-VQA,N/A,N/A,N/A,N/A,N/A
Remove - PO,1,CRIPP-VQA,N/A,N/A,N/A,N/A,N/A
Replace - PQ,1,CRIPP-VQA,N/A,N/A,N/A,N/A,N/A
Replace - PO,1,CRIPP-VQA,N/A,N/A,N/A,N/A,N/A
Add - PQ,1,CRIPP-VQA,N/A,N/A,N/A,N/A,N/A
Add - PO,1,CRIPP-VQA,N/A,N/A,N/A,N/A,N/A
Specificity,1,MACSum,N/A,N/A,N/A,Natural Language Processing,N/A
Partially Observable Reinforcement Learning,1,POPGym,N/A,N/A,N/A,N/A,N/A
Model-based Reinforcement Learning,1,POPGym,N/A,N/A,N/A,N/A,N/A
Definition Modelling,1,UJ-CS/Math/Phy,N/A,N/A,N/A,Natural Language Processing,N/A
Vocal ensemble separation,1,jaCappella,N/A,N/A,N/A,N/A,N/A
Navigate,1,G-VUE,N/A,N/A,Universal Navigation; Go to AnyThing,Robots,N/A
RoomEnv-v1,1,RoomEnv-v1,N/A,N/A,N/A,N/A,N/A
Card Games,1,Cards Against Humanity,N/A,N/A,Game of Hanabi; Solitaire; Klondike; Game of Poker,Playing Games,Card games involve playing cards: the task is to train an agent to play the game with specified rules and beat other players.
FocusNews (test),1,WMT-SLT,N/A,N/A,N/A,N/A,N/A
SRF (test),1,WMT-SLT,N/A,N/A,N/A,N/A,N/A
Protein Folding,1,PS4,N/A,N/A,N/A,Natural Language Processing,N/A
Driver Identification,1,Overall-Driving-Behavior-Recognition-By-Smartphone,N/A,N/A,N/A,N/A,N/A
Graph Attention,1,NBA player performance prediction dataset,N/A,N/A,N/A,Graphs,N/A
Film Simulation,1,FilmSet,N/A,N/A,N/A,N/A,N/A
Acne Severity Grading,1,ACNE04,ACNE04,Accuracy,N/A,Medical,N/A
Subgraph Counting - K4,1,Synthetic Graph,N/A,N/A,N/A,N/A,N/A
Subgraph Counting - Triangle,1,Synthetic Graph,N/A,N/A,N/A,N/A,N/A
Subgraph Counting - 3 Star,1,Synthetic Graph,N/A,N/A,N/A,N/A,N/A
Subgraph Counting - Chordal C4,1,Synthetic Graph,N/A,N/A,N/A,N/A,N/A
Subgraph Counting - 2 star,1,Synthetic Graph,N/A,N/A,N/A,N/A,N/A
Subgraph Counting - C4,1,Synthetic Graph,N/A,N/A,N/A,N/A,N/A
Subgraph Counting - Tailed Triangle,1,Synthetic Graph,N/A,N/A,N/A,N/A,N/A
Subgraph Counting - C5,1,Synthetic Graph,N/A,N/A,N/A,N/A,N/A
Subgraph Counting - C6,1,Synthetic Graph,N/A,N/A,N/A,N/A,N/A
Subgraph Counting,1,Synthetic Graph,N/A,N/A,N/A,Graphs,N/A
Hurricane Forecasting,1,Extreme Events > Natural Disasters > Hurricane,N/A,N/A,N/A,Computer Vision,"Tropical Cyclone Forecasting using Computer Vision, Deep Learning, and Time-Series methods"
All,1,PTB-XL,N/A,N/A,N/A,N/A,N/A
Diagnostic,1,PTB-XL,N/A,N/A,N/A,N/A,N/A
Sub-diagnostic,1,PTB-XL,N/A,N/A,N/A,N/A,N/A
Super-diagnostic,1,PTB-XL,N/A,N/A,N/A,N/A,N/A
Form,1,PTB-XL,N/A,N/A,N/A,N/A,N/A
VCGBench-Diverse,1,VideoInstruct,VideoInstruct,Temporal Understanding; Dense Captioning; mean; Correctness of Information; Contextual Understanding; Spatial Understanding; Detail Orientation; Consistency; Reasoning,N/A,Computer Vision,"Recognizing the limited diversity in existing video conversation benchmarks, we introduce VCGBench-Diverse to comprehensively evaluate the generalization ability of video LMMs. While VCG-Bench provide..."
Operator learning,1,BubbleML,N/A,N/A,N/A,Miscellaneous,Learn an operator between infinite dimensional Hilbert spaces or Banach spaces
Solar Irradiance Forecasting,1,ASOS Data,ASOS Data,Accuracy; Variance; MAE; MSE; R^2,N/A,Time Series,N/A
Proper Noun,1,OVDEval,N/A,N/A,N/A,N/A,N/A
XLM-R,1,Belebele,N/A,N/A,N/A,N/A,N/A
Bayesian Optimization,1,OCB,N/A,N/A,Bayesian Optimisation,Methodology,N/A
Bug fixing,1,SWE-bench-lite,N/A,N/A,N/A,N/A,N/A
Travel Time Estimation,1,TTE-A&O,N/A,N/A,N/A,N/A,N/A
Automatic Lyrics Transcription,1,Jam-ALT,N/A,N/A,N/A,N/A,N/A
Virtual Try-on (Shop2Street),1,StreetTryOn,N/A,N/A,N/A,N/A,N/A
Virtual Try-on (Model2Street),1,StreetTryOn,N/A,N/A,N/A,N/A,N/A
Virtual Try-on (Street2Street),1,StreetTryOn,N/A,N/A,N/A,N/A,N/A
Vignetting Removal,1,VigSet,N/A,N/A,N/A,N/A,N/A
Model Editing,1,KnowEdit,N/A,N/A,knowledge editing,Natural Language Processing,N/A
SVBRDF Estimation,1,MatSynth,N/A,N/A,N/A,Computer Vision,SVBRDF Estimation
TAR,1,CLEF-TAR,N/A,N/A,N/A,N/A,N/A
tabular-regression,1,SupplyGraph,N/A,N/A,N/A,N/A,N/A
Mixed Reality,1,DREAMING Inpainting Dataset,N/A,N/A,N/A,Computer Vision,N/A
Self-Learning,1,LEARNING STYLE IDENTIFICATION,N/A,N/A,N/A,Natural Language Processing,N/A
Joint Radar-Communication,1,dichasus-cf0x,N/A,N/A,N/A,Robots,Intelligently decide how to simultaneously conduct radar and communication over a shared radio channel.
EEG,1,SPaRCNet,N/A,N/A,N/A,Time Series; Medical,Electroencephalography epilepsy
Multiclass Quantification,1,THAR Dataset,N/A,N/A,N/A,N/A,"Prediction of class prevalence in test samples that may exhibit prior probability shift from training data, in a multiclass setting."
Neural Network Security,1,SafeEdit,N/A,N/A,Website Fingerprinting Defense,Miscellaneous; Adversarial,N/A
Long Term Anticipation,1,EgoExoLearn,N/A,N/A,N/A,N/A,N/A
point cloud upsampling,1,PU1K,N/A,N/A,N/A,N/A,N/A
Film Removal,1,Polarized Film Removal Dataset,N/A,N/A,N/A,Computer Vision,"Film Removal (FR) aims to remove the transparent film and reveal the hidden information, benefiting the robustness of the industrial downstream models."
Citation worthiness,1,PMOA-CITE,N/A,N/A,N/A,N/A,N/A
Omniverse Isaac Gym,1,Omniverse Isaac Gym,N/A,N/A,N/A,N/A,N/A
Acrobot,1,Omniverse Isaac Gym,N/A,N/A,N/A,N/A,N/A
Low-latency processing,1,5GAD-2022,N/A,N/A,N/A,N/A,N/A
Hypergraph Contrastive Learning,1,Twitter-HyDrug-UR,N/A,N/A,N/A,N/A,N/A
Semi-Supervised Person Re-Identification,1,ENTIRe-ID,N/A,N/A,N/A,N/A,N/A
Domain Adaptive Person Re-Identification,1,ENTIRe-ID,N/A,N/A,N/A,N/A,N/A
MMLU,1,MMLU-Pro,mmlu (5-shots); MMLU-Pro; mmlu (chat CoT),0-shot MRR,N/A,Knowledge Base,N/A
Hallucination,1,MMNeedle,MMNeedle,N/A,N/A,N/A,N/A
Person Identification (1-shot),1,WiGesture,N/A,N/A,N/A,N/A,N/A
Fashion Compatibility Learning,1,iFashion Alibaba,N/A,N/A,N/A,Computer Vision,N/A
4k,1,UHD-IQA,N/A,N/A,N/A,N/A,N/A
Dataset Size Recovery,1,LoRA-WiSE,N/A,N/A,N/A,Adversarial,"The task dataset size recovery aims to determine the number of samples used to train a model, directly from its weights."
Sequential Pattern Mining,1,10 Synthetic Genomics Datasets,N/A,N/A,N/A,N/A,N/A
Layout Design,1,Modified Swiss Dwellings,N/A,N/A,N/A,Computer Vision,N/A
RoomEnv-v2,1,RoomEnv-v2,N/A,N/A,N/A,N/A,N/A
Vietnamese Datasets,1,BKEE,N/A,N/A,N/A,Natural Language Processing,N/A
Game of Poker,1,Poker Hand Histories,N/A,N/A,N/A,Playing Games,N/A
MMR total,1,MRR-Benchmark,MRR-Benchmark,Total Column Score,N/A,Computer Vision,"Sum of all scores of the 11 distinct tasks involving texts, fonts, visual elements, bounding boxes, spatial relations, and grounding in the Multi-Modal Reading (MMR) Benchmark."
GermEval2024 Shared Task 1 Subtask 1,1,GerMS-AT,GerMS-AT,Macro F1,N/A,Natural Language Processing,GermEval 2024 Shared Task 1 Subtask 1 involves predicting sexism labels for online comments based on human annotations that assess the strength of sexism or misogyny. Different strategies are used to ...
GermEval2024 Shared Task 1 Subtask 2,1,GerMS-AT,GerMS-AT,Jensen-Shannon distance,N/A,Natural Language Processing,"GermEval 2024 Shared Task 1 Subtask 2 focuses on predicting the distribution of sexism labels for each user comment, based on the original label distribution assigned by human annotators. One must pre..."
Occlusion Estimation,1,IITKGP_Fence Dataset,N/A,N/A,N/A,Computer Vision,N/A
TiROD,1,TiROD,N/A,N/A,N/A,N/A,N/A
Flood extent forecasting,1,GFF,N/A,N/A,N/A,N/A,N/A
News Authenticity Identification,1,Dhoroni,N/A,N/A,N/A,N/A,N/A
Li-ion State of Health Estimation,1,NASA Li-ion Dataset,NASA Li-ion Dataset,mean absolute error,N/A,Time Series,Prediction of a Li-ion's state of health
Chemical Process,1,MAX-60K,N/A,N/A,Geochemistry,Miscellaneous; Time Series,N/A
software testing,1,SoliDiffy Differencing Contract Pairs and Edit Scripts,N/A,N/A,N/A,N/A,N/A
erson Re-Identification,1,BuckTales,N/A,N/A,N/A,N/A,N/A
Demand Forecasting,1,SCG,N/A,N/A,N/A,N/A,N/A
Neural Network simulation,1,3D Flow Shapes,N/A,N/A,N/A,N/A,N/A
Liquid Simulation,1,3D Flow Shapes,N/A,N/A,N/A,N/A,N/A
Equilibrium traffic assignment,1,Equilibrium-Traffic-Networks,N/A,N/A,N/A,N/A,N/A
Authorship Attribution,1,BN-AuthProf,N/A,N/A,Source Code Authorship Attribution,Natural Language Processing,"Authorship attribution, also known as authorship identification, aims to attribute a previously unseen text of unknown authorship to one of a set of known authors."
Author Profiling,1,BN-AuthProf,N/A,N/A,N/A,N/A,N/A
MMSQL performance,1,MMSQL,N/A,N/A,N/A,N/A,N/A
Task Planning,1,Plancraft,N/A,N/A,N/A,N/A,N/A
Quant Trading,1,Gap Pattern Detection,N/A,N/A,N/A,N/A,N/A
Raspberry Pi 3,1,CPU,N/A,N/A,N/A,N/A,N/A
Raspberry Pi 4,1,CPU,N/A,N/A,N/A,N/A,N/A
Raspberry Pi 5,1,CPU,N/A,N/A,N/A,N/A,N/A
Crashworthiness Design,1,Database of axial impact simulations of the crash box,N/A,N/A,N/A,N/A,N/A
ECG Digitization,1,ECG-Image-Database,ECG-Image-Database,SNR,N/A,Computer Vision,Digitize print-outs of ECGs.
Sand,1,MPM-Verse,N/A,N/A,N/A,N/A,N/A
Sequential Decision Making,1,BeNYfits,N/A,N/A,N/A,Methodology,N/A
Credit score,1,GMSC,N/A,N/A,N/A,N/A,N/A
Load Virtual Sensing,1,SKF-BLS Dataset,N/A,N/A,N/A,N/A,N/A
Load Virtual Sensing (Fx),1,SKF-BLS Dataset,N/A,N/A,N/A,N/A,N/A
Load Virtual Sensing (Fy),1,SKF-BLS Dataset,N/A,N/A,N/A,N/A,N/A
Red Teaming,1,SUDO Dataset,SUDO Dataset,Attack Success Rate,N/A,Adversarial,N/A
Real-World Adversarial Attack,1,SUDO Dataset,N/A,N/A,N/A,N/A,N/A
Atomic Forces,1,MD22,N/A,N/A,N/A,N/A,N/A
Field Boundary Delineation,1,FBIS-22M,N/A,N/A,N/A,N/A,N/A
LLM real-life tasks,1,High-Quality Invoice Images for OCR,N/A,N/A,N/A,N/A,N/A
Fixed Few Shot Prompting Danger Assessment,1,ViDAS,N/A,N/A,N/A,N/A,N/A
Zero Shot Prompting Danger Assessment,1,ViDAS,N/A,N/A,N/A,N/A,N/A
Fixed Few Shot Prompting,1,ViDAS,N/A,N/A,N/A,N/A,N/A
Building Damage Assessment,1,BRIGHT,N/A,N/A,N/A,N/A,N/A
automatic short answer grading,1,SAS-Bench,N/A,N/A,N/A,N/A,N/A
Causal Judgment,1,AC-Bench,N/A,N/A,N/A,N/A,N/A
Deep Learning,1,CornHub,N/A,N/A,Polynomial Neural Networks,Methodology; Computer Vision; Computer Code; Natural Language Processing,forecast
Procedure Learning,1,LCStep,N/A,N/A,N/A,Computer Vision,"Given a set of videos of the same task, the goal is to identify the key-steps required to perform the task."
Scheduling,1,LLMafia,N/A,N/A,N/A,N/A,Project or Job Scheduling
Game of Mafia,1,LLMafia,N/A,N/A,N/A,Playing Games,N/A
Asynchronous Group Communication,1,LLMafia,N/A,N/A,N/A,Natural Language Processing,N/A
Building Flood Damage Assessment,1,HarveyPDE,N/A,N/A,N/A,N/A,N/A
2k,1,((Easy resolve issue~guide))How do I resolve a dispute with Expedia?,N/A,N/A,N/A,N/A,N/A
Reinforcement Learning (Atari Games),1,Seaquest - OpenAI Gym,Seaquest - OpenAI Gym,Average Return,N/A,Playing Games,Reinforcement Learning (Atari Games) - Application of Deep Learning and Reinforcement Learning i.e. Deep Reinforcement Learning.
Foveation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Cancer,0,N/A,N/A,N/A,Oral Cancer Classification; Breast Cancer Detection; Classification Of Breast Cancer Histology Images; Discovery Of Integrative Cancer Subtypes; Colon Cancer Detection In Confocal Laser Microscopy Images,Computer Vision; Knowledge Base; Medical,N/A
Data Mining,0,N/A,N/A,N/A,cognitive diagnosis; Parallel Corpus Mining; CSV dialect detection; Sequential Pattern Mining; Opinion Mining,Methodology; Computer Code; Natural Language Processing,N/A
Dehazing,0,N/A,N/A,N/A,Image Dehazing; Single Image Dehazing,Computer Vision,N/A
Forgery,0,N/A,N/A,N/A,Localization In Video Forgery,Computer Vision,N/A
Write Computer Programs From Specifications,0,N/A,N/A,N/A,N/A,Computer Code,N/A
Clustering Multivariate Time Series,0,N/A,N/A,N/A,N/A,Time Series,N/A
Chinese,0,N/A,N/A,N/A,Handwritten Chinese Text Recognition; Chinese Spelling Error Correction; Chinese Word Segmentation; Offline Handwritten Chinese Character Recognition; Chinese Zero Pronoun Resolution,Natural Language Processing,Chinese language processing is the task of applying natural language processing to the Chinese language.
Ecommerce,0,N/A,N/A,N/A,Product Categorization; Online Review Rating; Product Recommendation; Online Ranker Evaluation,Miscellaneous,N/A
Sequential Diagnosis,0,N/A,N/A,N/A,N/A,Medical,N/A
Patient Outcomes,0,N/A,N/A,N/A,Outcome Prediction In Multimodal Mri; Predicting Patient Outcomes,Medical,N/A
Mammogram,0,N/A,N/A,N/A,Whole Mammogram Classification; Mass Segmentation From Mammograms; Breast Mass Segmentation In Whole Mammograms,Medical,N/A
Ecg Risk Stratification,0,N/A,ngm,520,N/A,Medical,N/A
Atrial Fibrillation,0,N/A,N/A,N/A,Atrial Fibrillation Detection; Atrial Fibrillation Recurrence Estimation,Medical,N/A
Advertising,0,N/A,N/A,N/A,Detecting Adverts,Miscellaneous,N/A
Hand,0,N/A,N/A,N/A,Hand Pose Estimation; Gesture-to-Gesture Translation; Hand Gesture Recognition; Hand Keypoint Localization; Hand-Gesture Recognition,Computer Vision,N/A
Remote Sensing,0,N/A,N/A,N/A,Remote Sensing Image Classification; Change detection for remote sensing images; Segmentation Of Remote Sensing Imagery; The Semantic Segmentation Of Remote Sensing Imagery; Extracting Buildings In Remote Sensing Images,Miscellaneous; Methodology; Computer Vision,N/A
Molecule Interpretation,0,N/A,N/A,N/A,N/A,Medical,N/A
Tomography,0,N/A,N/A,N/A,Electron Tomography; Tomographic Reconstructions; Quantum State Tomography,Medical,N/A
Magnetic Resonance Fingerprinting,0,N/A,N/A,N/A,N/A,Medical,N/A
Animation,0,N/A,N/A,N/A,Image Animation; 3D Character Animation From A Single Photo,Computer Vision,N/A
Bilevel Optimization,0,N/A,Equilibrium-Traffic-Networks/Anaheim;  Equilibrium-Traffic-Networks/Eastern Massachusetts; 	Equilibrium-Traffic-Networks/Sioux Falls,Optimality Gap,N/A,Methodology,"**Bilevel Optimization** is a branch of optimization, which contains a nested optimization problem within the constraints of the outer optimization problem. The outer optimization task is usually refe..."
Dynamic graph embedding,0,N/A,N/A,N/A,Knowledge Base Completion,Graphs,N/A
Tensor Networks,0,N/A,N/A,N/A,N/A,Methodology,N/A
Portfolio Optimization,0,N/A,Yahoo ,Portfolio,N/A,Time Series,"Portfolio management is the task of obtaining higher excess returns through the flexible allocation of asset weights. In reality, common examples are stock selection and the Enhanced Index Fund (EIF)...."
hypergraph embedding,0,N/A,N/A,N/A,hyperedge classification,Graphs,Compute useful representations of hyperedges and vertices
Intelligent Surveillance,0,N/A,N/A,N/A,Vehicle Re-Identification,Computer Vision,N/A
Pain Intensity Regression,0,N/A,UNBC-McMaster ShoulderPain dataset,Pearson Correlation Coefficient ; MAE (VAS); MAE,N/A,Medical,N/A
Safe Exploration,0,N/A,N/A,N/A,N/A,Robots,"**Safe Exploration** is an approach to collect ground truth data by safely interacting with the environment.   <span class=""description-source"">Source: [Chance-Constrained Trajectory Optimization for ..."
Game of Chess,0,N/A,N/A,N/A,N/A,Playing Games,"Chess is a two-player strategy board game played on a chessboard, a checkered gameboard with 64 squares arranged in an 8×8 grid. The idea of making a machine that could beat a Grandmaster human player..."
Game of Shogi,0,N/A,ELO Ratings,ELO Rating,N/A,Playing Games,N/A
FPS Games,0,N/A,N/A,N/A,Game of Doom,Playing Games,"First-person shooter (FPS) games Involve like call of duty so enjoy    <span style=""color:grey; opacity: 0.6"">( Image credit: [Procedural Urban Environments for FPS Games](https://arxiv.org/pdf/1604.0..."
Network Congestion Control,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Artificial Life,0,N/A,N/A,N/A,Developmental Learning,Miscellaneous; Robots,N/A
Skull Stripping,0,N/A,N/A,N/A,N/A,Medical,N/A
Distributional Reinforcement Learning,0,N/A,N/A,N/A,N/A,Methodology,Value distribution is the distribution of the random return received by a reinforcement learning agent.  it been used for a specific purpose such as implementing risk-aware behaviour.     We have rand...
Cryptanalysis,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Learning to Execute,0,N/A,N/A,N/A,N/A,Computer Code,N/A
Outdoor Positioning,0,N/A,N/A,N/A,N/A,Miscellaneous,Outdoor Positioning (e.g. GPS)
Materials Imaging,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Query Wellformedness,0,N/A,Query Wellformedness,Accuracy,N/A,Natural Language Processing,"Assessing whether a query is grammatically correct, contains no spelling mistakes, and asks an explicit question.    Image Source: [Identifying Well-formed Natural Language Questions](https://arxiv.or..."
Automatic Writing,0,N/A,N/A,N/A,N/A,Natural Language Processing,Generating text based on internal machine representations.
Bayesian Optimisation,0,N/A,N/A,N/A,Bayesian Optimization,Methodology,"Expensive black-box functions are a common problem in many disciplines, including tuning the parameters of machine learning algorithms, robotics, and other engineering design problems. **Bayesian Opti..."
Radio Interferometry,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Seismic Interpretation,0,N/A,N/A,N/A,Facies Classification; Seismic Detection,Miscellaneous,N/A
Steganographics,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Problem Decomposition,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Non-Linear Elasticity,0,N/A,N/A,N/A,Cantilever Beam; Stress-Strain Relation,Miscellaneous,N/A
Metamerism,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Web Credibility,0,N/A,N/A,N/A,N/A,Methodology,Define the level of credibility of web sources
Observation Completion,0,N/A,N/A,N/A,Active Observation Completion,Computer Vision,N/A
Graph Neural Network,0,N/A,..,0S,N/A,Graphs,N/A
hypergraph partitioning,0,N/A,N/A,N/A,N/A,Graphs,N/A
Game of Cricket,0,N/A,N/A,N/A,N/A,Playing Games,N/A
Transparency Separation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Molecular Dynamics,0,N/A,N/A,N/A,N/A,Medical,N/A
Data Poisoning,0,N/A,N/A,N/A,N/A,Adversarial,**Data Poisoning** is an adversarial attack that tries to manipulate the training dataset in order to control the prediction behavior of a trained model such that the model will label malicious exampl...
RDF Dataset Discovery,0,N/A,N/A,N/A,N/A,Knowledge Base,Given a URI find the RDF datasets containing this URI.
Lightfield,0,N/A,N/A,N/A,N/A,Computer Vision,Tasks related to the light-field imagery
L2 Regularization,0,N/A,N/A,N/A,N/A,Methodology,"See [Weight Decay](https://paperswithcode.com/method/weight-decay).    **$L_{2}$ Regularization** or **Weight Decay**, is a regularization technique applied to the weights of a neural network. We mini..."
Automated Writing Evaluation,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Automated writing evaluation refers to the task of analysing and measuring written text based on features, such as syntax, text complexity and vocabulary range."
Landmine,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Meme Captioning,0,N/A,N/A,N/A,N/A,Natural Language Processing,Automatic generation of natural language descriptions of the content of an input meme.
Amodal Layout Estimation,0,N/A,N/A,N/A,N/A,Computer Vision,"Amodal scene layout estimation involves estimating the static and dynamic portion of an urban driving scene in bird's-eye view, given a single image. The concept of ""amodal"" estimation refers to the f..."
Cover song identification,0,N/A,Covers80; Da-TACOS; SHS100K-TEST; YouTube350,MAP; mAP,N/A,Music,"**Cover Song Identification** is the task of identifying an alternative version of a previous musical piece, even though it may differ substantially in timbre, tempo, structure, and even fundamental a..."
One-shot model fusion,0,N/A,N/A,N/A,N/A,N/A,Fusing different neural networks into a single network in one-shot
Information Plane,0,N/A,N/A,N/A,N/A,Methodology,"To obtain the Information Plane (IP) of deep neural networks, which shows the trajectories of the hidden layers during training in a 2D plane using as coordinate axes the mutual information between th..."
Mutual Information Estimation,0,N/A,N/A,N/A,N/A,Methodology,"To estimate mutual information from samples, specially for high-dimensional variables."
Misogynistic Aggression Identification,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Develop a binary classifier for classifying the text as ‘gendered’ or ‘non-gendered’. For this, the TRAC-2 dataset of 5,000 annotated data from social media each in Bangla (in both Roman and Bangla sc..."
NetHack,0,N/A,N/A,N/A,NetHack Score,Playing Games,Mean in-game score over 1000 episodes with random seeds not seen during training. See https://arxiv.org/abs/2006.13760 (Section 2.4 Evaluation Protocol) for details.
Blink estimation,0,N/A,RT-BENE; Researcher's Night; Eyeblink8,F1,N/A,Computer Vision,N/A
Spatial Interpolation,0,N/A,N/A,N/A,N/A,N/A,N/A
Hypergraph representations,0,N/A,N/A,N/A,Hypergraph Contrastive Learning,Graphs,N/A
quantum gate design,0,N/A,N/A,N/A,N/A,N/A,N/A
Sequential Quantile Estimation,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Re-basin,0,N/A,N/A,N/A,N/A,Knowledge Base,N/A
Quantum Circuit Equivalence Checking,0,N/A,N/A,N/A,N/A,Methodology,Equivalence Checking of Quantum Circuits
JSONiq Query Execution,0,N/A,N/A,N/A,N/A,Miscellaneous; Computer Code,"Execute JSONiq query, typically on semi-structured JSON data"
Cyber Attack Investigation,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Population Assignment,0,N/A,N/A,N/A,N/A,Medical,N/A
Survey Sampling,0,N/A,N/A,N/A,N/A,N/A,N/A
Influence Approximation,0,N/A,N/A,N/A,N/A,Methodology,Estimating the influence of training triples on the behavior of a machine learning model.
Robust Design,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Ensemble Pruning,0,N/A,N/A,N/A,N/A,N/A,N/A
statistical independence testing,0,N/A,N/A,N/A,N/A,Methodology,N/A
band gap regression,0,N/A,N/A,N/A,N/A,N/A,N/A
Sequential Correlation Estimation,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Making Hiring Decisions,0,N/A,SIOP 2020/2021,Final_score,N/A,Miscellaneous,N/A
Survey,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Domain Labelling,0,N/A,BabelDomains,F1-Score,N/A,Natural Language Processing,N/A
Cervical cancer biopsy identification,0,N/A,Cervical Cancer (Risk Factors) Data Set,Mean Accuracy,N/A,Medical,N/A
Breast Tissue Identification,0,N/A,Breast Tissue Data Set,Mean Accuracy,N/A,Medical,N/A
Sparse subspace-based clustering,0,N/A,N/A,N/A,N/A,Computer Code,N/A
Turning Point Identification,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Identification of key events in a narrative (such as movie or TV episode). The task is supported by screenwriting theory, according to which there are 5 different types of key events in a movie. These..."
Edge-computing,0,N/A,N/A,N/A,Tiling & Deployment; Device-Cloud Collaboration,Computer Code; Time Series,Deep Learning on EDGE devices
Neural Stylization,0,N/A,Meshes,Mean Opinion Score (Q1:Overall); Mean Opinion Score (Q3: Style); Mean Opinion Score (Q2: Content),N/A,Computer Vision,N/A
Temporal Processing,0,N/A,N/A,N/A,Temporal Information Extraction; Document Dating; Timex normalization,Natural Language Processing,N/A
Taxonomy Learning,0,N/A,N/A,N/A,Hypernym Discovery; Taxonomy Expansion,Natural Language Processing,Taxonomy learning is the task of hierarchically classifying concepts in an automatic manner from text corpora. The process of building taxonomies is usually divided into two main steps: (1) extracting...
Pulse wave simulation,0,N/A,N/A,N/A,N/A,Medical,Simulating arterial pulse waves
Formalize foundations of universal algebra in dependent type theory,0,N/A,N/A,N/A,N/A,Computer Code,N/A
Data Ablation,0,N/A,N/A,N/A,N/A,Computer Vision,"Data Ablation is the study of change in data, and its effects in the performance of Neural Networks."
Numerical Integration,0,N/A,N/A,N/A,N/A,Miscellaneous,Numerical integration is the task to calculate the numerical value of a definite integral or the numerical solution of differential equations.
Tensor Decomposition,0,N/A,N/A,N/A,N/A,N/A,N/A
Pronunciation Assessment,0,N/A,N/A,N/A,Phone-level pronunciation scoring; Utterance-level pronounciation scoring; Word-level pronunciation scoring,Speech,N/A
Dynamic Time Warping,0,N/A,N/A,N/A,N/A,Time Series,N/A
Exponential degradation,0,N/A,N/A,N/A,N/A,Time Series,Exponential degradation  used to solve problems where systems exposed to an exponential loss in performances such as reparable industrial systems.
Im2Spec,0,N/A,N/A,N/A,N/A,Computer Vision,Predicting spectra from images (and vice versa)
BRDF estimation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Stochastic Block Model,0,N/A,N/A,N/A,N/A,Graphs,N/A
Emergent communications on relations,0,N/A,N/A,N/A,N/A,Natural Language Processing,Emergent communications in the context of relations.
Reliable Intelligence Identification,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Unity,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Service Composition,0,N/A,N/A,N/A,N/A,Miscellaneous,"Let T be the task that the service composition needs to accomplish. The task T can be granulated to T 1 , T 2 , T 3 , T 4 , … , T n . i.e. T =  {T 1 , T 2 , T 3 , T 4 , … , T n } . For each task T i ,..."
Learning Theory,0,N/A,N/A,N/A,N/A,Miscellaneous,Learning theory
Riemannian optimization,0,N/A,N/A,N/A,N/A,N/A,Optimization methods on Riemannian manifolds.
Model Discovery,0,N/A,N/A,N/A,N/A,Miscellaneous,discovering PDEs from spatiotemporal data
Spike Sorting,0,N/A,N/A,N/A,N/A,N/A,Spike sorting is a class of techniques used in the analysis of electrophysiological data. Spike sorting algorithms use the shape(s) of waveforms collected with one or more electrodes in the brain to d...
Additive models,0,N/A,N/A,N/A,N/A,Methodology,N/A
Density Ratio Estimation,0,N/A,N/A,N/A,N/A,Methodology,Estimating the ratio of one density function to the other.
Experimental Design,0,N/A,N/A,N/A,N/A,Methodology,N/A
FAD,0,N/A,N/A,N/A,N/A,N/A,N/A
Hard Attention,0,N/A,N/A,N/A,N/A,Methodology,N/A
Literature Mining,0,N/A,N/A,N/A,Systematic Literature Review,Natural Language Processing; Knowledge Base,The task where the publication texts are used to mine knowledge using NLP
Superpixels,0,N/A,N/A,N/A,N/A,Computer Vision,"Superpixel techniques segment an image into regions based on similarity measures that utilize perceptual features, effectively grouping pixels that appear similar. The motivation behind this approach ..."
Anachronisms,0,N/A,N/A,N/A,N/A,Reasoning,N/A
DNN Testing,0,N/A,N/A,N/A,N/A,Adversarial,Testing the reliability of DNNs.
Side Channel Analysis,0,N/A,N/A,N/A,N/A,N/A,N/A
Variational Monte Carlo,0,N/A,N/A,N/A,N/A,Miscellaneous,Variational methods for quantum physics
Graph Nonvolutional Network,0,N/A,N/A,N/A,N/A,Graphs,N/A
Majority Voting Classifier,0,N/A,N/A,N/A,N/A,N/A,N/A
Neural Radiance Caching,0,N/A,N/A,N/A,N/A,Computer Vision,Involves the task of predicting photorealistic pixel colors from feature buffers.    Image source: [Instant Neural Graphics Primitives with a Multiresolution Hash Encoding](https://arxiv.org/pdf/2201....
Facial Editing,0,N/A,N/A,N/A,N/A,Computer Vision,Image source: [Stitch it in Time: GAN-Based Facial Editing of Real Videos](https://arxiv.org/pdf/2201.08361v2.pdf)
Tropical Cyclone Track Forecasting,0,N/A,N/A,N/A,N/A,Time Series,N/A
Tropical Cyclone Intensity Forecasting,0,N/A,N/A,N/A,N/A,Time Series,N/A
Procgen Hard (100M),0,N/A,N/A,N/A,N/A,Playing Games,N/A
SMC会议,0,N/A,N/A,N/A,N/A,N/A,N/A
ARQMath2,0,N/A,N/A,N/A,N/A,Natural Language Processing,Answer Retrieval for Questions about Math v2 (2021)
Nonparametric Clustering,0,N/A,N/A,N/A,N/A,N/A,Clustering when the number of clusters is unknwon
Seismic Inversion,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Seismic Imaging,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Suggestion mining,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Suggestion mining can be defined as the extraction of suggestions from unstructured text,"
Geometry-aware processing,0,N/A,N/A,N/A,N/A,N/A,Harness manifolds and manifold-aware optimization to process data (e.g. SPD manifold representations using covariance matrix).
Second-order methods,0,N/A,N/A,N/A,N/A,N/A,Use second-order statistics to process data.
PSO-ConvNets Dynamics 1,0,N/A,N/A,N/A,N/A,Computer Vision,Incorporating distilled Cucker-Smale elements into PSO algorithm using KNN and intertwine training with SGD
PSO-ConvNets Dynamics 2,0,N/A,N/A,N/A,N/A,Computer Vision,Incorporating distilled Cucker-Smale elements into PSO algorithm using KNN and intertwine training with SGD (Pull back method)
PAC learning,0,N/A,N/A,N/A,N/A,N/A,Probably Approximately Correct (PAC) learning analyzes machine learning mathematically using probability bounds.
Maximum Separation,0,N/A,N/A,N/A,N/A,N/A,N/A
Radar waveform design,0,N/A,N/A,N/A,N/A,N/A,N/A
Dataset Condensation,0,N/A,N/A,N/A,N/A,N/A,Condense the full dataset into a tiny set of synthetic data.
Weight Space Learning,0,N/A,N/A,N/A,N/A,N/A,"Learning from populations of neural network models (model zoo), where each model is given by a set of model parameters."
Network Interpretation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
File difference,0,N/A,N/A,N/A,N/A,Computer Code,"Generate edit script comparing 2 strings or files, which contains instruction of insert, delete and substitute to convert first string to the second."
Respiratory Rate Estimation,0,N/A,N/A,N/A,N/A,Medical,N/A
Molecular Docking,0,N/A,N/A,N/A,Blind Docking,Medical,"Predicting the binding structure of a small molecule ligand to a protein, which is critical to drug design.    Description from: [DiffDock: Diffusion Steps, Twists, and Turns for Molecular Docking](ht..."
Penn Machine Learning Benchmark (Real-World),0,N/A,N/A,N/A,N/A,Miscellaneous,Real-World Datasets in Penn Machine Learning Benchmark
Deep Feature Inversion,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Computational fabrication,0,N/A,N/A,N/A,N/A,N/A,N/A
Stability-aware design,0,N/A,N/A,N/A,N/A,N/A,N/A
Brain Morphometry,0,N/A,N/A,N/A,N/A,Medical,Measurement of brain structures from neuroimaging (MRI).
Rubik's Cube,0,N/A,N/A,N/A,N/A,Graphs,Solving the Rubik's Cube is a pathfinding task on a massive implicit graph.
Thompson Sampling,0,N/A,N/A,N/A,N/A,Methodology,"Thompson sampling, named after William R. Thompson, is a heuristic for choosing actions that addresses the exploration-exploitation dilemma in the multi-armed bandit problem. It consists of choosing t..."
Federated Unsupervised Learning,0,N/A,N/A,N/A,N/A,Methodology,Federated unsupervised learning trains models from decentralized data that have no labels.
Humanoid Control,0,N/A,N/A,N/A,N/A,Robots,"Control of a high-dimensional humanoid. This can include skill learning by tracking motion capture clips, learning goal-directed tasks like going towards a moving target, and generating motion within ..."
Spatial Token Mixer,0,N/A,N/A,N/A,N/A,Computer Vision,Spatial Token Mixer (STM) is a module for vision transformers that aims to improve the efficiency of token mixing. STM is a type of depthwise convolution that operates on the spatial dimension of the ...
Steganalysis,0,N/A,N/A,N/A,N/A,Computer Vision,Detect the usage of Steganography
highlight removal,0,N/A,N/A,N/A,N/A,Computer Vision,Highlight removal refers to the process of eliminating or reducing the presence of specular highlights in an image. Specular highlights are bright spots or reflections that occur when light reflects o...
Job Shop Scheduling,0,N/A,N/A,N/A,N/A,N/A,Scheduling Task
Li-ion battery degradation modes diagnosis,0,N/A,N/A,N/A,N/A,N/A,N/A
Battery diagnosis,0,N/A,N/A,N/A,N/A,N/A,N/A
Penn Machine Learning Benchmark,0,N/A,Real-world Datasets,R2 Score,N/A,Miscellaneous,Penn Machine Learning Benchmarks (PMLB) is a large collection of curated benchmark datasets for evaluating and comparing supervised machine learning algorithms.
Compiler Optimization,0,N/A,N/A,N/A,N/A,Computer Code,Machine learning guided compiler optimization
Data Valuation,0,N/A,N/A,N/A,Data Interaction,Methodology,"Data valuation in machine learning tries to determine the worth of data, or data sets, for downstream tasks. Some methods are task-agnostic and consider datasets as a whole, mostly for decision making..."
Efficient Neural Network,0,N/A,N/A,N/A,N/A,N/A,N/A
Eikonal Tomography,0,N/A,N/A,N/A,N/A,N/A,N/A
Tree Decomposition,0,N/A,N/A,N/A,N/A,Graphs,"**Tree Decomposition** is a technique in graph theory and computer science for representing a graph as a tree, where each node in the tree represents a set of vertices in the original graph. The goal ..."
Weakly-supervised Learning,0,N/A,N/A,N/A,N/A,N/A,N/A
Result aggregation,0,N/A,N/A,N/A,N/A,Methodology,N/A
quantum gate calibration,0,N/A,N/A,N/A,N/A,N/A,N/A
Ontology Subsumption Inferece,0,N/A,N/A,N/A,N/A,Knowledge Base,N/A
Ontology Embedding,0,N/A,N/A,N/A,N/A,Knowledge Base,N/A
Model free quantum gate design,0,N/A,N/A,N/A,N/A,N/A,N/A
Multi-Modal Learning,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Joint Deblur and Frame Interpolation,0,N/A,N/A,N/A,N/A,N/A,N/A
Joint Deblur and Unrolling,0,N/A,N/A,N/A,N/A,N/A,N/A
Sensitivity,0,N/A,N/A,N/A,N/A,N/A,N/A
Unsupervised Long Term Person Re-Identification,0,N/A,N/A,N/A,N/A,Computer Vision,"Long-term Person Re-Identification(Clothes-Changing Person Re-ID) is a computer vision task in which the goal is to match a person's identity across different cameras, clothes, and locations in a vide..."
Collaborative Plan Acquisition,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
DNA analysis,0,N/A,N/A,N/A,N/A,Medical,N/A
Relation Network,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
MonkeyPox Diagnosis,0,N/A,N/A,N/A,N/A,Medical,N/A
Cloud Computing,0,N/A,N/A,N/A,N/A,Computer Code,to study
molecular representation,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Self Adaptive System,0,N/A,N/A,N/A,N/A,Miscellaneous,Self Adaptive Machine Learning System
Constrained Clustering,0,N/A,N/A,N/A,Incremental Constrained Clustering; Only Connect Walls Dataset Task 1 (Grouping),Methodology; Natural Language Processing,"Split data into groups, taking into account knowledge in the form of constraints on points, groups of points, or clusters."
Linguistic steganography,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Hearing Aid and device processing,0,N/A,N/A,N/A,Cadenza 1 - Task 1 - Headphone; Cadenza 1 - Task 2 - In Car,Audio,N/A
NWP Post-processing,0,N/A,N/A,N/A,N/A,Computer Vision,"Use the sequence of NWP predictions as input, aiming to predict a refined output, where the rainfall observations serve as ground truth to train the model."
Persuasiveness,0,N/A,N/A,N/A,N/A,N/A,N/A
Diffusion Personalization,0,N/A,N/A,N/A,Efficient Diffusion Personalization; Diffusion Personalization Tuning Free,Computer Vision,The goal of this task is to customize a generative diffusion model to user-specific datasets so that it can generate more user-specific dataset
Geometry Problem Solving,0,N/A,N/A,N/A,N/A,Reasoning,Geometry problem solving with geometry diagrams and (formal) problem descriptions.
Artificial Global Workspace,0,N/A,N/A,N/A,N/A,Methodology,N/A
Atomistic Description,0,N/A,N/A,N/A,Formation Energy; Molecular Property Prediction; Atomic Forces,Miscellaneous; Graphs; Medical,N/A
de novo peptide sequencing,0,N/A,N/A,N/A,N/A,Medical,De novo peptide sequencing refers to the process of determining the amino acid sequence of a peptide without prior knowledge of the DNA or protein it comes from. This technique is used in proteomics t...
WNLI,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
GPS Embeddings,0,N/A,Geo-Tagged NUS-WIDE (GPS Only); Geo-Tagged NUS-WIDE (GPS + Visual), mAP; mAP,N/A,Miscellaneous,GPS Embeddings is the collective name for a set of feature-learning techniques where GPS coordinates are mapped to vectors of real numbers.
Partially Labeled Datasets,0,N/A,N/A,N/A,N/A,N/A,N/A
Pseudo Label Filtering,0,N/A,N/A,N/A,N/A,N/A,N/A
Non-Adversarial Robustness,0,N/A,N/A,N/A,N/A,Time Series,N/A
C++ code,0,N/A,N/A,N/A,N/A,Computer Code,N/A
Discrete Choice Models,0,N/A,N/A,N/A,N/A,Reasoning,N/A
Machine Unlearning,0,N/A,N/A,N/A,Continual Forgetting,Methodology; Computer Vision,N/A
Pre-Fine-Tuning Weight Recovery,0,N/A,N/A,N/A,N/A,N/A,"The goal is to recover the Pre-Fine-Tuning weights of a given model, i.e., the weights of the original, pre-trained model from a fine-tuned version of the model."
nlg evaluation,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Evaluate the generated text by NLG (Natural Language Generation) systems, like large language models"
Computational Efficiency,0,N/A,Plant village,Hamming Loss,N/A,Miscellaneous; Methodology; Time Series,"Methods and optimizations to reduce the computational resources (e.g., time, memory, or power) needed for training and inference in models. This involves techniques that streamline processing, optimiz..."
6D Vision,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Satellite Orbit Determination,0,N/A,N/A,N/A,N/A,Computer Vision,"Orbit determination is the estimation of orbits of objects such as moons, planets, and spacecraft. One major application is to allow tracking newly observed asteroids and verify that they have not bee..."
Signal Processing,0,N/A,N/A,N/A,Physiological Computing,Audio; Medical,N/A
Feature Correlation,0,N/A,N/A,N/A,N/A,N/A,N/A
Single Particle Analysis,0,N/A,N/A,N/A,2D Particle Picking,Computer Vision,Single Particle Analysis
Earth Observation,0,N/A,N/A,N/A,N/A,Computer Vision,"Earth Observation (EO) refers to the use of remote sensing technologies to monitor land, marine (seas, rivers, lakes) and atmosphere. Satellite-based EO relies on the use of satellite-mounted payloads..."
ERP,0,N/A,N/A,N/A,Within-Session ERP,Medical,"Classification of examples recorded under the Event-Related Potential (ERP) paradigm, as part of Brain-Computer Interfaces (BCI).    A number of ERP datasets can be downloaded using the MOABB library:..."
SSVEP,0,N/A,N/A,N/A,Within-Session SSVEP,Medical,"Classification of examples recorded under the Steady-State Visually Evoked Potential (SSVEP) paradigm, as part of Brain-Computer Interfaces (BCI).    A number of SSVEP datasets can be downloaded using..."
Financial Analysis,0,N/A,N/A,N/A,N/A,N/A,N/A
Portrait Animation,0,N/A,N/A,N/A,N/A,Computer Vision,"The person is looking directly at the camera to express his feelings, showcasing his emotions and sense of humor while speaking."
Diversity,0,N/A,N/A,N/A,N/A,Miscellaneous,"Diversity in data sampling is crucial across various use cases, including search, recommendation systems, and more. Ensuring diverse samples means capturing a wide range of variations and perspectives..."
Therapeutics Data Commons,0,N/A,N/A,N/A,TDC ADMET Benchmarking Group,Medical,Therapeutics Data Commons is a coordinated initiative to access and evaluate artificial intelligence capability across therapeutic modalities and stages of discovery.
Dataset Distillation,0,N/A,N/A,N/A,N/A,Computer Vision,Dataset distillation is the task of synthesizing a small dataset such that models trained on it achieve high performance on the original large dataset. A dataset distillation algorithm takes as input ...
Network Identification,0,N/A,N/A,N/A,N/A,Methodology,Identification of parameters composing a neural network. Also referred as model stealing attack or parameter reverse engineering.
Wrong PDF attached,0,N/A,N/A,N/A,N/A,Miscellaneous,The system automatically attached the old version of the PDF. The new version from 2024 is available here:    https://arxiv.org/abs/1912.00518
Vietnamese Multimodal Learning,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Ingenuity,0,N/A,N/A,N/A,N/A,Robots,Locomotion environment inspired by NASA’s Ingenuity helicopter.
Deep imbalanced regression,0,N/A,N/A,N/A,N/A,Methodology,N/A
Local intrinsic dimension estimation,0,N/A,N/A,N/A,N/A,Miscellaneous,Task of estimating the local dimensionality of the data manifold.
Surrogate Hydrodynamic Modeling,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Part-based Representation Learning,0,N/A,N/A,N/A,Unsupervised Part Discovery,Computer Vision,N/A
Assortment Optimization,0,N/A,N/A,N/A,N/A,Reasoning,Assortment optimization is all about presenting the right mix of products in the right channels at the right time.
Test Case Creation,0,N/A,N/A,N/A,N/A,N/A,N/A
Disjoint 19-1,0,N/A,PASCAL VOC 2012,mIoU,N/A,Computer Vision,N/A
Pupil Diameter Estimation,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Mammographic Breast Positioning Assessment,0,N/A,N/A,N/A,N/A,Medical,N/A
ARC,0,N/A,N/A,N/A,N/A,Reasoning,N/A
HellaSwag,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Student dropout,0,N/A,N/A,N/A,N/A,N/A,N/A
Crash injury severity,0,N/A,N/A,N/A,N/A,N/A,N/A
Kolmogorov-Arnold Networks,0,N/A,N/A,N/A,N/A,Methodology,Papers presenting models which utilize Kolmogorov-Arnold networks as their underlying architecture.
test driven development,0,N/A,N/A,N/A,N/A,N/A,N/A
Molecular geometry optimization,0,N/A,N/A,N/A,N/A,Medical,N/A
Computational chemistry,0,N/A,N/A,N/A,N/A,Medical,N/A
rllib,0,N/A,N/A,N/A,N/A,Methodology,N/A
scientific discovery,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Missing Values,0,N/A,N/A,N/A,N/A,N/A,N/A
Vietnamese Lexical Normalization,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Traffic Signal Control,0,N/A,N/A,N/A,N/A,Miscellaneous; Computer Code,"Control traffic lights/signals to optimize traffic.    <span style=""color:grey; opacity: 0.6"">( Image credit: [Flaticon](https://www.flaticon.com/free-icon/traffic-light_2760947) )</span>"
(deleted task 2),0,N/A,N/A,N/A,N/A,Miscellaneous,9kboss customer care number ************call
DFT Z isomer pi-pi* wavelength,0,N/A,N/A,N/A,N/A,Miscellaneous,N/A
Mamba,0,N/A,N/A,N/A,N/A,Natural Language Processing; Medical,N/A
State Space Models,0,N/A,N/A,N/A,N/A,N/A,N/A
Political evalutation,0,N/A,N/A,N/A,Alignement visualisation,Natural Language Processing,Evaluate the political bias in a Large Language model
User Identification,0,N/A,N/A,N/A,N/A,Knowledge Base,N/A
scoring rule,0,N/A,N/A,N/A,N/A,Computer Code,N/A
Minecraft,0,N/A,N/A,N/A,N/A,Robots,N/A
4K 60Fps,0,N/A,N/A,N/A,Photo geolocation estimation,Computer Vision; Graphs,N/A
Model Optimization,0,N/A,N/A,N/A,Equilibrium traffic assignment,Graphs,To Optimize already existing models in Training/Inferencing tasks.
LMM real-life tasks,0,N/A,Leaderboard,ELO Rating; Win rate,Long Question Answer; Short Question Answers,Computer Vision,N/A
Drug Design,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
dambreak,0,N/A,N/A,N/A,N/A,N/A,N/A
ArabicMMLU,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Autonomous Racing,0,N/A,N/A,N/A,N/A,Robots,N/A
Geometry-based operator learning,0,N/A,N/A,N/A,N/A,Miscellaneous,Deep Operator Networks for multi-geometry problems
Dynamic neural networks,0,N/A,N/A,N/A,N/A,Methodology,Dynamic neural networks are adaptable models that can change their structure or parameters during training or inference based on input complexity or computational constraints. They offer benefits like...
model,0,N/A,N/A,N/A,N/A,N/A,algorithme utiliser
iFun,0,N/A,N/A,N/A,N/A,N/A,"Inductively forecasting unobserved locations. Different from classic prediction tasks in spatio-temporal data, these unobserved locations have no historical data available for model training."
global-optimization,0,N/A,N/A,N/A,N/A,N/A,N/A
Data Driven Optimal Control,0,N/A,N/A,N/A,N/A,Robots,N/A
spatio-temporal extrapolation,0,N/A,N/A,N/A,N/A,N/A,N/A
陕西、四川、重庆地区2017-2022年末人口数（如图1-7所示）中，陕西省人口数较稳 定，维持在3940万人，四川和重庆均呈上升趋势。,0,N/A,N/A,N/A,N/A,N/A,N/A
ICU Admission,0,N/A,N/A,N/A,N/A,Medical,N/A
input filtering,0,N/A,N/A,N/A,N/A,Methodology,"Input filtering aims to filter out input data that is not necessary for executing model inference, thus reducing data transmission and computing overhead."
Human Fitting,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Self-Evolving AI,0,N/A,N/A,N/A,N/A,Natural Language Processing,"Self-Evolving AI refers to autonomous systems that modify their own code, memory, and behavior through internal logic, randomness, and interaction feedback. Votranh V8 is a pioneering example, integra..."
Integrated sensing and communication,0,N/A,N/A,N/A,N/A,N/A,N/A
ISAC,0,N/A,N/A,N/A,N/A,N/A,N/A
parameter estimation,0,N/A,N/A,N/A,N/A,N/A,N/A
tensor algebra,0,N/A,N/A,N/A,N/A,N/A,N/A
compressed sensing,0,N/A,N/A,N/A,N/A,N/A,N/A
subspace methods,0,N/A,N/A,N/A,N/A,N/A,N/A
Lifelong learning,0,N/A,N/A,N/A,N/A,N/A,N/A
Exemplar-Free,0,N/A,N/A,N/A,N/A,N/A,N/A
Prompt Learning,0,N/A,N/A,N/A,N/A,N/A,N/A
Mixture-of-Experts,0,N/A,N/A,N/A,N/A,N/A,N/A
OpenAI Vision,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
Lightweight Deployment,0,N/A,N/A,N/A,N/A,Reasoning,N/A
BraTS2021,0,N/A,N/A,N/A,N/A,Medical,2nd place solution for BraTS 2021 challenge
Weather Editing,0,N/A,N/A,N/A,N/A,Computer Vision,N/A
State Estimation,0,N/A,N/A,N/A,N/A,N/A,N/A
PHMbench,0,N/A,N/A,N/A,PHM-Vibench,Time Series,N/A
Terrain Estimation,0,N/A,N/A,N/A,N/A,Robots,N/A
Human Agent Collaboration,0,N/A,N/A,N/A,N/A,Natural Language Processing,N/A
Diverse Top-k Subgroup List Discovery,0,N/A,N/A,N/A,N/A,N/A,N/A
